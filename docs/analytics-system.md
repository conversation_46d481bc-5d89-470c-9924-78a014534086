# Analytics System Documentation

## Overview

The Layrs platform includes a comprehensive analytics system that tracks user engagement, chat usage, and assessment performance. This system provides valuable insights for platform optimization and user experience improvements.

## Database Schema

### Tables Created

1. **chat_analytics**
   - Tracks chat sessions, message counts, and conversation duration
   - Fields: user_id, session_id, message_count, conversation_duration_seconds, question_id, context_type, context_id

2. **assessment_analytics**
   - Records assessment completions, scores, and performance metrics
   - Fields: user_id, question_id, context_type, context_id, score, duration_seconds, node_count, edge_count, components_used, is_best_score

3. **user_session_analytics**
   - Monitors user sessions, page views, and actions
   - Fields: user_id, session_start, session_end, page_views, actions_performed, questions_attempted

4. **component_usage_analytics**
   - Tracks component usage patterns (future enhancement)
   - Fields: user_id, component_type, question_id, context_type, context_id, usage_count

### Security

- **Row Level Security (RLS)** enabled on all tables
- Users can only access their own data
- Admin user (<EMAIL>) has read access to all data
- Custom `is_admin()` function for secure admin checks

## Usage

### Chat Analytics

```typescript
import { useChatAnalytics } from '@/hooks/useAnalytics';

const { startChatSession, trackMessage, endChatSession } = useChatAnalytics(
  questionId,
  'question', // context type
  contextId
);

// Start session when chat begins
await startChatSession();

// Track each message sent
await trackMessage();

// End session when chat closes
await endChatSession();
```

### Assessment Analytics

```typescript
import { useAssessmentAnalytics } from '@/hooks/useAnalytics';

const { startAssessment, recordAssessment } = useAssessmentAnalytics();

// Start tracking when assessment begins
startAssessment();

// Record completion
await recordAssessment(
  questionId,
  'question',
  contextId,
  score,
  nodeCount,
  edgeCount,
  componentsUsed,
  isBestScore
);
```

## Admin Dashboard

### Access
- Navigate to `/admin/analytics`
- Requires admin authentication (<EMAIL>)

### Features
- **Overview Tab**: Key performance indicators
- **Chat Analytics**: Session patterns and engagement metrics
- **Assessment Analytics**: Performance trends and score distributions
- **User Analytics**: Session behavior and retention patterns
- **Connection Test**: Verify database access and permissions

### Metrics Displayed
- Total chat sessions and messages
- Average conversation duration
- Assessment completion rates and scores
- User session patterns
- Component usage statistics

## Integration Points

### Automatic Tracking
- **ChatPanel.tsx**: Automatically tracks chat sessions
- **AssessmentHandler.tsx**: Records assessment completions
- **Index.tsx & GuidedMode.tsx**: Integrated analytics hooks

### Manual Tracking
- Use analytics hooks in custom components
- Call analytics functions directly for specific events

## Troubleshooting

### Permission Denied Errors
- Ensure user is authenticated
- Check if admin access is required for the operation
- Verify RLS policies are correctly configured

### Connection Issues
- Use the "Test Connection" button in admin dashboard
- Check Supabase connection and authentication
- Verify table permissions and policies

### Data Not Appearing
- Check browser console for errors
- Verify analytics functions are being called
- Ensure proper context and user IDs are passed

## Privacy & Compliance

- All data is anonymized and aggregated for reporting
- Individual user data is protected by RLS policies
- Only authorized administrators can access platform-wide statistics
- Data is encrypted at rest in Supabase

## Future Enhancements

- Time-series charts for trend analysis
- User cohort analysis and retention metrics
- Export capabilities for detailed reporting
- Alert system for unusual patterns
- Real-time analytics dashboard
- Component usage heatmaps

## API Reference

### Analytics Service Functions

```typescript
// Chat Analytics
chatAnalytics.startChatSession(userId, questionId?, contextType?, contextId?)
chatAnalytics.updateChatSession(sessionId, messageCount, durationSeconds?)
chatAnalytics.endChatSession(sessionId, finalMessageCount, totalDurationSeconds)

// Assessment Analytics
assessmentAnalytics.recordAssessment(userId, questionId, contextType, contextId, score, durationSeconds, nodeCount, edgeCount, componentsUsed, isBestScore)
assessmentAnalytics.getAssessmentStats(userId?)

// General Analytics
generalAnalytics.getPlatformStats() // Admin only
testAnalytics.testConnection() // Test database access
```

### React Hooks

```typescript
// Chat Analytics Hook
const { sessionId, messageCount, startChatSession, trackMessage, endChatSession, isActive } = useChatAnalytics(questionId?, contextType?, contextId?)

// Assessment Analytics Hook
const { isTracking, startAssessment, recordAssessment, getAssessmentStats } = useAssessmentAnalytics()

// Session Analytics Hook
const { sessionId, pageViews, actionsPerformed, questionsAttempted, trackPageView, trackAction, trackQuestionAttempt, endSession } = useSessionAnalytics()
```

## Support

For issues or questions about the analytics system:
1. Check the admin dashboard connection status
2. Review browser console for error messages
3. Verify user permissions and authentication
4. Contact the development team with specific error details
