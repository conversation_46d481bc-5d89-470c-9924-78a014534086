# DodoPayments Integration Documentation

## Overview
This document describes the database schema and webhook integration required to support subscriptions, payments, and credit management using DodoPayments.

---

## Database Tables

### 1. `plans`
**Purpose:** Stores all available subscription plans and their properties.

| Column                | Type      | Description                                 |
|-----------------------|-----------|---------------------------------------------|
| id                    | text (PK) | Unique plan identifier (e.g., 'monthly')    |
| name                  | text      | Display name for the plan                   |
| price                 | numeric   | Price per billing period                    |
| period                | text      | Billing period ('monthly', 'yearly', etc.)  |
| credits               | integer   | Credits allocated per period                |
| dodopayments_plan_id  | text      | (Optional) DodoPayments plan mapping        |

```sql
create table plans (
  id text primary key,
  name text not null,
  price numeric not null,
  period text not null,
  credits integer not null default 0,
  dodopayments_plan_id text
);
```

---

### 2. `subscriptions`
**Purpose:** Tracks each user's subscription status and plan.

| Column         | Type      | Description                                 |
|----------------|-----------|---------------------------------------------|
| id             | uuid (PK) | Unique subscription ID                      |
| user_id        | uuid      | Reference to `auth.users(id)`               |
| plan_id        | text      | Reference to `plans(id)`                    |
| status         | text      | 'active', 'canceled', 'on_hold', etc.       |
| start_date     | timestamptz | When the subscription started             |
| end_date       | timestamptz | When the subscription ended (nullable)    |
| next_billing_at| timestamptz | Next billing date (nullable)              |
| external_id    | text      | DodoPayments subscription ID                |
| created_at     | timestamptz | Record creation timestamp                 |
| updated_at     | timestamptz | Last update timestamp                     |

```sql
create table subscriptions (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id),
  plan_id text not null references plans(id),
  status text not null,
  start_date timestamptz not null default now(),
  end_date timestamptz,
  next_billing_at timestamptz,
  external_id text,
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);
create index subscriptions_user_id_idx on subscriptions(user_id);
```

---

### 3. `payments`
**Purpose:** Records all payment transactions.

| Column           | Type      | Description                                 |
|------------------|-----------|---------------------------------------------|
| id               | uuid (PK) | Unique payment ID                           |
| user_id          | uuid      | Reference to `auth.users(id)`               |
| subscription_id  | uuid      | Reference to `subscriptions(id)`            |
| amount           | numeric   | Payment amount                              |
| currency         | text      | Currency code (e.g., 'USD')                 |
| status           | text      | 'succeeded', 'failed', etc.                 |
| payment_method   | text      | 'dodopayments'                              |
| external_id      | text      | DodoPayments payment ID                     |
| created_at       | timestamptz | Payment creation timestamp                |

```sql
create table payments (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id),
  subscription_id uuid references subscriptions(id),
  amount numeric not null,
  currency text not null,
  status text not null,
  payment_method text not null,
  external_id text,
  created_at timestamptz not null default now()
);
create index payments_user_id_idx on payments(user_id);
```

---

### 4. `user_credits`
**Purpose:** Tracks the current credit balance for each user.

| Column     | Type      | Description                                 |
|------------|-----------|---------------------------------------------|
| user_id    | uuid (PK) | Reference to `auth.users(id)`               |
| credits    | integer   | Current credit balance                      |
| updated_at | timestamptz | Last update timestamp                     |

```sql
create table user_credits (
  user_id uuid primary key references auth.users(id),
  credits integer not null default 0,
  updated_at timestamptz not null default now()
);
```

---

## Webhook Integration

### Webhook Endpoint
Set up an endpoint (e.g., `/api/webhooks/dodopayments`) to receive webhook events from DodoPayments.

**Reference:**  
[DodoPayments Webhook Event Guide](https://docs.dodopayments.com/developer-resources/webhooks/intents/webhook-events-guide)

---

### Relevant Webhook Events

| Event Type                  | Description                                      | Action to Take                                 |
|-----------------------------|--------------------------------------------------|------------------------------------------------|
| payment.succeeded           | Payment processed successfully                   | Record payment, update credits if needed       |
| payment.failed              | Payment failed                                   | Record failure, notify user                    |
| subscription.active         | Subscription is now active                       | Create/update subscription, allocate credits   |
| subscription.renewed        | Subscription renewed                             | Update subscription, allocate new credits      |
| subscription.plan_changed   | Plan upgraded/downgraded                         | Update plan, adjust credits if needed          |
| subscription.cancelled      | Subscription cancelled                           | Mark as canceled, revoke features/credits      |
| subscription.on_hold        | Subscription on hold                             | Mark as on hold                                |
| subscription.paused         | Subscription paused                              | Mark as paused                                 |
| subscription.failed         | Subscription failed to activate                  | Mark as failed                                 |
| subscription.expired        | Subscription expired                             | Mark as expired                                |

---

### Webhook Handler Example (Node.js/Express)

```js
app.post('/api/webhooks/dodopayments', async (req, res) => {
  const event = req.body;

  switch (event.type) {
    case 'payment.succeeded':
      // Insert payment, update credits if needed
      break;
    case 'subscription.active':
      // Create or update subscription, allocate credits
      break;
    case 'subscription.renewed':
      // Update subscription, allocate new credits
      break;
    case 'subscription.plan_changed':
      // Update plan, adjust credits
      break;
    case 'subscription.cancelled':
      // Mark subscription as canceled
      break;
    // ... handle other events as needed
    default:
      console.log('Unhandled DodoPayments event:', event.type);
  }

  res.status(200).send('ok');
});
```

---

### Credit Allocation Logic
- On `subscription.active` or `subscription.renewed`, look up the plan’s credit allocation and add to `user_credits`.
- On usage, deduct credits from `user_credits`.
- On plan change, adjust credits as per business logic.

---

## Summary
- Use the provided SQL scripts to set up your tables in Supabase.
- Implement a webhook endpoint to handle DodoPayments events and update your tables accordingly.
- Use the `user_credits` table to manage and check user credits for feature access.

**For more details on event payloads and integration, see the [DodoPayments Webhook Event Guide](https://docs.dodopayments.com/developer-resources/webhooks/intents/webhook-events-guide).** 