/**
 * Basic autosave functionality tests
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { setupAutosaveTests, getDesignFromStorage, setDesignInStorage } from './setup';

setupAutosaveTests();

describe('Basic Autosave Tests', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('localStorage Persistence', () => {
    it('should save and retrieve design data from localStorage', () => {
      const testData = {
        nodes: [
          { id: 'node-1', position: { x: 100, y: 100 }, data: { label: 'Test Node' } }
        ],
        edges: [
          { id: 'edge-1', source: 'node-1', target: 'node-2', data: {} }
        ],
        userJourneys: 'Test journey',
        assumptions: 'Test assumptions',
        constraints: 'Test constraints'
      };

      // Save data
      setDesignInStorage('test-question', testData);

      // Retrieve data
      const retrievedData = getDesignFromStorage('test-question');

      expect(retrievedData).toEqual(testData);
      expect(retrievedData.nodes).toHaveLength(1);
      expect(retrievedData.edges).toHaveLength(1);
    });

    it('should handle different context types', () => {
      const questionData = { nodes: [], edges: [], userJourneys: 'Question data' };
      const freeData = { nodes: [], edges: [], userJourneys: 'Free canvas data' };

      // Save to different contexts
      setDesignInStorage('test-question', questionData, 'question');
      setDesignInStorage('free-canvas', freeData, 'free');

      // Retrieve from different contexts
      const retrievedQuestion = getDesignFromStorage('test-question', 'question');
      const retrievedFree = getDesignFromStorage('free-canvas', 'free');

      expect(retrievedQuestion.userJourneys).toBe('Question data');
      expect(retrievedFree.userJourneys).toBe('Free canvas data');
    });

    it('should return null for non-existent data', () => {
      const result = getDesignFromStorage('non-existent-question');
      expect(result).toBeNull();
    });

    it('should handle corrupted localStorage data', () => {
      // Set invalid JSON
      localStorage.setItem('layrs-design-question-test', 'invalid-json');

      const result = getDesignFromStorage('test');
      expect(result).toBeNull();
    });
  });

  describe('Data Structure Validation', () => {
    it('should preserve complex node data', () => {
      const complexData = {
        nodes: [
          {
            id: 'composite-1',
            type: 'compositeNode',
            position: { x: 0, y: 0 },
            data: {
              label: 'Load Balancer',
              components: ['child-1', 'child-2'],
              properties: {
                instances: 3,
                healthCheck: true
              }
            }
          },
          {
            id: 'child-1',
            type: 'webServer',
            position: { x: 50, y: 50 },
            parentId: 'composite-1',
            data: {
              label: 'Web Server 1',
              port: 8080
            }
          }
        ],
        edges: [
          {
            id: 'edge-1',
            source: 'composite-1',
            target: 'child-1',
            data: {
              connectionNumber: 'HTTP',
              bandwidth: '1Gbps'
            }
          }
        ],
        userJourneys: 'Complex system design',
        assumptions: 'High availability required',
        constraints: 'Budget: $10k/month'
      };

      setDesignInStorage('complex-design', complexData);
      const retrieved = getDesignFromStorage('complex-design');

      expect(retrieved.nodes[0].data.components).toEqual(['child-1', 'child-2']);
      expect(retrieved.nodes[0].data.properties.instances).toBe(3);
      expect(retrieved.nodes[1].parentId).toBe('composite-1');
      expect(retrieved.edges[0].data.bandwidth).toBe('1Gbps');
    });

    it('should handle empty designs', () => {
      const emptyData = {
        nodes: [],
        edges: [],
        userJourneys: '',
        assumptions: '',
        constraints: ''
      };

      setDesignInStorage('empty-design', emptyData);
      const retrieved = getDesignFromStorage('empty-design');

      expect(retrieved.nodes).toHaveLength(0);
      expect(retrieved.edges).toHaveLength(0);
      expect(retrieved.userJourneys).toBe('');
    });

    it('should handle large designs', () => {
      // Create a design with many components
      const nodes = Array.from({ length: 50 }, (_, i) => ({
        id: `node-${i}`,
        type: 'customNode',
        position: { x: (i % 10) * 100, y: Math.floor(i / 10) * 100 },
        data: {
          label: `Component ${i}`,
          description: `This is component number ${i} with detailed properties`,
          properties: {
            cpu: '2 cores',
            memory: '4GB',
            storage: '100GB'
          }
        }
      }));

      const edges = Array.from({ length: 30 }, (_, i) => ({
        id: `edge-${i}`,
        source: `node-${i}`,
        target: `node-${(i + 1) % 50}`,
        data: {
          connectionNumber: `Connection ${i}`,
          protocol: 'HTTP',
          bandwidth: '1Gbps'
        }
      }));

      const largeData = {
        nodes,
        edges,
        userJourneys: 'Large system design with many components',
        assumptions: 'Scalable architecture needed',
        constraints: 'Performance critical'
      };

      setDesignInStorage('large-design', largeData);
      const retrieved = getDesignFromStorage('large-design');

      expect(retrieved.nodes).toHaveLength(50);
      expect(retrieved.edges).toHaveLength(30);
      expect(retrieved.nodes[25].data.label).toBe('Component 25');
      expect(retrieved.edges[15].data.protocol).toBe('HTTP');
    });
  });

  describe('Storage Key Generation', () => {
    it('should generate correct storage keys', () => {
      const questionKey = 'layrs-design-question-test-question';
      const freeKey = 'layrs-design-free-free-canvas';
      const guidedKey = 'layrs-design-guided-course-1';

      // Test by setting and checking localStorage directly
      localStorage.setItem(questionKey, JSON.stringify({ test: 'question' }));
      localStorage.setItem(freeKey, JSON.stringify({ test: 'free' }));
      localStorage.setItem(guidedKey, JSON.stringify({ test: 'guided' }));

      const questionData = getDesignFromStorage('test-question', 'question');
      const freeData = getDesignFromStorage('free-canvas', 'free');
      const guidedData = getDesignFromStorage('course-1', 'guided');

      expect(questionData.test).toBe('question');
      expect(freeData.test).toBe('free');
      expect(guidedData.test).toBe('guided');
    });
  });

  describe('Browser Compatibility', () => {
    it('should handle localStorage quota exceeded', () => {
      // Mock localStorage to throw quota exceeded error
      const originalSetItem = localStorage.setItem;
      localStorage.setItem = vi.fn().mockImplementation(() => {
        throw new Error('QuotaExceededError');
      });

      // Should not throw error when saving fails
      expect(() => {
        setDesignInStorage('test', { nodes: [], edges: [] });
      }).not.toThrow();

      // Restore original method
      localStorage.setItem = originalSetItem;
    });

    it('should handle localStorage being disabled', () => {
      // This test validates that the utility functions handle localStorage gracefully
      // Since we're using mocks, we'll test the error handling path instead

      // Should handle gracefully when localStorage throws errors
      expect(() => {
        setDesignInStorage('test', { nodes: [], edges: [] });
      }).not.toThrow();

      // Should return data when localStorage is working (with our mock)
      const result = getDesignFromStorage('test');
      expect(result).toEqual({ nodes: [], edges: [] });
    });
  });
});
