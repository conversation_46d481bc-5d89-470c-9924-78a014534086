/**
 * Integration tests for autosave functionality
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  setupAutosaveTests, 
  createMockDesignData,
  setDesignInStorage,
  getDesignFromStorage,
  simulatePageReload,
  simulateTabSwitch,
  simulateNetworkChange
} from './setup';

setupAutosaveTests();

describe('Autosave Integration Scenarios', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('Cross-Session Persistence', () => {
    it('should persist design across page reloads', async () => {
      const originalDesign = createMockDesignData({
        nodes: [
          { id: 'lb-1', type: 'loadBalancer', position: { x: 100, y: 100 }, data: { label: 'Load Balancer' } },
          { id: 'web-1', type: 'webServer', position: { x: 50, y: 200 }, data: { label: 'Web Server' } }
        ],
        edges: [
          { id: 'edge-1', source: 'lb-1', target: 'web-1', data: { connectionNumber: 'HTTP' } }
        ]
      });

      // Save design before "page reload"
      setDesignInStorage('system-design', originalDesign);

      // Simulate page reload
      simulatePageReload();

      // Design should persist
      const restoredDesign = getDesignFromStorage('system-design');
      expect(restoredDesign).toBeTruthy();
      expect(restoredDesign.nodes).toHaveLength(2);
      expect(restoredDesign.edges).toHaveLength(1);
      expect(restoredDesign.nodes[0].data.label).toBe('Load Balancer');
    });

    it('should handle multiple context switches', async () => {
      const questionDesign = createMockDesignData({
        userJourneys: 'Question context design',
        nodes: [{ id: 'q1', position: { x: 0, y: 0 }, data: { label: 'Question Node' } }]
      });

      const freeDesign = createMockDesignData({
        userJourneys: 'Free canvas design',
        nodes: [{ id: 'f1', position: { x: 100, y: 100 }, data: { label: 'Free Node' } }]
      });

      // Save to different contexts
      setDesignInStorage('interview-q1', questionDesign, 'question');
      setDesignInStorage('free-canvas', freeDesign, 'free');

      // Retrieve from different contexts
      const retrievedQuestion = getDesignFromStorage('interview-q1', 'question');
      const retrievedFree = getDesignFromStorage('free-canvas', 'free');

      expect(retrievedQuestion.userJourneys).toBe('Question context design');
      expect(retrievedFree.userJourneys).toBe('Free canvas design');
      expect(retrievedQuestion.nodes[0].data.label).toBe('Question Node');
      expect(retrievedFree.nodes[0].data.label).toBe('Free Node');
    });
  });

  describe('Real-World Design Workflows', () => {
    it('should handle incremental design building', async () => {
      // Step 1: Start with basic architecture
      const step1 = createMockDesignData({
        nodes: [
          { id: 'client', type: 'client', position: { x: 0, y: 0 }, data: { label: 'Client' } }
        ],
        edges: [],
        userJourneys: 'User makes request'
      });

      setDesignInStorage('incremental-design', step1);

      // Step 2: Add load balancer
      const step2 = {
        ...step1,
        nodes: [
          ...step1.nodes,
          { id: 'lb', type: 'loadBalancer', position: { x: 200, y: 0 }, data: { label: 'Load Balancer' } }
        ],
        edges: [
          { id: 'e1', source: 'client', target: 'lb', data: { connectionNumber: 'HTTPS' } }
        ]
      };

      setDesignInStorage('incremental-design', step2);

      // Step 3: Add web servers
      const step3 = {
        ...step2,
        nodes: [
          ...step2.nodes,
          { id: 'web1', type: 'webServer', position: { x: 150, y: 100 }, data: { label: 'Web Server 1' } },
          { id: 'web2', type: 'webServer', position: { x: 250, y: 100 }, data: { label: 'Web Server 2' } }
        ],
        edges: [
          ...step2.edges,
          { id: 'e2', source: 'lb', target: 'web1', data: { connectionNumber: 'HTTP' } },
          { id: 'e3', source: 'lb', target: 'web2', data: { connectionNumber: 'HTTP' } }
        ]
      };

      setDesignInStorage('incremental-design', step3);

      // Verify final state
      const finalDesign = getDesignFromStorage('incremental-design');
      expect(finalDesign.nodes).toHaveLength(4); // client, lb, web1, web2
      expect(finalDesign.edges).toHaveLength(3); // client->lb, lb->web1, lb->web2
    });

    it('should handle complex system design with microservices', async () => {
      const microservicesDesign = createMockDesignData({
        nodes: [
          { id: 'api-gw', type: 'apiGateway', position: { x: 200, y: 0 }, data: { label: 'API Gateway' } },
          { id: 'user-svc', type: 'microservice', position: { x: 100, y: 150 }, data: { label: 'User Service' } },
          { id: 'order-svc', type: 'microservice', position: { x: 200, y: 150 }, data: { label: 'Order Service' } },
          { id: 'user-db', type: 'database', position: { x: 100, y: 300 }, data: { label: 'User DB' } },
          { id: 'order-db', type: 'database', position: { x: 200, y: 300 }, data: { label: 'Order DB' } }
        ],
        edges: [
          { id: 'gw-user', source: 'api-gw', target: 'user-svc', data: { connectionNumber: 'REST' } },
          { id: 'gw-order', source: 'api-gw', target: 'order-svc', data: { connectionNumber: 'REST' } },
          { id: 'user-db-conn', source: 'user-svc', target: 'user-db', data: { connectionNumber: 'SQL' } },
          { id: 'order-db-conn', source: 'order-svc', target: 'order-db', data: { connectionNumber: 'SQL' } }
        ],
        userJourneys: 'E-commerce platform with microservices architecture',
        assumptions: 'High availability, eventual consistency acceptable',
        constraints: 'Must handle 10k concurrent users'
      });

      setDesignInStorage('microservices-ecommerce', microservicesDesign);

      const retrieved = getDesignFromStorage('microservices-ecommerce');
      expect(retrieved.nodes).toHaveLength(5);
      expect(retrieved.edges).toHaveLength(4);
      expect(retrieved.userJourneys).toContain('microservices');
      expect(retrieved.constraints).toContain('10k concurrent');
    });
  });

  describe('Browser Event Simulation', () => {
    it('should handle tab switching during design work', () => {
      const workInProgress = createMockDesignData({
        nodes: [
          { id: 'draft-1', position: { x: 0, y: 0 }, data: { label: 'Draft Component' } }
        ],
        userJourneys: 'Work in progress'
      });

      setDesignInStorage('wip-design', workInProgress);

      // Simulate tab switch (user checks documentation)
      // Note: In real tests, this would be async, but for unit tests we just verify persistence
      simulateTabSwitch();

      // Design should still be there
      const afterTabSwitch = getDesignFromStorage('wip-design');
      expect(afterTabSwitch).toBeTruthy();
      expect(afterTabSwitch.userJourneys).toBe('Work in progress');
    });

    it('should handle network connectivity changes', async () => {
      const onlineDesign = createMockDesignData({
        nodes: [{ id: 'online-node', position: { x: 0, y: 0 }, data: { label: 'Created Online' } }]
      });

      // Save while online
      setDesignInStorage('network-test', onlineDesign);

      // Go offline
      simulateNetworkChange(false);

      // Should still be able to read from localStorage
      const offlineRead = getDesignFromStorage('network-test');
      expect(offlineRead).toBeTruthy();
      expect(offlineRead.nodes[0].data.label).toBe('Created Online');

      // Add more data while offline
      const offlineDesign = {
        ...offlineRead,
        nodes: [
          ...offlineRead.nodes,
          { id: 'offline-node', position: { x: 100, y: 100 }, data: { label: 'Created Offline' } }
        ]
      };

      setDesignInStorage('network-test', offlineDesign);

      // Come back online
      simulateNetworkChange(true);

      // Data should still be there
      const backOnline = getDesignFromStorage('network-test');
      expect(backOnline.nodes).toHaveLength(2);
      expect(backOnline.nodes[1].data.label).toBe('Created Offline');
    });
  });

  describe('Performance and Scale', () => {
    it('should handle large design files efficiently', async () => {
      const startTime = Date.now();

      // Create a large design
      const largeDesign = createMockDesignData({
        nodes: Array.from({ length: 30 }, (_, i) => ({
          id: `node-${i}`,
          type: 'customNode',
          position: { x: (i % 6) * 100, y: Math.floor(i / 6) * 100 },
          data: {
            label: `Component ${i}`,
            description: `Detailed description for component ${i}`
          }
        })),
        edges: Array.from({ length: 20 }, (_, i) => ({
          id: `edge-${i}`,
          source: `node-${i}`,
          target: `node-${(i + 1) % 30}`,
          data: {
            connectionNumber: `Connection ${i}`,
            protocol: 'HTTP'
          }
        }))
      });

      // Save large design
      setDesignInStorage('large-design', largeDesign);

      const saveTime = Date.now() - startTime;

      // Retrieve large design
      const retrieveStart = Date.now();
      const retrieved = getDesignFromStorage('large-design');
      const retrieveTime = Date.now() - retrieveStart;

      // Verify data integrity
      expect(retrieved.nodes).toHaveLength(30);
      expect(retrieved.edges).toHaveLength(20);
      expect(retrieved.nodes[15].data.label).toBe('Component 15');

      // Performance should be reasonable (less than 1 second for large designs)
      expect(saveTime).toBeLessThan(1000);
      expect(retrieveTime).toBeLessThan(1000);
    });

    it('should handle rapid successive saves', async () => {
      let currentDesign = createMockDesignData();

      // Simulate rapid saves (like user dragging components quickly)
      for (let i = 0; i < 5; i++) {
        currentDesign = {
          ...currentDesign,
          nodes: [
            ...currentDesign.nodes,
            { id: `rapid-${i}`, position: { x: i * 10, y: i * 10 }, data: { label: `Rapid ${i}` } }
          ]
        };

        setDesignInStorage('rapid-saves', currentDesign);
      }

      // Final state should have all components
      const finalDesign = getDesignFromStorage('rapid-saves');
      expect(finalDesign.nodes).toHaveLength(7); // 2 original + 5 rapid
      expect(finalDesign.nodes[6].data.label).toBe('Rapid 4');
    });
  });
});
