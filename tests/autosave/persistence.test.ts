/**
 * Tests for autosave persistence across page reloads, tab switches, and app switches
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  setupAutosaveTests, 
  createMockDesignData,
  setDesignInStorage,
  getDesignFromStorage,
  simulatePageReload,
  simulateTabSwitch,
  simulateAppSwitch,
  simulateNetworkChange
} from './setup';

setupAutosaveTests();

describe('Autosave Persistence Scenarios', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('Page Reload Scenarios', () => {
    it('should persist design data across page reload', () => {
      const designData = createMockDesignData({
        nodes: [
          { id: 'lb-1', type: 'loadBalancer', position: { x: 100, y: 100 }, data: { label: 'Load Balancer' } },
          { id: 'web-1', type: 'webServer', position: { x: 50, y: 200 }, data: { label: 'Web Server 1' } }
        ],
        edges: [
          { id: 'edge-1', source: 'lb-1', target: 'web-1', data: { connectionNumber: 'HTTP' } }
        ],
        userJourneys: 'Load balancer distributes traffic to web servers'
      });
      
      // Save design before "page reload"
      setDesignInStorage('test-question', designData);

      // Simulate page reload
      simulatePageReload();

      // Design should persist after reload
      const restoredData = getDesignFromStorage('test-question');
      expect(restoredData).toBeTruthy();
      expect(restoredData.nodes).toHaveLength(2);
      expect(restoredData.edges).toHaveLength(1);
      expect(restoredData.userJourneys).toBe('Load balancer distributes traffic to web servers');
      expect(restoredData.nodes[0].data.label).toBe('Load Balancer');
    });

    it('should handle multiple rapid saves before reload', () => {
      let currentDesign = createMockDesignData();

      // Simulate rapid saves (like user adding components quickly)
      for (let i = 1; i <= 3; i++) {
        currentDesign = {
          ...currentDesign,
          nodes: [
            ...currentDesign.nodes,
            { id: `node-${i}`, position: { x: i * 50, y: i * 50 }, data: { label: `Component ${i}` } }
          ]
        };
        setDesignInStorage('rapid-design', currentDesign);
      }

      // Simulate page reload
      simulatePageReload();

      // Should save the latest state
      const savedData = getDesignFromStorage('rapid-design');
      expect(savedData.nodes).toHaveLength(5); // 2 original + 3 added
      expect(savedData.nodes[4].data.label).toBe('Component 3');
    });
  });

  describe('Tab Switch Scenarios', () => {
    it('should maintain data when tab becomes hidden and visible', () => {
      const workInProgress = createMockDesignData({
        nodes: [
          { id: 'api-1', type: 'apiGateway', position: { x: 100, y: 100 }, data: { label: 'API Gateway' } }
        ],
        userJourneys: 'API connects to database'
      });

      setDesignInStorage('wip-design', workInProgress);

      // Simulate tab switch (user checks documentation)
      simulateTabSwitch();

      // Design should still be there
      const afterTabSwitch = getDesignFromStorage('wip-design');
      expect(afterTabSwitch).toBeTruthy();
      expect(afterTabSwitch.nodes).toHaveLength(1);
      expect(afterTabSwitch.userJourneys).toBe('API connects to database');
    });

    it('should handle tab visibility changes during design work', () => {
      const initialDesign = createMockDesignData({
        nodes: [{ id: 'start', position: { x: 0, y: 0 }, data: { label: 'Start' } }]
      });

      setDesignInStorage('tab-test', initialDesign);

      // Switch tabs
      simulateTabSwitch();

      // Add more components after tab switch
      const updatedDesign = {
        ...initialDesign,
        nodes: [
          ...initialDesign.nodes,
          { id: 'end', position: { x: 100, y: 100 }, data: { label: 'End' } }
        ]
      };

      setDesignInStorage('tab-test', updatedDesign);

      const finalData = getDesignFromStorage('tab-test');
      expect(finalData.nodes).toHaveLength(2);
      expect(finalData.nodes[1].data.label).toBe('End');
    });
  });

  describe('Application Switch Scenarios', () => {
    it('should handle window focus/blur events', () => {
      const designData = createMockDesignData({
        nodes: [
          { id: 'service-1', type: 'microservice', position: { x: 0, y: 0 }, data: { label: 'User Service' } }
        ],
        userJourneys: 'Microservices architecture'
      });

      setDesignInStorage('focus-test', designData);

      // Simulate app switch (user checks Slack)
      simulateAppSwitch();

      // Data should persist
      const afterAppSwitch = getDesignFromStorage('focus-test');
      expect(afterAppSwitch).toBeTruthy();
      expect(afterAppSwitch.nodes).toHaveLength(1);
      expect(afterAppSwitch.userJourneys).toBe('Microservices architecture');
    });

    it('should maintain save queue across focus changes', () => {
      let currentDesign = createMockDesignData();

      // Start multiple saves
      for (let i = 1; i <= 2; i++) {
        currentDesign = {
          ...currentDesign,
          nodes: [
            ...currentDesign.nodes,
            { id: `focus-${i}`, position: { x: i * 100, y: 0 }, data: { label: `Focus ${i}` } }
          ]
        };
        setDesignInStorage('focus-queue', currentDesign);
      }

      // Switch apps
      simulateAppSwitch();

      // All saves should complete
      const finalData = getDesignFromStorage('focus-queue');
      expect(finalData.nodes).toHaveLength(4); // 2 original + 2 added
    });
  });

  describe('Network Connectivity Scenarios', () => {
    it('should handle going offline during save', () => {
      const onlineDesign = createMockDesignData({
        nodes: [
          { id: 'cdn-1', type: 'cdn', position: { x: 0, y: 0 }, data: { label: 'CDN' } }
        ],
        userJourneys: 'CDN serves static content'
      });

      // Save while online
      setDesignInStorage('network-test', onlineDesign);

      // Go offline
      simulateNetworkChange(false);

      // Should still save to localStorage
      const offlineRead = getDesignFromStorage('network-test');
      expect(offlineRead).toBeTruthy();
      expect(offlineRead.nodes).toHaveLength(1);
    });

    it('should resume saves when coming back online', () => {
      const offlineDesign = createMockDesignData({
        nodes: [{ id: 'offline-node', position: { x: 0, y: 0 }, data: { label: 'Created Offline' } }]
      });

      // Start offline
      simulateNetworkChange(false);

      // Save while offline
      setDesignInStorage('offline-test', offlineDesign);

      // Come back online
      simulateNetworkChange(true);

      // Data should still be there
      const onlineRead = getDesignFromStorage('offline-test');
      expect(onlineRead).toBeTruthy();
      expect(onlineRead.nodes[0].data.label).toBe('Created Offline');
    });

    it('should handle intermittent connectivity', () => {
      const designData = createMockDesignData();

      // Save while online
      simulateNetworkChange(true);
      setDesignInStorage('intermittent-test', designData);

      // Go offline, then online, then offline again
      simulateNetworkChange(false);
      simulateNetworkChange(true);
      simulateNetworkChange(false);

      // Should handle gracefully
      const savedData = getDesignFromStorage('intermittent-test');
      expect(savedData).toBeTruthy();
    });
  });

  describe('Data Integrity Scenarios', () => {
    it('should preserve complex design data across persistence', () => {
      const complexDesign = createMockDesignData({
        nodes: [
          {
            id: 'composite-1',
            type: 'compositeNode',
            position: { x: 0, y: 0 },
            data: { 
              label: 'Composite Node',
              components: ['child-1'],
              properties: {
                replicas: 3,
                healthCheck: true
              }
            }
          },
          {
            id: 'child-1',
            type: 'customNode',
            position: { x: 50, y: 50 },
            parentId: 'composite-1',
            data: { label: 'Child 1', port: 8080 }
          }
        ],
        edges: [
          {
            id: 'edge-1',
            source: 'composite-1',
            target: 'child-1',
            data: { 
              connectionNumber: 'Internal Connection',
              protocol: 'gRPC'
            }
          }
        ]
      });

      setDesignInStorage('complex-design', complexDesign);

      const savedData = getDesignFromStorage('complex-design');
      expect(savedData.nodes).toHaveLength(2);
      expect(savedData.nodes[0].data.components).toEqual(['child-1']);
      expect(savedData.nodes[0].data.properties.replicas).toBe(3);
      expect(savedData.nodes[1].parentId).toBe('composite-1');
      expect(savedData.edges[0].data.protocol).toBe('gRPC');
    });

    it('should handle corrupted localStorage data gracefully', () => {
      // Set corrupted data in localStorage
      localStorage.setItem('layrs-design-question-corrupted', 'invalid-json');

      // Should return null for corrupted data
      const result = getDesignFromStorage('corrupted');
      expect(result).toBeNull();
    });

    it('should handle large design files', () => {
      // Create large design with many components
      const largeDesign = createMockDesignData({
        nodes: Array.from({ length: 10 }, (_, i) => ({
          id: `large-node-${i}`,
          type: 'customNode',
          position: { x: (i % 5) * 100, y: Math.floor(i / 5) * 100 },
          data: {
            label: `Large Component ${i}`,
            description: `This is component ${i}`
          }
        })),
        edges: Array.from({ length: 5 }, (_, i) => ({
          id: `large-edge-${i}`,
          source: `large-node-${i}`,
          target: `large-node-${(i + 1) % 10}`,
          data: {
            connectionNumber: `Connection ${i}`,
            protocol: 'HTTP'
          }
        }))
      });

      setDesignInStorage('large-design', largeDesign);

      const savedData = getDesignFromStorage('large-design');
      expect(savedData.nodes).toHaveLength(10);
      expect(savedData.edges).toHaveLength(5);
      expect(savedData.nodes[5].data.label).toBe('Large Component 5');
    });
  });
});
