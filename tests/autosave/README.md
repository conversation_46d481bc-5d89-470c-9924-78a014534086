# Autosave Test Suite

This directory contains comprehensive tests for the autosave functionality in the Layrs design tool.

## Test Structure

### Core Tests
- **`useAutoSave.test.ts`** - Tests for the core autosave hook
- **`integration.test.ts`** - Integration tests with React Flow and design state
- **`persistence.test.ts`** - Tests for data persistence across sessions
- **`realworld.test.ts`** - Real-world scenario tests

### Test Utilities
- **`setup.ts`** - Common test setup, mocks, and utilities

## Test Coverage

### Core Functionality
- ✅ Basic autosave triggering (localStorage and Supabase)
- ✅ Race condition prevention with delayed data capture
- ✅ Status management and reset timers
- ✅ Error handling and retry logic
- ✅ Data deduplication
- ✅ Offline/online handling

### Persistence Scenarios
- ✅ Page reload with data recovery
- ✅ Browser tab switching
- ✅ Application switching (focus/blur)
- ✅ Network connectivity changes
- ✅ Data integrity across sessions
- ✅ Corrupted data handling

### Integration Scenarios
- ✅ Canvas change handling (major vs minor)
- ✅ Text field change debouncing
- ✅ Component lifecycle (add/delete/connect)
- ✅ Context switching (questions vs free canvas)
- ✅ Mixed change type handling

### Real-World Scenarios
- ✅ Complete design session workflows
- ✅ Design sessions with interruptions
- ✅ Unstable network conditions
- ✅ Slow network responses
- ✅ Browser refresh during saves
- ✅ Multiple browser tabs
- ✅ Rapid component additions
- ✅ Large design data handling

## Running Tests

```bash
# Run all autosave tests
npm test tests/autosave

# Run specific test file
npm test tests/autosave/useAutoSave.test.ts

# Run with coverage
npm test tests/autosave -- --coverage

# Run in watch mode
npm test tests/autosave -- --watch
```

## Test Scenarios Covered

### 1. Component Addition Race Condition
Tests that the last component added is properly saved by ensuring React Flow state updates before autosave captures data.

### 2. Page Reload Recovery
Verifies that designs are properly restored from localStorage after page reloads, maintaining all components and connections.

### 3. Tab Switching Persistence
Tests that autosave continues to work when users switch browser tabs or return to the application.

### 4. Network Interruption Handling
Ensures that designs are saved locally when network is unavailable and synced to Supabase when connectivity returns.

### 5. Rapid User Actions
Tests autosave behavior when users add multiple components quickly, ensuring all changes are captured without overwhelming the system.

### 6. Status Indicator Accuracy
Verifies that the "Auto saved" status appears for the correct duration (1 second) and resets properly.

### 7. Large Design Performance
Tests autosave performance with complex designs containing many components and connections.

### 8. Error Recovery
Ensures the system gracefully handles and recovers from save failures.

## Mock Strategy

The tests use comprehensive mocks for:
- **localStorage** - Custom implementation that persists across test scenarios
- **React Flow Instance** - Mocked with controllable node/edge data
- **Supabase Client** - Mocked with configurable success/failure responses
- **Network Events** - Simulated online/offline state changes
- **Browser Events** - Simulated tab switching, focus changes, etc.

## Key Test Utilities

### `simulatePageReload()`
Simulates a browser refresh by clearing timers while preserving localStorage data.

### `simulateTabSwitch()`
Simulates tab visibility changes using the Page Visibility API.

### `simulateAppSwitch()`
Simulates window focus/blur events when switching between applications.

### `simulateNetworkChange(online: boolean)`
Simulates network connectivity changes.

### `waitForAutosave()`
Waits for autosave operations to complete, accounting for delays and processing time.

## Expected Behavior

All tests verify that:
1. **Data Integrity** - No components or connections are lost
2. **Performance** - Operations complete within reasonable timeframes
3. **User Experience** - Status indicators behave correctly
4. **Reliability** - System handles errors and edge cases gracefully
5. **Persistence** - Data survives browser sessions and interruptions

## Adding New Tests

When adding new autosave functionality:

1. Add unit tests to the appropriate test file
2. Add integration tests if the feature affects multiple systems
3. Add real-world scenario tests for complex user workflows
4. Update this README with new test coverage

## Debugging Tests

Use these techniques for debugging test failures:

```typescript
// Enable console logs in tests
vi.spyOn(console, 'log').mockImplementation(console.log);

// Check localStorage state
console.log('Storage:', getDesignFromStorage('test-question'));

// Verify mock calls
console.log('Save calls:', mockSaveDesign.mock.calls);

// Check timing
console.log('Timer state:', vi.getTimerCount());
```
