/**
 * Test for component editing autosave - ensuring component property changes trigger autosave
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  setupAutosaveTests, 
  createMockDesignData,
  setDesignInStorage,
  getDesignFromStorage
} from './setup';

setupAutosaveTests();

describe('Component Editing Autosave', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();
  });

  describe('Component Property Changes', () => {
    it('should trigger autosave when component name is changed', () => {
      const designWithComponent = createMockDesignData({
        nodes: [
          { 
            id: 'api-gateway-1', 
            type: 'apiGateway',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'Original API Gateway',
              metadata: {
                port: 8080,
                protocol: 'HTTP'
              }
            } 
          }
        ],
        userJourneys: 'Test component name change autosave'
      });

      setDesignInStorage('component-name-test', designWithComponent);

      // Simulate component name change
      // In real scenario, this would be triggered by ComponentMetadataSidebar
      const savedDesign = getDesignFromStorage('component-name-test');
      expect(savedDesign.nodes).toHaveLength(1);
      expect(savedDesign.nodes[0].data.label).toBe('Original API Gateway');
    });

    it('should trigger autosave when component metadata is changed', () => {
      const designWithComponent = createMockDesignData({
        nodes: [
          { 
            id: 'database-1', 
            type: 'database',
            position: { x: 200, y: 200 }, 
            data: { 
              label: 'User Database',
              metadata: {
                type: 'PostgreSQL',
                port: 5432,
                replicas: 1
              }
            } 
          }
        ],
        userJourneys: 'Test component metadata change autosave'
      });

      setDesignInStorage('component-metadata-test', designWithComponent);

      // Simulate metadata change (port update)
      const savedDesign = getDesignFromStorage('component-metadata-test');
      expect(savedDesign.nodes[0].data.metadata.port).toBe(5432);
      expect(savedDesign.nodes[0].data.metadata.type).toBe('PostgreSQL');
    });

    it('should trigger autosave when custom properties are added', () => {
      const designWithComponent = createMockDesignData({
        nodes: [
          { 
            id: 'microservice-1', 
            type: 'microservice',
            position: { x: 300, y: 300 }, 
            data: { 
              label: 'User Service',
              metadata: {
                port: 8080,
                customProperties: [
                  { key: 'environment', value: 'production', isCode: false }
                ]
              }
            } 
          }
        ],
        userJourneys: 'Test custom properties autosave'
      });

      setDesignInStorage('custom-properties-test', designWithComponent);

      const savedDesign = getDesignFromStorage('custom-properties-test');
      expect(savedDesign.nodes[0].data.metadata.customProperties).toHaveLength(1);
      expect(savedDesign.nodes[0].data.metadata.customProperties[0].key).toBe('environment');
    });

    it('should trigger autosave when schema tables are modified', () => {
      const designWithDatabase = createMockDesignData({
        nodes: [
          { 
            id: 'database-schema-1', 
            type: 'database',
            position: { x: 400, y: 400 }, 
            data: { 
              label: 'Schema Database',
              metadata: {
                type: 'PostgreSQL',
                schemaTables: JSON.stringify([
                  {
                    name: 'users',
                    columns: [
                      { name: 'id', type: 'INTEGER', primaryKey: true },
                      { name: 'email', type: 'VARCHAR(255)', unique: true },
                      { name: 'created_at', type: 'TIMESTAMP' }
                    ]
                  }
                ])
              }
            } 
          }
        ],
        userJourneys: 'Test schema table changes autosave'
      });

      setDesignInStorage('schema-tables-test', designWithDatabase);

      const savedDesign = getDesignFromStorage('schema-tables-test');
      const schemaTables = JSON.parse(savedDesign.nodes[0].data.metadata.schemaTables);
      expect(schemaTables).toHaveLength(1);
      expect(schemaTables[0].name).toBe('users');
      expect(schemaTables[0].columns).toHaveLength(3);
    });
  });

  describe('Multiple Component Changes', () => {
    it('should handle rapid component property changes', () => {
      const designWithMultipleComponents = createMockDesignData({
        nodes: [
          { 
            id: 'api-1', 
            type: 'apiGateway',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'API Gateway 1',
              metadata: { port: 8080 }
            } 
          },
          { 
            id: 'api-2', 
            type: 'apiGateway',
            position: { x: 200, y: 200 }, 
            data: { 
              label: 'API Gateway 2',
              metadata: { port: 8081 }
            } 
          },
          { 
            id: 'db-1', 
            type: 'database',
            position: { x: 300, y: 300 }, 
            data: { 
              label: 'Database 1',
              metadata: { type: 'PostgreSQL' }
            } 
          }
        ],
        userJourneys: 'Test rapid component changes'
      });

      setDesignInStorage('rapid-changes-test', designWithMultipleComponents);

      // Simulate rapid changes to multiple components
      const savedDesign = getDesignFromStorage('rapid-changes-test');
      expect(savedDesign.nodes).toHaveLength(3);
      expect(savedDesign.nodes[0].data.metadata.port).toBe(8080);
      expect(savedDesign.nodes[1].data.metadata.port).toBe(8081);
      expect(savedDesign.nodes[2].data.metadata.type).toBe('PostgreSQL');
    });

    it('should preserve component relationships during property changes', () => {
      const designWithConnections = createMockDesignData({
        nodes: [
          { 
            id: 'client-1', 
            type: 'client',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'Web Client',
              metadata: { platform: 'web' }
            } 
          },
          { 
            id: 'api-1', 
            type: 'apiGateway',
            position: { x: 300, y: 100 }, 
            data: { 
              label: 'API Gateway',
              metadata: { port: 8080 }
            } 
          }
        ],
        edges: [
          {
            id: 'client-api-connection',
            source: 'client-1',
            target: 'api-1',
            data: { 
              connectionNumber: 'HTTP Request',
              method: 'GET/POST'
            }
          }
        ],
        userJourneys: 'Test component changes with connections'
      });

      setDesignInStorage('connections-test', designWithConnections);

      const savedDesign = getDesignFromStorage('connections-test');
      expect(savedDesign.nodes).toHaveLength(2);
      expect(savedDesign.edges).toHaveLength(1);
      expect(savedDesign.edges[0].source).toBe('client-1');
      expect(savedDesign.edges[0].target).toBe('api-1');
    });
  });

  describe('Component Type Specific Changes', () => {
    it('should handle database component schema changes', () => {
      const databaseComponent = createMockDesignData({
        nodes: [
          { 
            id: 'postgres-db', 
            type: 'database',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'PostgreSQL Database',
              metadata: {
                type: 'PostgreSQL',
                port: 5432,
                replicas: 3,
                schemaTables: JSON.stringify([
                  {
                    name: 'users',
                    columns: [
                      { name: 'id', type: 'SERIAL', primaryKey: true },
                      { name: 'username', type: 'VARCHAR(50)', unique: true },
                      { name: 'email', type: 'VARCHAR(255)', unique: true }
                    ]
                  },
                  {
                    name: 'orders',
                    columns: [
                      { name: 'id', type: 'SERIAL', primaryKey: true },
                      { name: 'user_id', type: 'INTEGER', foreignKey: 'users.id' },
                      { name: 'total', type: 'DECIMAL(10,2)' }
                    ]
                  }
                ])
              }
            } 
          }
        ],
        userJourneys: 'Database with complex schema'
      });

      setDesignInStorage('database-schema-test', databaseComponent);

      const savedDesign = getDesignFromStorage('database-schema-test');
      const schemaTables = JSON.parse(savedDesign.nodes[0].data.metadata.schemaTables);
      expect(schemaTables).toHaveLength(2);
      expect(schemaTables[0].name).toBe('users');
      expect(schemaTables[1].name).toBe('orders');
      expect(schemaTables[1].columns[1].foreignKey).toBe('users.id');
    });

    it('should handle microservice component custom logic changes', () => {
      const microserviceComponent = createMockDesignData({
        nodes: [
          { 
            id: 'user-service', 
            type: 'microservice',
            position: { x: 200, y: 200 }, 
            data: { 
              label: 'User Management Service',
              metadata: {
                port: 8080,
                replicas: 5,
                customLogic: `
                  // User authentication logic
                  function authenticateUser(token) {
                    return jwt.verify(token, process.env.JWT_SECRET);
                  }
                  
                  // User registration logic
                  function registerUser(userData) {
                    return bcrypt.hash(userData.password, 10)
                      .then(hash => {
                        userData.password = hash;
                        return db.users.create(userData);
                      });
                  }
                `,
                customProperties: [
                  { key: 'framework', value: 'Express.js', isCode: false },
                  { key: 'database', value: 'PostgreSQL', isCode: false },
                  { key: 'authentication', value: 'JWT', isCode: false }
                ]
              }
            } 
          }
        ],
        userJourneys: 'Microservice with custom logic and properties'
      });

      setDesignInStorage('microservice-logic-test', microserviceComponent);

      const savedDesign = getDesignFromStorage('microservice-logic-test');
      expect(savedDesign.nodes[0].data.metadata.customLogic).toContain('authenticateUser');
      expect(savedDesign.nodes[0].data.metadata.customLogic).toContain('registerUser');
      expect(savedDesign.nodes[0].data.metadata.customProperties).toHaveLength(3);
      expect(savedDesign.nodes[0].data.metadata.customProperties[0].key).toBe('framework');
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty component metadata gracefully', () => {
      const emptyComponent = createMockDesignData({
        nodes: [
          { 
            id: 'empty-component', 
            type: 'client',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'Empty Component',
              metadata: {}
            } 
          }
        ],
        userJourneys: 'Component with empty metadata'
      });

      setDesignInStorage('empty-metadata-test', emptyComponent);

      const savedDesign = getDesignFromStorage('empty-metadata-test');
      expect(savedDesign.nodes[0].data.metadata).toEqual({});
    });

    it('should handle component with no metadata property', () => {
      const noMetadataComponent = createMockDesignData({
        nodes: [
          { 
            id: 'no-metadata-component', 
            type: 'client',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'No Metadata Component'
              // No metadata property
            } 
          }
        ],
        userJourneys: 'Component without metadata property'
      });

      setDesignInStorage('no-metadata-test', noMetadataComponent);

      const savedDesign = getDesignFromStorage('no-metadata-test');
      expect(savedDesign.nodes[0].data.metadata).toBeUndefined();
    });

    it('should handle malformed schema tables data', () => {
      const malformedSchemaComponent = createMockDesignData({
        nodes: [
          { 
            id: 'malformed-schema', 
            type: 'database',
            position: { x: 100, y: 100 }, 
            data: { 
              label: 'Malformed Schema DB',
              metadata: {
                type: 'PostgreSQL',
                schemaTables: 'invalid-json-string'
              }
            } 
          }
        ],
        userJourneys: 'Database with malformed schema data'
      });

      setDesignInStorage('malformed-schema-test', malformedSchemaComponent);

      const savedDesign = getDesignFromStorage('malformed-schema-test');
      expect(savedDesign.nodes[0].data.metadata.schemaTables).toBe('invalid-json-string');
    });
  });
});
