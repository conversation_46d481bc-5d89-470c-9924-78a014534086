/**
 * Tests for the core useAutoSave hook
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAutoSave } from '@/hooks/useAutoSave';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { 
  setupAutosaveTests, 
  createMockReactFlowInstance, 
  createMockDesignData,
  waitForAutosave,
  waitFor
} from './setup';

setupAutosaveTests();

describe('useAutoSave Hook', () => {
  let mockReactFlowInstance: any;
  let mockSaveDesign: any;
  let mockSetHasUnsavedChanges: any;
  let mockMergeNodesAndEdges: any;
  let mockOnStatusChange: any;

  beforeEach(() => {
    mockReactFlowInstance = createMockReactFlowInstance();
    mockSaveDesign = vi.fn().mockResolvedValue(true);
    mockSetHasUnsavedChanges = vi.fn();
    mockMergeNodesAndEdges = vi.fn((nodes, edges) => ({ nodes, edges }));
    mockOnStatusChange = vi.fn();
  });

  const defaultProps = {
    currentQuestionId: 'test-question',
    contextType: 'question' as const,
    contextId: 'test-question',
    reactFlowInstance: mockReactFlowInstance,
    userJourneys: 'Test journey',
    assumptions: 'Test assumptions',
    constraints: 'Test constraints',
    saveDesign: mockSaveDesign,
    setHasUnsavedChanges: mockSetHasUnsavedChanges,
    mergeNodesAndEdges: mockMergeNodesAndEdges,
    onStatusChange: mockOnStatusChange,
    isOnline: true,
    enabled: true
  };

  describe('Basic Functionality', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      expect(result.current.status).toBe(SaveStatus.IDLE);
      expect(result.current.isEnabled).toBe(true);
      expect(result.current.queueLength).toBe(0);
    });

    it('should trigger localStorage save for canvas changes', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerAutoSave(false);
      });

      // Wait for localStorage save to complete
      await act(async () => {
        vi.advanceTimersByTime(600); // localStorage delay + processing
      });

      // Should have triggered status changes
      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVING, undefined);
      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVED, expect.any(Date));
    });

    it('should trigger full save for major changes', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerFullAutoSave(true); // Major change
      });

      await act(async () => {
        vi.advanceTimersByTime(2000); // Supabase delay
      });

      expect(mockSaveDesign).toHaveBeenCalledWith(
        'test-question',
        expect.objectContaining({
          nodes: [],
          edges: [],
          userJourneys: 'Test journey',
          assumptions: 'Test assumptions',
          constraints: 'Test constraints'
        }),
        'question',
        'test-question',
        true,
        false
      );
    });
  });

  describe('Race Condition Prevention', () => {
    it('should delay data capture for major changes', async () => {
      // Setup nodes that will be "added" during the delay
      const initialNodes = [{ id: 'node-1', position: { x: 0, y: 0 }, data: {} }];
      const updatedNodes = [
        { id: 'node-1', position: { x: 0, y: 0 }, data: {} },
        { id: 'node-2', position: { x: 100, y: 100 }, data: {} }
      ];

      mockReactFlowInstance.getNodes
        .mockReturnValueOnce(initialNodes) // First call (immediate)
        .mockReturnValue(updatedNodes); // Subsequent calls (after delay)

      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerFullAutoSave(true); // Major change with delay
      });

      // Advance past the major change delay (100ms)
      await act(async () => {
        vi.advanceTimersByTime(100);
      });

      // Advance past the Supabase delay
      await act(async () => {
        vi.advanceTimersByTime(2000);
      });

      // Should capture the updated nodes (after delay)
      expect(mockSaveDesign).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          nodes: updatedNodes // Should have the new node
        }),
        expect.any(String),
        expect.any(String),
        true,
        false
      );
    });

    it('should not delay data capture for non-major changes', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerAutoSave(false); // Non-major change
      });

      // Should capture immediately without delay
      expect(mockReactFlowInstance.getNodes).toHaveBeenCalledTimes(1);
    });
  });

  describe('Status Management', () => {
    it('should reset status to IDLE after 1 second', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerAutoSave(false);
      });

      // Complete the save
      await act(async () => {
        vi.advanceTimersByTime(600); // Complete save operation
      });

      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVED, expect.any(Date));

      // Advance past the 1-second reset timer
      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.IDLE, expect.any(Date));
    });

    it('should handle rapid status changes correctly', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      // Trigger multiple saves rapidly
      act(() => {
        result.current.triggerAutoSave(false);
      });

      await act(async () => {
        vi.advanceTimersByTime(300);
      });

      act(() => {
        result.current.triggerAutoSave(false);
      });

      await act(async () => {
        vi.advanceTimersByTime(300);
      });

      act(() => {
        result.current.triggerAutoSave(false);
      });

      // Complete all saves
      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      // Should eventually reset to IDLE
      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      expect(mockOnStatusChange).toHaveBeenLastCalledWith(SaveStatus.IDLE, expect.any(Date));
    });
  });

  describe('Error Handling', () => {
    it('should handle Supabase save failures', async () => {
      mockSaveDesign.mockRejectedValueOnce(new Error('Network error'));

      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerFullAutoSave(true);
      });

      await act(async () => {
        vi.advanceTimersByTime(2100); // Complete save attempt
      });

      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.ERROR, undefined);
    });

    it('should retry failed Supabase saves', async () => {
      mockSaveDesign
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(true);

      const { result } = renderHook(() => useAutoSave(defaultProps));

      act(() => {
        result.current.triggerFullAutoSave(true);
      });

      // First attempt fails
      await act(async () => {
        vi.advanceTimersByTime(2100);
      });

      // Retry after exponential backoff
      await act(async () => {
        vi.advanceTimersByTime(2000); // First retry delay
      });

      expect(mockSaveDesign).toHaveBeenCalledTimes(2);
    });
  });

  describe('Offline Handling', () => {
    it('should skip Supabase saves when offline', async () => {
      const { result } = renderHook(() => useAutoSave({
        ...defaultProps,
        isOnline: false
      }));

      act(() => {
        result.current.triggerFullAutoSave(true);
      });

      await act(async () => {
        vi.advanceTimersByTime(2100);
      });

      // Should not call Supabase save when offline
      expect(mockSaveDesign).not.toHaveBeenCalled();
      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVED, expect.any(Date));
    });
  });

  describe('Data Deduplication', () => {
    it('should skip saves when data has not changed', async () => {
      const { result } = renderHook(() => useAutoSave(defaultProps));

      // First save
      act(() => {
        result.current.triggerAutoSave(false);
      });

      await act(async () => {
        vi.advanceTimersByTime(600);
      });

      // Second save with same data
      act(() => {
        result.current.triggerAutoSave(false);
      });

      await act(async () => {
        vi.advanceTimersByTime(600);
      });

      // Should only process the first save
      expect(result.current.queueLength).toBe(0);
    });
  });
});
