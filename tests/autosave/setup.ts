/**
 * Test setup and utilities for autosave functionality tests
 */

import { beforeEach, afterEach, vi } from 'vitest';
import '@testing-library/jest-dom';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => {
      const keys = Object.keys(store);
      return keys[index] || null;
    }
  };
})();

// Mock navigator.onLine
const navigatorMock = {
  onLine: true
};

// Mock React Flow instance
export const createMockReactFlowInstance = (nodes: any[] = [], edges: any[] = []) => ({
  getNodes: vi.fn(() => nodes),
  getEdges: vi.fn(() => edges),
  setNodes: vi.fn(),
  setEdges: vi.fn(),
  fitView: vi.fn(),
  setViewport: vi.fn(),
  getViewport: vi.fn(() => ({ x: 0, y: 0, zoom: 1 }))
});

// Mock design data
export const createMockDesignData = (overrides: any = {}) => ({
  questionId: 'test-question',
  nodes: [
    {
      id: 'node-1',
      type: 'customNode',
      position: { x: 100, y: 100 },
      data: { label: 'Test Node 1' }
    },
    {
      id: 'node-2', 
      type: 'customNode',
      position: { x: 200, y: 200 },
      data: { label: 'Test Node 2' }
    }
  ],
  edges: [
    {
      id: 'edge-1',
      source: 'node-1',
      target: 'node-2',
      data: { connectionNumber: '1' }
    }
  ],
  userJourneys: 'Test user journey',
  assumptions: 'Test assumptions',
  constraints: 'Test constraints',
  contextType: 'question' as const,
  contextId: 'test-question',
  lastModified: new Date().toISOString(),
  ...overrides
});

// Mock Supabase client
export const createMockSupabaseClient = () => ({
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(() => Promise.resolve({ data: null, error: null }))
      }))
    })),
    insert: vi.fn(() => Promise.resolve({ data: null, error: null })),
    upsert: vi.fn(() => Promise.resolve({ data: null, error: null })),
    update: vi.fn(() => ({
      eq: vi.fn(() => Promise.resolve({ data: null, error: null }))
    })),
    delete: vi.fn(() => ({
      eq: vi.fn(() => Promise.resolve({ data: null, error: null }))
    }))
  }))
});

// Test utilities
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const waitForAutosave = () => waitFor(150); // Wait for autosave delay + processing

export const simulatePageReload = () => {
  // Simulate page reload by clearing all timers and reinitializing
  vi.clearAllTimers();
  // localStorage persists across "reloads"
};

export const simulateTabSwitch = async () => {
  // Simulate tab becoming hidden
  Object.defineProperty(document, 'hidden', {
    writable: true,
    value: true
  });
  
  // Dispatch visibility change event
  const event = new Event('visibilitychange');
  document.dispatchEvent(event);
  
  await waitFor(100);
  
  // Simulate tab becoming visible again
  Object.defineProperty(document, 'hidden', {
    writable: true,
    value: false
  });
  
  const visibleEvent = new Event('visibilitychange');
  document.dispatchEvent(visibleEvent);
};

export const simulateAppSwitch = async () => {
  // Simulate window losing focus
  const blurEvent = new Event('blur');
  window.dispatchEvent(blurEvent);
  
  await waitFor(100);
  
  // Simulate window gaining focus
  const focusEvent = new Event('focus');
  window.dispatchEvent(focusEvent);
};

export const simulateNetworkChange = (online: boolean) => {
  Object.defineProperty(navigator, 'onLine', {
    writable: true,
    value: online
  });
  
  const event = new Event(online ? 'online' : 'offline');
  window.dispatchEvent(event);
};

// Setup and teardown
export const setupAutosaveTests = () => {
  beforeEach(() => {
    // Mock global objects
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    });
    
    Object.defineProperty(window, 'navigator', {
      value: navigatorMock,
      writable: true
    });
    
    // Clear localStorage
    localStorageMock.clear();
    
    // Reset navigator.onLine
    navigatorMock.onLine = true;
    
    // Mock timers
    vi.useFakeTimers();
    
    // Mock console methods to reduce noise
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    // Clear all mocks
    vi.clearAllMocks();
    
    // Clear localStorage
    localStorageMock.clear();
    
    // Restore timers
    vi.useRealTimers();
    
    // Restore console
    vi.restoreAllMocks();
  });
};

// Storage key helpers
export const getDesignKey = (questionId: string, contextType: string = 'question', contextId?: string) => {
  const id = contextId || questionId;
  return `layrs-design-${contextType}-${id}`;
};

export const setDesignInStorage = (questionId: string, designData: any, contextType: string = 'question', contextId?: string) => {
  const key = getDesignKey(questionId, contextType, contextId);
  try {
    localStorageMock.setItem(key, JSON.stringify(designData));
  } catch (error) {
    console.warn('Failed to save design data to localStorage:', error);
    // Don't throw error, just log it
  }
};

export const getDesignFromStorage = (questionId: string, contextType: string = 'question', contextId?: string) => {
  // Check if localStorage is available
  if (typeof window !== 'undefined' && !window.localStorage) {
    return null;
  }

  const key = getDesignKey(questionId, contextType, contextId);
  const data = localStorageMock.getItem(key);
  if (!data) return null;

  try {
    return JSON.parse(data);
  } catch (error) {
    console.warn('Failed to parse design data from localStorage:', error);
    return null;
  }
};
