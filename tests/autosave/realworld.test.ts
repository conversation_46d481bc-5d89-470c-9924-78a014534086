/**
 * Real-world scenario tests for autosave functionality
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useAutoSaveIntegration } from '@/hooks/useAutoSaveIntegration';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { 
  setupAutosaveTests, 
  createMockReactFlowInstance, 
  createMockDesignData,
  simulatePageReload,
  simulateTabSwitch,
  simulateAppSwitch,
  simulateNetworkChange,
  setDesignInStorage,
  getDesignFromStorage,
  waitFor
} from './setup';

setupAutosaveTests();

describe('Real-World Autosave Scenarios', () => {
  let mockReactFlowInstance: any;
  let mockSaveDesign: any;
  let mockSetHasUnsavedChanges: any;
  let mockMergeNodesAndEdges: any;
  let mockOnStatusChange: any;

  beforeEach(() => {
    mockReactFlowInstance = createMockReactFlowInstance();
    mockSaveDesign = vi.fn().mockResolvedValue(true);
    mockSetHasUnsavedChanges = vi.fn();
    mockMergeNodesAndEdges = vi.fn((nodes, edges) => ({ nodes, edges }));
    mockOnStatusChange = vi.fn();
  });

  const defaultProps = {
    currentQuestionId: 'test-question',
    contextType: 'question' as const,
    contextId: 'test-question',
    reactFlowInstance: mockReactFlowInstance,
    userJourneys: 'Test journey',
    assumptions: 'Test assumptions',
    constraints: 'Test constraints',
    saveDesign: mockSaveDesign,
    setHasUnsavedChanges: mockSetHasUnsavedChanges,
    mergeNodesAndEdges: mockMergeNodesAndEdges,
    onStatusChange: mockOnStatusChange,
    isOnline: true,
    enabled: true
  };

  describe('User Workflow: Building a System Design', () => {
    it('should handle complete design session with multiple components', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      // Step 1: Add load balancer
      const nodes = [
        { id: 'lb-1', type: 'loadBalancer', position: { x: 100, y: 100 }, data: { label: 'Load Balancer' } }
      ];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Step 2: Add web servers
      nodes.push(
        { id: 'web-1', type: 'webServer', position: { x: 50, y: 200 }, data: { label: 'Web Server 1' } },
        { id: 'web-2', type: 'webServer', position: { x: 150, y: 200 }, data: { label: 'Web Server 2' } }
      );
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Step 3: Add database
      nodes.push(
        { id: 'db-1', type: 'database', position: { x: 100, y: 300 }, data: { label: 'Database' } }
      );
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Step 4: Add connections
      const edges = [
        { id: 'edge-1', source: 'lb-1', target: 'web-1', data: { connectionNumber: 'HTTP' } },
        { id: 'edge-2', source: 'lb-1', target: 'web-2', data: { connectionNumber: 'HTTP' } },
        { id: 'edge-3', source: 'web-1', target: 'db-1', data: { connectionNumber: 'SQL' } },
        { id: 'edge-4', source: 'web-2', target: 'db-1', data: { connectionNumber: 'SQL' } }
      ];
      mockReactFlowInstance.getEdges.mockReturnValue(edges);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Verify final state was saved
      expect(mockSaveDesign).toHaveBeenLastCalledWith(
        'test-question',
        expect.objectContaining({
          nodes: expect.arrayContaining([
            expect.objectContaining({ id: 'lb-1' }),
            expect.objectContaining({ id: 'web-1' }),
            expect.objectContaining({ id: 'web-2' }),
            expect.objectContaining({ id: 'db-1' })
          ]),
          edges: expect.arrayContaining([
            expect.objectContaining({ id: 'edge-1' }),
            expect.objectContaining({ id: 'edge-2' }),
            expect.objectContaining({ id: 'edge-3' }),
            expect.objectContaining({ id: 'edge-4' })
          ])
        }),
        'question',
        'test-question',
        true,
        false
      );
    });

    it('should handle design session with interruptions', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      // Start building design
      const nodes = [
        { id: 'api-1', type: 'apiGateway', position: { x: 100, y: 100 }, data: { label: 'API Gateway' } }
      ];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      // Simulate tab switch (user checks documentation)
      await simulateTabSwitch();

      // Continue building
      nodes.push(
        { id: 'service-1', type: 'microservice', position: { x: 100, y: 200 }, data: { label: 'User Service' } }
      );
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      // Simulate app switch (user checks Slack)
      await simulateAppSwitch();

      // Add more components
      nodes.push(
        { id: 'cache-1', type: 'cache', position: { x: 200, y: 200 }, data: { label: 'Redis Cache' } }
      );
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Should have saved all components despite interruptions
      expect(mockSaveDesign).toHaveBeenLastCalledWith(
        expect.any(String),
        expect.objectContaining({
          nodes: expect.arrayContaining([
            expect.objectContaining({ id: 'api-1' }),
            expect.objectContaining({ id: 'service-1' }),
            expect.objectContaining({ id: 'cache-1' })
          ])
        }),
        expect.any(String),
        expect.any(String),
        true,
        false
      );
    });
  });

  describe('Network Reliability Scenarios', () => {
    it('should handle unstable network during design session', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      // Start with good network
      const nodes = [
        { id: 'node-1', position: { x: 0, y: 0 }, data: {} }
      ];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      // Network goes down
      simulateNetworkChange(false);

      // Continue adding components (should save to localStorage)
      nodes.push({ id: 'node-2', position: { x: 100, y: 100 }, data: {} });
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Network comes back
      simulateNetworkChange(true);

      // Add another component (should sync to Supabase)
      nodes.push({ id: 'node-3', position: { x: 200, y: 200 }, data: {} });
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Should have saved to localStorage during offline period
      const savedData = getDesignFromStorage('test-question');
      expect(savedData).toBeTruthy();
      expect(savedData.nodes).toHaveLength(3);

      // Should have synced to Supabase when online
      expect(mockSaveDesign).toHaveBeenCalled();
    });

    it('should handle slow network responses', async () => {
      // Mock slow Supabase response
      mockSaveDesign.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(true), 5000))
      );

      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      act(() => {
        result.current.handleCanvasChange('major');
      });

      // Start save operation
      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVING, undefined);

      // Add another component while first save is still pending
      act(() => {
        result.current.handleCanvasChange('major');
      });

      // Complete both saves
      await act(async () => {
        vi.advanceTimersByTime(7000);
      });

      // Should handle gracefully
      expect(mockOnStatusChange).toHaveBeenCalledWith(SaveStatus.AUTO_SAVED, expect.any(Date));
    });
  });

  describe('Browser Behavior Scenarios', () => {
    it('should handle browser refresh during save', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      const nodes = [
        { id: 'node-1', position: { x: 0, y: 0 }, data: {} },
        { id: 'node-2', position: { x: 100, y: 100 }, data: {} }
      ];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes);

      // Start save
      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(1000);
      });

      // Simulate page reload (localStorage should persist)
      simulatePageReload();

      // Verify data persisted
      const savedData = getDesignFromStorage('test-question');
      expect(savedData).toBeTruthy();
      expect(savedData.nodes).toHaveLength(2);
    });

    it('should handle multiple browser tabs', async () => {
      // Simulate first tab
      const { result: tab1 } = renderHook(() => useAutoSaveIntegration(defaultProps));

      const nodes1 = [{ id: 'node-1', position: { x: 0, y: 0 }, data: {} }];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes1);

      act(() => {
        tab1.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Simulate second tab (should load existing data)
      const { result: tab2 } = renderHook(() => useAutoSaveIntegration(defaultProps));

      // Second tab adds component
      const nodes2 = [...nodes1, { id: 'node-2', position: { x: 100, y: 100 }, data: {} }];
      mockReactFlowInstance.getNodes.mockReturnValue(nodes2);

      act(() => {
        tab2.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Should save latest state
      const savedData = getDesignFromStorage('test-question');
      expect(savedData.nodes).toHaveLength(2);
    });
  });

  describe('Performance Scenarios', () => {
    it('should handle rapid component additions efficiently', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      const nodes: any[] = [];

      // Add 10 components rapidly
      for (let i = 1; i <= 10; i++) {
        nodes.push({
          id: `node-${i}`,
          position: { x: i * 50, y: i * 50 },
          data: { label: `Node ${i}` }
        });
        
        mockReactFlowInstance.getNodes.mockReturnValue([...nodes]);

        act(() => {
          result.current.handleCanvasChange('major');
        });

        // Small delay between additions
        await act(async () => {
          vi.advanceTimersByTime(50);
        });
      }

      // Complete all saves
      await act(async () => {
        vi.advanceTimersByTime(3000);
      });

      // Should save final state with all components
      expect(mockSaveDesign).toHaveBeenCalled();
      
      const lastCall = mockSaveDesign.mock.calls[mockSaveDesign.mock.calls.length - 1];
      expect(lastCall[1].nodes).toHaveLength(10);
    });

    it('should handle large design data efficiently', async () => {
      const { result } = renderHook(() => useAutoSaveIntegration(defaultProps));

      // Create large design with many components and connections
      const nodes = Array.from({ length: 50 }, (_, i) => ({
        id: `node-${i}`,
        type: 'customNode',
        position: { x: (i % 10) * 100, y: Math.floor(i / 10) * 100 },
        data: { 
          label: `Component ${i}`,
          description: `This is a detailed description for component ${i}`,
          properties: {
            cpu: '2 cores',
            memory: '4GB',
            storage: '100GB'
          }
        }
      }));

      const edges = Array.from({ length: 30 }, (_, i) => ({
        id: `edge-${i}`,
        source: `node-${i}`,
        target: `node-${(i + 1) % 50}`,
        data: { 
          connectionNumber: `Connection ${i}`,
          protocol: 'HTTP',
          bandwidth: '1Gbps'
        }
      }));

      mockReactFlowInstance.getNodes.mockReturnValue(nodes);
      mockReactFlowInstance.getEdges.mockReturnValue(edges);

      act(() => {
        result.current.handleCanvasChange('major');
      });

      await act(async () => {
        vi.advanceTimersByTime(2200);
      });

      // Should handle large data efficiently
      expect(mockSaveDesign).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          nodes: expect.arrayContaining(nodes),
          edges: expect.arrayContaining(edges)
        }),
        expect.any(String),
        expect.any(String),
        true,
        false
      );

      // Verify localStorage can handle large data
      const savedData = getDesignFromStorage('test-question');
      expect(savedData.nodes).toHaveLength(50);
      expect(savedData.edges).toHaveLength(30);
    });
  });
});
