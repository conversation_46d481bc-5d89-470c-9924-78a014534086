import { Course } from '@/types/course';

export const urlShortenerCourse: Course = {
  id: 'url-shortener',
  title: 'URL Shortener Design',
  description: 'Learn how to design a scalable URL shortening service like Bit.ly or TinyURL that can handle millions of redirects daily.',
  difficulty: 'beginner',
  estimatedTime: '30 minutes',
  currentStepIndex: 0,
  steps: [
    {
      id: 'introduction',
      title: 'Step 1: Introduction to URL Shorteners',
      description: 'Welcome to the URL Shortener System Design course!\n\nIn this course, you\'ll learn how to design a scalable URL shortening service like Bit.ly or TinyURL that converts long URLs into short, easy-to-share links.\n\nURL shorteners handle millions of redirects daily and must be highly available with minimal latency.',
      explanation: 'URL shorteners solve a simple problem: they make long, unwieldy URLs shorter and easier to share. Behind this simple concept is a distributed system that must handle high read throughput (redirects), maintain data consistency, and provide fast response times. The core functionality involves generating a unique short code for each URL and redirecting users when they visit the shortened link.',
      hints: [
        'URL shorteners typically use a base62 encoding (a-z, A-Z, 0-9) to create short codes',
        'The system must handle a read-heavy workload (many more redirects than new URL creations)',
        'Shortened URLs should ideally be as short as possible while remaining unique',
        'The service must be highly available - if it goes down, all shortened links stop working',
        'Latency is critical - users expect near-instant redirects when clicking shortened links'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      isInformationalOnly: true
    },
    {
      id: 'create-frontend',
      title: 'Step 2: Create the Frontend',
      description: 'Let\'s start by adding the Frontend component to our system.\n\nThe frontend for a URL shortener provides a simple interface where users can input long URLs and receive shortened versions. It also handles redirect requests when users click on shortened links.\n\nFor our URL shortener, we\'ll use a lightweight React application that communicates with our backend services.',
      explanation: 'The frontend for a URL shortener serves two distinct purposes: (1) It provides a user interface for creating new shortened URLs, and (2) It handles incoming requests to shortened URLs and redirects users to the original destination. These two functions could be handled by separate frontend services in a large-scale deployment, but for simplicity, we\'ll use a single frontend component in our design.',
      hints: [
        'Drag a Frontend component from the palette to the canvas',
        'Name it "URL Shortener Frontend"',
        'Set the framework property to "React" for a modern web application',
        'Consider how the frontend will handle both URL creation and redirects',
        'Think about the user interface elements needed (URL input field, submit button, copy button for shortened URLs)'
      ],
      goal: {
        components: ['frontend'],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend']
    },
    {
      id: 'add-api-gateway',
      title: 'Step 3: Add API Gateway',
      description: 'Now, let\'s add an API Gateway to manage requests from the frontend.\n\nThe API Gateway will route requests to the appropriate backend services based on the URL path. It will handle both URL creation requests and redirection requests.\n\nThis component is essential for a URL shortener as it provides a single entry point for all client requests while enabling different handling for creation vs. redirection.',
      explanation: 'The API Gateway pattern provides a unified entry point for all client requests. For our URL shortener, the gateway will distinguish between two main types of requests: (1) API calls to create new shortened URLs (typically POST requests to an endpoint like /api/shorten), and (2) Redirection requests when users visit a shortened URL (typically GET requests to paths like /abc123). The gateway routes these different request types to the appropriate backend services.',
      hints: [
        'Add an API Gateway component to the canvas',
        'Connect the Frontend to the API Gateway',
        'Set rate limiting properties to protect backend services',
        'Consider how the gateway will distinguish between URL creation requests and redirection requests',
        'Think about how rate limiting might be different for creation vs. redirection'
      ],
      goal: {
        components: ['apigateway'],
        connections: [['frontend', 'apigateway']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend', 'apigateway']
    },
    {
      id: 'create-url-service',
      title: 'Step 4: Create URL Service',
      description: 'Let\'s build the core URL Service that will handle the creation of shortened URLs.\n\nThis service is responsible for generating unique short codes, storing the mapping between short codes and original URLs, and handling the business logic of URL shortening.\n\nThe URL Service is the heart of our system, implementing the algorithms and logic that make URL shortening possible.',
      explanation: 'The URL Service implements the core business logic of our system. When it receives a request to shorten a URL, it performs several steps: (1) Validates the input URL, (2) Generates a unique short code (using techniques like base62 encoding or hash functions), (3) Stores the mapping between the short code and original URL, and (4) Returns the complete shortened URL to the client. The service must ensure that generated codes are unique and collision-resistant, even at high scale.',
      hints: [
        'Add a Server component and name it "URL Service"',
        'Connect the API Gateway to the URL Service',
        'Set the "language" property to "Node.js" for the API implementation',
        'Set the "scaling" property to "Horizontal" to handle high traffic',
        'Consider how the service will generate unique short codes (hash function, counter-based, random)',
        'Think about collision handling when two different URLs might generate the same short code'
      ],
      goal: {
        components: ['server'],
        connections: [['apigateway', 'server']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'apigateway', 'frontend']
    },
    {
      id: 'add-url-database',
      title: 'Step 5: Add URL Database',
      description: 'Now, let\'s add a Database to store the mappings between short codes and original URLs.\n\nThis database is critical for our URL shortener as it maintains the relationship between shortened URLs and their original destinations.\n\nWe\'ll need to ensure fast read access since redirects (reads) will be much more frequent than new URL creations (writes).',
      explanation: 'The URL Database stores the core data of our system: the mapping between short codes and original URLs. This is a classic key-value use case, where the short code serves as the key and the original URL is the value. The database must support fast lookups by short code to minimize redirect latency. Since reads (redirects) typically outnumber writes (new URL creations) by orders of magnitude, the database should be optimized for read performance, potentially using in-memory caching or a database engine designed for read-heavy workloads.',
      hints: [
        'Add a Database component to the canvas',
        'Connect the URL Service to the Database',
        'Set the "engine" property to "Redis" or "MongoDB" for fast key-value lookups',
        'Set the "scaling" property to "Sharded" for horizontal scalability',
        'Consider adding a simple schema like "short_code (PK), original_url, creation_date, click_count"',
        'Think about indexing strategies to optimize lookup performance'
      ],
      goal: {
        components: ['database'],
        connections: [['server', 'database']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['database', 'server', 'apigateway', 'frontend']
    },
    {
      id: 'create-redirect-service',
      title: 'Step 6: Create Redirect Service',
      description: 'Let\'s add a dedicated Redirect Service to handle the high volume of redirect requests.\n\nThis service will receive requests for shortened URLs, look up the original URL, and return the appropriate HTTP redirect response.\n\nSeparating this functionality from the URL creation service allows us to scale each service independently based on their different workload patterns.',
      explanation: 'The Redirect Service is specialized for handling the most frequent operation in our system: redirecting users from shortened URLs to original destinations. When a user visits a shortened URL, this service: (1) Extracts the short code from the URL, (2) Looks up the original URL in the database, (3) Returns an HTTP 301 or 302 redirect response to the client. By separating this functionality from URL creation, we can optimize and scale this service specifically for the high-volume, low-latency requirements of redirects.',
      hints: [
        'Add another Server component and name it "Redirect Service"',
        'Connect the API Gateway to the Redirect Service',
        'Connect the Redirect Service to the Database',
        'Set the "language" property to "Go" or "Rust" for high-performance request handling',
        'Set the "scaling" property to "Horizontal" to handle millions of redirects',
        'Consider how the service will handle missing or expired short codes',
        'Think about whether to use 301 (permanent) or 302 (temporary) redirects'
      ],
      goal: {
        components: ['server'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'add-cache',
      title: 'Step 7: Add Caching Layer',
      description: 'Let\'s improve performance by adding a Cache for frequently accessed URLs.\n\nSince most shortened URLs follow a power-law distribution (a small percentage of URLs receive most of the traffic), caching can significantly reduce database load and improve response times.\n\nThis is a critical optimization for a production URL shortener.',
      explanation: 'Caching is essential for a read-heavy system like a URL shortener. By storing the mappings for frequently accessed URLs in memory, we can reduce database load and decrease redirect latency. The cache follows a simple pattern: when a redirect request arrives, the service first checks the cache for the short code. If found (cache hit), it returns the original URL immediately without querying the database. If not found (cache miss), it queries the database, stores the result in the cache for future requests, and then returns the original URL.',
      hints: [
        'Add a Cache component to the canvas',
        'Connect the Redirect Service to the Cache',
        'Set the "engine" property to "Redis" for in-memory caching',
        'Set the "evictionPolicy" property to "LRU" (Least Recently Used) to keep popular URLs in cache',
        'Set an appropriate TTL (Time To Live) value like 86400 (1 day in seconds)',
        'Consider the cache hit ratio you might expect (typically 80-90% for URL shorteners)',
        'Think about cache invalidation strategies if a URL mapping is updated or deleted'
      ],
      goal: {
        components: ['cache'],
        connections: [['server', 'cache']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['cache', 'server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'configure-stress-test',
      title: 'Step 8: Configure & Stress Test Your System',
      description: 'Let\'s test how our system performs under load.\n\nWe\'ll configure performance parameters for our services and run a simulation to identify bottlenecks.\n\nURL shorteners typically handle many more read requests (redirects) than write requests (URL creations), so we\'ll focus on testing the redirect path.',
      explanation: 'Performance testing is critical for URL shorteners, which must handle high traffic with minimal latency. During the simulation, we\'ll generate a high volume of redirect requests to test the system\'s capacity. We\'ll monitor key metrics like response time, throughput, error rate, and resource utilization. This helps identify bottlenecks and validate that our architecture can handle real-world traffic patterns.',
      hints: [
        'Set a custom property named "maxQPS" on the Redirect Service (try values between 50-200)',
        'Set a custom property named "maxQPS" on the URL Service (try values between 10-50)',
        'Configure simulation for 150 QPS to simulate a traffic spike',
        'Watch the real-time graph showing latency and throughput',
        'Notice which components become bottlenecks first',
        'Observe how the cache helps reduce load on the database',
        'Try different QPS settings to find the optimal balance for your architecture'
      ],
      goal: {
        components: ['server', 'cache'],
        connections: [],
        simulation: {
          required: true,
          minQPS: 100,
          maxQPS: 200,
          minDuration: 10,
          maxDuration: 20,
          payload: {
            shortCode: "abc123",
            action: "redirect"
          },
          customProperties: [
            { componentType: 'server', key: 'maxQPS', value: '100' }
          ]
        }
      },
      completed: false,
      filterComponents: ['server', 'cache', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'add-analytics-service',
      title: 'Step 9: Add Analytics Service',
      description: 'Let\'s add an Analytics Service to track URL performance.\n\nThis service will collect data about clicks, geographic distribution, referrers, and other metrics that provide insights into how shortened URLs are being used.\n\nFor URL shortener businesses, these analytics are often a key value proposition for premium users.',
      explanation: 'Analytics are a crucial feature for URL shorteners, providing valuable insights to users about their link performance. The Analytics Service works by: (1) Receiving click events from the Redirect Service, (2) Processing and aggregating this data, and (3) Storing it for reporting purposes. To avoid impacting the critical redirect path, analytics processing is typically done asynchronously using a queue-based architecture.',
      hints: [
        'Add a Server component and name it "Analytics Service"',
        'Add a Queue component for buffering analytics events',
        'Connect the Redirect Service to the Queue',
        'Connect the Queue to the Analytics Service',
        'Add a Database component for analytics data storage',
        'Connect the Analytics Service to the Analytics Database',
        'Consider what metrics would be valuable to track (clicks, geography, devices, referrers)',
        'Think about how to handle analytics processing without impacting redirect performance'
      ],
      goal: {
        components: ['server', 'queue', 'database'],
        connections: [
          ['server', 'queue'],
          ['queue', 'server'],
          ['server', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'queue', 'database', 'cache', 'apigateway', 'frontend']
    },
    {
      id: 'final-simulation',
      title: 'Step 10: Final System Test',
      description: 'Now let\'s test your complete URL shortener architecture under load.\n\nRun the simulation to see how your system handles high traffic and component failures.\n\nWatch for bottlenecks and see how your cache and queue help manage the load.',
      explanation: 'The final system test validates that all components work together effectively under stress. The simulation will show a comprehensive dashboard of system performance metrics including throughput, latency, error rates, and component health. You\'ll also see how your architecture handles random component failures, simulating real-world outages and testing the resilience of your design.',
      hints: [
        'Click the "Run Simulation" button',
        'Watch the real-time dashboard showing system performance metrics',
        'Notice how requests flow through your system',
        'See how the cache reduces load on your database',
        'Observe how the queue buffers analytics events without impacting redirects',
        'Try enabling "Chaos Testing" to randomly fail components and test system resilience',
        'Look for bottlenecks in your system and consider how you might address them'
      ],
      goal: {
        components: ['server', 'database', 'cache', 'queue'],
        connections: [],
        simulation: {
          required: true,
          customProperties: []
        }
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'cache', 'apigateway']
    },
    {
      id: 'export-system',
      title: 'Step 11: Export Your System & Review',
      description: 'Congratulations on building a complete URL shortener architecture!\n\nYou\'ve created a scalable system that can handle millions of redirects, store URL mappings efficiently, and provide analytics on link performance.\n\nExport your system design as a PDF or image to share with others, and review how your design compares to real-world URL shorteners.',
      explanation: 'Exporting your system design allows you to share your architecture with teammates, stakeholders, or include it in documentation. Your design incorporates key principles used by real-world URL shorteners: separation of read and write paths, caching for performance, asynchronous analytics processing, and horizontal scalability. The actual implementations of services like Bit.ly and TinyURL include additional components for features like custom domains, link expiration, and advanced analytics.',
      hints: [
        'Click the "Export" button in the toolbar',
        'Choose your preferred export format',
        'Save the file to your computer',
        'Review the "Architecture Review" to see automated feedback on your design',
        'Compare your design to the reference architecture',
        'Consider how you might extend this design to support additional features like custom domains or link expiration',
        'Think about what security measures you might add to prevent abuse of the service'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false },
        exportRequired: true
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'cache', 'apigateway']
    }
  ]
};
