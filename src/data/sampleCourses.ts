
import { Course } from '@/types/course';

// Sample courses data - this would typically come from an API or database
export const sampleCourses: Course[] = [
  {
    id: 'design-instagram',
    title: 'Design Instagram',
    description: 'Learn to design a scalable Instagram-like system',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    currentStepIndex: 0,
    steps: [
      {
        id: 'add-client-frontend',
        title: 'Step 1: Add Client and Frontend',
        description: 'Start by adding a Client and a Frontend component to your design.\n\nFor Instagram, the client represents mobile apps (iOS/Android using React Native) and web browsers (using React), while the Frontend is a server-rendered application that handles UI and initial requests.',
        explanation: 'Every web application begins with a client (user\'s device) that connects to a frontend service. In Instagram\'s case, the frontend handles UI rendering, initial API requests, and maintains the user session state.',
        hints: [
          'Drag a Client component from the palette',
          'Next, drag a Frontend component',
          'Connect the Client to the Frontend',
          'Click on the connection line and add the label "User Requests" to describe the traffic flow',
          'Set the "framework" property to "React" on the Frontend',
          'Set the "platform" property to "Mobile Web" or "Web" on the Frontend'
        ],
        goal: {
          components: ['client', 'frontend'],
          connections: [['client', 'frontend']]
        },
        completed: false,
        filterComponents: ['client', 'frontend']
      },
      {
        id: 'add-api-gateway',
        title: 'Step 2: Add an API Gateway',
        description: 'An API Gateway acts as the single entry point to your backend services. It handles routing, rate limiting, authentication, and more.\n\nFor Instagram, the API Gateway validates auth tokens, routes requests to appropriate services, and protects against DDoS attacks.\n\nAdd an API Gateway component and connect it to your Frontend.',
        explanation: 'API Gateways are crucial for managing and securing traffic into your backend systems. Instagram uses API Gateways to handle millions of requests per second while ensuring security and performance.\n\nThis main API Gateway will serve as the central entry point for all client requests and will route them to various backend services based on the request path and authentication status.',
        hints: [
          'Drag an API Gateway component from the palette',
          'Connect the Frontend to the API Gateway',
          'Click on the connection line and add the label "API Requests" to describe the traffic flow',
          'Set the "rateLimit" property to "1000 rps" to handle Instagram\'s high traffic',
          'Check the "authRequired" checkbox to enforce authentication',
          'Select "JWT" and "OAuth2" in the "authTypes" multi-select field',
          'This main API Gateway will route requests to different services (Auth, Media, etc.)',
          'Consider the trade-off: setting a strict rate limit improves security but may impact user experience during traffic spikes'
        ],
        goal: {
          components: ['apigateway'],
          connections: [['frontend', 'apigateway']]
        },
        completed: false,
        filterComponents: ['apigateway']
      },
      {
        id: 'build-auth-service',
        title: 'Step 3: Build an Authentication Service',
        description: 'Drag a Composite component onto the canvas and rename it \'Authentication Service\'.\n\nInside it, create a small internal architecture using components like API Gateway, Auth Server, and User Database.\n\nFor Instagram, the Authentication Service handles user registration, login, session management, and social logins (Facebook, Google).',
        explanation: 'Authentication services manage user identity, sessions, and security tokens. Instagram\'s auth service uses OAuth 2.0 for authorization and stores JWT tokens in Redis for fast validation. The user database contains profile information and encrypted credentials.\n\nThe Authentication Service consists of several components working together:\n\n1. **Internal API Gateway**: Handles authentication-specific endpoints like /login, /register, and /validate-token. This is different from the main API Gateway which routes to multiple services.\n\n2. **Auth Server**: Processes authentication logic, validates credentials, generates tokens, and manages sessions.\n\n3. **User Database**: Stores user credentials, profile information, and account settings securely.\n\n4. **Cache**: Stores active session tokens for fast validation without hitting the database on every request.',
        hints: [
          'First drag a Composite component onto the canvas',
          'Rename it to "Authentication Service"',
          'Inside it, add an API Gateway, Server, and Database',
          'The internal API Gateway handles auth-specific endpoints (/login, /register, /validate-token)',
          'Connect the API Gateway to the Server',
          'Click on the connection and add the label "Auth Requests" to describe the traffic flow',
          'The Auth Server processes login requests, validates credentials, and generates tokens',
          'Connect the Server to the Database',
          'Click on the connection and add the label "User Data" to describe the data flow',
          'The User Database stores encrypted credentials and profile information',
          'Add a Cache component and connect it to the Server to store session tokens',
          'Click on the connection and add the label "Session Tokens" to describe what\'s being cached',
          'The Cache (typically Redis) stores active tokens for fast validation',
          'Optional: Add a custom property "Auth Protocol: OAuth 2.0" to the Server'
        ],
        goal: {
          components: ['composite', 'apigateway', 'server', 'database', 'cache'],
          connections: [['apigateway', 'server'], ['server', 'database'], ['server', 'cache']]
        },
        completed: false,
        filterComponents: ['composite', 'apigateway', 'server', 'database', 'cache']
      },
      {
        id: 'connect-auth-service',
        title: 'Step 4: Connect the Auth Service',
        description: 'Now that your Authentication Service is ready, connect it to the main API Gateway.\n\nThis connection enables the authentication flow where users log in and receive tokens that are validated on subsequent requests.\n\nIn Instagram, this connection handles millions of login attempts daily while protecting against credential stuffing attacks.',
        explanation: 'The authentication flow works like this: (1) User submits credentials, (2) Auth Service validates them against the database, (3) If valid, a JWT token is generated and returned, (4) The token is stored in the client and sent with future requests, (5) API Gateway validates the token before allowing access to protected resources.\n\nThis connection links the main API Gateway (from Step 2) to the Authentication Service composite. The main API Gateway routes authentication-related requests to the Authentication Service, which then processes them through its internal components.',
        hints: [
          'Connect the main API Gateway (from Step 2) to the Authentication Service composite',
          'The connection should go from the main API Gateway to Authentication Service',
          'Click on the connection and add the label "Authentication" to describe the traffic flow',
          'This connection represents a secure HTTPS communication channel',
          'The main API Gateway routes login/register requests to the Auth Service',
          'It also sends token validation requests to verify user authentication',
          'Think about what happens when an unauthenticated request tries to access protected resources'
        ],
        goal: {
          components: ['apigateway'],
          connections: [['apigateway', 'compositeNode']]
        },
        completed: false,
        filterComponents: ['apigateway', 'composite']
      },
      {
        id: 'add-media-service',
        title: 'Step 5: Add a Protected Media Service',
        description: 'Now let\'s route authenticated requests to a backend.\n\nAdd a new Server component and name it \'Media Service\'.\n\nConnect the Authentication Service composite to it.\n\nIn Instagram, the Media Service is a cluster of specialized microservices that handle image/video uploads, processing, filtering, and serving content to users.',
        explanation: 'Once users are authenticated, they can access protected resources like media files. The Media Service in Instagram processes over 100 million photos and videos daily, applying filters, compression, and generating multiple resolutions for different devices. It uses technologies like Node.js for API handling and specialized C++ services for image processing.',
        hints: [
          'Drag a Server component onto the canvas',
          'Rename it to "Media Service"',
          'Connect the Authentication Service to the Media Service',
          'Click on the connection and add the label "Authenticated Requests" to describe the traffic flow',
          'Set the "language" property to "Node.js" for the API layer',
          'Set the "apiType" property to "REST" for standard API access',
          'Set the "scaling" property to "Horizontal" to handle Instagram\'s massive scale',
          'Set the "retries" property to "3 with exponential backoff" for resilience',
          'Think about how the service would handle different types of media (photos, videos, stories)'
        ],
        goal: {
          components: ['server'],
          connections: [['compositeNode', 'server']]
        },
        completed: false,
        filterComponents: ['server', 'composite']
      },
      {
        id: 'simulate-traffic',
        title: 'Step 6: Configure & Stress Test Your System',
        description: 'Let\'s see what happens when your system is under pressure.\n\nFirst, configure your components by setting a max QPS (Queries Per Second). Then run a simulation with higher traffic to test system limits.\n\nInstagram handles over 500 million daily active users with traffic spikes during major events. Understanding how your system behaves under load is critical.',
        explanation: 'In real-world scenarios, services have capacity limits. When traffic exceeds these limits, systems may become overloaded, causing degraded performance or failures. During the simulation, you\'ll see requests flowing through your system with visual indicators showing bottlenecks and failures. The system health score will decrease as components get stressed.',
        hints: [
          'Set a custom property named "maxQPS" on Authentication Service (try different values between 5-20)',
          'Set a custom property named "maxQPS" on Media Service (try different values between 5-20)',
          'Configure simulation for 25 QPS to simulate a traffic spike',
          'Watch the real-time graph showing latency and throughput',
          'Notice which components become bottlenecks first',
          'Try different QPS settings to find the optimal balance for your architecture'
        ],
        goal: {
          components: ['compositeNode', 'server', 'apigateway'],
          connections: [],
          simulation: {
            required: true,
            minQPS: 15,
            maxQPS: 25,
            minDuration: 10,
            maxDuration: 20,
            payload: {
              userId: "abc123",
              action: "upload_photo"
            },
            customProperties: [
              { componentType: 'compositeNode', key: 'maxQPS', value: '10' },
              { componentType: 'server', key: 'maxQPS', value: '10' }
            ]
          }
        },
        completed: false,
        filterComponents: ['compositeNode', 'server', 'apigateway', 'frontend']
      },
      {
        id: 'add-queue',
        title: 'Step 7: Mitigate Overload with a Queue',
        description: 'Let\'s make your system more resilient.\n\nWhen your Media Service can\'t handle incoming load, we can buffer requests with a Queue.\n\nAdd a Queue component between the Authentication Service and Media Service, and reconnect the flow.\n\nInstagram uses message queues like Kafka and RabbitMQ to handle traffic spikes during major events and to ensure reliable processing of uploads even when services are under heavy load.',
        explanation: 'Queues act as buffers that store requests when downstream services are at capacity, preventing data loss and improving system resilience under load. During the simulation, you\'ll see how requests get buffered in the queue when the Media Service is overloaded, and then processed when capacity becomes available. Without a queue, these requests would be dropped, resulting in failed uploads and poor user experience.',
        hints: [
          'Add a Queue component to the canvas',
          'Disconnect the direct edge between Auth Service and Media Service',
          'Connect Auth Service to the Queue',
          'Click on the connection and add the label "Incoming Requests" to describe the traffic flow',
          'Connect the Queue to Media Service',
          'Click on the connection and add the label "Buffered Requests" to describe the traffic flow',
          'Set a custom property "maxQueueSize" to "50" on the Queue (this is required)',
          'Optional: Add a custom property "Type" with either "Fast Queue" (smaller capacity, faster processing) or "Large Queue" (higher capacity, slower processing)',
          'Run the simulation again to see how the queue buffers excess requests',
          'Watch what happens when the queue fills up completely (queue overflow)'
        ],
        goal: {
          components: ['queue'],
          connections: [
            ['compositeNode', 'queue'],
            ['queue', 'server']
          ],
          simulation: {
            required: false,
            customProperties: [
              { componentType: 'queue', key: 'maxQueueSize', value: '50' }
            ]
          }
        },
        completed: false,
        filterComponents: ['queue', 'composite', 'server']
      },
      {
        id: 'add-media-database',
        title: 'Step 8: Store Media in Scalable Storage',
        description: 'Add a Database to persist uploaded media. Connect it to the Media Service to store photos and videos securely.\n\nInstagram stores billions of photos and videos, requiring specialized storage solutions. The actual media files (photos/videos) are stored in object storage like Amazon S3, while metadata (captions, timestamps, user references) is stored in databases like Cassandra for fast access.',
        explanation: 'Media applications need reliable, scalable storage for user-generated content. Traditional relational databases aren\'t suitable for storing large binary files like photos and videos. Instead, Instagram uses a hybrid approach: object storage (S3) for the actual media files and distributed NoSQL databases for metadata. This separation allows for independent scaling of storage capacity and query performance.',
        hints: [
          'Add a Database component to the canvas',
          'Name it "Media DB"',
          'Connect the Media Service to the Media DB',
          'Click on the connection and add the label "Media Storage" to describe the data flow',
          'Set the "engine" property to "Amazon S3 + Cassandra" to represent Instagram\'s hybrid approach',
          'Set the "replication" property to "Sharded" for horizontal scalability',
          'In the "schema" field, add a simple schema for media metadata like "media_id, user_id, caption, timestamp, url"',
          'Consider how data would be distributed across storage nodes for scalability'
        ],
        goal: {
          components: ['database'],
          connections: [['server', 'database']]
        },
        completed: false,
        filterComponents: ['database', 'server']
      },
      {
        id: 'add-cdn',
        title: 'Step 9: Accelerate Delivery with CDN',
        description: 'Use a CDN (Content Delivery Network) to deliver images quickly to users across the globe.\n\nInstagram uses CDNs like Akamai and Cloudflare to cache photos and videos at edge locations worldwide, dramatically reducing load times for users and offloading traffic from origin servers.\n\nAdd a CDN component and connect it to your Media DB to enable this optimization.',
        explanation: 'CDNs cache content at edge locations worldwide, reducing latency and improving user experience by serving media from locations closer to users. For Instagram, this is critical - a user in Tokyo can view photos posted by someone in New York without the data traveling across the world for each request. CDNs also provide protection against DDoS attacks and reduce load on origin servers by serving cached content.',
        hints: [
          'Add a CDN component to the canvas',
          'Connect the Media DB to the CDN',
          'Click on the connection and add the label "Cache Source" to describe the data flow',
          'Connect the Frontend to the CDN for direct image fetches',
          'Click on the connection and add the label "Fast Media Delivery" to describe the traffic flow',
          'Set the "cacheTTL" property to 300 (5 minutes in seconds)',
          'Check the "geoDistribution" checkbox to enable worldwide edge caching',
          'Select "Images" and "Video" in the "contentTypes" multi-select field',
          'Consider the TTL trade-off: Short TTL (5 mins) ensures fresh content but increases origin load, while Long TTL (1 hour) reduces origin load but may serve stale content'
        ],
        goal: {
          components: ['cdn'],
          connections: [['database', 'cdn'], ['cdn', 'frontend']]
        },
        completed: false,
        filterComponents: ['cdn', 'database', 'frontend']
      },
      {
        id: 'add-analytics',
        title: 'Step 10: Add Analytics Service',
        description: 'Let\'s add an Analytics Service to track user engagement.\n\nCreate a Composite component named "Analytics Service" and connect it to the Media Service.\n\nInstagram\'s analytics system processes billions of events daily to understand user behavior, content performance, and system health. This data drives the recommendation algorithm, content moderation, and business decisions.',
        explanation: 'Analytics services collect and process user interaction data, providing insights that help improve content recommendations and user experience. Instagram tracks metrics like engagement rate (likes, comments, shares), session length, retention, and content preferences. This data is processed through both real-time streams (for immediate actions like content moderation) and batch processing (for long-term analysis and machine learning model training).',
        hints: [
          'Add a Composite component to the canvas',
          'Name it "Analytics Service"',
          'Connect it to the Media Service',
          'Click on the connection and add the label "Usage Events" to describe the data flow',
          'Inside the composite, add a Queue for event streaming, a Server for processing, and a Database for storage',
          'Connect the Queue to the Server and add the label "Event Stream"',
          'Connect the Server to the Database and add the label "Processed Data"',
          'Optional: Add a custom property "Processing Type" with either "Real-time" (immediate insights, higher cost) or "Batch" (delayed insights, lower cost)',
          'Optional: Add a custom property "Metrics" with value like "User Engagement" or "System Performance"'
        ],
        goal: {
          components: ['composite'],
          connections: [['server', 'composite']],
          simulation: { required: false }
        },
        completed: false,
        filterComponents: ['composite', 'server', 'database', 'queue']
      },
      {
        id: 'add-notifications',
        title: 'Step 11: Add Notifications',
        description: 'Let\'s add a notification system to keep users engaged.\n\nAdd a Server component named "Notification Service" and a Queue component for event processing.\n\nConnect the Media Service to the Queue, and the Queue to the Notification Service.\n\nInstagram sends billions of push notifications daily for likes, comments, follows, and other events. These notifications are critical for bringing users back to the app and increasing engagement.',
        explanation: 'Notification systems keep users engaged by alerting them about new content, interactions, or system events. Instagram uses Firebase Cloud Messaging (FCM) for Android and Apple Push Notification Service (APNS) for iOS to deliver notifications to mobile devices. The queue ensures notifications are delivered reliably even during high load and provides a buffer when notification services experience downtime. Different notification types have different priorities - a direct message notification is more urgent than a weekly activity summary.',
        hints: [
          'Add a Server component and name it "Notification Service"',
          'Add a Queue component for event processing',
          'Connect the Media Service to the Queue',
          'Click on the connection and add the label "Notification Events" to describe the data flow',
          'Connect the Queue to the Notification Service',
          'Click on the connection and add the label "Queued Notifications" to describe the data flow',
          'Optional: Add a custom property "Delivery Mode" with either "High Volume" (batched notifications, more efficient) or "Low Latency" (immediate delivery, higher resource usage)',
          'Optional: Add a "Dead Letter Queue" to handle failed notification attempts',
          'Consider how different notification types (likes, comments, follows) might have different priority handling'
        ],
        goal: {
          components: ['server', 'queue'],
          connections: [
            ['server', 'queue'],
            ['queue', 'server']
          ],
          simulation: { required: false }
        },
        completed: false,
        filterComponents: ['server', 'queue', 'database', 'cache']
      },
      {
        id: 'final-simulation',
        title: 'Step 12: Final System Test',
        description: 'Now let\'s test your complete system under load.\n\nRun the simulation to see how your architecture handles high traffic and even component failures.\n\nWatch for bottlenecks and see how your queue and CDN help manage the load.\n\nInstagram\'s production systems undergo constant load testing and chaos engineering to ensure reliability during major events like New Year\'s Eve when millions of users post simultaneously.',
        explanation: 'System testing under load helps identify bottlenecks and verify that your architecture can handle real-world traffic patterns. The simulation will show a comprehensive dashboard of system performance metrics including throughput, latency, error rates, and component health. You\'ll also see how your architecture handles random component failures, simulating real-world outages and testing the resilience of your design.',
        hints: [
          'Click the "Run Simulation" button',
          'Watch the real-time dashboard showing system performance metrics',
          'Notice how requests flow through your system',
          'See how the queue buffers requests during high load',
          'Observe how the CDN reduces load on your media service',
          'Try enabling "Chaos Testing" to randomly fail components and test system resilience',
          'Look for bottlenecks in your system and consider how you might address them',
          'Compare your system\'s performance to an "optimal" architecture'
        ],
        goal: {
          components: ['compositeNode', 'server', 'queue', 'database', 'cdn'],
          connections: [],
          simulation: {
            required: true,
            customProperties: []
          }
        },
        completed: false,
        filterComponents: ['frontend', 'server', 'database', 'queue', 'cdn', 'composite', 'cache', 'loadbalancer', 'client', 'apigateway']
      },
      {
        id: 'export-system',
        title: 'Step 13: Export Your System & Review',
        description: 'Congratulations on building a complete Instagram-like architecture!\n\nYou\'ve created a scalable system that can handle millions of users, store and deliver media efficiently, and provide a responsive user experience.\n\nExport your system design as a PDF or image to share with others, and review how your design compares to Instagram\'s actual architecture.',
        explanation: 'Exporting your system design allows you to share your architecture with teammates, stakeholders, or include it in documentation. Your design incorporates key principles used by Instagram\'s actual system: authentication, media processing, queuing for resilience, scalable storage, CDN for performance, analytics for insights, and notifications for engagement. The real Instagram system adds many more components for features like direct messaging, stories, reels, and shopping, plus extensive monitoring and security systems.',
        hints: [
          'Click the "Export" button in the toolbar',
          'Choose your preferred export format',
          'Save the file to your computer',
          'Review the "Architecture Review" to see automated feedback on your design',
          'Compare your design to the reference architecture',
          'Consider how you might extend this design to support additional features like direct messaging or stories',
          'Think about what monitoring and security components you might add in a production environment'
        ],
        goal: {
          components: [],
          connections: [],
          simulation: { required: false },
          exportRequired: true
        },
        completed: false,
        isInformationalOnly: true,
        filterComponents: ['frontend', 'server', 'database', 'queue', 'cdn', 'composite', 'cache', 'loadbalancer', 'client', 'apigateway']
      }
    ]
  }
];
