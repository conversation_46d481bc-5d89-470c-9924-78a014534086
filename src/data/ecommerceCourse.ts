import { Course } from '@/types/course';

export const ecommerceCourse: Course = {
  id: 'ecommerce-platform',
  title: 'E-commerce Platform Design',
  description: 'Learn how to design a scalable e-commerce platform like Amazon or Shopify that can handle millions of products and orders.',
  difficulty: 'intermediate',
  estimatedTime: '45 minutes',
  currentStepIndex: 0,
  steps: [
    {
      id: 'introduction',
      title: 'Step 1: Introduction to E-commerce Architecture',
      description: 'Welcome to the E-commerce Platform Design course!\n\nIn this course, you\'ll learn how to design a scalable architecture for an e-commerce platform that can handle millions of products, users, and orders.\n\nE-commerce platforms like Amazon and Shopify process billions of dollars in transactions annually and must maintain high availability even during peak shopping events like Black Friday.',
      explanation: 'E-commerce architectures are complex distributed systems that combine multiple critical subsystems: product catalog, inventory management, shopping cart, checkout process, payment processing, order fulfillment, and customer management. These systems must handle variable traffic patterns with significant spikes during sales events, maintain strong consistency for inventory and payments, while providing a responsive user experience.',
      hints: [
        'E-commerce platforms experience extreme traffic variations, with 10-100x normal volume during sales events',
        'Inventory management requires strong consistency to prevent overselling',
        'Payment processing must be highly reliable and secure',
        'Product search and discovery is a key differentiator for successful platforms',
        'Personalization and recommendations drive significant revenue increases'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      isInformationalOnly: true
    },
    {
      id: 'create-frontend',
      title: 'Step 2: Create the Frontend',
      description: 'Let\'s start by adding the Frontend component to our system.\n\nThe frontend for an e-commerce platform provides the user interface for browsing products, managing shopping carts, and completing purchases.\n\nModern e-commerce frontends are typically built as responsive web applications that work across desktop and mobile devices.',
      explanation: 'The frontend is the primary touchpoint for customers and significantly impacts conversion rates and average order values. E-commerce frontends must be fast-loading (each 100ms delay reduces conversion by ~1%), responsive across devices, and provide a seamless shopping experience. They typically implement client-side caching, lazy loading of content, and optimistic UI updates to create a fluid experience even when network conditions are suboptimal.',
      hints: [
        'Drag a Frontend component from the palette to the canvas',
        'Name it "E-commerce Frontend"',
        'Set the framework property to "React" for a modern web application',
        'Consider how the frontend will handle both desktop and mobile users',
        'Think about critical frontend performance optimizations like code splitting and lazy loading'
      ],
      goal: {
        components: ['frontend'],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend']
    },
    {
      id: 'add-api-gateway',
      title: 'Step 3: Add API Gateway',
      description: 'Now, let\'s add an API Gateway to manage requests from the frontend.\n\nThe API Gateway will route requests to the appropriate backend services based on the request type (product search, cart management, checkout, etc.).\n\nThis component is essential for an e-commerce platform as it provides a unified entry point while allowing backend services to evolve independently.',
      explanation: 'The API Gateway pattern provides a single entry point for all client requests while enabling backend services to be developed, deployed, and scaled independently. For e-commerce platforms, the gateway handles cross-cutting concerns like authentication, rate limiting, and request routing. It also enables different optimization strategies for different types of requests - for example, product browsing can be heavily cached while checkout flows require strong consistency.',
      hints: [
        'Add an API Gateway component to the canvas',
        'Connect the Frontend to the API Gateway',
        'Set rate limiting properties to protect backend services',
        'Configure authentication requirements',
        'Consider how the gateway will route different types of requests (product search, cart operations, checkout)'
      ],
      goal: {
        components: ['apigateway'],
        connections: [['frontend', 'apigateway']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend', 'apigateway']
    },
    {
      id: 'build-auth-service',
      title: 'Step 4: Build Authentication Service',
      description: 'Let\'s create the Authentication Service to handle user identity and access control.\n\nThe Authentication Service manages user registration, login, session management, and authorization.\n\nFor e-commerce platforms, robust authentication is critical as it protects customer data and payment information.',
      explanation: 'The Authentication Service is a critical security component that verifies user identities and manages access to protected resources. E-commerce platforms typically support multiple authentication methods (email/password, social logins, SSO for enterprise customers) and must implement strong security measures to protect sensitive customer and payment data. The service must scale to handle millions of users while maintaining low latency, as authentication is a prerequisite for most user interactions.',
      hints: [
        'Add a Composite component and name it "Authentication Service"',
        'Inside the composite, add a Server component for the auth logic',
        'Add a Database component for user credentials and sessions',
        'Connect the API Gateway to the Authentication Service',
        'Set properties like authentication methods (OAuth, JWT) and session duration',
        'Consider security features like rate limiting for login attempts and MFA support'
      ],
      goal: {
        components: ['composite'],
        connections: [['apigateway', 'composite']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'create-product-catalog',
      title: 'Step 5: Create Product Catalog Service',
      description: 'Now, let\'s build the Product Catalog Service to manage product information.\n\nThis service is responsible for storing and retrieving product details, categories, pricing, and availability.\n\nFor e-commerce platforms, the product catalog must support complex queries, faceted search, and high read throughput.',
      explanation: 'The Product Catalog Service manages the core data of an e-commerce platform: products, categories, attributes, pricing, and availability. This service must support complex queries (filtering, sorting, faceted navigation) while handling high read throughput. The data model is typically denormalized for read performance, with specialized indexes for different query patterns. For large catalogs (millions of products), search functionality is often implemented using specialized search engines like Elasticsearch.',
      hints: [
        'Add a Server component and name it "Product Catalog Service"',
        'Connect the API Gateway to the Product Catalog Service',
        'Add a Database component for product data',
        'Connect the Product Catalog Service to the Database',
        'Set the "engine" property to "MongoDB" or "PostgreSQL" based on your data model preferences',
        'Consider how to handle product variations, categories, and attributes',
        'Think about caching strategies for frequently accessed products'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite']
    },
    {
      id: 'add-search-service',
      title: 'Step 6: Add Search Service',
      description: 'Let\'s add a dedicated Search Service to enable efficient product discovery.\n\nThe Search Service indexes product data and provides fast, relevant search results with filtering and faceting capabilities.\n\nFor e-commerce platforms, search quality directly impacts conversion rates and revenue.',
      explanation: 'Product search is a critical function for e-commerce platforms, with studies showing that users who search have 2-3x higher conversion rates than those who only browse. The Search Service indexes product data (name, description, attributes, categories) and provides fast, relevant results with support for filtering, faceting, and sorting. Modern e-commerce search systems incorporate machine learning for relevance tuning, typo tolerance, and personalization. The service typically uses specialized search engines like Elasticsearch or Solr that are optimized for this use case.',
      hints: [
        'Add a Composite component and name it "Search Service"',
        'Inside the composite, add a Server component for search logic',
        'Add a specialized Database component for the search index',
        'Connect the API Gateway to the Search Service',
        'Connect the Product Catalog Service to the Search Service for indexing new products',
        'Set properties like indexing frequency and search algorithms',
        'Consider how to handle faceted search, filters, and sorting options'
      ],
      goal: {
        components: ['composite'],
        connections: [
          ['apigateway', 'composite'],
          ['server', 'composite']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'build-cart-service',
      title: 'Step 7: Build Shopping Cart Service',
      description: 'Let\'s create the Shopping Cart Service to manage user shopping sessions.\n\nThis service handles adding/removing items, applying promotions, and maintaining cart state across sessions.\n\nThe cart service must be highly available and responsive to provide a seamless shopping experience.',
      explanation: 'The Shopping Cart Service manages temporary user state during the shopping process. It must handle operations like adding/removing items, updating quantities, applying promotions, and calculating totals. Cart data needs to be persisted across sessions and devices, allowing users to continue shopping where they left off. The service requires low latency for a responsive user experience and must handle session expiration, inventory validation, and price recalculation when items change.',
      hints: [
        'Add a Server component and name it "Cart Service"',
        'Connect the API Gateway to the Cart Service',
        'Add a Database component for cart data (typically a NoSQL or in-memory database)',
        'Connect the Cart Service to the Database',
        'Connect the Cart Service to the Product Catalog Service for product information',
        'Set properties like session timeout and consistency requirements',
        'Consider how to handle cart merging when users log in with items in their anonymous cart'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database'],
          ['server', 'server']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite', 'cache']
    },
    {
      id: 'add-inventory-service',
      title: 'Step 8: Add Inventory Management Service',
      description: 'Let\'s add an Inventory Management Service to track product availability.\n\nThis service maintains real-time inventory levels, reserves stock during checkout, and prevents overselling.\n\nInventory management requires strong consistency and must integrate with both online and physical sales channels.',
      explanation: 'The Inventory Management Service tracks product availability across warehouses and sales channels. It must maintain accurate, real-time inventory levels to prevent overselling while maximizing product availability. The service implements inventory reservation during checkout, handles backorders and pre-orders, and often integrates with warehouse management systems. Inventory updates require strong consistency guarantees, making this one of the few components in an e-commerce system that prioritizes consistency over availability.',
      hints: [
        'Add a Server component and name it "Inventory Service"',
        'Connect the API Gateway to the Inventory Service',
        'Add a Database component for inventory data',
        'Connect the Inventory Service to the Database',
        'Connect the Cart Service to the Inventory Service for availability checks',
        'Set the "consistency" property to "Strong" for accurate inventory tracking',
        'Consider how to handle inventory reservation during checkout',
        'Think about strategies for handling high-demand products with limited stock'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database'],
          ['server', 'server']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite', 'cache']
    },
    {
      id: 'create-order-service',
      title: 'Step 9: Create Order Processing Service',
      description: 'Let\'s build the Order Processing Service to handle the checkout and order management flow.\n\nThis service manages the entire lifecycle of an order from checkout to fulfillment and returns.\n\nOrder processing is a critical path that requires high reliability and transactional integrity.',
      explanation: 'The Order Processing Service manages the entire lifecycle of customer orders. During checkout, it orchestrates a complex workflow involving multiple services: validating the cart, checking inventory, processing payment, and creating the order. After checkout, it tracks order status, manages fulfillment, handles cancellations and returns, and communicates updates to customers. This service must maintain transactional integrity across multiple systems while providing visibility into order status at each stage.',
      hints: [
        'Add a Server component and name it "Order Service"',
        'Connect the API Gateway to the Order Service',
        'Add a Database component for order data',
        'Connect the Order Service to the Database',
        'Connect the Order Service to the Cart Service and Inventory Service',
        'Set properties like transaction management and retry policies',
        'Consider how to implement the checkout workflow as a saga pattern',
        'Think about how to handle partial fulfillment and order splitting'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database'],
          ['server', 'server']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite', 'cache']
    },
    {
      id: 'add-payment-service',
      title: 'Step 10: Add Payment Processing Service',
      description: 'Let\'s add a Payment Processing Service to handle financial transactions.\n\nThis service integrates with payment gateways, processes credit cards and alternative payment methods, and ensures secure handling of financial data.\n\nPayment processing must be highly secure, reliable, and compliant with regulations like PCI DSS.',
      explanation: 'The Payment Processing Service handles all financial transactions in the e-commerce platform. It integrates with payment gateways and processors to support multiple payment methods (credit cards, digital wallets, buy-now-pay-later). The service must comply with PCI DSS requirements, typically by tokenizing sensitive payment data and minimizing its storage. Payment processing requires high reliability with idempotent operations to prevent double-charging, and must handle complex scenarios like partial refunds, chargebacks, and payment authorization holds.',
      hints: [
        'Add a Server component and name it "Payment Service"',
        'Connect the Order Service to the Payment Service',
        'Add a Database component for payment records (not actual payment details)',
        'Connect the Payment Service to the Database',
        'Set properties like supported payment methods and security compliance',
        'Consider how to handle payment gateway integration',
        'Think about retry strategies for failed payments',
        'Consider how to implement idempotent payment processing'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['server', 'server'],
          ['server', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite', 'cache']
    },
    {
      id: 'configure-stress-test',
      title: 'Step 11: Configure & Stress Test Your System',
      description: 'Let\'s test how our system performs under load.\n\nWe\'ll configure performance parameters for our services and run a simulation to identify bottlenecks.\n\nE-commerce platforms must handle extreme traffic variations, with 10-100x normal volume during sales events like Black Friday.',
      explanation: 'Performance testing is critical for e-commerce platforms that experience extreme traffic variations. During major sales events like Black Friday, traffic can spike dramatically, and the system must handle this increased load without degradation. By setting capacity limits on components and simulating high traffic, we can identify bottlenecks and failure points before they affect customers in production.',
      hints: [
        'Set a custom property named "maxQPS" on the Product Catalog Service (try values between 50-200)',
        'Set a custom property named "maxQPS" on the Cart Service (try values between 20-100)',
        'Set a custom property named "maxQPS" on the Order Service (try values between 10-50)',
        'Configure simulation for 150 QPS to simulate a Black Friday traffic spike',
        'Watch the real-time graph showing latency and throughput',
        'Notice which components become bottlenecks first',
        'Try different QPS settings to find the optimal balance for your architecture'
      ],
      goal: {
        components: ['server', 'composite'],
        connections: [],
        simulation: {
          required: true,
          minQPS: 100,
          maxQPS: 200,
          minDuration: 10,
          maxDuration: 20,
          payload: {
            userId: "user123",
            action: "browse_products"
          },
          customProperties: [
            { componentType: 'server', key: 'maxQPS', value: '100' },
            { componentType: 'composite', key: 'maxQPS', value: '150' }
          ]
        }
      },
      completed: false,
      filterComponents: ['server', 'composite', 'apigateway', 'frontend']
    },
    {
      id: 'add-recommendation-engine',
      title: 'Step 12: Add Recommendation Engine',
      description: 'Let\'s add a Recommendation Engine to personalize the shopping experience.\n\nThis service analyzes user behavior and product relationships to suggest relevant items to customers.\n\nPersonalized recommendations can increase average order value by 10-30% and are a key revenue driver for e-commerce platforms.',
      explanation: 'Recommendation engines analyze user behavior, purchase history, and product relationships to suggest relevant items to customers. These systems typically combine multiple algorithms: collaborative filtering (users who bought X also bought Y), content-based filtering (items with similar attributes), and contextual recommendations (based on current browsing session). The recommendation service processes user events in real-time to update user profiles while periodically running batch jobs to train recommendation models.',
      hints: [
        'Add a Composite component and name it "Recommendation Engine"',
        'Inside the composite, add a Server for recommendation logic and a Database for user profiles',
        'Connect the API Gateway to the Recommendation Engine',
        'Connect the Product Catalog Service to the Recommendation Engine',
        'Add a Queue component for processing user events',
        'Connect the Frontend to the Queue for sending user behavior events',
        'Connect the Queue to the Recommendation Engine',
        'Consider different recommendation algorithms and their trade-offs',
        'Think about the balance between real-time and batch processing'
      ],
      goal: {
        components: ['composite', 'queue'],
        connections: [
          ['apigateway', 'composite'],
          ['server', 'composite'],
          ['frontend', 'queue'],
          ['queue', 'composite']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'queue', 'server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'add-caching-layer',
      title: 'Step 13: Add Caching Layer',
      description: 'Let\'s improve performance by adding a distributed caching layer.\n\nCaching is essential for e-commerce platforms to reduce database load and improve response times for frequently accessed data.\n\nWe\'ll implement caching for product data, search results, and user sessions.',
      explanation: 'Caching is a critical optimization for e-commerce platforms with high read-to-write ratios. By storing frequently accessed data in memory, caches reduce database load and improve response times. E-commerce platforms typically implement multiple caching layers: CDN for static assets, API response caching for product listings, fragment caching for page components, and distributed caches for session data. Each cache has different invalidation strategies based on the data lifecycle and consistency requirements.',
      hints: [
        'Add Cache components for frequently accessed data',
        'Connect the Product Catalog Service to a Product Cache',
        'Connect the Search Service to a Search Results Cache',
        'Connect the Cart Service to a Session Cache',
        'Set appropriate TTL values based on data update frequency',
        'Consider cache invalidation strategies when data changes',
        'Think about cache hit ratios and how they affect system performance',
        'Consider the trade-off between cache freshness and database load'
      ],
      goal: {
        components: ['cache'],
        connections: [
          ['server', 'cache'],
          ['composite', 'cache']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['cache', 'server', 'composite', 'apigateway', 'frontend']
    },
    {
      id: 'final-simulation',
      title: 'Step 14: Final System Test',
      description: 'Now let\'s test your complete e-commerce architecture under load.\n\nRun the simulation to see how your system handles high traffic and component failures.\n\nWatch for bottlenecks and see how your caching and queuing mechanisms help manage the load.',
      explanation: 'The final system test validates that all components work together effectively under stress. The simulation will show a comprehensive dashboard of system performance metrics including throughput, latency, error rates, and component health. You\'ll also see how your architecture handles random component failures, simulating real-world outages and testing the resilience of your design.',
      hints: [
        'Click the "Run Simulation" button',
        'Watch the real-time dashboard showing system performance metrics',
        'Notice how requests flow through your system',
        'See how caches reduce load on your backend services',
        'Observe how queues buffer requests during high load',
        'Try enabling "Chaos Testing" to randomly fail components and test system resilience',
        'Look for bottlenecks in your system and consider how you might address them'
      ],
      goal: {
        components: ['compositeNode', 'server', 'queue', 'database', 'cache'],
        connections: [],
        simulation: {
          required: true,
          customProperties: []
        }
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'composite', 'cache', 'apigateway']
    },
    {
      id: 'export-system',
      title: 'Step 15: Export Your System & Review',
      description: 'Congratulations on building a complete e-commerce platform architecture!\n\nYou\'ve created a scalable system that can handle millions of products and orders, provide personalized shopping experiences, and maintain high availability during peak events.\n\nExport your system design as a PDF or image to share with others, and review how your design compares to real-world e-commerce platforms.',
      explanation: 'Exporting your system design allows you to share your architecture with teammates, stakeholders, or include it in documentation. Your design incorporates key principles used by real-world e-commerce platforms: service-oriented architecture, caching strategies, asynchronous processing, and specialized services for critical functions. The actual implementations of platforms like Amazon and Shopify include many more components for features like seller management, logistics, fraud detection, and advanced analytics.',
      hints: [
        'Click the "Export" button in the toolbar',
        'Choose your preferred export format',
        'Save the file to your computer',
        'Review the "Architecture Review" to see automated feedback on your design',
        'Compare your design to the reference architecture',
        'Consider how you might extend this design to support additional features like multi-seller marketplaces or international shipping',
        'Think about what monitoring and security components you might add in a production environment'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false },
        exportRequired: true
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'composite', 'cache', 'apigateway']
    }
  ]
};
