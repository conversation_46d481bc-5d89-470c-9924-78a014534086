{"design": {"problem": "Design a service that converts long URLs into short, unique aliases (e.g., tinyurl.com/abc123) and redirects users to the original URL when accessed.", "system_name": "URL Shortener", "components": [{"id": "cdn", "type": "primitive", "subtype": "cdn", "label": "Global CDN", "metadata": {"purpose": "Global content delivery and edge caching", "provider": "CloudFlare", "features": ["Geographic distribution across 200+ locations", "Edge caching for popular short URLs", "DDoS protection and security filtering", "SSL termination and HTTP/2 support"], "cache_strategy": {"ttl": "24 hours for popular URLs", "invalidation": "Real-time via API", "hit_ratio_target": "85%+"}, "performance": {"global_latency": "< 50ms", "bandwidth": "100+ Gbps per edge location"}}}, {"id": "load_balancer", "type": "primitive", "subtype": "load_balancer", "label": "Global Load Balancer", "metadata": {"purpose": "Distribute traffic across multiple regions and data centers", "algorithm": "Geographic routing with health-based failover", "health_checks": {"endpoint": "/health", "interval": "5s", "timeout": "2s", "failure_threshold": 3}, "features": ["DNS-based geographic routing", "Automatic failover between regions", "Traffic splitting for A/B testing", "Rate limiting and DDoS protection"], "capacity": "1M+ requests per second"}}, {"id": "api_gateway", "type": "primitive", "subtype": "api_gateway", "label": "API Gateway", "metadata": {"purpose": "API management, authentication, and request routing", "features": ["Request authentication and authorization", "Rate limiting per user/IP (1000 req/hour free tier)", "Request/response transformation", "API versioning and backward compatibility", "Request logging and monitoring"], "rate_limiting": {"free_tier": "1000 requests/hour", "premium_tier": "100000 requests/hour", "burst_capacity": "10x normal rate for 1 minute"}, "authentication": ["API key authentication", "OAuth 2.0 integration", "JWT token validation"]}}, {"id": "url_creation_service", "type": "primitive", "subtype": "application_server", "label": "URL Creation Service", "metadata": {"purpose": "Handle URL shortening requests and generate unique short codes", "language": "Go", "scaling": "Horizontal with auto-scaling", "logic": ["Validate and normalize input URLs", "Generate unique short codes using Base62 encoding", "Handle collision detection and resolution", "Support custom aliases with availability checking", "Implement rate limiting per user"], "short_code_generation": {"algorithm": "Counter-based with Base62 encoding", "length": "6-8 characters (a-z, A-Z, 0-9)", "collision_handling": "Database uniqueness constraint + retry", "custom_aliases": "User-defined with availability check"}, "api_endpoints": ["POST /api/v1/shorten - Create short URL", "POST /api/v1/custom - Create custom short URL", "GET /api/v1/urls/{userId} - List user's URLs", "DELETE /api/v1/urls/{shortCode} - Delete URL"], "performance": {"target_latency": "< 200ms", "throughput": "10000 requests/second per instance", "auto_scaling": "CPU > 70% or Queue depth > 100"}}}, {"id": "redirect_service", "type": "primitive", "subtype": "application_server", "label": "Redirect Service", "metadata": {"purpose": "Handle redirect requests with minimal latency", "language": "Rust", "scaling": "Horizontal with aggressive auto-scaling", "logic": ["Extract short code from request URL", "Check multi-tier cache hierarchy", "Query database only on cache miss", "Return HTTP 301/302 redirect response", "Emit analytics events asynchronously"], "caching_strategy": {"l1_cache": "In-memory LRU cache (10K most popular URLs)", "l2_cache": "Redis cluster lookup", "l3_cache": "Database with read replicas", "cache_warming": "Preload trending URLs"}, "api_endpoints": ["GET /{shortCode} - Redirect to original URL", "GET /api/v1/resolve/{shortCode} - Get URL without redirect"], "performance": {"target_latency": "< 50ms (cache hit), < 100ms (cache miss)", "throughput": "50000 requests/second per instance", "cache_hit_ratio": "95%+"}}}, {"id": "redis_cluster", "type": "primitive", "subtype": "cache", "label": "Redis Cluster", "metadata": {"purpose": "High-performance distributed caching for URL mappings", "engine": "Redis Cluster", "scaling": "Sharded across multiple nodes", "config": {"nodes": "6 nodes (3 masters, 3 replicas)", "memory_per_node": "32GB", "eviction_policy": "allkeys-lru", "persistence": "RDB snapshots + AOF logging"}, "data_structure": {"key_pattern": "url:{shortCode}", "value_format": "JSON with original_url, created_at, click_count", "ttl": "7 days for regular URLs, 30 days for popular URLs"}, "performance": {"latency": "< 1ms", "throughput": "100K+ ops/second per node", "memory_efficiency": "Compression enabled"}, "monitoring": ["Memory usage and eviction rates", "Cache hit/miss ratios", "Replication lag", "Cluster health and failover events"]}}, {"id": "url_database", "type": "primitive", "subtype": "nosql_db", "label": "URL Database (Cassandra)", "metadata": {"purpose": "Persistent storage for URL mappings with horizontal scalability", "engine": "Apache Cassandra", "scaling": "Distributed across multiple data centers", "schema": {"url_mappings": {"short_code": "text PRIMARY KEY", "original_url": "text", "user_id": "uuid", "created_at": "timestamp", "expires_at": "timestamp", "click_count": "counter", "is_custom": "boolean", "status": "text"}, "user_urls": {"user_id": "uuid", "created_at": "timestamp", "short_code": "text", "PRIMARY KEY": "(user_id, created_at)"}}, "partitioning": {"strategy": "Hash partitioning on short_code", "replication_factor": 3, "consistency_level": "QUORUM for writes, ONE for reads"}, "performance": {"write_latency": "< 10ms", "read_latency": "< 5ms", "throughput": "100K+ writes/second, 1M+ reads/second"}}}, {"id": "analytics_database", "type": "primitive", "subtype": "time_series_db", "label": "Analytics Database (InfluxDB)", "metadata": {"purpose": "Store and analyze click analytics and usage patterns", "engine": "InfluxDB", "scaling": "Time-based sharding with retention policies", "schema": {"click_events": {"measurement": "url_clicks", "tags": ["short_code", "country", "device_type", "referrer_domain"], "fields": ["click_count", "response_time", "user_agent"], "timestamp": "nanosecond precision"}, "aggregated_stats": {"measurement": "url_stats_hourly", "tags": ["short_code", "hour"], "fields": ["total_clicks", "unique_visitors", "avg_response_time"]}}, "retention_policy": {"raw_events": "30 days", "hourly_aggregates": "1 year", "daily_aggregates": "5 years"}, "performance": {"ingestion_rate": "1M+ events/second", "query_latency": "< 100ms for dashboard queries", "compression_ratio": "10:1"}}}, {"id": "event_stream", "type": "primitive", "subtype": "message_queue", "label": "Event Stream (Kafka)", "metadata": {"purpose": "Real-time event streaming for analytics and monitoring", "engine": "Apache Kafka", "scaling": "Multi-partition topics with replication", "topics": ["url-clicks: Click events from redirect service", "url-created: New URL creation events", "url-analytics: Processed analytics data", "system-metrics: Performance and health metrics"], "configuration": {"partitions": "12 per topic for parallelism", "replication_factor": 3, "retention": "7 days for click events, 30 days for metrics"}, "performance": {"throughput": "1M+ messages/second", "latency": "< 10ms end-to-end", "durability": "Replicated across 3 brokers"}}}, {"id": "analytics_service", "type": "primitive", "subtype": "stream_processor", "label": "Analytics Service", "metadata": {"purpose": "Process click events and generate real-time analytics", "language": "Scala with Apache Flink", "scaling": "Horizontal with dynamic parallelism", "logic": ["Consume click events from Kafka stream", "Enrich events with geographic and device data", "Aggregate metrics in real-time windows", "Detect anomalies and fraud patterns", "Store processed data in analytics database"], "stream_processing": {"framework": "Apache Flink", "windowing": "Tumbling windows (1 minute, 1 hour, 1 day)", "state_backend": "RocksDB for fault tolerance", "checkpointing": "Every 30 seconds"}, "metrics_generated": ["Click counts per URL/hour/day", "Geographic distribution of clicks", "Device and browser analytics", "Referrer domain analysis", "Response time percentiles"], "performance": {"processing_latency": "< 5 seconds", "throughput": "500K+ events/second", "fault_tolerance": "Exactly-once processing guarantees"}}}, {"id": "monitoring_system", "type": "primitive", "subtype": "monitoring", "label": "Monitoring & Alerting", "metadata": {"purpose": "Comprehensive system monitoring, alerting, and observability", "stack": "Prometheus + Grafana + AlertManager", "metrics_collected": ["Request latency percentiles (p50, p95, p99)", "Throughput (requests/second per service)", "Error rates and status code distribution", "Cache hit ratios and performance", "Database connection pools and query performance", "System resources (CPU, memory, disk, network)"], "alerting_rules": ["Redirect latency > 100ms for 5 minutes", "Error rate > 1% for 2 minutes", "Cache hit ratio < 90% for 10 minutes", "Database connection pool > 80% for 5 minutes", "Any service instance down for 1 minute"], "dashboards": ["Real-time system overview", "Service-specific performance metrics", "Business metrics (URLs created, clicks served)", "Infrastructure health and capacity"], "log_aggregation": {"system": "ELK Stack (Elasticsearch, Logstash, Kibana)", "retention": "30 days for application logs, 7 days for access logs", "indexing": "Time-based indices with automated rotation"}}}], "connections": [{"id": "1", "start": "cdn", "end": "load_balancer", "label": "HTTPS Traffic"}, {"id": "2", "start": "load_balancer", "end": "api_gateway", "label": "Load Balanced Requests"}, {"id": "3", "start": "api_gateway", "end": "url_creation_service", "label": "URL Creation API"}, {"id": "4", "start": "api_gateway", "end": "redirect_service", "label": "Redirect Requests"}, {"id": "5", "start": "url_creation_service", "end": "redis_cluster", "label": "<PERSON><PERSON> New URLs"}, {"id": "6", "start": "redirect_service", "end": "redis_cluster", "label": "<PERSON><PERSON>"}, {"id": "7", "start": "url_creation_service", "end": "url_database", "label": "Store URL Mappings"}, {"id": "8", "start": "redirect_service", "end": "url_database", "label": "Database Fallback"}, {"id": "9", "start": "redirect_service", "end": "event_stream", "label": "Click Events"}, {"id": "10", "start": "event_stream", "end": "analytics_service", "label": "Event Stream"}, {"id": "11", "start": "analytics_service", "end": "analytics_database", "label": "Store Analytics"}, {"id": "12", "start": "analytics_service", "end": "monitoring_system", "label": "Metrics & Alerts"}, {"id": "13", "start": "url_creation_service", "end": "monitoring_system", "label": "Service Metrics"}, {"id": "14", "start": "redirect_service", "end": "monitoring_system", "label": "Performance Metrics"}, {"id": "15", "start": "redis_cluster", "end": "monitoring_system", "label": "<PERSON><PERSON>"}, {"id": "16", "start": "url_database", "end": "monitoring_system", "label": "Database Metrics"}], "core_user_journey": "1. User submits long URL via web interface or API. 2. System validates URL format and generates unique 6-8 character short code using Base62 encoding. 3. Stores mapping in distributed database with replication. 4. Returns shortened URL within 200ms. 5. When user clicks shortened URL, CDN checks cache, then load balancer routes to redirect service. 6. Redirect service checks Redis cache, falls back to database if needed. 7. Returns HTTP 301/302 redirect within 100ms. 8. Click events sent asynchronously to analytics pipeline.", "functional_requirements": ["Generate unique short URLs (6-8 characters) using Base62 encoding with collision handling", "Redirect users from short URLs to original URLs with < 100ms latency", "Allow custom short URLs with availability checking and user-defined aliases", "Track comprehensive analytics including click count, geographic data, device types, and referrer domains with real-time processing"], "non_functional_requirements": ["Handle 10M+ URL shortens per day (500 URLs/second peak) with horizontal auto-scaling", "Achieve redirect latency < 100ms through multi-tier caching (CDN + Redis + in-memory)", "Maintain 99.9% availability with automatic failover, health checks, and circuit breakers"]}}