import { Course } from '@/types/course';

export const twitterCourse: Course = {
  id: 'twitter-architecture',
  title: 'Twitter System Design',
  description: 'Learn how to design a scalable Twitter-like platform that can handle hundreds of millions of tweets daily with real-time delivery to followers.',
  difficulty: 'intermediate',
  estimatedTime: '45 minutes',
  currentStepIndex: 0,
  steps: [
    {
      id: 'introduction',
      title: 'Step 1: Introduction to Twitter Architecture',
      description: 'Welcome to the Twitter System Design course!\n\nIn this course, you\'ll learn how to design a scalable architecture for a Twitter-like platform that can handle hundreds of millions of tweets daily and deliver them in real-time to millions of users.\n\nTwitter processes over 500 million tweets per day and supports more than 330 million monthly active users, requiring a highly distributed and resilient architecture.',
      explanation: 'Twitter\'s architecture is designed to handle massive scale with extremely low latency. The system must support three core functions: tweeting (write), timeline generation (read), and search. Each of these functions has unique scaling challenges. Twitter uses a service-oriented architecture with specialized components that can scale independently based on their specific requirements.',
      hints: [
        'Twitter\'s architecture has evolved from a monolithic Ruby on Rails application to a highly distributed system',
        'The platform must handle asymmetric relationships (users can have millions of followers)',
        'Real-time delivery is a key requirement, with tweets appearing in timelines within seconds',
        'The system must support high write throughput (new tweets) and even higher read throughput (timeline generation)',
        'Search functionality requires specialized indexing and retrieval systems'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      isInformationalOnly: true
    },
    {
      id: 'create-frontend',
      title: 'Step 2: Create the Frontend',
      description: 'Let\'s start by adding the Frontend component to our system.\n\nThe Twitter frontend consists of web and mobile clients that provide the user interface for creating tweets, viewing timelines, and interacting with content.\n\nTwitter uses React for its web frontend and native development for its mobile applications, with a shared design system to maintain consistency across platforms.',
      explanation: 'The frontend is responsible for rendering the user interface and communicating with backend services. It handles user authentication, displays timelines, enables tweet composition, and manages user interactions like likes, retweets, and replies. Twitter\'s frontend is designed to be fast and responsive, with optimizations for both high-end devices and users with limited connectivity.',
      hints: [
        'Drag a Frontend component from the palette to the canvas',
        'Name it "Twitter Frontend"',
        'Set the framework property to "React" for the web version',
        'Consider how the frontend handles different form factors (desktop, mobile, tablet)',
        'Think about progressive enhancement for users with slower connections'
      ],
      goal: {
        components: ['frontend'],
        connections: [],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend']
    },
    {
      id: 'add-api-gateway',
      title: 'Step 3: Add API Gateway',
      description: 'Now, let\'s add an API Gateway to manage requests from the frontend.\n\nThe API Gateway serves as the entry point for all client requests, routing them to the appropriate backend services.\n\nTwitter uses a sophisticated API Gateway to handle authentication, rate limiting, and request routing, processing billions of API calls daily.',
      explanation: 'The API Gateway pattern provides a single entry point for all client requests, which simplifies the client architecture and provides a layer of abstraction between clients and services. It handles cross-cutting concerns like authentication, rate limiting, and request/response transformation. This pattern is essential for Twitter\'s microservice architecture, allowing backend services to evolve independently without affecting clients.',
      hints: [
        'Add an API Gateway component to the canvas',
        'Connect the Frontend to the API Gateway',
        'Set rate limiting properties to protect backend services',
        'Configure authentication requirements',
        'Consider how the gateway routes different types of requests (tweets, timelines, user profiles)'
      ],
      goal: {
        components: ['apigateway'],
        connections: [['frontend', 'apigateway']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['frontend', 'apigateway']
    },
    {
      id: 'build-auth-service',
      title: 'Step 4: Build Authentication Service',
      description: 'Let\'s create the Authentication Service to handle user identity and access control.\n\nThe Authentication Service manages user registration, login, session management, and authorization.\n\nTwitter\'s auth system handles millions of authentication requests per second and must be highly available to ensure users can always access the platform.',
      explanation: 'The Authentication Service is critical infrastructure that must be extremely reliable and secure. It verifies user identities, issues access tokens, and maintains session state. Twitter uses OAuth for authentication and implements sophisticated security measures to protect user accounts. The service must scale horizontally to handle authentication spikes during major events when millions of users may log in simultaneously.',
      hints: [
        'Add a Composite component and name it "Authentication Service"',
        'Inside the composite, add a Server component for the auth logic',
        'Add a Database component for user credentials and sessions',
        'Connect the API Gateway to the Authentication Service',
        'Set properties like authentication methods (OAuth, JWT) and session duration',
        'Consider security features like rate limiting for login attempts and MFA support'
      ],
      goal: {
        components: ['composite'],
        connections: [['apigateway', 'composite']],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'server', 'database', 'apigateway', 'frontend']
    },
    {
      id: 'create-tweet-service',
      title: 'Step 5: Create Tweet Service',
      description: 'Now, let\'s build the Tweet Service to handle tweet creation and storage.\n\nThe Tweet Service is responsible for processing new tweets, storing them, and initiating the fan-out process to deliver tweets to followers.\n\nTwitter\'s tweet service handles over 6,000 tweets per second during peak times, with each tweet potentially being delivered to millions of followers.',
      explanation: 'The Tweet Service is at the core of Twitter\'s functionality. It validates tweets, stores them durably, and initiates the complex process of delivering tweets to followers\' timelines. The service must be highly available for write operations and must guarantee that tweets are never lost once accepted. It also handles media attachments, hashtag extraction, and @mentions processing.',
      hints: [
        'Add a Server component and name it "Tweet Service"',
        'Connect the API Gateway to the Tweet Service',
        'Add a Database component for tweet storage',
        'Connect the Tweet Service to the Database',
        'Set properties like tweet character limit (280) and supported media types',
        'Consider how the service handles attachments, hashtags, and mentions'
      ],
      goal: {
        components: ['server', 'database'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'database', 'apigateway', 'frontend', 'composite']
    },
    {
      id: 'add-timeline-service',
      title: 'Step 6: Add Timeline Service',
      description: 'Let\'s add the Timeline Service to generate user timelines.\n\nThe Timeline Service assembles tweets for display in users\' home timelines, combining tweets from followed accounts.\n\nTwitter uses a hybrid approach for timeline generation, pre-computing timelines for most users while generating them on-demand for power users who follow thousands of accounts.',
      explanation: 'Timeline generation is one of the most computationally intensive operations in Twitter\'s architecture. For each user request, the system must gather tweets from all followed accounts, merge them chronologically, and apply ranking algorithms. Twitter uses a combination of pre-computation and real-time generation to balance performance and freshness. The service also implements sophisticated caching strategies to reduce database load.',
      hints: [
        'Add a Server component and name it "Timeline Service"',
        'Connect the API Gateway to the Timeline Service',
        'Connect the Timeline Service to the Tweet Database',
        'Add a Cache component for storing pre-computed timelines',
        'Connect the Timeline Service to the Cache',
        'Set properties like cache TTL and timeline length',
        'Consider how the service balances between freshness and performance'
      ],
      goal: {
        components: ['server', 'cache'],
        connections: [
          ['apigateway', 'server'],
          ['server', 'database'],
          ['server', 'cache']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'cache', 'apigateway', 'frontend', 'composite', 'database']
    },
    {
      id: 'implement-fanout-service',
      title: 'Step 7: Implement Fan-out Service with Queue',
      description: 'Now, let\'s implement the Fan-out Service to deliver tweets to followers.\n\nThe Fan-out Service is responsible for the complex task of delivering new tweets to all followers\' timelines.\n\nTwitter\'s fan-out mechanism must handle the asymmetric nature of the social graph, where some users (celebrities) have millions of followers, creating massive fan-out operations for each tweet.',
      explanation: 'Fan-out is one of the most challenging aspects of Twitter\'s architecture. When a user with millions of followers tweets, the system must update millions of timelines quickly. Twitter uses a combination of push and pull models: for users with few followers, tweets are immediately pushed to followers\' timelines; for users with massive follower counts, tweets are pulled when followers request their timelines. This hybrid approach balances write and read efficiency.',
      hints: [
        'Add a Server component and name it "Fan-out Service"',
        'Connect the Tweet Service to the Fan-out Service',
        'Add a Queue component to buffer fan-out tasks',
        'Connect the Fan-out Service to the Queue',
        'Connect the Queue to the Timeline Service',
        'Set properties like queue capacity and processing priority',
        'Consider how the service handles high-follower accounts differently'
      ],
      goal: {
        components: ['server', 'queue'],
        connections: [
          ['server', 'queue'],
          ['queue', 'server']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'queue', 'apigateway', 'frontend', 'composite', 'database', 'cache']
    },
    {
      id: 'add-search-service',
      title: 'Step 8: Add Search Service',
      description: 'Let\'s add a Search Service to enable tweet discovery.\n\nThe Search Service indexes tweets and provides real-time search capabilities across the entire tweet corpus.\n\nTwitter\'s search system indexes hundreds of billions of tweets and must support complex queries with sub-second response times.',
      explanation: 'Twitter\'s search functionality requires specialized indexing and retrieval systems optimized for real-time content. Unlike traditional search engines that batch-process content, Twitter\'s search must index new tweets within seconds of creation. The system uses inverted indices, in-memory caching, and distributed search clusters to achieve high performance. It also supports complex query operators, filters, and relevance ranking.',
      hints: [
        'Add a Composite component and name it "Search Service"',
        'Inside the composite, add a Server component for search logic',
        'Add a specialized Database component for the search index',
        'Connect the API Gateway to the Search Service',
        'Connect the Tweet Service to the Search Service for indexing new tweets',
        'Set properties like indexing latency and query capabilities',
        'Consider how the service handles trending topics and real-time indexing'
      ],
      goal: {
        components: ['composite'],
        connections: [
          ['apigateway', 'composite'],
          ['server', 'composite']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'server', 'database', 'apigateway', 'frontend', 'queue', 'cache']
    },
    {
      id: 'configure-stress-test',
      title: 'Step 9: Configure & Stress Test Your System',
      description: 'Let\'s test how our system performs under load.\n\nWe\'ll configure performance parameters for our services and run a simulation to identify bottlenecks.\n\nTwitter experiences massive traffic spikes during major events, with tweet rates increasing by 10-100x normal volumes.',
      explanation: 'Performance testing is critical for social media platforms that experience extreme traffic variations. During major events like elections or sports finals, Twitter\'s traffic can spike dramatically. The system must be designed to handle these peaks without degradation. By setting capacity limits on components and simulating high traffic, we can identify bottlenecks and failure points before they affect users in production.',
      hints: [
        'Set a custom property named "maxQPS" on the Tweet Service (try values between 5-20)',
        'Set a custom property named "maxQPS" on the Timeline Service (try values between 10-30)',
        'Configure simulation for 25 QPS to simulate a traffic spike',
        'Watch the real-time graph showing latency and throughput',
        'Notice which components become bottlenecks first',
        'Try different QPS settings to find the optimal balance for your architecture'
      ],
      goal: {
        components: ['server', 'composite'],
        connections: [],
        simulation: {
          required: true,
          minQPS: 15,
          maxQPS: 25,
          minDuration: 10,
          maxDuration: 20,
          payload: {
            userId: "user123",
            action: "post_tweet"
          },
          customProperties: [
            { componentType: 'server', key: 'maxQPS', value: '10' },
            { componentType: 'composite', key: 'maxQPS', value: '15' }
          ]
        }
      },
      completed: false,
      filterComponents: ['server', 'composite', 'apigateway', 'frontend']
    },
    {
      id: 'add-caching-layer',
      title: 'Step 10: Add Caching Layer',
      description: 'Let\'s improve performance by adding a distributed caching layer.\n\nA robust caching strategy is essential for Twitter\'s read-heavy workload, reducing database load and improving response times.\n\nTwitter uses multiple layers of caching, from CDNs for static content to in-memory caches for frequently accessed data like timelines and user profiles.',
      explanation: 'Caching is a critical optimization for social platforms with high read-to-write ratios. Twitter implements a multi-tiered caching strategy: edge caches for static assets, distributed in-memory caches for timelines and user data, and database query caches. Each cache layer reduces load on downstream systems and improves response times. The caching strategy must balance freshness (showing the latest content) with efficiency (reducing backend load).',
      hints: [
        'Add Cache components for frequently accessed data',
        'Connect the Timeline Service to a dedicated Timeline Cache',
        'Connect the Authentication Service to a User Profile Cache',
        'Set appropriate TTL values based on data update frequency',
        'Consider cache invalidation strategies when data changes',
        'Think about cache hit ratios and how they affect system performance'
      ],
      goal: {
        components: ['cache'],
        connections: [
          ['server', 'cache'],
          ['composite', 'cache']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['cache', 'server', 'composite', 'apigateway', 'frontend']
    },
    {
      id: 'add-notification-service',
      title: 'Step 11: Add Notification Service',
      description: 'Let\'s add a Notification Service to keep users engaged.\n\nThe Notification Service delivers real-time alerts for mentions, replies, likes, and other interactions.\n\nTwitter sends billions of notifications daily across multiple channels (push, email, in-app), requiring a highly scalable and reliable notification infrastructure.',
      explanation: 'Notifications are crucial for user engagement on social platforms. Twitter\'s notification system must track all user interactions, determine which ones should trigger notifications based on user preferences, and deliver those notifications across multiple channels. The system uses queues to handle delivery retries and backpressure during traffic spikes. It also implements sophisticated batching and throttling to avoid overwhelming users with too many notifications.',
      hints: [
        'Add a Server component and name it "Notification Service"',
        'Add a Queue component for notification delivery',
        'Connect relevant services (Tweet, Timeline) to the Notification Service',
        'Connect the Notification Service to the Queue',
        'Set properties like delivery channels (push, email, in-app) and batching strategy',
        'Consider how notifications are prioritized and rate-limited'
      ],
      goal: {
        components: ['server', 'queue'],
        connections: [
          ['server', 'queue'],
          ['server', 'server']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['server', 'queue', 'apigateway', 'frontend', 'composite', 'cache']
    },
    {
      id: 'implement-analytics',
      title: 'Step 12: Implement Analytics Service',
      description: 'Let\'s add an Analytics Service to process user behavior data.\n\nThe Analytics Service collects and processes data about user interactions, content performance, and system health.\n\nTwitter\'s analytics infrastructure processes petabytes of data daily to power features like trending topics, content recommendations, and business intelligence.',
      explanation: 'Analytics systems for social platforms operate at massive scale, processing every user interaction. Twitter uses a combination of real-time and batch processing: real-time analytics for features like trending topics and engagement metrics, and batch processing for deeper insights and machine learning. The analytics pipeline must be decoupled from the main application flow to avoid impacting user experience, typically using event streaming platforms like Kafka.',
      hints: [
        'Add a Composite component and name it "Analytics Service"',
        'Inside the composite, add a Queue for event streaming',
        'Add a Server for processing and a Database for storage',
        'Connect relevant services to the Analytics Service',
        'Set properties like data retention policies and processing latency',
        'Consider how real-time and batch analytics differ in implementation'
      ],
      goal: {
        components: ['composite'],
        connections: [
          ['server', 'composite'],
          ['composite', 'database']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['composite', 'server', 'database', 'queue', 'apigateway', 'frontend', 'cache']
    },
    {
      id: 'add-content-delivery-network',
      title: 'Step 13: Add Content Delivery Network',
      description: 'Let\'s add a CDN to improve media delivery and reduce latency.\n\nA Content Delivery Network caches static assets and media content at edge locations worldwide.\n\nTwitter uses CDNs to deliver images, videos, and static assets quickly to users around the globe, significantly reducing load times and bandwidth costs.',
      explanation: 'CDNs are essential for global platforms serving media content. By caching static assets (images, videos, JavaScript, CSS) at edge locations close to users, CDNs dramatically reduce latency and backbone traffic. Twitter\'s CDN strategy includes multiple providers for redundancy and specialized handling for different content types. The CDN layer also provides protection against DDoS attacks by absorbing traffic at the edge.',
      hints: [
        'Add a CDN component to the canvas',
        'Connect the Frontend to the CDN',
        'Connect the Tweet Service to the CDN for media storage',
        'Set properties like cache TTL and geographic distribution',
        'Consider how content is invalidated when updated',
        'Think about how the CDN handles different types of media (images vs. videos)'
      ],
      goal: {
        components: ['cdn'],
        connections: [
          ['frontend', 'cdn'],
          ['server', 'cdn']
        ],
        simulation: { required: false }
      },
      completed: false,
      filterComponents: ['cdn', 'server', 'frontend', 'apigateway', 'composite', 'cache']
    },
    {
      id: 'final-simulation',
      title: 'Step 14: Final System Test',
      description: 'Now let\'s test your complete Twitter architecture under load.\n\nRun the simulation to see how your system handles high traffic and component failures.\n\nTwitter\'s production systems undergo constant load testing and chaos engineering to ensure reliability during major events when millions of users interact simultaneously.',
      explanation: 'Final system testing validates that all components work together effectively under stress. The simulation will show a comprehensive dashboard of system performance metrics including throughput, latency, error rates, and component health. You\'ll also see how your architecture handles random component failures, simulating real-world outages and testing the resilience of your design.',
      hints: [
        'Click the "Run Simulation" button',
        'Watch the real-time dashboard showing system performance metrics',
        'Notice how requests flow through your system',
        'See how caches and queues help manage load spikes',
        'Observe how the CDN reduces load on your backend services',
        'Try enabling "Chaos Testing" to randomly fail components and test system resilience',
        'Look for bottlenecks in your system and consider how you might address them'
      ],
      goal: {
        components: ['compositeNode', 'server', 'queue', 'database', 'cdn', 'cache'],
        connections: [],
        simulation: {
          required: true,
          customProperties: []
        }
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'cdn', 'composite', 'cache', 'loadbalancer', 'client', 'apigateway']
    },
    {
      id: 'export-system',
      title: 'Step 15: Export Your System & Review',
      description: 'Congratulations on building a complete Twitter-like architecture!\n\nYou\'ve created a scalable system that can handle millions of tweets, deliver them to followers in real-time, and provide a responsive user experience.\n\nExport your system design as a PDF or image to share with others, and review how your design compares to Twitter\'s actual architecture.',
      explanation: 'Exporting your system design allows you to share your architecture with teammates, stakeholders, or include it in documentation. Your design incorporates key principles used by Twitter\'s actual system: service-oriented architecture, hybrid fan-out model, multi-tiered caching, and real-time processing. The real Twitter system adds many more components for features like direct messaging, Spaces, and Lists, plus extensive monitoring and security systems.',
      hints: [
        'Click the "Export" button in the toolbar',
        'Choose your preferred export format',
        'Save the file to your computer',
        'Review the "Architecture Review" to see automated feedback on your design',
        'Compare your design to the reference architecture',
        'Consider how you might extend this design to support additional features like direct messaging or Spaces',
        'Think about what monitoring and security components you might add in a production environment'
      ],
      goal: {
        components: [],
        connections: [],
        simulation: { required: false },
        exportRequired: true
      },
      completed: false,
      filterComponents: ['frontend', 'server', 'database', 'queue', 'cdn', 'composite', 'cache', 'loadbalancer', 'client', 'apigateway']
    }
  ]
};
