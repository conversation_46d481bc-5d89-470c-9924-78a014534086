// Sample solutions for system design questions
// Each solution is designed to score 9-10 points

export interface SampleSolution {
  questionId: number;
  title: string;
  description: string;
  components: ComponentData[];
  connections: ConnectionData[];
  context: {
    CUJs: string;
    assumptions: string;
    constraints: string;
  };
  expectedScore: number;
  keyFeatures: string[];
}

export interface ComponentData {
  id: string;
  type: string;
  subtype: string;
  label: string;
  metadata: Record<string, any>;
  position?: { x: number; y: number };
  children?: {
    components: ComponentData[];
    connections: ConnectionData[];
  };
}

export interface ConnectionData {
  source: string;
  target: string;
  connectionNumber: string;
  label?: string;
}

export const urlShortenerSampleSolution: SampleSolution = {
  questionId: 1,
  title: "URL Shortener - Production-Grade Architecture",
  description: "A comprehensive URL shortener design that handles 10M+ URLs per day with sub-100ms redirect latency, featuring distributed architecture, caching, analytics, and high availability.",
  expectedScore: 10,
  keyFeatures: [
    "Multi-tier caching strategy (CDN + Redis + Application cache)",
    "Distributed database with sharding for horizontal scalability", 
    "Separate read/write services for optimal performance",
    "Real-time analytics pipeline with event streaming",
    "Global load balancing with geographic distribution",
    "Comprehensive monitoring and alerting",
    "Auto-scaling and fault tolerance mechanisms"
  ],
  components: [
    {
      id: "cdn",
      type: "primitive",
      subtype: "cdn",
      label: "Global CDN",
      position: { x: 100, y: 50 },
      metadata: {
        purpose: "Global content delivery and edge caching",
        provider: "CloudFlare",
        features: [
          "Geographic distribution across 200+ locations",
          "Edge caching for popular short URLs",
          "DDoS protection and security filtering",
          "SSL termination and HTTP/2 support"
        ],
        cache_strategy: {
          ttl: "24 hours for popular URLs",
          invalidation: "Real-time via API",
          hit_ratio_target: "85%+"
        },
        performance: {
          global_latency: "< 50ms",
          bandwidth: "100+ Gbps per edge location"
        }
      }
    },
    {
      id: "load_balancer",
      type: "primitive", 
      subtype: "load_balancer",
      label: "Global Load Balancer",
      position: { x: 300, y: 50 },
      metadata: {
        purpose: "Distribute traffic across multiple regions and data centers",
        algorithm: "Geographic routing with health-based failover",
        health_checks: {
          endpoint: "/health",
          interval: "5s",
          timeout: "2s",
          failure_threshold: 3
        },
        features: [
          "DNS-based geographic routing",
          "Automatic failover between regions",
          "Traffic splitting for A/B testing",
          "Rate limiting and DDoS protection"
        ],
        capacity: "1M+ requests per second"
      }
    },
    {
      id: "api_gateway",
      type: "primitive",
      subtype: "api_gateway", 
      label: "API Gateway",
      position: { x: 500, y: 50 },
      metadata: {
        purpose: "API management, authentication, and request routing",
        features: [
          "Request authentication and authorization",
          "Rate limiting per user/IP (1000 req/hour free tier)",
          "Request/response transformation",
          "API versioning and backward compatibility",
          "Request logging and monitoring"
        ],
        rate_limiting: {
          free_tier: "1000 requests/hour",
          premium_tier: "100000 requests/hour",
          burst_capacity: "10x normal rate for 1 minute"
        },
        authentication: [
          "API key authentication",
          "OAuth 2.0 integration", 
          "JWT token validation"
        ]
      }
    },
    {
      id: "url_creation_service",
      type: "primitive",
      subtype: "application_server",
      label: "URL Creation Service",
      position: { x: 200, y: 200 },
      metadata: {
        purpose: "Handle URL shortening requests and generate unique short codes",
        language: "Go",
        scaling: "Horizontal with auto-scaling",
        logic: [
          "Validate and normalize input URLs",
          "Generate unique short codes using Base62 encoding",
          "Handle collision detection and resolution",
          "Support custom aliases with availability checking",
          "Implement rate limiting per user"
        ],
        short_code_generation: {
          algorithm: "Counter-based with Base62 encoding",
          length: "6-8 characters (a-z, A-Z, 0-9)",
          collision_handling: "Database uniqueness constraint + retry",
          custom_aliases: "User-defined with availability check"
        },
        api_endpoints: [
          "POST /api/v1/shorten - Create short URL",
          "POST /api/v1/custom - Create custom short URL",
          "GET /api/v1/urls/{userId} - List user's URLs",
          "DELETE /api/v1/urls/{shortCode} - Delete URL"
        ],
        performance: {
          target_latency: "< 200ms",
          throughput: "10000 requests/second per instance",
          auto_scaling: "CPU > 70% or Queue depth > 100"
        }
      }
    },
    {
      id: "redirect_service",
      type: "primitive",
      subtype: "application_server",
      label: "Redirect Service",
      position: { x: 600, y: 200 },
      metadata: {
        purpose: "Handle redirect requests with minimal latency",
        language: "Rust",
        scaling: "Horizontal with aggressive auto-scaling",
        logic: [
          "Extract short code from request URL",
          "Check multi-tier cache hierarchy",
          "Query database only on cache miss",
          "Return HTTP 301/302 redirect response",
          "Emit analytics events asynchronously"
        ],
        caching_strategy: {
          l1_cache: "In-memory LRU cache (10K most popular URLs)",
          l2_cache: "Redis cluster lookup",
          l3_cache: "Database with read replicas",
          cache_warming: "Preload trending URLs"
        },
        api_endpoints: [
          "GET /{shortCode} - Redirect to original URL",
          "GET /api/v1/resolve/{shortCode} - Get URL without redirect"
        ],
        performance: {
          target_latency: "< 50ms (cache hit), < 100ms (cache miss)",
          throughput: "50000 requests/second per instance",
          cache_hit_ratio: "95%+"
        }
      }
    },
    {
      id: "redis_cluster",
      type: "primitive",
      subtype: "cache",
      label: "Redis Cluster",
      position: { x: 400, y: 350 },
      metadata: {
        purpose: "High-performance distributed caching for URL mappings",
        engine: "Redis Cluster",
        scaling: "Sharded across multiple nodes",
        config: {
          nodes: "6 nodes (3 masters, 3 replicas)",
          memory_per_node: "32GB",
          eviction_policy: "allkeys-lru",
          persistence: "RDB snapshots + AOF logging"
        },
        data_structure: {
          key_pattern: "url:{shortCode}",
          value_format: "JSON with original_url, created_at, click_count",
          ttl: "7 days for regular URLs, 30 days for popular URLs"
        },
        performance: {
          latency: "< 1ms",
          throughput: "100K+ ops/second per node",
          memory_efficiency: "Compression enabled"
        },
        monitoring: [
          "Memory usage and eviction rates",
          "Cache hit/miss ratios",
          "Replication lag",
          "Cluster health and failover events"
        ]
      }
    },
    {
      id: "url_database",
      type: "primitive",
      subtype: "nosql_db",
      label: "URL Database (Cassandra)",
      position: { x: 200, y: 500 },
      metadata: {
        purpose: "Persistent storage for URL mappings with horizontal scalability",
        engine: "Apache Cassandra",
        scaling: "Distributed across multiple data centers",
        schema: {
          url_mappings: {
            short_code: "text PRIMARY KEY",
            original_url: "text",
            user_id: "uuid",
            created_at: "timestamp",
            expires_at: "timestamp",
            click_count: "counter",
            is_custom: "boolean",
            status: "text"
          },
          user_urls: {
            user_id: "uuid",
            created_at: "timestamp",
            short_code: "text",
            "PRIMARY KEY": "(user_id, created_at)"
          }
        },
        partitioning: {
          strategy: "Hash partitioning on short_code",
          replication_factor: 3,
          consistency_level: "QUORUM for writes, ONE for reads"
        },
        performance: {
          write_latency: "< 10ms",
          read_latency: "< 5ms",
          throughput: "100K+ writes/second, 1M+ reads/second"
        }
      }
    },
    {
      id: "analytics_database",
      type: "primitive",
      subtype: "time_series_db",
      label: "Analytics Database",
      position: { x: 600, y: 500 },
      metadata: {
        purpose: "Store and analyze click analytics and usage patterns",
        engine: "InfluxDB",
        scaling: "Time-based sharding with retention policies",
        schema: {
          click_events: {
            measurement: "url_clicks",
            tags: ["short_code", "country", "device_type", "referrer_domain"],
            fields: ["click_count", "response_time", "user_agent"],
            timestamp: "nanosecond precision"
          },
          aggregated_stats: {
            measurement: "url_stats_hourly",
            tags: ["short_code", "hour"],
            fields: ["total_clicks", "unique_visitors", "avg_response_time"]
          }
        },
        retention_policy: {
          raw_events: "30 days",
          hourly_aggregates: "1 year",
          daily_aggregates: "5 years"
        },
        performance: {
          ingestion_rate: "1M+ events/second",
          query_latency: "< 100ms for dashboard queries",
          compression_ratio: "10:1"
        }
      }
    },
    {
      id: "event_stream",
      type: "primitive",
      subtype: "message_queue",
      label: "Event Stream (Kafka)",
      position: { x: 800, y: 350 },
      metadata: {
        purpose: "Real-time event streaming for analytics and monitoring",
        engine: "Apache Kafka",
        scaling: "Multi-partition topics with replication",
        topics: [
          "url-clicks: Click events from redirect service",
          "url-created: New URL creation events",
          "url-analytics: Processed analytics data",
          "system-metrics: Performance and health metrics"
        ],
        configuration: {
          partitions: "12 per topic for parallelism",
          replication_factor: 3,
          retention: "7 days for click events, 30 days for metrics"
        },
        performance: {
          throughput: "1M+ messages/second",
          latency: "< 10ms end-to-end",
          durability: "Replicated across 3 brokers"
        }
      }
    },
    {
      id: "analytics_service",
      type: "primitive",
      subtype: "stream_processor",
      label: "Analytics Service",
      position: { x: 800, y: 200 },
      metadata: {
        purpose: "Process click events and generate real-time analytics",
        language: "Scala with Apache Flink",
        scaling: "Horizontal with dynamic parallelism",
        logic: [
          "Consume click events from Kafka stream",
          "Enrich events with geographic and device data",
          "Aggregate metrics in real-time windows",
          "Detect anomalies and fraud patterns",
          "Store processed data in analytics database"
        ],
        stream_processing: {
          framework: "Apache Flink",
          windowing: "Tumbling windows (1 minute, 1 hour, 1 day)",
          state_backend: "RocksDB for fault tolerance",
          checkpointing: "Every 30 seconds"
        },
        metrics_generated: [
          "Click counts per URL/hour/day",
          "Geographic distribution of clicks",
          "Device and browser analytics",
          "Referrer domain analysis",
          "Response time percentiles"
        ],
        performance: {
          processing_latency: "< 5 seconds",
          throughput: "500K+ events/second",
          fault_tolerance: "Exactly-once processing guarantees"
        }
      }
    },
    {
      id: "monitoring_system",
      type: "primitive",
      subtype: "monitoring",
      label: "Monitoring & Alerting",
      position: { x: 1000, y: 200 },
      metadata: {
        purpose: "Comprehensive system monitoring, alerting, and observability",
        stack: "Prometheus + Grafana + AlertManager",
        metrics_collected: [
          "Request latency percentiles (p50, p95, p99)",
          "Throughput (requests/second per service)",
          "Error rates and status code distribution",
          "Cache hit ratios and performance",
          "Database connection pools and query performance",
          "System resources (CPU, memory, disk, network)"
        ],
        alerting_rules: [
          "Redirect latency > 100ms for 5 minutes",
          "Error rate > 1% for 2 minutes",
          "Cache hit ratio < 90% for 10 minutes",
          "Database connection pool > 80% for 5 minutes",
          "Any service instance down for 1 minute"
        ],
        dashboards: [
          "Real-time system overview",
          "Service-specific performance metrics",
          "Business metrics (URLs created, clicks served)",
          "Infrastructure health and capacity"
        ],
        log_aggregation: {
          system: "ELK Stack (Elasticsearch, Logstash, Kibana)",
          retention: "30 days for application logs, 7 days for access logs",
          indexing: "Time-based indices with automated rotation"
        }
      }
    }
  ],
  connections: [
    { source: "cdn", target: "load_balancer", connectionNumber: "1", label: "HTTPS Traffic" },
    { source: "load_balancer", target: "api_gateway", connectionNumber: "2", label: "Load Balanced Requests" },
    { source: "api_gateway", target: "url_creation_service", connectionNumber: "3", label: "URL Creation API" },
    { source: "api_gateway", target: "redirect_service", connectionNumber: "4", label: "Redirect Requests" },
    { source: "url_creation_service", target: "redis_cluster", connectionNumber: "5", label: "Cache New URLs" },
    { source: "url_creation_service", target: "url_database", connectionNumber: "6", label: "Store URL Mappings" },
    { source: "redirect_service", target: "redis_cluster", connectionNumber: "7", label: "Cache Lookup" },
    { source: "redirect_service", target: "url_database", connectionNumber: "8", label: "Database Fallback" },
    { source: "redirect_service", target: "event_stream", connectionNumber: "9", label: "Click Events" },
    { source: "event_stream", target: "analytics_service", connectionNumber: "10", label: "Event Stream" },
    { source: "analytics_service", target: "analytics_database", connectionNumber: "11", label: "Store Analytics" },
    { source: "analytics_service", target: "monitoring_system", connectionNumber: "12", label: "Metrics & Alerts" },
    { source: "url_creation_service", target: "monitoring_system", connectionNumber: "13", label: "Service Metrics" },
    { source: "redirect_service", target: "monitoring_system", connectionNumber: "14", label: "Performance Metrics" },
    { source: "redis_cluster", target: "monitoring_system", connectionNumber: "15", label: "Cache Metrics" },
    { source: "url_database", target: "monitoring_system", connectionNumber: "16", label: "Database Metrics" }
  ],
  context: {
    CUJs: `Core User Journeys:

1. **URL Shortening Journey**:
   - User submits long URL via web interface or API
   - System validates URL format and accessibility
   - Generates unique 6-8 character short code using Base62 encoding
   - Stores mapping in distributed database with replication
   - Returns shortened URL (e.g., short.ly/abc123)
   - User receives shortened URL within 200ms

2. **URL Redirect Journey**:
   - User clicks shortened URL in browser/app
   - CDN edge server checks local cache for mapping
   - If cache miss, request routes through load balancer to redirect service
   - Redirect service checks Redis cluster cache
   - If Redis miss, queries Cassandra database
   - Returns HTTP 301/302 redirect to original URL
   - User redirected to destination within 100ms
   - Click event sent asynchronously to analytics pipeline

3. **Analytics Dashboard Journey**:
   - User accesses analytics dashboard for their URLs
   - System queries aggregated metrics from InfluxDB
   - Displays real-time charts: clicks over time, geographic distribution, device types
   - Updates dashboard every 30 seconds with fresh data
   - Provides export functionality for detailed reports`,

    assumptions: `System Assumptions:

**Scale Requirements**:
- 10 million URL shortenings per day (116 URLs/second average, 500 URLs/second peak)
- 100:1 read-to-write ratio (1 billion redirects per day)
- 11,574 redirects/second average, 50,000 redirects/second peak
- Global user base across multiple time zones

**Data Characteristics**:
- Average URL length: 200 characters
- Short code length: 6-8 characters (Base62: 62^6 = 56B possible combinations)
- URL lifetime: 80% accessed within first week, long tail over years
- Popular URLs follow power law distribution (80/20 rule)

**User Behavior**:
- 70% of traffic from mobile devices
- 60% of clicks happen within 24 hours of creation
- Geographic distribution: 40% North America, 30% Europe, 20% Asia, 10% other
- Peak traffic during business hours in respective time zones

**Business Requirements**:
- Free tier: 1000 URLs/month, basic analytics
- Premium tier: Unlimited URLs, custom domains, advanced analytics
- Enterprise: API access, bulk operations, dedicated support`,

    constraints: `Technical Constraints & Design Decisions:

**Short Code Generation**:
- Use Base62 encoding (a-z, A-Z, 0-9) for URL-safe characters
- Counter-based approach with multiple counter ranges per service instance
- Database uniqueness constraints to handle rare collisions
- Custom aliases checked for availability before assignment

**Caching Strategy**:
- Multi-tier caching: CDN (edge) → Redis (application) → Database
- Cache popular URLs (top 20%) with longer TTL (30 days)
- Cache warming for trending URLs during peak hours
- Intelligent cache eviction based on access patterns

**Database Design**:
- Cassandra for horizontal scalability and multi-datacenter replication
- Partition key: short_code for even distribution
- Replication factor: 3 with QUORUM writes, ONE reads
- Separate table for user-to-URLs mapping for dashboard queries

**Performance Optimization**:
- Separate read/write services for optimal resource allocation
- Connection pooling and prepared statements for database efficiency
- Asynchronous analytics processing to avoid blocking redirect path
- Geographic load balancing to minimize latency

**Reliability & Monitoring**:
- Health checks every 5 seconds with automatic failover
- Circuit breakers to prevent cascade failures
- Comprehensive monitoring with sub-minute alerting
- Graceful degradation: serve from cache even if database is down`
  }
};

export const sampleSolutions: SampleSolution[] = [
  urlShortenerSampleSolution
];
