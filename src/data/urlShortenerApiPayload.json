{"design": {"problem": "Design a service that converts long URLs into short, unique aliases (e.g., tinyurl.com/abc123) and redirects users to the original URL when accessed.", "system_name": "URL Shortener", "components": [{"id": "cdn-global-1", "type": "primitive", "subtype": "cdn", "label": "Global CDN", "metadata": {"provider": "CloudFlare", "locations": "200+ edge locations globally", "cacheStrategy": "24h TTL for popular URLs, real-time invalidation", "performance": "< 50ms global latency, 100+ Gbps per edge", "features": "DDoS protection, SSL termination, HTTP/2", "hitRatioTarget": "85%+", "bandwidth": "100+ Gbps per edge", "securityFeatures": "DDoS protection, WAF, Bot management"}}, {"id": "dns-failover-1", "type": "primitive", "subtype": "dns", "label": "DNS Failover Manager", "metadata": {"provider": "AWS Route 53 with health checks", "healthCheckInterval": "30 seconds", "failoverTime": "< 60 seconds DNS propagation", "geolocation": "Geographic routing with latency-based failover", "ttl": "60 seconds for fast failover", "monitoring": "DNS query success rates, health check status"}}, {"id": "load-balancer-global-1", "type": "primitive", "subtype": "load_balancer", "label": "Global Load Balancer #1", "metadata": {"algorithm": "Geographic + Health-based", "healthCheckInterval": "5 seconds", "failoverTime": "< 30 seconds", "capacity": "1M+ RPS", "instanceRole": "Primary load balancer", "redundancy": "Active-passive with DNS failover"}}, {"id": "load-balancer-global-2", "type": "primitive", "subtype": "load_balancer", "label": "Global Load Balancer #2", "metadata": {"algorithm": "Geographic + Health-based", "healthCheckInterval": "5 seconds", "failoverTime": "< 30 seconds", "capacity": "1M+ RPS", "instanceRole": "Secondary load balancer", "redundancy": "Active-passive with DNS failover"}}, {"id": "api-gateway-1", "type": "primitive", "subtype": "api_gateway", "label": "API Gateway #1", "metadata": {"rateLimitFree": "1000 requests/hour", "rateLimitPremium": "100000 requests/hour", "authMethods": "API Key, OAuth 2.0, JWT", "burstCapacity": "10x normal rate", "failover": "Active-active with health checks", "loadBalancing": "Round-robin with sticky sessions"}}, {"id": "api-gateway-2", "type": "primitive", "subtype": "api_gateway", "label": "API Gateway #2", "metadata": {"rateLimitFree": "1000 requests/hour", "rateLimitPremium": "100000 requests/hour", "authMethods": "API Key, OAuth 2.0, JWT", "burstCapacity": "10x normal rate", "instanceRole": "Secondary gateway for load balancing", "failover": "Active-active with health checks"}}, {"id": "service-mesh-1", "type": "primitive", "subtype": "application_server", "label": "Service Mesh (Istio)", "metadata": {"framework": "<PERSON><PERSON><PERSON> with Envoy sidecars", "trafficManagement": "Load balancing, circuit breakers, retries, timeouts", "security": "mTLS between services, policy enforcement, certificate management", "observability": "Distributed tracing, metrics collection, access logs", "faultTolerance": "Circuit breakers, bulkhead isolation, retry policies", "debugging": "Request tracing, service topology visualization, traffic mirroring"}}, {"id": "url-creation-service-1", "type": "primitive", "subtype": "application_server", "label": "URL Creation Service #1", "metadata": {"language": "Go", "targetLatency": "< 200ms", "throughput": "10K requests/sec per instance", "autoScaling": "CPU > 70% or Queue > 100", "shortCodeAlgorithm": "Counter-based Base62", "instances": "5 instances across 3 regions", "minInstances": "3", "maxInstances": "50", "serviceMonitoring": "Prometheus metrics: /metrics endpoint, custom counters for URL creation rate, error rate, latency histograms", "alerting": "Service-specific alerts: Creation latency > 200ms, Error rate > 0.5%, Queue depth > 1000", "security": "TLS 1.3 for all connections, JWT token validation, rate limiting per API key"}}, {"id": "url-creation-service-2", "type": "primitive", "subtype": "application_server", "label": "URL Creation Service #2", "metadata": {"language": "Go", "targetLatency": "< 200ms", "throughput": "10K requests/sec per instance", "autoScaling": "CPU > 70% or Queue > 100", "shortCodeAlgorithm": "Counter-based Base62", "instanceRole": "Secondary instance for load balancing", "serviceMonitoring": "Prometheus metrics with custom counters", "security": "TLS 1.3 for all connections, JWT token validation"}}, {"id": "redirect-service-1", "type": "primitive", "subtype": "application_server", "label": "Redirect Service #1", "metadata": {"language": "Rust", "cacheHitLatency": "< 50ms", "cacheMissLatency": "< 100ms", "throughput": "50K requests/sec per instance", "cacheHitRatio": "95%+", "instances": "10 instances across 3 regions", "minInstances": "5", "maxInstances": "100", "cachingStrategy": "L1: Local in-memory cache (10K URLs), L2: Primary Redis cluster, L3: Secondary Redis cluster, L4: Database", "resilience": "Circuit breaker for Redis, fallback to local cache then database, graceful degradation", "cacheFailover": "Primary Redis → Secondary Redis → Local Cache → Database fallback chain"}}], "connections": [{"id": "1", "start": "cdn-global-1", "end": "dns-failover-1", "label": "DNS Resolution"}, {"id": "2", "start": "dns-failover-1", "end": "load-balancer-global-1", "label": "Primary DNS Route"}, {"id": "3", "start": "dns-failover-1", "end": "load-balancer-global-2", "label": "Failover DNS Route"}, {"id": "4", "start": "load-balancer-global-1", "end": "api-gateway-1", "label": "Load Balanced Requests"}, {"id": "5", "start": "load-balancer-global-1", "end": "api-gateway-2", "label": "Load Balanced Requests"}, {"id": "6", "start": "load-balancer-global-2", "end": "api-gateway-1", "label": "Load Balanced Requests (LB2)"}, {"id": "7", "start": "load-balancer-global-2", "end": "api-gateway-2", "label": "Load Balanced Requests (LB2)"}, {"id": "8", "start": "service-mesh-1", "end": "url-creation-service-1", "label": "Service Mesh Management"}, {"id": "9", "start": "service-mesh-1", "end": "redirect-service-1", "label": "Service Mesh Management"}, {"id": "10", "start": "api-gateway-1", "end": "url-creation-service-1", "label": "URL Creation API"}, {"id": "11", "start": "api-gateway-1", "end": "redirect-service-1", "label": "Redirect Requests"}, {"id": "12", "start": "api-gateway-2", "end": "url-creation-service-2", "label": "URL Creation API (Gateway 2)"}], "core_user_journey": "1. URL Shortening Journey: User submits long URL via web interface → System validates URL format → Generates unique 6-8 character Base62 short code → Stores mapping in Cassandra with replication → Returns shortened URL within 200ms. 2. URL Redirect Journey: User clicks shortened URL → CDN edge server checks cache → DNS failover routes to available load balancer → Load balancer routes to API gateway → Service mesh manages communication to redirect service → Service checks local cache, then Redis clusters, then database → Returns HTTP 301 redirect within 100ms → Analytics event sent to Kafka. 3. Analytics Journey: User views dashboard → Queries aggregated metrics from InfluxDB → Real-time charts updated every 30 seconds.", "functional_requirements": ["Generate unique short URLs (6-8 characters) using Base62 encoding with collision handling and custom alias support", "Redirect users from short URLs to original URLs with < 100ms latency through 4-tier caching strategy", "Track comprehensive analytics including click count, geographic data, device types, and referrer domains with real-time Kafka stream processing", "Support custom short URLs with availability checking and user-defined aliases", "Provide user dashboard for URL management and analytics visualization"], "non_functional_requirements": ["Handle 10M+ URL shortens per day (500 URLs/second peak) with horizontal auto-scaling across multiple regions", "Achieve redirect latency < 100ms through multi-tier caching (CDN + Redis clusters + local cache + database)", "Maintain 99.9% availability with zero single points of failure (dual load balancers, API gateways, Redis clusters)", "Ensure data security with TLS 1.3 encryption, AES-256 at rest, mTLS between services via service mesh", "Provide comprehensive observability with service-level monitoring, distributed tracing, and sub-minute alerting"]}}