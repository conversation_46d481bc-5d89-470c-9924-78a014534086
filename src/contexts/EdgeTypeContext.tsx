import React, { createContext, useContext, useState, ReactNode } from 'react';

export type PathType = 'bezier' | 'smooth';
export type VisualStyle = 'normal' | 'dashed' | 'dotted' | 'thick' | 'doubleArrow';

export interface EdgeTypeConfig {
  pathType: PathType;
  visualStyle: VisualStyle;
}

interface EdgeTypeContextType {
  selectedPathType: PathType;
  selectedVisualStyle: VisualStyle;
  setSelectedPathType: (type: PathType) => void;
  setSelectedVisualStyle: (style: VisualStyle) => void;
  getEdgeType: () => string; // Returns combined type like 'dashed-bezier'
}

const EdgeTypeContext = createContext<EdgeTypeContextType | undefined>(undefined);

interface EdgeTypeProviderProps {
  children: ReactNode;
}

export const EdgeTypeProvider: React.FC<EdgeTypeProviderProps> = ({ children }) => {
  const [selectedPathType, setSelectedPathType] = useState<PathType>('smooth');
  const [selectedVisualStyle, setSelectedVisualStyle] = useState<VisualStyle>('normal');

  const getEdgeType = (): string => {
    if (selectedVisualStyle === 'normal') {
      return selectedPathType;
    }
    return `${selectedVisualStyle}-${selectedPathType}`;
  };

  return (
    <EdgeTypeContext.Provider value={{ 
      selectedPathType, 
      selectedVisualStyle, 
      setSelectedPathType, 
      setSelectedVisualStyle,
      getEdgeType
    }}>
      {children}
    </EdgeTypeContext.Provider>
  );
};

export const useEdgeType = (): EdgeTypeContextType => {
  const context = useContext(EdgeTypeContext);
  if (context === undefined) {
    throw new Error('useEdgeType must be used within an EdgeTypeProvider');
  }
  return context;
}; 