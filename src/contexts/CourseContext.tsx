
import React, { createContext, useContext } from 'react';
import { CourseStateReturn, useCourseState } from '@/hooks/useCourseState';
import { Node, Edge } from '@xyflow/react';

// Create the context with the full type
const CourseContext = createContext<CourseStateReturn | undefined>(undefined);

export const CourseProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use the custom hook to get all the state and functions
  const courseState = useCourseState();

  // If there's a CourseContext that provides the checkStepCompletion function,
  // ensure it forwards both arguments
  const checkStepCompletion = (nodes: Node[], edges: Edge[]) => {
    return courseState.checkStepCompletion(nodes, edges);
  };

  // Include in the context value
  const value = {
    ...courseState,
    checkStepCompletion,
  };

  return <CourseContext.Provider value={value}>{children}</CourseContext.Provider>;
};

export const useCourse = (): CourseStateReturn => {
  const context = useContext(CourseContext);
  if (context === undefined) {
    throw new Error('useCourse must be used within a CourseProvider');
  }
  return context;
};
