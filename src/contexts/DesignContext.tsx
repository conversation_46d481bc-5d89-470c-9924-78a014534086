import React, { createContext, useContext, useState, useEffect, useRef, useCallback } from 'react';
import { Node, Edge } from '@xyflow/react';
import { useAuth } from '@/contexts/AuthContext';
import {
  saveDesignToSupabase,
  loadDesignFromSupabase,
  listDesignsFromSupabase,
  deleteDesignFromSupabase
} from '@/services/designService';
import { useDesignSync } from '@/hooks/useDesignSync';
import { DesignConflictDialog } from '@/components/DesignConflictDialog';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Define the design context types
export type DesignContextCategory = 'question' | 'course' | 'free';

// Define the SavedDesign interface
export interface SavedDesign {
  questionId: string;
  nodes: Node[];
  edges: Edge[];
  userJourneys: string;
  assumptions: string;
  constraints: string;
  lastModified: string;
  name?: string;
  contextType: DesignContextCategory;
  contextId: string; // questionId for questions, courseId for courses, 'free' for free canvas
}

interface DesignContextType {
  currentDesign: SavedDesign | null;
  setCurrentDesign: (design: SavedDesign | null) => void;
  saveDesign: (
    questionId: string,
    data: Partial<SavedDesign>,
    contextType?: DesignContextCategory,
    contextId?: string,
    syncToSupabase?: boolean
  ) => Promise<void>;
  loadDesign: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<SavedDesign | null>;
  listDesigns: () => Promise<SavedDesign[]>;
  deleteDesign: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<void>;
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (value: boolean) => void;

  isLoading: boolean;
  forceReloadDesign: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<SavedDesign | null>;
  forceSaveToSupabase: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<void>;
  getDesignKey: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => string;

  // Design sync methods
  clearAndRepopulate: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<SavedDesign | null>;
  checkForStaleData: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string
  ) => Promise<{ isStale: boolean; localDesign?: SavedDesign; serverDesign?: SavedDesign }>;
  syncDesign: (
    questionId: string,
    contextType?: DesignContextCategory,
    contextId?: string,
    forceUpdate?: boolean
  ) => Promise<void>;
}

export const DesignContext = createContext<DesignContextType | undefined>(undefined);

export const DesignProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentDesign, setCurrentDesign] = useState<SavedDesign | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChangesState] = useState<boolean>(false);

  // Conflict resolution state
  const [conflictDialogOpen, setConflictDialogOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    local: SavedDesign;
    server: SavedDesign;
    questionId: string;
    contextType: DesignContextCategory;
    contextId?: string;
  } | null>(null);

  // Track the last time we set the needsSupabaseSync flag
  const lastSupabaseSyncFlagSetTime = useRef<number>(0);
  // Minimum time between setting the sync flag (to prevent excessive syncs)
  const MIN_SYNC_FLAG_INTERVAL = 5000; // 5 seconds

  // Conflict resolution handlers
  const handleConflictDetected = useCallback((localDesign: SavedDesign, serverDesign: SavedDesign) => {
    setConflictData({
      local: localDesign,
      server: serverDesign,
      questionId: localDesign.questionId,
      contextType: localDesign.contextType || 'question',
      contextId: localDesign.contextId
    });
    setConflictDialogOpen(true);
  }, []);

  const handleDesignUpdated = useCallback((design: SavedDesign) => {
    setCurrentDesign(design);
    setHasUnsavedChanges(false);
  }, []);

  // Initialize design sync
  const designSync = useDesignSync({
    currentQuestionId: currentDesign?.questionId,
    contextType: currentDesign?.contextType,
    contextId: currentDesign?.contextId,
    onDesignUpdated: handleDesignUpdated,
    onConflictDetected: handleConflictDetected
  });

  // Custom setter for hasUnsavedChanges that also sets needsSupabaseSync with rate limiting
  const setHasUnsavedChanges = (value: boolean) => {
    setHasUnsavedChangesState(value);
    if (value === true) {
      const now = Date.now();
      // Only set the sync flag if we haven't set it recently
      if (now - lastSupabaseSyncFlagSetTime.current > MIN_SYNC_FLAG_INTERVAL) {
        // If there are unsaved changes, mark that we need to sync to Supabase
        debugLog('Setting needsSupabaseSync flag to true (rate limited)');
        needsSupabaseSync.current = true;
        lastSupabaseSyncFlagSetTime.current = now;
      }
    }
  };
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuth();

  // Track if we need to sync to Supabase
  const needsSupabaseSync = useRef<boolean>(false);

  // Helper function to generate a consistent design key based on context
  const getDesignKey = (
    questionId: string,
    contextType: DesignContextCategory = 'question',
    contextId?: string
  ): string => {
    const id = contextId || questionId;
    return `layrs-design-${contextType}-${id}`;
  };

  // Load designs from localStorage and Supabase
  const loadDesign = useCallback(async (
    questionId: string,
    contextType: DesignContextCategory = 'question',
    contextId?: string
  ): Promise<SavedDesign | null> => {
    const id = contextId || questionId;
    const designKey = getDesignKey(questionId, contextType, contextId);
    debugLog(`Loading design for ${contextType} ${id} with key ${designKey}`);

    // First check if we already have this design in memory (current design)
    if (currentDesign &&
        currentDesign.questionId === questionId &&
        currentDesign.contextType === contextType &&
        currentDesign.contextId === id) {
      debugLog(`✅ Design already in current state for ${contextType} ${id}, returning current version`);
      return currentDesign;
    }

    // Always check localStorage first, regardless of user state
    // This ensures we load the most recent changes if the user closed and reopened the tab
    try {
      const savedDesignJson = localStorage.getItem(designKey);
      if (savedDesignJson) {
        debugLog('Design found in localStorage, loading it');
        const design = JSON.parse(savedDesignJson) as SavedDesign;

        // Ensure the design has the correct context information
        const updatedDesign = {
          ...design,
          contextType: contextType,
          contextId: id
        };

        return updatedDesign;
      }
    } catch (e) {
      debugError('Error loading from localStorage:', e);
    }

    // If no user, we're done (already checked localStorage)
    if (!user) {
      debugLog('No user found and no design in localStorage');
      // Clear current design since no design was found
      setCurrentDesign(null);
      return null;
    }

    // If we have a user and no design in localStorage, try Supabase
    try {
      setIsLoading(true);
      debugLog(`Loading design from Supabase for user ${user.id} and ${contextType} ${id}`);

      // Load from Supabase with context information
      const design = await loadDesignFromSupabase(user.id, questionId, contextType, contextId);

      if (design) {
        debugLog('Design loaded from Supabase:', design);

        // Ensure the design has the correct context information
        const updatedDesign = {
          ...design,
          contextType: contextType,
          contextId: id
        };

        // Save to localStorage for future sessions
        try {
          localStorage.setItem(designKey, JSON.stringify(updatedDesign));
          debugLog('Saved Supabase design to localStorage for future sessions');
        } catch (e) {
          debugError('Error saving Supabase design to localStorage:', e);
        }

        return updatedDesign;
      }

      // No design found in Supabase either
      debugLog('No design found in Supabase');
      // Clear current design since no design was found
      setCurrentDesign(null);
      return null;
    } catch (error) {
      debugError('Error loading design from Supabase:', error);
      // Clear current design since loading failed
      setCurrentDesign(null);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Save design to localStorage and optionally to Supabase
  const saveDesign = async (
    questionId: string,
    data: Partial<SavedDesign>,
    contextType: DesignContextCategory = 'question',
    contextId?: string,
    syncToSupabase = false,
    updateCurrentDesign = true // New parameter to control state updates
  ) => {
    // Create the design object regardless of user state
    let designToSave: SavedDesign;
    const now = new Date().toISOString();
    const id = contextId || questionId;
    const designKey = getDesignKey(questionId, contextType, contextId);

    if (currentDesign &&
        currentDesign.questionId === questionId &&
        currentDesign.contextType === contextType &&
        currentDesign.contextId === id) {
      // Updating existing design
      designToSave = {
        ...currentDesign,
        ...data,
        lastModified: now
      };
    } else {
      // Creating new design
      designToSave = {
        questionId,
        nodes: [],
        edges: [],
        userJourneys: '',
        assumptions: '',
        constraints: '',
        contextType,
        contextId: id,
        ...data,
        lastModified: now
      };
    }

    // Always save to localStorage immediately
    try {
      // Check if the design has actually changed before saving
      const existingDesignJson = localStorage.getItem(designKey);
      const newDesignJson = JSON.stringify(designToSave);

      // Only save if the design has changed or doesn't exist
      if (!existingDesignJson || existingDesignJson !== newDesignJson) {
        localStorage.setItem(designKey, newDesignJson);
        debugLog(`Design saved to localStorage with key ${designKey} (change detected)`);
      } else {
        debugLog('Skipping localStorage save - no changes detected');
      }
    } catch (e) {
      debugError('Error saving to localStorage:', e);
    }

    // Only update current design state if requested (avoid race conditions during auto-save)
    if (updateCurrentDesign) {
      setCurrentDesign(designToSave);
      setHasUnsavedChanges(false);
    }

    // If no user, we're done (localStorage only)
    if (!user) {
      debugLog('No user found, saved to localStorage only');
      return;
    }

    // Only sync to Supabase if explicitly requested
    if (!syncToSupabase) {
      return;
    }

    // If we have a user and should sync, save to Supabase
    try {
      setIsLoading(true);
      debugLog(`Syncing design to Supabase for ${contextType} ${id} and user ${user.id}`);

      // Ensure the design has the correct context information
      const designWithContext = {
        ...designToSave,
        contextType,
        contextId: id
      };

      const savedDesign = await saveDesignToSupabase(user.id, questionId, designWithContext);

      if (savedDesign) {
        debugLog('Design synced to Supabase successfully');
      } else {
        debugError('Failed to sync design to Supabase, will try again later');
      }
    } catch (error) {
      debugError('Error syncing design to Supabase:', error);
      // We'll try again on the next sync interval
    } finally {
      setIsLoading(false);
    }
  };

  // Force save to Supabase regardless of time since last sync
  const forceSaveToSupabase = async (
    questionId: string,
    contextType: DesignContextCategory = 'question',
    contextId?: string
  ): Promise<void> => {
    if (!currentDesign ||
        currentDesign.questionId !== questionId ||
        currentDesign.contextType !== contextType ||
        currentDesign.contextId !== (contextId || questionId)) {
      debugError('Cannot force save: no current design or design context mismatch');
      return;
    }

    debugLog('Force saving design to Supabase');
    await saveDesign(questionId, currentDesign, contextType, contextId, true);
  };

  // List all saved designs
  const listDesigns = async (): Promise<SavedDesign[]> => {
    // First try to get designs from localStorage
    const localDesigns: SavedDesign[] = [];
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('layrs-design-')) {
          const design = JSON.parse(localStorage.getItem(key) || '{}') as SavedDesign;

          // Ensure the design has context information
          if (!design.contextType) {
            // For backward compatibility, assume it's a question design
            design.contextType = 'question';
            design.contextId = design.questionId;
          }

          localDesigns.push(design);
        }
      }
    } catch (e) {
      debugError('Error listing designs from localStorage:', e);
    }

    // If no user, return localStorage designs only
    if (!user) {
      debugLog('No user found, returning localStorage designs only');
      return localDesigns.sort((a, b) =>
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
      );
    }

    // If user exists, try to get designs from Supabase
    try {
      setIsLoading(true);
      const supabaseDesigns = await listDesignsFromSupabase(user.id);

      // Merge designs from Supabase and localStorage
      // Prefer Supabase designs when there's a conflict
      const mergedDesigns = [...localDesigns];

      for (const supabaseDesign of supabaseDesigns) {
        const existingIndex = mergedDesigns.findIndex(d => d.questionId === supabaseDesign.questionId);
        if (existingIndex >= 0) {
          mergedDesigns[existingIndex] = supabaseDesign;
        } else {
          mergedDesigns.push(supabaseDesign);
        }
      }

      return mergedDesigns.sort((a, b) =>
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
      );
    } catch (error) {
      debugError('Error listing designs from Supabase:', error);
      // Return localStorage designs if Supabase fails
      return localDesigns.sort((a, b) =>
        new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime()
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a saved design
  const deleteDesign = async (
    questionId: string,
    contextType: DesignContextCategory = 'question',
    contextId?: string
  ) => {
    const id = contextId || questionId;
    const designKey = getDesignKey(questionId, contextType, contextId);

    // Always remove from localStorage
    try {
      localStorage.removeItem(designKey);
      debugLog(`Design removed from localStorage with key ${designKey}`);

      if (currentDesign?.questionId === questionId &&
          currentDesign?.contextType === contextType &&
          currentDesign?.contextId === id) {
        setCurrentDesign(null);
      }
    } catch (e) {
      debugError('Error removing design from localStorage:', e);
    }

    // If no user, we're done
    if (!user) {
      debugLog('No user found, removed from localStorage only');
      return;
    }

    // If user exists, try to delete from Supabase
    try {
      setIsLoading(true);
      const success = await deleteDesignFromSupabase(user.id, questionId, contextType, contextId);

      if (success) {
        debugLog('Design deleted from Supabase');
      } else {
        debugError('Failed to delete design from Supabase');
      }
    } catch (error) {
      debugError('Error deleting design from Supabase:', error);
    } finally {
      setIsLoading(false);
    }
  };



  // Save design when user leaves the page - DISABLED FOR TRULY MANUAL SAVES
  // useEffect(() => {
  //   const handleBeforeUnload = (e: BeforeUnloadEvent) => {
  //     if (hasUnsavedChanges) {
  //       autoSave();
  //       // Standard way to show a confirmation dialog when leaving the page
  //       e.preventDefault();
  //       // Modern browsers ignore custom messages and show their own standard message
  //       return 'You have unsaved changes. Are you sure you want to leave?';
  //     }
  //   };

  //   window.addEventListener('beforeunload', handleBeforeUnload);
  //   return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  // }, [hasUnsavedChanges]);

  // Clear all localStorage design data
  const clearAllCaches = () => {
    debugLog('🧹 Clearing ALL design storage...');

    // Clear localStorage (all design-related keys)
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('layrs-design-') ||
        key.startsWith('layrs-course-progress-') ||
        key.startsWith('supabase.auth.token')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      debugLog(`Removed localStorage key: ${key}`);
    });

    // Clear current design state
    setCurrentDesign(null);

    debugLog('✅ All caches cleared');
  };

  // Nuclear option: Clear ALL Instagram-related data
  const clearInstagramData = () => {
    console.log('🧨 NUCLEAR: Clearing ALL Instagram-related data...');

    // Clear ALL localStorage keys (not just Layrs ones)
    const allKeysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('instagram') ||
        key.includes('design-instagram') ||
        key.startsWith('layrs-') ||
        key.startsWith('supabase') ||
        key.startsWith('course-') ||
        key.includes('guided')
      )) {
        allKeysToRemove.push(key);
      }
    }

    allKeysToRemove.forEach(key => {
      localStorage.removeItem(key);
      console.log(`🧨 Removed localStorage key: ${key}`);
    });

    // Clear current design state
    setCurrentDesign(null);

    // Clear any global variables that might cache data
    if (typeof window !== 'undefined') {
      (window as any).layrsInstagramCache = null;
      (window as any).layrsDesignCache = null;
      (window as any).layrsCurrentDesign = null;
    }

    console.log('🧨 NUCLEAR CLEAR COMPLETE - All Instagram data eliminated');
  };

  // Expose debug functions globally for console access
  useEffect(() => {
    if (typeof window !== 'undefined') {
      (window as any).layrsDebug = {
        clearAllCaches,
        clearInstagramData,
        showStorageInfo: () => {
          console.log('🔍 Storage Information:');

          const layrsKeys: string[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('layrs-')) {
              layrsKeys.push(key);
            }
          }
          console.log('LocalStorage Keys:', layrsKeys);
        },

        findInstagramData: () => {
          console.log('🔍 Searching for Instagram-related data...');

          // Check localStorage
          const instagramKeys: string[] = [];
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && (
              key.includes('instagram') ||
              key.includes('design-instagram') ||
              key.includes('course-design-instagram')
            )) {
              instagramKeys.push(key);
            }
          }

          console.log('📱 Instagram localStorage keys:', instagramKeys);
          console.log('🎯 Current design:', currentDesign);

          const hasInstagramData = instagramKeys.length > 0;
          console.log(`${hasInstagramData ? '⚠️' : '✅'} Instagram data ${hasInstagramData ? 'FOUND' : 'not found'}`);

          return {
            localStorage: instagramKeys,
            currentDesign,
            hasData: hasInstagramData
          };
        }
      };
    }
  }, [clearAllCaches]);

  // Force reload a design from Supabase/localStorage
  const forceReloadDesign = async (
    questionId: string,
    contextType: DesignContextCategory = 'question',
    contextId?: string
  ): Promise<SavedDesign | null> => {
    const id = contextId || questionId;
    console.log(`Force reloading design for ${contextType} ${id}`);
    // Load the design fresh
    return await loadDesign(questionId, contextType, contextId);
  };

  // Conflict resolution handlers
  const handleChooseLocal = useCallback(() => {
    if (!conflictData) return;

    // Keep local version - save it to Supabase to overwrite server
    saveDesign(
      conflictData.questionId,
      conflictData.local,
      conflictData.contextType,
      conflictData.contextId,
      true, // sync to Supabase
      true  // update current design
    );

    setConflictDialogOpen(false);
    setConflictData(null);
  }, [conflictData, saveDesign]);

  const handleChooseServer = useCallback(() => {
    if (!conflictData) return;

    // Use server version - update localStorage and current state
    const designKey = getDesignKey(conflictData.questionId, conflictData.contextType, conflictData.contextId);
    localStorage.setItem(designKey, JSON.stringify(conflictData.server));
    setCurrentDesign(conflictData.server);
    setHasUnsavedChanges(false);

    setConflictDialogOpen(false);
    setConflictData(null);
  }, [conflictData, getDesignKey]);

  const handleCloseConflictDialog = useCallback(() => {
    setConflictDialogOpen(false);
    setConflictData(null);
  }, []);

  // Enhanced value with sync capabilities
  const value = {
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    listDesigns,
    deleteDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    isLoading,
    forceReloadDesign,
    forceSaveToSupabase,
    getDesignKey,
    // Design sync methods
    clearAndRepopulate: designSync.clearAndRepopulate,
    checkForStaleData: designSync.checkForStaleData,
    syncDesign: designSync.syncDesign
  };

  return (
    <DesignContext.Provider value={value}>
      {children}
      {conflictData && (
        <DesignConflictDialog
          isOpen={conflictDialogOpen}
          onClose={handleCloseConflictDialog}
          localDesign={conflictData.local}
          serverDesign={conflictData.server}
          onChooseServer={handleChooseServer}
        />
      )}
    </DesignContext.Provider>
  );
};

export const useDesign = (): DesignContextType => {
  const context = useContext(DesignContext);
  if (context === undefined) {
    throw new Error('useDesign must be used within a DesignProvider');
  }
  return context;
};
