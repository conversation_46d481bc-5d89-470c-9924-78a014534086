import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User, Provider } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { clearSubscriptionCache } from '@/services/subscriptionService';

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  isEmailVerified: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
    data: Session | null;
  }>;
  signUp: (email: string, password: string) => Promise<{
    error: Error | null;
    data: { user: User | null; session: Session | null };
  }>;
  signInWithSocial: (provider: Provider) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{
    error: Error | null;
    data: {} | null;
  }>;
  sendVerificationEmail: (email: string) => Promise<{
    error: Error | null;
    data: {} | null;
  }>;
  updateEmail: (newEmail: string) => Promise<{
    error: Error | null;
    data: User | null;
  }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEmailVerified, setIsEmailVerified] = useState(false);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);

      // Check email verification status
      if (session?.user) {
        const emailVerified = session.user.email_confirmed_at !== null;
        setIsEmailVerified(emailVerified);
      }

      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);

      // Check email verification status on auth state change
      if (session?.user) {
        const emailVerified = session.user.email_confirmed_at !== null;
        setIsEmailVerified(emailVerified);
      } else {
        setIsEmailVerified(false);
      }

      setLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        return { error, data: null };
      }

      toast.success('Signed in successfully!');
      return { error: null, data: data.session };
    } catch (error) {
      toast.error('An unexpected error occurred');
      return { error: error as Error, data: null };
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/login`,
        },
      });

      if (error) {
        toast.error(error.message);
        return { error, data: { user: null, session: null } };
      }

      // Check if email confirmation is required
      if (data.user && data.user.identities && data.user.identities.length === 0) {
        toast.error('This email is already registered. Please sign in or reset your password.');
        return { error: new Error('Email already registered'), data: { user: null, session: null } };
      }

      if (data.user && !data.session) {
        // Email confirmation required
        toast.success('Please check your email to verify your account.');
      } else {
        toast.success('Signed up successfully!');
      }

      return { error: null, data };
    } catch (error) {
      toast.error('An unexpected error occurred');
      return { error: error as Error, data: { user: null, session: null } };
    }
  };

  const signOut = async () => {
    try {
      // Clear subscription cache before signing out
      if (user?.id) {
        clearSubscriptionCache(user.id);
      }

      await supabase.auth.signOut();
      toast.success('Signed out successfully!');
    } catch (error) {
      toast.error('Error signing out');
      console.error('Error signing out:', error);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        toast.error(error.message);
        return { error, data: null };
      }

      toast.success('Password reset email sent!');
      return { error: null, data };
    } catch (error) {
      toast.error('An unexpected error occurred');
      return { error: error as Error, data: null };
    }
  };

  const signInWithSocial = async (provider: Provider) => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/`,
        },
      });

      if (error) {
        toast.error(error.message);
      }
    } catch (error) {
      toast.error('An unexpected error occurred during social login');
      console.error('Social login error:', error);
    }
  };

  // Send verification email
  const sendVerificationEmail = async (email: string) => {
    try {
      const { data, error } = await supabase.auth.resend({
        type: 'signup',
        email,
        options: {
          emailRedirectTo: `${window.location.origin}/login`,
        },
      });

      if (error) {
        toast.error(error.message);
        return { error, data: null };
      }

      toast.success('Verification email sent!');
      return { error: null, data };
    } catch (error) {
      toast.error('An unexpected error occurred');
      return { error: error as Error, data: null };
    }
  };

  // Update email address
  const updateEmail = async (newEmail: string) => {
    try {
      const { data, error } = await supabase.auth.updateUser({
        email: newEmail
      });

      if (error) {
        toast.error(error.message);
        return { error, data: null };
      }

      toast.success('Verification email sent to your new email address. Please check your inbox.');
      return { error: null, data: data.user };
    } catch (error) {
      toast.error('An unexpected error occurred');
      return { error: error as Error, data: null };
    }
  };

  const value = {
    session,
    user,
    loading,
    isEmailVerified,
    signIn,
    signUp,
    signOut,
    resetPassword,
    signInWithSocial,
    sendVerificationEmail,
    updateEmail,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
