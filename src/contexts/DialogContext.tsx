import React, { createContext, useContext, useState, ReactNode } from 'react';
import DeleteConnectionDialog from '@/components/DeleteConnectionDialog';

interface DialogContextType {
  showDeleteConnectionDialog: (
    edgeId: string,
    edgeLabel: string,
    onDelete: () => void,
    onUpdateLabel: (newLabel: string) => void,
    currentStyle?: string,
    onUpdateStyle?: (newStyle: string) => void,
    currentPathType?: string,
    onUpdatePathType?: (newPathType: string) => void
  ) => void;
  hideDeleteConnectionDialog: () => void;
}

const DialogContext = createContext<DialogContextType | undefined>(undefined);

interface DialogProviderProps {
  children: ReactNode;
}

export const DialogProvider: React.FC<DialogProviderProps> = ({ children }) => {
  const [deleteDialogState, setDeleteDialogState] = useState<{
    isOpen: boolean;
    edgeId: string | null;
    edgeLabel: string;
    onDelete: (() => void) | null;
    onUpdateLabel: ((newLabel: string) => void) | null;
    currentStyle?: string;
    onUpdateStyle?: (newStyle: string) => void;
    currentPathType?: string;
    onUpdatePathType?: (newPathType: string) => void;
  }>({
    isOpen: false,
    edgeId: null,
    edgeLabel: '',
    onDelete: null,
    onUpdateLabel: null,
    currentStyle: 'smooth',
    onUpdateStyle: undefined,
    currentPathType: 'smooth',
    onUpdatePathType: undefined
  });

  const showDeleteConnectionDialog = (
    edgeId: string,
    edgeLabel: string,
    onDelete: () => void,
    onUpdateLabel: (newLabel: string) => void,
    currentStyle?: string,
    onUpdateStyle?: (newStyle: string) => void,
    currentPathType?: string,
    onUpdatePathType?: (newPathType: string) => void
  ) => {
    setDeleteDialogState({
      isOpen: true,
      edgeId,
      edgeLabel,
      onDelete,
      onUpdateLabel,
      currentStyle: currentStyle || 'smooth',
      onUpdateStyle,
      currentPathType: currentPathType || 'smooth',
      onUpdatePathType
    });
  };

  const hideDeleteConnectionDialog = () => {
    setDeleteDialogState({
      isOpen: false,
      edgeId: null,
      edgeLabel: '',
      onDelete: null,
      onUpdateLabel: null,
      currentStyle: 'smooth',
      onUpdateStyle: undefined,
      currentPathType: 'smooth',
      onUpdatePathType: undefined
    });
  };

  const handleDelete = () => {
    if (deleteDialogState.onDelete) {
      deleteDialogState.onDelete();
    }
    hideDeleteConnectionDialog();
  };

  const handleUpdateLabel = (newLabel: string) => {
    if (deleteDialogState.onUpdateLabel) {
      deleteDialogState.onUpdateLabel(newLabel);
    }
    // Close the dialog after updating the label
    hideDeleteConnectionDialog();
  };

  const handleUpdateStyle = (newStyle: string) => {
    if (deleteDialogState.onUpdateStyle) {
      deleteDialogState.onUpdateStyle(newStyle);
    }
    // Do not close dialog on style change
  };

  const handleUpdatePathType = (newPathType: string) => {
    if (deleteDialogState.onUpdatePathType) {
      deleteDialogState.onUpdatePathType(newPathType);
    }
    // Do not close dialog on path type change
  };

  return (
    <DialogContext.Provider value={{ showDeleteConnectionDialog, hideDeleteConnectionDialog }}>
      {children}
      
      {/* Render dialogs at the top level */}
      <DeleteConnectionDialog
        isOpen={deleteDialogState.isOpen}
        onClose={hideDeleteConnectionDialog}
        onDelete={handleDelete}
        edgeLabel={deleteDialogState.edgeLabel}
        onUpdateLabel={handleUpdateLabel}
        currentStyle={deleteDialogState.currentStyle}
        onUpdateStyle={handleUpdateStyle}
        currentPathType={deleteDialogState.currentPathType}
        onUpdatePathType={handleUpdatePathType}
      />
    </DialogContext.Provider>
  );
};

export const useDialog = (): DialogContextType => {
  const context = useContext(DialogContext);
  if (context === undefined) {
    throw new Error('useDialog must be used within a DialogProvider');
  }
  return context;
}; 