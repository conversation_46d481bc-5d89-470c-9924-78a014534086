import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Question } from '@/types/Question';
import { QuestionService } from '@/services/questionService';

interface QuestionsContextType {
  questions: Question[];
  currentQuestion: Question | null;
  loading: boolean;
  error: string | null;
  setCurrentQuestion: (question: Question | null) => void;
  getQuestionById: (id: number) => Promise<Question | null>;
  filterQuestions: (filters: QuestionFilters) => void;
  resetFilters: () => void;
  filteredQuestions: Question[];
  // Admin functions
  checkQuestionsExist: () => Promise<{ success: boolean; count: number; error?: string }>;
  createQuestion: (question: Omit<Question, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Question | null>;
  updateQuestion: (id: number, updates: Partial<Omit<Question, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<Question | null>;
  deleteQuestion: (id: number) => Promise<boolean>;
  refreshQuestions: () => Promise<void>;
  getConnectionStatus: () => Promise<{ connected: boolean; tableExists: boolean; recordCount: number }>;
}

interface QuestionFilters {
  difficulty?: ('easy' | 'medium' | 'hard')[];
  category?: string[];
  searchTerm?: string;
}

export const QuestionsContext = createContext<QuestionsContextType | undefined>(undefined);

export const QuestionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [filteredQuestions, setFilteredQuestions] = useState<Question[]>([]);
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load questions from mock data (this would be an API call in a real app)
  useEffect(() => {
    const loadQuestions = async () => {
      try {
        setLoading(true);
        console.log('📋 Loading questions from Supabase...');

        // Load questions from Supabase (with fallback to mock data)
        const loadedQuestions = await QuestionService.loadQuestions();

        setQuestions(loadedQuestions);
        setFilteredQuestions(loadedQuestions);
        setError(null);

        console.log(`✅ Loaded ${loadedQuestions.length} questions`);
      } catch (err) {
        console.error('❌ Failed to load questions:', err);
        setError('Failed to load questions');
        setQuestions([]);
        setFilteredQuestions([]);
      } finally {
        setLoading(false);
      }
    };

    loadQuestions();
  }, []);

  const getQuestionById = useCallback(async (id: number): Promise<Question | null> => {
    // Since we load ALL questions at startup, we should only look in the cache
    // If it's not in the cache, it means the question doesn't exist or isn't active
    const cachedQuestion = questions.find(q => q.id === id);
    if (cachedQuestion) {
      console.log(`📋 Found question ${id} in cache:`, cachedQuestion.title);
      return cachedQuestion;
    }

    // If not found in cache and we're still loading, wait for loading to complete
    if (loading) {
      console.log(`📋 Question ${id} not found, but still loading questions. Waiting...`);
      // Return a promise that resolves after loading is complete
      return new Promise((resolve) => {
        const checkAgain = () => {
          const question = questions.find(q => q.id === id);
          if (question) {
            console.log(`📋 Found question ${id} after loading completed:`, question.title);
            resolve(question);
          } else {
            console.log(`❌ Question ${id} not found after loading completed`);
            resolve(null);
          }
        };

        // Check again in a short interval
        const interval = setInterval(() => {
          if (!loading) {
            clearInterval(interval);
            checkAgain();
          }
        }, 100);

        // Timeout after 5 seconds
        setTimeout(() => {
          clearInterval(interval);
          console.log(`⏰ Timeout waiting for question ${id}`);
          resolve(null);
        }, 5000);
      });
    }

    // If loading is complete and question not found, it doesn't exist
    console.log(`❌ Question ${id} not found in loaded questions (${questions.length} total)`);
    return null;
  }, [questions, loading]);

  const filterQuestions = useCallback((filters: QuestionFilters) => {
    let filtered = [...questions];

    // Filter by difficulty
    if (filters.difficulty && filters.difficulty.length > 0) {
      filtered = filtered.filter(q => filters.difficulty?.includes(q.difficulty));
    }

    // Filter by category
    if (filters.category && filters.category.length > 0) {
      filtered = filtered.filter(q => filters.category?.includes(q.category));
    }

    // Filter by search term
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(
        q =>
          q.title.toLowerCase().includes(searchTerm) ||
          q.description.toLowerCase().includes(searchTerm) ||
          q.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    setFilteredQuestions(filtered);
  }, [questions]);

  const resetFilters = useCallback(() => {
    setFilteredQuestions(questions);
  }, [questions]);

  // Admin functions
  const checkQuestionsExist = useCallback(async () => {
    return await QuestionService.checkQuestionsExist();
  }, []);

  const refreshQuestions = useCallback(async () => {
    try {
      setLoading(true);
      const loadedQuestions = await QuestionService.loadQuestions();
      setQuestions(loadedQuestions);
      setFilteredQuestions(loadedQuestions);
      setError(null);
    } catch (err) {
      console.error('Failed to refresh questions:', err);
      setError('Failed to refresh questions');
    } finally {
      setLoading(false);
    }
  }, []);

  const createQuestion = useCallback(async (question: Omit<Question, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newQuestion = await QuestionService.createQuestion(question);
    if (newQuestion) {
      // Refresh questions list
      await refreshQuestions();
    }
    return newQuestion;
  }, [refreshQuestions]);

  const updateQuestion = useCallback(async (id: number, updates: Partial<Omit<Question, 'id' | 'createdAt' | 'updatedAt'>>) => {
    const updatedQuestion = await QuestionService.updateQuestion(id, updates);
    if (updatedQuestion) {
      // Refresh questions list
      await refreshQuestions();
    }
    return updatedQuestion;
  }, [refreshQuestions]);

  const deleteQuestion = useCallback(async (id: number) => {
    const success = await QuestionService.deleteQuestion(id);
    if (success) {
      // Refresh questions list
      await refreshQuestions();
    }
    return success;
  }, [refreshQuestions]);

  const getConnectionStatus = useCallback(async () => {
    return await QuestionService.getConnectionStatus();
  }, []);

  const value = {
    questions,
    currentQuestion,
    loading,
    error,
    setCurrentQuestion,
    getQuestionById,
    filterQuestions,
    resetFilters,
    filteredQuestions,
    // Admin functions
    checkQuestionsExist,
    createQuestion,
    updateQuestion,
    deleteQuestion,
    refreshQuestions,
    getConnectionStatus
  };

  return <QuestionsContext.Provider value={value}>{children}</QuestionsContext.Provider>;
};

export const useQuestions = (): QuestionsContextType => {
  const context = useContext(QuestionsContext);
  if (context === undefined) {
    throw new Error('useQuestions must be used within a QuestionsProvider');
  }
  return context;
};
