// Optional: Custom analytics configuration and tracking functions
// This file provides additional analytics utilities beyond the basic Vercel Analytics

import { track } from '@vercel/analytics';

// Custom event tracking functions
export const trackEvent = {
  // Design-related events
  designCreated: (questionId?: string) => {
    track('design_created', { questionId });
  },
  
  designSaved: (questionId?: string, nodeCount?: number) => {
    track('design_saved', { questionId, nodeCount });
  },
  
  designExported: (format: 'png' | 'pdf', questionId?: string) => {
    track('design_exported', { format, questionId });
  },
  
  // Assessment events
  assessmentStarted: (questionId?: string) => {
    track('assessment_started', { questionId });
  },
  
  assessmentCompleted: (questionId?: string, score?: number) => {
    track('assessment_completed', { questionId, score });
  },
  
  // Component usage
  componentAdded: (componentType: string, questionId?: string) => {
    track('component_added', { componentType, questionId });
  },
  
  // Tutorial events
  tutorialStarted: () => {
    track('tutorial_started');
  },
  
  tutorialCompleted: () => {
    track('tutorial_completed');
  },
  
  // Question navigation
  questionViewed: (questionId: string) => {
    track('question_viewed', { questionId });
  },
  
  // Best design events
  bestDesignLoaded: (questionId?: string, score?: number) => {
    track('best_design_loaded', { questionId, score });
  },
  
  // Guided mode events
  guidedModeStarted: (courseId: string) => {
    track('guided_mode_started', { courseId });
  },
  
  guidedStepCompleted: (courseId: string, stepId: string) => {
    track('guided_step_completed', { courseId, stepId });
  },
  
  // User engagement
  panelToggled: (panel: 'left' | 'right', action: 'opened' | 'closed') => {
    track('panel_toggled', { panel, action });
  },
  
  canvasInteraction: (action: 'zoom' | 'pan' | 'fit_view') => {
    track('canvas_interaction', { action });
  }
};

// Page view tracking (automatically handled by Vercel Analytics, but can be customized)
export const trackPageView = (page: string, properties?: Record<string, any>) => {
  track('page_view', { page, ...properties });
};

// Error tracking
export const trackError = (error: string, context?: string) => {
  track('error_occurred', { error, context });
};

// Performance tracking
export const trackPerformance = (metric: string, value: number, unit?: string) => {
  track('performance_metric', { metric, value, unit });
};
