@import './styles/chat.css';
@import './styles/landing-page.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 142.4 71.8% 29.2%;
  }
}

@layer base {
  * {
    @apply border-border selection:bg-gray-800/50 selection:text-white;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  html, body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    min-height: 100vh;
    background-color: #ffffff; /* Match your dark purple background */
  }

  /* Component styles */
  .component-input {
    @apply w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-base shadow-sm focus:border-layrs-primary focus:outline-none focus:ring-1 focus:ring-layrs-primary;
    color: #000000;
  }
  .component-textarea {
    @apply w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-base shadow-sm focus:border-layrs-primary focus:outline-none focus:ring-1 focus:ring-layrs-primary resize-y;
    color: #000000;
  }
  .component-label {
    @apply block text-base font-medium text-gray-700;
  }
  .code-textarea {
    @apply font-mono text-sm bg-gray-50 rounded-md p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-layrs-primary focus:border-transparent resize-y min-h-[120px] w-full;
    color: #000000;
  }

  /* Landing page typography */
  h1 {
    @apply font-bold text-[#EDE9FE] text-3xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply font-bold text-[#A78BFA] text-2xl md:text-4xl;
  }

  p {
    @apply text-[#000] text-base md:text-lg leading-relaxed;
  }
}

/* Landing page styles */
.gradient-text {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400;
}

.shimmer {
  @apply relative overflow-hidden;
}

.shimmer::before {
  content: '';
  @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
  animation: shimmer 2s infinite;
}

.glass-card {
  @apply backdrop-blur-lg bg-white/5 border border-white/10;
}

.neo-blur {
  @apply backdrop-blur-xl bg-black/40 border border-white/10;
}

/* Add container styles for consistent section width */
.container {
  @apply max-w-4xl mx-auto px-4;
}

/* Consistent section padding */
section {
  @apply py-16 md:py-20;
}

/* Animation for floating elements */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Background glow effect */
.bg-hero-glow {
  background: radial-gradient(circle at center, rgba(39, 39, 42, 0.3) 0%, rgba(15, 23, 42, 0) 70%);
}

/* Add these classes to support the dynamic layout */
.w-3\/10 {
  width: 30%;
}

.w-7\/10 {
  width: 70%;
}

/* Add a transition for smooth resizing */
.w-1\/4, .w-2\/4, .w-3\/10, .w-7\/10 {
  transition: width 0.3s ease-in-out;
}

/* Fix for Sonner toast container */
[data-sonner-toaster] {
  position: fixed !important;
  top: auto !important;
  height: auto !important;
  width: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  pointer-events: none !important;
}

[data-sonner-toaster][data-position="bottom-right"] {
  bottom: 16px !important;
  right: 16px !important;
  left: auto !important;
}

[data-sonner-toaster] > * {
  pointer-events: auto;
}

/* Hide Sonner's aria-live region that creates blank space */
section[aria-label*="Notifications"] {
  display: none !important;
  visibility: hidden !important;
  position: absolute !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Assessment button styles */
.assessment-button {
  position: relative;
  z-index: 20;
  cursor: pointer !important;
  pointer-events: auto !important;
  transition: all 0.2s ease-in-out;
}

.assessment-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.assessment-button:active:not(:disabled) {
  transform: translateY(0);
}

.assessment-button:disabled {
  opacity: 0.7;
  cursor: not-allowed !important;
}

/* Typing indicator styles */
.typing-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background-color: #6b7280;
  border-radius: 50%;
  display: inline-block;
  opacity: 0.6;
}

.typing-indicator span:nth-child(1) {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation: typing 1.4s infinite ease-in-out 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation: typing 1.4s infinite ease-in-out 0.4s;
}

@keyframes typing {
  0%, 100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Monaco Editor padding fixes */
.monaco-editor > section {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor .view-lines {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor .monaco-scrollable-element {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor .overflow-guard {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.monaco-editor .monaco-editor-background {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Target any Monaco editor containers that might have padding */
.monaco-editor * {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/* Specific fix for custom logic editor container */
.custom-logic-editor-container {
  padding: 0 !important;
  margin: 0 !important;
}

.custom-logic-editor-container .monaco-editor {
  padding: 0 !important;
  margin: 0 !important;
}

.custom-logic-editor-container .monaco-editor * {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Ultra-specific Monaco Editor padding override */
div.custom-logic-editor-container div.monaco-editor div.overflow-guard,
div.custom-logic-editor-container div.monaco-editor div.monaco-scrollable-element,
div.custom-logic-editor-container div.monaco-editor div.view-lines,
div.custom-logic-editor-container div.monaco-editor div.monaco-editor-background,
div.custom-logic-editor-container div.monaco-editor section,
div.custom-logic-editor-container div.monaco-editor > section {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Force zero padding on all Monaco elements with maximum specificity */
.properties-panel .custom-logic-editor-container .monaco-editor,
.properties-panel .custom-logic-editor-container .monaco-editor *,
.properties-panel .custom-logic-editor-container .monaco-editor > *,
.properties-panel .custom-logic-editor-container .monaco-editor div,
.properties-panel .custom-logic-editor-container .monaco-editor section {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Nuclear option - override everything with inline styles */
.custom-logic-editor-container [style*="padding"] {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.custom-logic-editor-container [style*="margin"] {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Top Component Palette Styles */
.top-component-palette {
  /* Ensure smooth horizontal scrolling */
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  /* Smooth height transitions when expanding/collapsing */
  transition: height 0.3s ease-in-out;
  /* Ensure palette stays above other content */
  position: relative;
  z-index: 20;
}

.top-component-palette::-webkit-scrollbar {
  height: 6px;
}

.top-component-palette::-webkit-scrollbar-track {
  background: transparent;
}

.top-component-palette::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.top-component-palette::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Ensure component items don't shrink too much on small screens */
.top-component-palette .flex-shrink-0 {
  min-width: fit-content;
}

/* Panel overlay animations */
.panel-overlay-enter {
  transform: translateX(-100%);
  opacity: 0;
}

.panel-overlay-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.panel-overlay-exit {
  transform: translateX(0);
  opacity: 1;
}

.panel-overlay-exit-active {
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.3s ease-in, opacity 0.3s ease-in;
}

/* Right panel overlay animations */
.right-panel-overlay-enter {
  transform: translateX(100%);
  opacity: 0;
}

.right-panel-overlay-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

.right-panel-overlay-exit {
  transform: translateX(0);
  opacity: 1;
}

.right-panel-overlay-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.3s ease-in, opacity 0.3s ease-in;
}

/* Custom resize handle for left panel */
.left-panel-resize-handle {
  position: absolute;
  right: 0;
  top: 0;
  width: 4px;
  height: 100%;
  background-color: rgba(156, 163, 175, 0.3);
  cursor: col-resize;
  transition: background-color 0.2s ease;
  z-index: 50;
}

.left-panel-resize-handle:hover {
  background-color: rgba(156, 163, 175, 0.6);
}

.left-panel-resize-handle:active {
  background-color: rgba(99, 102, 241, 0.8);
}

/* Add Properties Button Styling */
.custom-node .group:hover [data-properties-button],
.composite-node .group:hover [data-properties-button] {
  opacity: 1;
}

.custom-node [data-properties-button],
.composite-node [data-properties-button] {
  opacity: 0;
  transition: opacity 0.2s ease-in-out, background-color 0.2s ease-in-out;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid rgba(156, 163, 175, 0.3);
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

.custom-node [data-properties-button]:hover,
.composite-node [data-properties-button]:hover {
  background-color: rgba(243, 244, 246, 1);
  border-color: rgba(156, 163, 175, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Smooth expansion animation for component items */
.top-component-palette .flex.items-center.gap-2.ml-3 {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Override green outline on edge selector dropdown */
[data-radix-select-item] {
  outline: none !important;
  box-shadow: none !important;
}

[data-radix-select-item]:focus {
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

[data-radix-select-item][data-state="checked"] {
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

[data-radix-select-item][data-highlighted] {
  background-color: rgba(243, 244, 246, 0.5) !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Additional specificity for edge selector */
.edge-type-selector [data-radix-select-item],
.edge-type-selector [data-radix-select-trigger] {
  outline: none !important;
  box-shadow: none !important;
}

.edge-type-selector [data-radix-select-item]:focus,
.edge-type-selector [data-radix-select-trigger]:focus {
  outline: none !important;
  box-shadow: none !important;
  ring: none !important;
}

/* Target the specific select components */
.edge-type-selector [role="option"] {
  outline: none !important;
  box-shadow: none !important;
}

.edge-type-selector [role="option"]:focus {
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.edge-type-selector [role="option"][data-state="checked"] {
  background-color: transparent !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Override any ring styles */
.edge-type-selector * {
  --tw-ring-color: transparent !important;
  --tw-ring-offset-color: transparent !important;
}

.edge-type-selector *:focus {
  --tw-ring-color: transparent !important;
  --tw-ring-offset-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

