import { supabase } from '@/lib/supabase';

interface QuestionUpdate {
  id: number;
  functional_requirements: string[];
  non_functional_requirements: string[];
  constraints: string[];
}

// Updated question data with proper FR/NFR separation
const questionUpdates: QuestionUpdate[] = [
  {
    id: 14,
    functional_requirements: [
      "List properties with descriptions/photos",
      "Search/filter by location, dates, price",
      "Book and pay for stays",
      "Leave reviews after checkout"
    ],
    non_functional_requirements: [
      "Search latency < 200ms",
      "Handle 1M+ bookings per day",
      "99.9% transaction integrity"
    ],
    constraints: [
      "Search ranking: Use relevance score (e.g., location, reviews)",
      "Fraud detection: Flag suspicious bookings in real-time",
      "Payment processing: Integrate with Stripe/PayPal",
      "Calendar management: Track availability in a distributed cache"
    ]
  },
  {
    id: 15,
    functional_requirements: [
      "Create/edit playlists (public/private)",
      "Add/remove songs",
      "Stream music with low latency",
      "Generate recommendations (e.g., 'Discover Weekly')"
    ],
    non_functional_requirements: [
      "Stream latency < 500ms",
      "Support 100M+ monthly active users",
      "99.95% uptime"
    ],
    constraints: [
      "Audio streaming: Use HTTP Live Streaming (HLS) or DASH",
      "Collaborative playlists: Use CRDTs (Conflict-Free Replicated Data Types)",
      "Recommendations: Train ML models on user listening history",
      "Licensing: Track playback for royalty calculations"
    ]
  },
  {
    id: 16,
    functional_requirements: [
      "Track driver GPS coordinates",
      "Estimate arrival time for riders",
      "Update ETA dynamically during the trip"
    ],
    non_functional_requirements: [
      "ETA accuracy within 10%",
      "Handle 10M+ ETA requests per minute",
      "Sub-second update latency"
    ],
    constraints: [
      "Geospatial indexing: Use Elasticsearch or PostgreSQL with PostGIS",
      "Traffic data: Integrate with Google Maps API or HERE Technologies",
      "Kalman filter: Smooth GPS noise for accurate positioning",
      "Edge computing: Process location data locally to reduce latency"
    ]
  },
  {
    id: 17,
    functional_requirements: [
      "Add/remove items",
      "Update cart across devices",
      "Reserve inventory during checkout",
      "Handle cart abandonment"
    ],
    non_functional_requirements: [
      "99.9% cart consistency",
      "Handle 100k+ carts per second",
      "Low latency (< 200ms)"
    ],
    constraints: [
      "Distributed sessions: Use sticky sessions or Redis for cart data",
      "Inventory reservation: Use compensating transactions (e.g., Saga pattern)",
      "Price consistency: Cache prices with short TTLs",
      "A/B testing: Dynamically show different cart UIs"
    ]
  },
  {
    id: 18,
    functional_requirements: [
      "Create/edit recurring events",
      "Invite attendees with RSVP",
      "Send reminders (email, push notifications)",
      "Share calendars (public/private)"
    ],
    non_functional_requirements: [
      "Sync latency < 1s",
      "Support 1B+ events per day",
      "99.99% uptime"
    ],
    constraints: [
      "Event storage: Use a relational database (e.g., Google Bigtable)",
      "Reminders: Use a pub/sub system (e.g., Kafka) for notifications",
      "Free/busy detection: Cache availability for faster scheduling",
      "Time zones: Normalize all timestamps to UTC"
    ]
  },
  {
    id: 19,
    functional_requirements: [
      "Send/receive messages",
      "Display typing indicators",
      "Track read receipts",
      "Store message history"
    ],
    non_functional_requirements: [
      "Delivery latency < 500ms",
      "Handle 1M+ messages per second",
      "99.95% message persistence"
    ],
    constraints: [
      "Offline storage: Use a message queue (e.g., RabbitMQ) for delayed delivery",
      "Typing indicators: Use WebSocket heartbeats",
      "Read receipts: Update a distributed cache (e.g., Memcached)",
      "Encryption: Use TLS for data in transit"
    ]
  },
  {
    id: 20,
    functional_requirements: [
      "Post stories (images/videos) that expire in 24h",
      "Add interactive elements (e.g., polls, quizzes)",
      "Track views and engagement",
      "Allow story highlights"
    ],
    non_functional_requirements: [
      "Story upload latency < 2s",
      "Handle 100M+ story views per hour",
      "99.9% accuracy in analytics"
    ],
    constraints: [
      "Ephemerality: Use TTL (time-to-live) in the database",
      "Polls: Use a distributed cache (e.g., Redis) for real-time results",
      "View tracking: Sample views to reduce write load",
      "Augmented reality: Integrate with Spark AR for filters"
    ]
  }
];

/**
 * Update questions with proper FR/NFR separation
 */
export const updateQuestionsWithFRNFR = async (): Promise<{ success: boolean; error?: string; updated: number }> => {
  try {
    let updatedCount = 0;
    
    for (const update of questionUpdates) {
      const { error } = await supabase
        .from('questions')
        .update({
          functional_requirements: update.functional_requirements,
          non_functional_requirements: update.non_functional_requirements,
          constraints: update.constraints
        })
        .eq('id', update.id);
      
      if (error) {
        console.error(`Error updating question ${update.id}:`, error);
        return { success: false, error: `Failed to update question ${update.id}: ${error.message}`, updated: updatedCount };
      }
      
      updatedCount++;
      console.log(`✅ Updated question ${update.id} with FR/NFR separation`);
    }
    
    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error('Error updating questions:', error);
    return { success: false, error: 'Failed to update questions', updated: 0 };
  }
};
