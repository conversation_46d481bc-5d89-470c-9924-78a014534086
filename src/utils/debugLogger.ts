const DEBUG_ENABLED = false; // Set to true to enable debug logs

export function debugLog(...args: any[]) {
  if (DEBUG_ENABLED) {
    // eslint-disable-next-line no-console
    console.log('[DEBUG]', ...args);
  }
}

export function debugWarn(...args: any[]) {
  if (DEBUG_ENABLED) {
    // eslint-disable-next-line no-console
    console.warn('[DEBUG]', ...args);
  }
}

export function debugError(...args: any[]) {
  if (DEBUG_ENABLED) {
    // eslint-disable-next-line no-console
    console.error('[DEBUG]', ...args);
  }
} 