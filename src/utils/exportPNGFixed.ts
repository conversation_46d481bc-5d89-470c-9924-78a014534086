import html2canvas from 'html2canvas';
import { toast } from 'sonner';
import { ReactFlowInstance } from '@xyflow/react';

/**
 * Export PNG with proper bounds calculation and viewport handling
 */
export const exportToPNGFixed = async (reactFlowInstance: ReactFlowInstance) => {
  if (!reactFlowInstance) {
    toast.error("No diagram to export. Please add components to the canvas first.");
    return;
  }

  try {
    toast.loading('Preparing diagram for export...');

    // Get all nodes and edges
    const nodes = reactFlowInstance.getNodes();
    const edges = reactFlowInstance.getEdges();
    
    if (nodes.length === 0) {
      toast.error("No components to export. Please add components to the canvas first.");
      return;
    }

    console.log('Nodes to export:', nodes.length);
    console.log('Edges to export:', edges.length);

    // Store original viewport
    const originalViewport = reactFlowInstance.getViewport();
    console.log('Original viewport:', originalViewport);

    // Calculate proper bounds - fixed calculation
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    
    nodes.forEach(node => {
      const nodeWidth = node.width || node.data?.width || 200;
      const nodeHeight = node.height || node.data?.height || 100;
      
      minX = Math.min(minX, node.position.x);
      minY = Math.min(minY, node.position.y);
      maxX = Math.max(maxX, node.position.x + nodeWidth);
      maxY = Math.max(maxY, node.position.y + nodeHeight);
    });

    // Add padding
    const padding = 50;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;

    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;

    console.log('Calculated bounds:', { minX, minY, maxX, maxY, contentWidth, contentHeight });

    // Use fitView to ensure everything is visible
    reactFlowInstance.fitView({
      padding: 0.1,
      includeHiddenNodes: false,
      minZoom: 0.1,
      maxZoom: 2,
      duration: 0
    });

    // Wait for fitView to complete
    await new Promise(resolve => setTimeout(resolve, 300));

    const afterFitViewport = reactFlowInstance.getViewport();
    console.log('After fitView viewport:', afterFitViewport);

    toast.loading('Capturing diagram...');

    // Get the ReactFlow container
    const container = document.querySelector('.react-flow') as HTMLElement;
    if (!container) {
      toast.error('Diagram container not found.');
      return;
    }

    // Ensure all edges are visible
    const edgeElements = document.querySelectorAll('.react-flow__edge');
    const edgePaths = document.querySelectorAll('.react-flow__edge-path');
    
    edgeElements.forEach(edge => {
      const el = edge as HTMLElement;
      el.style.visibility = 'visible';
      el.style.opacity = '1';
    });

    edgePaths.forEach(path => {
      const el = path as SVGPathElement;
      el.style.stroke = '#374151'; // Gray-700
      el.style.strokeWidth = '2';
      el.style.visibility = 'visible';
      el.style.opacity = '1';
    });

    // Force a repaint
    container.offsetHeight;

    // Capture with html2canvas
    const canvas = await html2canvas(container, {
      backgroundColor: '#ffffff',
      scale: 2,
      logging: false,
      useCORS: true,
      allowTaint: false,
      foreignObjectRendering: true,
      imageTimeout: 15000,
      ignoreElements: (element) => {
        // Ignore UI controls
        if (element.classList && (
          element.classList.contains('react-flow__controls') ||
          element.classList.contains('react-flow__minimap') ||
          element.classList.contains('react-flow__panel') ||
          element.classList.contains('react-flow__attribution')
        )) {
          return true;
        }
        return false;
      }
    });

    // Restore original viewport
    reactFlowInstance.setViewport(originalViewport);

    toast.loading('Downloading image...');

    // Create and trigger download
    const link = document.createElement('a');
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.download = `system-design-${timestamp}.png`;
    link.href = canvas.toDataURL('image/png', 1.0);

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(`PNG exported successfully! (${nodes.length} components, ${edges.length} connections)`);

  } catch (error) {
    console.error('Export failed:', error);
    toast.error('Export failed. Please try again or check the browser console.');
  }
};