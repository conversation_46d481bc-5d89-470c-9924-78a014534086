import { supabase } from '@/lib/supabase';

/**
 * Verify that all medium questions (11-30) have proper FR/NFR separation
 */
export const verifyFRNFRMigration = async () => {
  try {
    console.log('🔍 Verifying FR/NFR migration for questions 11-30...');
    
    const { data: questions, error } = await supabase
      .from('questions')
      .select('id, title, functional_requirements, non_functional_requirements, constraints')
      .gte('id', '11')
      .lte('id', '30')
      .order('id');
    
    if (error) {
      console.error('❌ Error fetching questions:', error);
      return { success: false, error: error.message };
    }
    
    console.log(`✅ Found ${questions.length} questions to verify`);
    
    const results = questions.map(q => {
      const hasFR = q.functional_requirements && q.functional_requirements.length > 0;
      const hasNFR = q.non_functional_requirements && q.non_functional_requirements.length > 0;
      const hasConstraints = q.constraints && q.constraints.length > 0;
      
      return {
        id: q.id,
        title: q.title,
        hasFR,
        hasNFR,
        hasConstraints,
        frCount: q.functional_requirements?.length || 0,
        nfrCount: q.non_functional_requirements?.length || 0,
        constraintsCount: q.constraints?.length || 0,
        isComplete: hasFR && hasNFR && hasConstraints
      };
    });
    
    // Summary statistics
    const totalQuestions = results.length;
    const completeQuestions = results.filter(r => r.isComplete).length;
    const questionsWithFR = results.filter(r => r.hasFR).length;
    const questionsWithNFR = results.filter(r => r.hasNFR).length;
    const questionsWithConstraints = results.filter(r => r.hasConstraints).length;
    
    console.log('\n📊 Migration Summary:');
    console.log(`Total Questions: ${totalQuestions}`);
    console.log(`Complete (FR + NFR + Constraints): ${completeQuestions}/${totalQuestions}`);
    console.log(`With Functional Requirements: ${questionsWithFR}/${totalQuestions}`);
    console.log(`With Non-Functional Requirements: ${questionsWithNFR}/${totalQuestions}`);
    console.log(`With Constraints: ${questionsWithConstraints}/${totalQuestions}`);
    
    console.log('\n📋 Detailed Results:');
    results.forEach(r => {
      const status = r.isComplete ? '✅' : '⚠️';
      console.log(`${status} Q${r.id}: ${r.title}`);
      console.log(`   FR: ${r.frCount} items, NFR: ${r.nfrCount} items, Constraints: ${r.constraintsCount} items`);
    });
    
    // Identify incomplete questions
    const incompleteQuestions = results.filter(r => !r.isComplete);
    if (incompleteQuestions.length > 0) {
      console.log('\n⚠️ Incomplete Questions:');
      incompleteQuestions.forEach(q => {
        const missing = [];
        if (!q.hasFR) missing.push('FR');
        if (!q.hasNFR) missing.push('NFR');
        if (!q.hasConstraints) missing.push('Constraints');
        console.log(`   Q${q.id}: Missing ${missing.join(', ')}`);
      });
    }
    
    return {
      success: true,
      summary: {
        total: totalQuestions,
        complete: completeQuestions,
        withFR: questionsWithFR,
        withNFR: questionsWithNFR,
        withConstraints: questionsWithConstraints
      },
      results,
      incompleteQuestions
    };
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Test a specific question's FR/NFR display
 */
export const testQuestionDisplay = async (questionId: string) => {
  try {
    console.log(`🔍 Testing question ${questionId} display...`);
    
    const { data: question, error } = await supabase
      .from('questions')
      .select('*')
      .eq('id', questionId)
      .single();
    
    if (error) {
      console.error('❌ Error fetching question:', error);
      return { success: false, error: error.message };
    }
    
    console.log(`✅ Question: ${question.title}`);
    
    // Test the same logic as ProblemContext.tsx
    const getRequirements = (q: any) => {
      if (q.functional_requirements && q.non_functional_requirements) {
        return {
          functionalReqs: q.functional_requirements,
          nonFunctionalReqs: q.non_functional_requirements,
          source: 'explicit_columns'
        };
      }
      
      // Fallback logic would go here
      return {
        functionalReqs: q.requirements || [],
        nonFunctionalReqs: [],
        source: 'legacy_fallback'
      };
    };
    
    const { functionalReqs, nonFunctionalReqs, source } = getRequirements(question);
    
    console.log(`📊 Data source: ${source}`);
    console.log(`📋 Functional Requirements (${functionalReqs.length}):`);
    functionalReqs.forEach((req: string, i: number) => {
      console.log(`   ${i + 1}. ${req}`);
    });
    
    console.log(`⚡ Non-Functional Requirements (${nonFunctionalReqs.length}):`);
    nonFunctionalReqs.forEach((req: string, i: number) => {
      console.log(`   ${i + 1}. ${req}`);
    });
    
    if (question.constraints && question.constraints.length > 0) {
      console.log(`🔒 Technical Constraints (${question.constraints.length}):`);
      question.constraints.forEach((constraint: string, i: number) => {
        console.log(`   ${i + 1}. ${constraint}`);
      });
    }
    
    return {
      success: true,
      question,
      functionalReqs,
      nonFunctionalReqs,
      constraints: question.constraints || [],
      source
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Console helper to run all verification tests
 */
export const runAllVerificationTests = async () => {
  console.log('🚀 Starting comprehensive FR/NFR verification...\n');
  
  // Step 1: Verify migration
  const migrationResult = await verifyFRNFRMigration();
  if (!migrationResult.success) {
    console.log('❌ Migration verification failed, stopping tests');
    return;
  }
  
  // Step 2: Test a few specific questions
  const testQuestions = ['11', '26', '30']; // Twitter Feed, Google Drive, Instagram Reels
  
  for (const qId of testQuestions) {
    console.log(`\n${'='.repeat(50)}`);
    await testQuestionDisplay(qId);
  }
  
  console.log(`\n${'='.repeat(50)}`);
  console.log('🎉 All verification tests completed!');
  
  if (migrationResult.summary.complete === migrationResult.summary.total) {
    console.log('✅ All questions have complete FR/NFR separation!');
  } else {
    console.log(`⚠️ ${migrationResult.summary.total - migrationResult.summary.complete} questions need attention`);
  }
};
