import { supabase } from '@/lib/supabase';

interface QuestionData {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  category: string;
  tags: string[];
  requirements: string[];
  constraints: string[];
  hints?: string[];
  sample_solution?: string;
}

// Remaining medium questions (23-30)
const remainingMediumQuestions: QuestionData[] = [
  {
    id: "23",
    title: "Spotify Connect",
    description: "Design a system for seamless multi-device audio streaming and synchronization.",
    difficulty: "medium",
    category: "system-design",
    tags: ["spotify", "multi-device", "streaming", "synchronization"],
    requirements: [
      "Stream music to speakers, TVs, or cars",
      "Synchronize playback across devices",
      "Adjust audio quality based on bandwidth",
      "Support session handoff (e.g., phone → speaker)"
    ],
    constraints: [
      "Stream latency < 500ms",
      "Handle 10M+ active streams",
      "99.9% playback continuity",
      "Audio codecs: Use Ogg Vorbis or AAC for compression",
      "Device discovery: Use mDNS/DNS-SD for local networks",
      "Session management: Track device state in Redis",
      "DRM compliance: Encrypt streams for licensed content"
    ]
  },
  {
    id: "24",
    title: "Twitter Search",
    description: "Implement a search system for tweets, including trending topics and autocomplete suggestions.",
    difficulty: "medium",
    category: "system-design",
    tags: ["search", "trending", "autocomplete", "twitter"],
    requirements: [
      "Search tweets by keywords, hashtags, or users",
      "Display trending topics (local/global)",
      "Autocomplete search queries",
      "Filter by time, location, or language"
    ],
    constraints: [
      "Search latency < 300ms",
      "Handle 10k+ queries per second",
      "99.9% relevance in results",
      "Indexing: Use Elasticsearch for full-text search",
      "Trending detection: Aggregate tweets with Storm/Kafka",
      "Query parsing: Handle typos with fuzzy matching",
      "Caching: Cache popular searches in Memcached"
    ]
  },
  {
    id: "25",
    title: "Amazon SQS",
    description: "Design a message queue system for decoupling microservices, ensuring at-least-once delivery.",
    difficulty: "medium",
    category: "system-design",
    tags: ["message-queue", "microservices", "distributed-systems", "aws"],
    requirements: [
      "Send/receive messages between services",
      "Support FIFO ordering",
      "Handle message retries and dead-letter queues",
      "Provide visibility timeouts"
    ],
    constraints: [
      "Message latency < 100ms",
      "Handle 1M+ messages per second",
      "99.99% message durability",
      "Storage: Use a distributed ledger (e.g., Amazon DynamoDB)",
      "Retry logic: Exponential backoff for failed deliveries",
      "Throttling: Limit message rates per consumer",
      "Batching: Group messages for efficiency"
    ]
  },
  {
    id: "26",
    title: "Google Drive",
    description: "Build a cloud storage system for files, including sharing, versioning, and synchronization.",
    difficulty: "medium",
    category: "system-design",
    tags: ["cloud-storage", "file-sharing", "versioning", "synchronization"],
    requirements: [
      "Upload/download files (up to 10GB)",
      "Share files/folders with permissions",
      "Track edits with version history",
      "Sync changes across devices"
    ],
    constraints: [
      "Upload/download latency < 1s",
      "Support 1B+ files per day",
      "99.99% data durability",
      "Storage: Use a distributed file system (e.g., Google Colossus)",
      "Sync algorithm: Use rsync-like delta encoding",
      "Permissions: Enforce ACLs (access control lists)",
      "Deduplication: Store unique files once"
    ]
  },
  {
    id: "27",
    title: "Facebook Messenger",
    description: "Design a messaging platform with group chats, integrations (e.g., bots), and cross-platform sync.",
    difficulty: "medium",
    category: "system-design",
    tags: ["messaging", "group-chat", "bots", "cross-platform"],
    requirements: [
      "Support 1:1 and group chats (up to 250 users)",
      "Integrate third-party bots (e.g., weather, shopping)",
      "Sync conversations across web/mobile",
      "Send read receipts"
    ],
    constraints: [
      "Message delivery < 500ms",
      "Handle 100M+ daily active users",
      "99.95% message persistence",
      "Bots: Use a webhook API for integration",
      "Group chats: Use a pub/sub model for fan-out",
      "Search: Index messages with Elasticsearch",
      "Compliance: Encrypt data at rest (e.g., AES-256)"
    ]
  },
  {
    id: "28",
    title: "Netflix Playlist",
    description: "Create a system for video streaming, resume playback, and personalized recommendations.",
    difficulty: "medium",
    category: "system-design",
    tags: ["video-streaming", "recommendations", "playback", "netflix"],
    requirements: [
      "Stream videos at multiple bitrates",
      "Resume playback from last position",
      "Generate recommendations (e.g., 'Because you watched X')",
      "Support multiple profiles per account"
    ],
    constraints: [
      "Stream latency < 1s",
      "Handle 100M+ concurrent streams",
      "99.9% recommendation accuracy",
      "Adaptive streaming: Use DASH or HLS for bitrate switching",
      "Recommendations: Use collaborative filtering (e.g., Spark MLlib)",
      "DRM: Implement Widevine/PlayReady for piracy protection",
      "A/B testing: Dynamically adjust recommendation algorithms"
    ]
  },
  {
    id: "29",
    title: "Twitter Notifications",
    description: "Design a pub/sub system for real-time notifications (likes, retweets, mentions).",
    difficulty: "medium",
    category: "system-design",
    tags: ["notifications", "pub-sub", "real-time", "twitter"],
    requirements: [
      "Trigger notifications for user actions",
      "Support email, push, and in-app alerts",
      "Track read/unread status",
      "Filter spam notifications"
    ],
    constraints: [
      "Notification latency < 500ms",
      "Handle 1M+ notifications per second",
      "99.9% delivery success rate",
      "Pub/sub: Use Kafka for event streaming",
      "Fan-out: Deliver notifications via Firebase Cloud Messaging (FCM)",
      "Deduplication: Track unique notifications in Redis",
      "Priority queues: Process urgent notifications first"
    ]
  },
  {
    id: "30",
    title: "Instagram Reels",
    description: "Build a short-video sharing platform with recommendations, duets, and analytics.",
    difficulty: "medium",
    category: "system-design",
    tags: ["short-video", "recommendations", "duets", "analytics"],
    requirements: [
      "Upload 15-60 second videos with effects",
      "Recommend Reels based on user interests",
      "Support duets (side-by-side videos)",
      "Track views, likes, and shares"
    ],
    constraints: [
      "Reel load latency < 1s",
      "Handle 100M+ Reel views per hour",
      "99.9% recommendation relevance",
      "Video processing: Use FFmpeg for transcoding",
      "Recommendations: Use a hybrid model (collaborative filtering + content-based)",
      "Duets: Store composite videos in a distributed file system",
      "Moderation: Use AI for automated content review"
    ]
  }
];

/**
 * Add remaining questions to the database
 */
export const insertRemainingMediumQuestions = async (): Promise<{ success: boolean; error?: string; inserted: number }> => {
  try {
    let insertedCount = 0;
    
    for (const question of remainingMediumQuestions) {
      const { error } = await supabase
        .from('questions')
        .insert([question]);
      
      if (error) {
        console.error(`Error inserting question ${question.id}:`, error);
        return { success: false, error: `Failed to insert question ${question.id}: ${error.message}`, inserted: insertedCount };
      }
      
      insertedCount++;
      console.log(`✅ Inserted question ${question.id}: ${question.title}`);
    }
    
    return { success: true, inserted: insertedCount };
  } catch (error) {
    console.error('Error inserting questions:', error);
    return { success: false, error: 'Failed to insert questions', inserted: 0 };
  }
};

/**
 * Insert all medium questions (combines both batches)
 */
export const insertAllMediumQuestions = async (): Promise<{ success: boolean; error?: string; inserted: number }> => {
  try {
    const result = await insertRemainingMediumQuestions();
    return result;
  } catch (error) {
    console.error('Error inserting all questions:', error);
    return { success: false, error: 'Failed to insert questions', inserted: 0 };
  }
};
