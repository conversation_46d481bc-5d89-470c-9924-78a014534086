import { SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Utility functions for design synchronization
 */

// Check if localStorage has stale data compared to a reference timestamp
export const isLocalStorageStale = (
  designKey: string,
  referenceTimestamp: string
): boolean => {
  try {
    const localDataJson = localStorage.getItem(designKey);
    if (!localDataJson) return false;

    const localData = JSON.parse(localDataJson) as SavedDesign;
    const localTimestamp = new Date(localData.lastModified).getTime();
    const refTimestamp = new Date(referenceTimestamp).getTime();

    return refTimestamp > localTimestamp;
  } catch (error) {
    console.error('Error checking localStorage staleness:', error);
    return false;
  }
};

// Clear localStorage for a specific design
export const clearLocalStorageDesign = (designKey: string): boolean => {
  try {
    localStorage.removeItem(designKey);
    console.log(`✅ Cleared localStorage for key: ${designKey}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to clear localStorage for key ${designKey}:`, error);
    return false;
  }
};

// Get all design keys from localStorage
export const getAllDesignKeys = (): string[] => {
  const keys: string[] = [];
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('layrs-design-')) {
        keys.push(key);
      }
    }
  } catch (error) {
    console.error('Error getting design keys:', error);
  }
  return keys;
};

// Clear all design data from localStorage
export const clearAllDesignData = (): number => {
  const keys = getAllDesignKeys();
  let cleared = 0;
  
  keys.forEach(key => {
    if (clearLocalStorageDesign(key)) {
      cleared++;
    }
  });
  
  console.log(`🧹 Cleared ${cleared} design entries from localStorage`);
  return cleared;
};

// Get design info from localStorage without parsing the full data
export const getDesignInfo = (designKey: string): {
  exists: boolean;
  lastModified?: string;
  questionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  size?: number;
} => {
  try {
    const localDataJson = localStorage.getItem(designKey);
    if (!localDataJson) {
      return { exists: false };
    }

    const localData = JSON.parse(localDataJson) as SavedDesign;
    return {
      exists: true,
      lastModified: localData.lastModified,
      questionId: localData.questionId,
      contextType: localData.contextType,
      contextId: localData.contextId,
      size: new Blob([localDataJson]).size
    };
  } catch (error) {
    console.error(`Error getting design info for ${designKey}:`, error);
    return { exists: false };
  }
};

// Generate design key from components
export const generateDesignKey = (
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): string => {
  const id = contextId || questionId;
  return `layrs-design-${contextType}-${id}`;
};

// Debug function to show all localStorage design data
export const debugLocalStorageDesigns = (): void => {
  console.log('🔍 LocalStorage Design Debug Info:');
  
  const keys = getAllDesignKeys();
  console.log(`📊 Found ${keys.length} design entries`);
  
  keys.forEach(key => {
    const info = getDesignInfo(key);
    console.log(`📄 ${key}:`, {
      lastModified: info.lastModified,
      questionId: info.questionId,
      contextType: info.contextType,
      contextId: info.contextId,
      sizeKB: info.size ? Math.round(info.size / 1024 * 100) / 100 : 0
    });
  });
  
  // Calculate total storage used
  const totalSize = keys.reduce((total, key) => {
    const info = getDesignInfo(key);
    return total + (info.size || 0);
  }, 0);
  
  console.log(`💾 Total storage used: ${Math.round(totalSize / 1024 * 100) / 100} KB`);
};

// Expose debug functions globally
if (typeof window !== 'undefined') {
  (window as any).layrsDesignSync = {
    debugLocalStorageDesigns,
    clearAllDesignData,
    getAllDesignKeys,
    getDesignInfo,
    clearLocalStorageDesign,
    isLocalStorageStale,
    generateDesignKey
  };
}
