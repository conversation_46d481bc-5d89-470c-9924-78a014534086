import html2canvas from 'html2canvas';
import { toast } from 'sonner';

/**
 * Export React Flow diagram as PNG using html2canvas
 */
export const exportToPNG = async (reactFlowInstance: any) => {
  if (!reactFlowInstance) {
    toast.error("No diagram to export. Please add components to the canvas first.");
    return;
  }

  try {
    toast.loading('Exporting PNG...');

    // Get the React Flow container
    const target = document.querySelector('.react-flow') as HTMLElement;
    if (!target) {
      toast.error('Diagram container not found.');
      return;
    }

    // Fit the view to show all content with padding
    reactFlowInstance.fitView({
      padding: 0.1, // 10% padding around content
      duration: 0   // No animation
    });

    // Wait for the view to update
    await new Promise(resolve => setTimeout(resolve, 500));

    // Make sure all edges are visible
    const edgeElements = document.querySelectorAll('.react-flow__edge');
    edgeElements.forEach(edge => {
      (edge as HTMLElement).style.visibility = 'visible';
      (edge as HTMLElement).style.opacity = '1';
    });

    // Make edge paths more visible
    const edgePaths = document.querySelectorAll('.react-flow__edge-path');
    edgePaths.forEach(path => {
      (path as SVGPathElement).style.stroke = '#333333';
      (path as SVGPathElement).style.strokeWidth = '2';
      (path as SVGPathElement).style.visibility = 'visible';
      (path as SVGPathElement).style.opacity = '1';
    });

    // Capture the canvas
    const canvas = await html2canvas(target, {
      backgroundColor: '#ffffff',
      scale: 2,
      logging: false,
      useCORS: true,
      allowTaint: false,
      ignoreElements: (element) => {
        // Ignore UI controls but keep diagram content
        if (element.classList && (
          element.classList.contains('react-flow__controls') ||
          element.classList.contains('react-flow__minimap') ||
          element.classList.contains('react-flow__panel') ||
          element.classList.contains('react-flow__attribution')
        )) {
          return true;
        }
        return false;
      }
    });

    // Create download link
    const link = document.createElement('a');
    link.download = `system-design-${Date.now()}.png`;
    link.href = canvas.toDataURL('image/png', 1.0);

    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('PNG exported successfully!');

  } catch (error) {
    console.error('Export failed:', error);
    toast.error('Export failed. Please try again.');
  }
};
