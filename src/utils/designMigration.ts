/**
 * Utility functions to help migrate existing designs to the new context-based format
 */

import { DesignContextCategory, SavedDesign } from '@/contexts/DesignContext';

/**
 * Migrates a design to include context information if it doesn't already have it
 * @param design The design to migrate
 * @param contextType The context type to use if not already present
 * @param contextId The context ID to use if not already present
 * @returns The migrated design
 */
export const migrateDesign = (
  design: SavedDesign,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): SavedDesign => {
  // If the design already has context information, return it as is
  if (design.contextType && design.contextId) {
    return design;
  }

  // Otherwise, add the context information
  return {
    ...design,
    contextType: contextType,
    contextId: contextId || design.questionId
  };
};

/**
 * Migrates all designs in localStorage to include context information
 * This should be run once when the app starts
 */
export const migrateLocalStorageDesigns = (): void => {
  console.log('Migrating localStorage designs to include context information');
  
  try {
    // Find all designs in localStorage
    const designKeys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('layrs-design-') && !key.includes('-question-') && !key.includes('-course-') && !key.includes('-free-')) {
        designKeys.push(key);
      }
    }
    
    console.log(`Found ${designKeys.length} designs to migrate`);
    
    // Migrate each design
    for (const key of designKeys) {
      try {
        const designJson = localStorage.getItem(key);
        if (designJson) {
          const design = JSON.parse(designJson) as SavedDesign;
          
          // Determine the context type and ID
          // By default, assume it's a question design
          const contextType: DesignContextCategory = 'question';
          const contextId = design.questionId;
          
          // Migrate the design
          const migratedDesign = migrateDesign(design, contextType, contextId);
          
          // Create a new key with the context information
          const newKey = `layrs-design-${contextType}-${contextId}`;
          
          // Save the migrated design with the new key
          localStorage.setItem(newKey, JSON.stringify(migratedDesign));
          
          // Remove the old design
          localStorage.removeItem(key);
          
          console.log(`Migrated design ${key} to ${newKey}`);
        }
      } catch (e) {
        console.error(`Error migrating design ${key}:`, e);
      }
    }
    
    console.log('Design migration complete');
  } catch (e) {
    console.error('Error migrating designs:', e);
  }
};
