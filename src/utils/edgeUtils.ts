
import { Node } from '@xyflow/react';

// Interface for node bounds
interface NodeBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

// Function to get node bounds with padding
const getNodeBounds = (node: Node, padding: number = 20): NodeBounds => {
  return {
    x: node.position.x - padding,
    y: node.position.y - padding,
    width: (node.measured?.width || node.width || 150) + (padding * 2),
    height: (node.measured?.height || node.height || 100) + (padding * 2)
  };
};

// Function to check if a point is inside a rectangle
const isPointInRect = (x: number, y: number, rect: NodeBounds): boolean => {
  return x >= rect.x && x <= rect.x + rect.width &&
         y >= rect.y && y <= rect.y + rect.height;
};

// Function to check if a line segment intersects with a rectangle
const lineIntersectsRect = (x1: number, y1: number, x2: number, y2: number, rect: NodeBounds): boolean => {
  // Check if either endpoint is inside the rectangle
  if (isPointInRect(x1, y1, rect) || isPointInRect(x2, y2, rect)) {
    return true;
  }

  // Check if line intersects any of the rectangle's edges
  const left = rect.x;
  const right = rect.x + rect.width;
  const top = rect.y;
  const bottom = rect.y + rect.height;

  // Line intersection logic for horizontal/vertical lines
  if (x1 === x2) { // Vertical line
    return x1 >= left && x1 <= right &&
           ((y1 <= top && y2 >= top) || (y1 >= top && y2 <= top) ||
            (y1 <= bottom && y2 >= bottom) || (y1 >= bottom && y2 <= bottom));
  } else if (y1 === y2) { // Horizontal line
    return y1 >= top && y1 <= bottom &&
           ((x1 <= left && x2 >= left) || (x1 >= left && x2 <= left) ||
            (x1 <= right && x2 >= right) || (x1 >= right && x2 <= right));
  }

  return false;
};

// Function to calculate orthogonal path with obstacle avoidance
export const getOrthogonalPath = ({
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  nodes = [],
  sourceNodeId,
  targetNodeId
}: {
  sourceX: number;
  sourceY: number;
  targetX: number;
  targetY: number;
  sourcePosition: string;
  targetPosition: string;
  nodes?: Node[];
  sourceNodeId?: string;
  targetNodeId?: string;
}): [string, number, number] => {
  // Get obstacle nodes (exclude source and target nodes)
  const obstacles = nodes.filter(node =>
    node.id !== sourceNodeId &&
    node.id !== targetNodeId &&
    !node.hidden
  ).map(node => getNodeBounds(node));

  // Define offsets based on handle positions
  let sourceOffsetX = 0, sourceOffsetY = 0;
  let targetOffsetX = 0, targetOffsetY = 0;

  // Distance for the first segment from the source/target
  const segmentLength = 20;

  // Determine source point offset direction based on handle position
  switch (sourcePosition) {
    case 'top':
      sourceOffsetY = -segmentLength;
      break;
    case 'bottom':
      sourceOffsetY = segmentLength;
      break;
    case 'left':
      sourceOffsetX = -segmentLength;
      break;
    case 'right':
      sourceOffsetX = segmentLength;
      break;
  }

  // Determine target point offset direction based on handle position
  switch (targetPosition) {
    case 'top':
      targetOffsetY = -segmentLength;
      break;
    case 'bottom':
      targetOffsetY = segmentLength;
      break;
    case 'left':
      targetOffsetX = -segmentLength;
      break;
    case 'right':
      targetOffsetX = segmentLength;
      break;
  }
  
  // Calculate the points for corners of the orthogonal path
  const sourceCornerX = sourceX + sourceOffsetX;
  const sourceCornerY = sourceY + sourceOffsetY;
  const targetCornerX = targetX + targetOffsetX;
  const targetCornerY = targetY + targetOffsetY;

  // Try to find a path that avoids obstacles
  const pathWithAvoidance = findPathWithObstacleAvoidance(
    sourceX, sourceY, sourceCornerX, sourceCornerY,
    targetX, targetY, targetCornerX, targetCornerY,
    sourcePosition, targetPosition, obstacles
  );

  if (pathWithAvoidance) {
    return pathWithAvoidance;
  }

  // Fallback to simple path if obstacle avoidance fails
  const midX = (sourceCornerX + targetCornerX) / 2;
  const midY = (sourceCornerY + targetCornerY) / 2;

  // Create the SVG path string for the orthogonal connection
  let pathStr = `M${sourceX},${sourceY}`;
  pathStr += ` L${sourceCornerX},${sourceCornerY}`;

  // For horizontal source connections, create horizontal line before vertical
  if (sourcePosition === 'left' || sourcePosition === 'right') {
    pathStr += ` L${midX},${sourceCornerY}`;
    pathStr += ` L${midX},${targetCornerY}`;
  } else {
    pathStr += ` L${sourceCornerX},${midY}`;
    pathStr += ` L${targetCornerX},${midY}`;
  }

  pathStr += ` L${targetCornerX},${targetCornerY}`;
  pathStr += ` L${targetX},${targetY}`;

  const labelX = midX;
  const labelY = midY;

  return [pathStr, labelX, labelY];
};

// Function to find path with obstacle avoidance
const findPathWithObstacleAvoidance = (
  sourceX: number, sourceY: number, sourceCornerX: number, sourceCornerY: number,
  targetX: number, targetY: number, targetCornerX: number, targetCornerY: number,
  sourcePosition: string, targetPosition: string, obstacles: NodeBounds[]
): [string, number, number] | null => {

  // Try different routing strategies
  const strategies = [
    () => tryDirectRoute(sourceX, sourceY, sourceCornerX, sourceCornerY, targetX, targetY, targetCornerX, targetCornerY, sourcePosition, targetPosition, obstacles),
    () => tryAroundRoute(sourceX, sourceY, sourceCornerX, sourceCornerY, targetX, targetY, targetCornerX, targetCornerY, sourcePosition, targetPosition, obstacles, 'top'),
    () => tryAroundRoute(sourceX, sourceY, sourceCornerX, sourceCornerY, targetX, targetY, targetCornerX, targetCornerY, sourcePosition, targetPosition, obstacles, 'bottom'),
    () => tryAroundRoute(sourceX, sourceY, sourceCornerX, sourceCornerY, targetX, targetY, targetCornerX, targetCornerY, sourcePosition, targetPosition, obstacles, 'left'),
    () => tryAroundRoute(sourceX, sourceY, sourceCornerX, sourceCornerY, targetX, targetY, targetCornerX, targetCornerY, sourcePosition, targetPosition, obstacles, 'right')
  ];

  for (const strategy of strategies) {
    const result = strategy();
    if (result) return result;
  }

  return null;
};

// Try direct route (original logic)
const tryDirectRoute = (
  sourceX: number, sourceY: number, sourceCornerX: number, sourceCornerY: number,
  targetX: number, targetY: number, targetCornerX: number, targetCornerY: number,
  sourcePosition: string, targetPosition: string, obstacles: NodeBounds[]
): [string, number, number] | null => {

  const midX = (sourceCornerX + targetCornerX) / 2;
  const midY = (sourceCornerY + targetCornerY) / 2;

  // Check if the path intersects with any obstacles
  const segments = [];
  if (sourcePosition === 'left' || sourcePosition === 'right') {
    segments.push(
      [sourceCornerX, sourceCornerY, midX, sourceCornerY],
      [midX, sourceCornerY, midX, targetCornerY],
      [midX, targetCornerY, targetCornerX, targetCornerY]
    );
  } else {
    segments.push(
      [sourceCornerX, sourceCornerY, sourceCornerX, midY],
      [sourceCornerX, midY, targetCornerX, midY],
      [targetCornerX, midY, targetCornerX, targetCornerY]
    );
  }

  // Check if any segment intersects with obstacles
  for (const [x1, y1, x2, y2] of segments) {
    for (const obstacle of obstacles) {
      if (lineIntersectsRect(x1, y1, x2, y2, obstacle)) {
        return null; // Path blocked
      }
    }
  }

  // Path is clear, create it
  let pathStr = `M${sourceX},${sourceY} L${sourceCornerX},${sourceCornerY}`;

  if (sourcePosition === 'left' || sourcePosition === 'right') {
    pathStr += ` L${midX},${sourceCornerY} L${midX},${targetCornerY}`;
  } else {
    pathStr += ` L${sourceCornerX},${midY} L${targetCornerX},${midY}`;
  }

  pathStr += ` L${targetCornerX},${targetCornerY} L${targetX},${targetY}`;

  return [pathStr, midX, midY];
};

// Try routing around obstacles
const tryAroundRoute = (
  sourceX: number, sourceY: number, sourceCornerX: number, sourceCornerY: number,
  targetX: number, targetY: number, targetCornerX: number, targetCornerY: number,
  sourcePosition: string, targetPosition: string, obstacles: NodeBounds[], direction: 'top' | 'bottom' | 'left' | 'right'
): [string, number, number] | null => {

  // Find the bounds of all obstacles to determine detour points
  if (obstacles.length === 0) return null;

  let detourOffset = 50; // Distance to go around obstacles
  let detourX = (sourceCornerX + targetCornerX) / 2;
  let detourY = (sourceCornerY + targetCornerY) / 2;

  // Calculate detour point based on direction
  switch (direction) {
    case 'top':
      detourY = Math.min(...obstacles.map(o => o.y)) - detourOffset;
      break;
    case 'bottom':
      detourY = Math.max(...obstacles.map(o => o.y + o.height)) + detourOffset;
      break;
    case 'left':
      detourX = Math.min(...obstacles.map(o => o.x)) - detourOffset;
      break;
    case 'right':
      detourX = Math.max(...obstacles.map(o => o.x + o.width)) + detourOffset;
      break;
  }

  // Create path segments for the detour
  const segments = [];
  if (sourcePosition === 'left' || sourcePosition === 'right') {
    if (direction === 'top' || direction === 'bottom') {
      segments.push(
        [sourceCornerX, sourceCornerY, detourX, sourceCornerY],
        [detourX, sourceCornerY, detourX, detourY],
        [detourX, detourY, detourX, targetCornerY],
        [detourX, targetCornerY, targetCornerX, targetCornerY]
      );
    } else {
      segments.push(
        [sourceCornerX, sourceCornerY, detourX, sourceCornerY],
        [detourX, sourceCornerY, detourX, targetCornerY],
        [detourX, targetCornerY, targetCornerX, targetCornerY]
      );
    }
  } else {
    if (direction === 'left' || direction === 'right') {
      segments.push(
        [sourceCornerX, sourceCornerY, sourceCornerX, detourY],
        [sourceCornerX, detourY, detourX, detourY],
        [detourX, detourY, targetCornerX, detourY],
        [targetCornerX, detourY, targetCornerX, targetCornerY]
      );
    } else {
      segments.push(
        [sourceCornerX, sourceCornerY, sourceCornerX, detourY],
        [sourceCornerX, detourY, targetCornerX, detourY],
        [targetCornerX, detourY, targetCornerX, targetCornerY]
      );
    }
  }

  // Check if detour path intersects with obstacles
  for (const [x1, y1, x2, y2] of segments) {
    for (const obstacle of obstacles) {
      if (lineIntersectsRect(x1, y1, x2, y2, obstacle)) {
        return null; // Detour path also blocked
      }
    }
  }

  // Create the detour path
  let pathStr = `M${sourceX},${sourceY} L${sourceCornerX},${sourceCornerY}`;

  for (const [x1, y1, x2, y2] of segments) {
    pathStr += ` L${x2},${y2}`;
  }

  pathStr += ` L${targetX},${targetY}`;

  // Label position is at the detour point
  return [pathStr, detourX, detourY];
};
