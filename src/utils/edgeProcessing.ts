import { Edge, MarkerType, Node } from '@xyflow/react';

/**
 * Processes loaded edges to add the onChange handler for label editing
 * This ensures that edges loaded from saved designs can have their labels edited
 * and have consistent styling with newly created edges
 * Also deduplicates edges by id to prevent duplicate keys in React.
 */
export const processLoadedEdges = (
  edges: Edge[],
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>,
  nodes: Node[] = []
): Edge[] => {
  // Deduplicate edges by id
  const seen = new Set();
  const uniqueEdges = edges.filter(edge => {
    if (seen.has(edge.id)) return false;
    seen.add(edge.id);
    return true;
  });

  // Create the updateEdgeLabel function using the provided setEdges
  const updateEdgeLabel = (edgeId: string, newLabel: string) => {
    console.log(`Updating edge ${edgeId} label to: ${newLabel}`);
    setEdges(edges =>
      edges.map(edge => {
        if (edge.id === edgeId) {
          return {
            ...edge,
            data: { ...edge.data, connectionNumber: newLabel },
            label: newLabel
          };
        }
        return edge;
      })
    );
  };

  return uniqueEdges.map(edge => {
    // Determine if this is an internal connection (both nodes have the same parent)
    const isInternal = edge.data?.isInternal || false;

    // Convert old 'custom' edge type to 'smooth' for backward compatibility
    let edgeType = edge.type || 'smooth';
    if (edgeType === 'custom') {
      edgeType = 'smooth';
    }

    // Handle legacy edge types by converting them to the new format
    const legacyEdgeTypes = ['dashed', 'dotted', 'thick', 'doubleArrow'];
    if (legacyEdgeTypes.includes(edgeType)) {
      // Convert legacy types to new format (default to bezier path)
      edgeType = `${edgeType}-bezier`;
    }

    // Ensure consistent styling with newly created edges
    const normalizedEdge = {
      ...edge,
      // Use the converted edge type
      type: edgeType,
      // Normalize marker styling
      markerEnd: edge.markerEnd || {
        type: MarkerType.ArrowClosed,
        width: isInternal ? 15 : 20,
        height: isInternal ? 15 : 20,
        color: '#222',
      },
      // Normalize edge styling to match newly created edges
      style: {
        strokeWidth: isInternal ? 2.5 : 2,
        stroke: '#222',
        filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.3))',
        zIndex: isInternal ? 1000 : 0,
        // Preserve any additional styling from the saved edge
        ...edge.style,
      },
      // Normalize label styling
      labelStyle: edge.labelStyle || {
        fill: '#222',
        fontWeight: 700,
        fontSize: 12
      },
      labelBgStyle: edge.labelBgStyle || {
        fill: 'white',
        fillOpacity: 0.8,
        stroke: '#ccc',
        strokeWidth: 1
      },
      labelBgPadding: edge.labelBgPadding || [40, 20] as [number, number],
      data: {
        ...edge.data,
        onChange: updateEdgeLabel, // Add the onChange handler for in-place editing
        nodes: nodes // Pass nodes for obstacle avoidance
      }
    };

    return normalizedEdge;
  });
};
