import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Utility for managing localStorage operations with error handling and cleanup
 */

export interface LocalStorageItem<T = any> {
  data: T;
  timestamp: number;
  version?: string;
}

export class LocalStorageManager {
  private static readonly VERSION = '1.0';
  private static readonly MAX_STORAGE_SIZE = 5 * 1024 * 1024; // 5MB limit

  /**
   * Save data to localStorage with metadata
   */
  static save<T>(key: string, data: T): boolean {
    try {
      const item: LocalStorageItem<T> = {
        data,
        timestamp: Date.now(),
        version: this.VERSION
      };

      const serialized = JSON.stringify(item);
      
      // Check if we're approaching storage limits
      if (this.getStorageSize() + serialized.length > this.MAX_STORAGE_SIZE) {
        debugWarn('localStorage approaching size limit, cleaning up old entries');
        this.cleanupOldEntries();
      }

      localStorage.setItem(key, serialized);
      debugLog(`💾 Saved to localStorage: ${key} (${this.formatBytes(serialized.length)})`);
      return true;
    } catch (error) {
      debugError(`❌ Failed to save to localStorage: ${key}`, error);
      
      // If quota exceeded, try cleanup and retry once
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        this.cleanupOldEntries();
        try {
          const item: LocalStorageItem<T> = {
            data,
            timestamp: Date.now(),
            version: this.VERSION
          };
          localStorage.setItem(key, JSON.stringify(item));
          debugLog(`💾 Saved to localStorage after cleanup: ${key}`);
          return true;
        } catch (retryError) {
          debugError(`❌ Failed to save even after cleanup: ${key}`, retryError);
        }
      }
      return false;
    }
  }

  /**
   * Load data from localStorage with validation
   */
  static load<T>(key: string): T | null {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) return null;

      const item: LocalStorageItem<T> = JSON.parse(stored);
      
      // Validate structure
      if (!item || typeof item !== 'object' || !('data' in item) || !('timestamp' in item)) {
        debugWarn(`Invalid localStorage item structure for key: ${key}`);
        this.remove(key);
        return null;
      }

      return item.data;
    } catch (error) {
      debugError(`❌ Failed to load from localStorage: ${key}`, error);
      // Remove corrupted data
      this.remove(key);
      return null;
    }
  }

  /**
   * Get metadata for a localStorage item
   */
  static getMetadata(key: string): { timestamp: number; version?: string } | null {
    try {
      const stored = localStorage.getItem(key);
      if (!stored) return null;

      const item: LocalStorageItem = JSON.parse(stored);
      return {
        timestamp: item.timestamp,
        version: item.version
      };
    } catch (error) {
      debugError(`❌ Failed to get metadata for: ${key}`, error);
      return null;
    }
  }

  /**
   * Remove item from localStorage
   */
  static remove(key: string): boolean {
    try {
      localStorage.removeItem(key);
      debugLog(`🗑️ Removed from localStorage: ${key}`);
      return true;
    } catch (error) {
      debugError(`❌ Failed to remove from localStorage: ${key}`, error);
      return false;
    }
  }

  /**
   * Check if an item exists in localStorage
   */
  static exists(key: string): boolean {
    return localStorage.getItem(key) !== null;
  }

  /**
   * Get all keys matching a pattern
   */
  static getKeys(pattern?: string): string[] {
    const keys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (!pattern || key.includes(pattern))) {
        keys.push(key);
      }
    }
    return keys;
  }

  /**
   * Clean up old entries to free space
   */
  private static cleanupOldEntries(): void {
    const layrsKeys = this.getKeys('layrs-');
    const itemsWithAge = layrsKeys
      .map(key => {
        const metadata = this.getMetadata(key);
        return {
          key,
          age: metadata ? Date.now() - metadata.timestamp : 0
        };
      })
      .sort((a, b) => b.age - a.age); // Sort by age, oldest first

    // Remove oldest 25% of items
    const itemsToRemove = Math.ceil(itemsWithAge.length * 0.25);
    for (let i = 0; i < itemsToRemove; i++) {
      this.remove(itemsWithAge[i].key);
    }

    debugLog(`🧹 Cleaned up ${itemsToRemove} old localStorage entries`);
  }

  /**
   * Get current localStorage usage
   */
  private static getStorageSize(): number {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total;
  }

  /**
   * Format bytes for human-readable display
   */
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get storage usage statistics
   */
  static getStorageStats(): {
    totalSize: number;
    layrsSize: number;
    itemCount: number;
    layrsItemCount: number;
  } {
    const totalSize = this.getStorageSize();
    const layrsKeys = this.getKeys('layrs-');
    
    let layrsSize = 0;
    layrsKeys.forEach(key => {
      const item = localStorage.getItem(key);
      if (item) {
        layrsSize += item.length + key.length;
      }
    });

    return {
      totalSize,
      layrsSize,
      itemCount: localStorage.length,
      layrsItemCount: layrsKeys.length
    };
  }
}
