
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { toast } from 'sonner';

/**
 * Helper function to add text with bold formatting to PDF
 * Simplified approach: convert **text** to UPPERCASE for emphasis in PDF
 */
const addTextWithBoldToPDF = (pdf: jsPDF, text: string, x: number, yPosition: number, maxWidth: number): number => {
  if (!text) return yPosition;

  // For PDF, we'll convert **bold** text to UPPERCASE for emphasis
  // since inline bold formatting is complex in jsPDF
  const processedText = text.replace(/\*\*(.*?)\*\*/g, (_, boldText) => {
    return boldText.toUpperCase();
  });

  const textLines = pdf.splitTextToSize(processedText, maxWidth);
  pdf.text(textLines, x, yPosition);
  return yPosition + (textLines.length * 4);
};

// Function to export React Flow as SVG and convert to PDF
const exportReactFlowAsSVG = async (reactFlowInstance: any): Promise<string> => {
  // Get all nodes and edges
  const nodes = reactFlowInstance.getNodes();
  const edges = reactFlowInstance.getEdges();
  const viewport = reactFlowInstance.getViewport();

  // Calculate bounds of all elements
  let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

  nodes.forEach((node: any) => {
    const x = node.position.x;
    const y = node.position.y;
    const width = node.width || 150;
    const height = node.height || 50;

    minX = Math.min(minX, x);
    minY = Math.min(minY, y);
    maxX = Math.max(maxX, x + width);
    maxY = Math.max(maxY, y + height);
  });

  // Add padding
  const padding = 50;
  minX -= padding;
  minY -= padding;
  maxX += padding;
  maxY += padding;

  const width = maxX - minX;
  const height = maxY - minY;

  // Create SVG string
  let svgContent = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                refX="9" refY="3.5" orient="auto">
          <polygon points="0 0, 10 3.5, 0 7" fill="#222" />
        </marker>
      </defs>
      <rect width="100%" height="100%" fill="white"/>
  `;

  // Add edges first (so they appear behind nodes)
  edges.forEach((edge: any) => {
    const sourceNode = nodes.find((n: any) => n.id === edge.source);
    const targetNode = nodes.find((n: any) => n.id === edge.target);

    if (sourceNode && targetNode) {
      const sourceX = sourceNode.position.x + (sourceNode.width || 150) / 2 - minX;
      const sourceY = sourceNode.position.y + (sourceNode.height || 50) / 2 - minY;
      const targetX = targetNode.position.x + (targetNode.width || 150) / 2 - minX;
      const targetY = targetNode.position.y + (targetNode.height || 50) / 2 - minY;

      // Simple straight line for now
      svgContent += `
        <line x1="${sourceX}" y1="${sourceY}" x2="${targetX}" y2="${targetY}"
              stroke="#222" stroke-width="2" marker-end="url(#arrowhead)" />
      `;

      // Add label if exists
      if (edge.label) {
        const labelX = (sourceX + targetX) / 2;
        const labelY = (sourceY + targetY) / 2;
        svgContent += `
          <rect x="${labelX - 20}" y="${labelY - 10}" width="40" height="20"
                fill="white" stroke="#ccc" stroke-width="1" rx="4" />
          <text x="${labelX}" y="${labelY + 4}" text-anchor="middle"
                font-family="Arial" font-size="12" font-weight="bold" fill="#222">
            ${edge.label}
          </text>
        `;
      }
    }
  });

  // Add nodes
  nodes.forEach((node: any) => {
    const x = node.position.x - minX;
    const y = node.position.y - minY;
    const width = node.width || 150;
    const height = node.height || 50;

    svgContent += `
      <rect x="${x}" y="${y}" width="${width}" height="${height}"
            fill="white" stroke="#ccc" stroke-width="1" rx="8" />
      <text x="${x + width/2}" y="${y + height/2 + 4}" text-anchor="middle"
            font-family="Arial" font-size="14" font-weight="500" fill="#222">
        ${node.data.label || node.id}
      </text>
    `;
  });

  svgContent += '</svg>';
  return svgContent;
};

/**
 * Helper function to add user context to PDF
 */
const addUserContextToPDF = (pdf: jsPDF, userContext: any, yPosition: number): number => {
  if (!userContext) return yPosition;

  console.log('Adding user context to PDF:', userContext); // Debug log

  pdf.setFontSize(14);
  pdf.setTextColor(0, 0, 0);
  pdf.text('User Context', 10, yPosition);
  yPosition += 8;

  pdf.setFontSize(10);
  pdf.setTextColor(60, 60, 60);

  // Add problem statement
  if (userContext.problem) {
    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Problem:', 10, yPosition);
    yPosition += 5;

    pdf.setFontSize(10);
    pdf.setTextColor(60, 60, 60);
    const problemLines = pdf.splitTextToSize(userContext.problem, 185);
    pdf.text(problemLines, 15, yPosition);
    yPosition += problemLines.length * 4 + 5;
  }

  // Add user journeys if available
  if (userContext.userJourneys && userContext.userJourneys.trim()) {
    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text('User Journeys:', 10, yPosition);
    yPosition += 5;

    pdf.setFontSize(10);
    pdf.setTextColor(60, 60, 60);
    const journeyLines = pdf.splitTextToSize(userContext.userJourneys, 185);
    pdf.text(journeyLines, 15, yPosition);
    yPosition += journeyLines.length * 4 + 5;
  }

  // Add assumptions
  if (userContext.assumptions && userContext.assumptions.length > 0) {
    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Assumptions:', 10, yPosition);
    yPosition += 5;

    pdf.setFontSize(10);
    pdf.setTextColor(60, 60, 60);
    userContext.assumptions.forEach((assumption: string) => {
      if (assumption.trim()) {
        const assumptionLines = pdf.splitTextToSize(`• ${assumption}`, 180);
        pdf.text(assumptionLines, 15, yPosition);
        yPosition += assumptionLines.length * 4;
      }
    });
    yPosition += 5;
  }

  // Add constraints
  if (userContext.constraints && userContext.constraints.length > 0) {
    pdf.setFontSize(11);
    pdf.setTextColor(0, 0, 0);
    pdf.text('Constraints:', 10, yPosition);
    yPosition += 5;

    pdf.setFontSize(10);
    pdf.setTextColor(60, 60, 60);
    userContext.constraints.forEach((constraint: string) => {
      if (constraint.trim()) {
        const constraintLines = pdf.splitTextToSize(`• ${constraint}`, 180);
        pdf.text(constraintLines, 15, yPosition);
        yPosition += constraintLines.length * 4;
      }
    });
    yPosition += 5;
  }

  return yPosition + 5;
};

/**
 * Helper function to add assessment results to PDF
 */
const addAssessmentToPDF = (pdf: jsPDF, assessmentResult: any, yPosition: number): number => {
  if (!assessmentResult) return yPosition;

  console.log('Adding assessment to PDF:', assessmentResult); // Debug log

  // Check if we need a new page
  if (yPosition > 250) {
    pdf.addPage();
    yPosition = 20;
  }

  pdf.setFontSize(14);
  pdf.setTextColor(0, 0, 0);
  pdf.text('Assessment Results', 10, yPosition);
  yPosition += 8;

  pdf.setFontSize(10);

  // Add overall rating from score or validationResponse
  const rating = assessmentResult.score?.rating || assessmentResult.validationResponse?.rating;
  if (rating) {
    pdf.setTextColor(0, 100, 0);
    pdf.text(`Overall Rating: ${rating}/10`, 10, yPosition);
    yPosition += 6;
  }

  // Get assessment data from validationResponse if available, otherwise from root
  const assessmentData = assessmentResult.validationResponse || assessmentResult;

  // Parse and add assessment sections
  const sections = [
    { key: 'overall_assessment', title: 'Overall Assessment', color: [60, 60, 60] },
    { key: 'strengths', title: 'Strengths', color: [0, 120, 0] },
    { key: 'weaknesses', title: 'Weaknesses', color: [180, 0, 0] },
    { key: 'suggestions', title: 'Suggestions', color: [100, 0, 150] },
    { key: 'questions', title: 'Questions', color: [0, 0, 180] }
  ];

  sections.forEach(section => {
    const content = assessmentData[section.key];
    if (content) {
      console.log(`Adding section ${section.title}:`, content); // Debug log

      // Check if we need a new page
      if (yPosition > 240) {
        pdf.addPage();
        yPosition = 20;
      }

      pdf.setTextColor(section.color[0], section.color[1], section.color[2]);
      pdf.setFontSize(12);
      pdf.text(section.title, 10, yPosition);
      yPosition += 6;

      pdf.setTextColor(60, 60, 60);
      pdf.setFontSize(10);

      // Handle both string and array content
      if (typeof content === 'string') {
        // Parse markdown-style headings and format text
        const lines = content.split('\n');
        lines.forEach(line => {
          if (line.trim()) {
            // Check for bold headings (**text**)
            const headingMatch = line.match(/^\*\*(.*?)\*\*$/);
            if (headingMatch) {
              pdf.setFontSize(11);
              pdf.setTextColor(section.color[0], section.color[1], section.color[2]);
              pdf.text(headingMatch[1], 15, yPosition);
              pdf.setFontSize(10);
              pdf.setTextColor(60, 60, 60);
              yPosition += 5;
            } else {
              // Handle inline bold text within paragraphs
              yPosition = addTextWithBoldToPDF(pdf, line, 15, yPosition, 185);
            }
          }
        });
      } else if (Array.isArray(content)) {
        content.forEach((item: string) => {
          // Handle bold text in array items too
          yPosition = addTextWithBoldToPDF(pdf, `• ${item}`, 15, yPosition, 185);
        });
      }
      yPosition += 5;
    }
  });

  // Add scoring explanation if available
  if (assessmentData.scoring_explanation) {
    if (yPosition > 240) {
      pdf.addPage();
      yPosition = 20;
    }

    pdf.setTextColor(60, 60, 60);
    pdf.setFontSize(12);
    pdf.text('Scoring Explanation', 10, yPosition);
    yPosition += 6;

    pdf.setFontSize(10);
    const explanationLines = pdf.splitTextToSize(assessmentData.scoring_explanation, 185);
    pdf.text(explanationLines, 15, yPosition);
    yPosition += explanationLines.length * 4 + 5;
  }

  return yPosition;
};

export const exportToPDF = async (reactFlowInstance: any, userContext?: any, assessmentResult?: any) => {
  if (!reactFlowInstance) {
    toast.error("No diagram to export. Please add components to the canvas first.");
    return;
  }

  // Debug logging
  console.log('PDF Export - User Context:', userContext);
  console.log('PDF Export - Assessment Result:', assessmentResult);

  try {
    // Show a loading toast
    toast.loading("Generating comprehensive PDF...");

    // Try SVG-based export first (better for connections)
    try {
      const svgContent = await exportReactFlowAsSVG(reactFlowInstance);

      // Convert SVG to canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      await new Promise((resolve, reject) => {
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx?.drawImage(img, 0, 0);
          resolve(void 0);
        };
        img.onerror = reject;
        img.src = 'data:image/svg+xml;base64,' + btoa(svgContent);
      });

      // Create PDF from canvas
      const margin = 10;
      const imgWidth = 210 - (margin * 2); // A4 width in mm - margins
      const imgHeight = canvas.height * imgWidth / canvas.width;

      const pdf = new jsPDF('p', 'mm', 'a4');

      // Add metadata
      const date = new Date();
      const dateString = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
      pdf.setProperties({
        title: `System Design - ${dateString}`,
        subject: 'System Architecture Diagram',
        creator: 'Layrs App'
      });

      let currentY = 15;

      // Add title to PDF
      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);
      pdf.text('System Architecture Diagram', margin, currentY);
      currentY += 8;

      pdf.setFontSize(10);
      pdf.setTextColor(100, 100, 100);
      pdf.text(`Generated on ${dateString}`, margin, currentY);
      currentY += 10;

      // Add user context if available
      if (userContext) {
        currentY = addUserContextToPDF(pdf, userContext, currentY);
      }

      // Add the diagram
      const imgData = canvas.toDataURL('image/png');

      // Check if we need a new page for the diagram
      if (currentY + imgHeight > 280) {
        pdf.addPage();
        currentY = 20;
        pdf.setFontSize(14);
        pdf.setTextColor(0, 0, 0);
        pdf.text('System Architecture Diagram', margin, currentY);
        currentY += 10;
      }

      pdf.addImage(imgData, 'PNG', margin, currentY, imgWidth, imgHeight);
      currentY += imgHeight + 10;

      // Add assessment results if available
      if (assessmentResult) {
        currentY = addAssessmentToPDF(pdf, assessmentResult, currentY);
      }

      // Save the PDF
      pdf.save(`system-design-${date.getTime()}.pdf`);
      toast.success("Comprehensive PDF exported successfully!");
      return;
    } catch (svgError) {
      console.warn('SVG export failed, falling back to html2canvas:', svgError);
    }

    // Fallback to html2canvas method
    const viewportElement = document.querySelector('.react-flow__viewport') || document.querySelector('.react-flow');
    if (!viewportElement) {
      toast.error("Could not find the diagram canvas element.");
      return;
    }

    reactFlowInstance.fitView({ padding: 0.2 });
    await new Promise((resolve) => setTimeout(resolve, 300));

    const edgeElements = document.querySelectorAll('.react-flow__edge');
    edgeElements.forEach(edge => {
      (edge as HTMLElement).style.visibility = 'visible';
      (edge as HTMLElement).style.opacity = '1';
    });

    const canvas = await html2canvas(viewportElement as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      logging: false,
      useCORS: true,
      allowTaint: false,
      ignoreElements: (element) => {
        if (element.classList &&
            (element.classList.contains('react-flow__controls') ||
             element.classList.contains('react-flow__minimap'))) {
          return true;
        }
        return false;
      }
    });

    // Calculate dimensions with a fixed margin
    const margin = 10; // in mm
    const imgWidth = 210 - margin * 2; // A4 width in mm - margins
    const imgHeight = canvas.height * imgWidth / canvas.width;

    // Create PDF and add the image
    const pdf = new jsPDF('p', 'mm', 'a4');

    // Add metadata
    const date = new Date();
    const dateString = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
    pdf.setProperties({
      title: `System Design - ${dateString}`,
      subject: 'System Architecture Diagram',
      creator: 'Layrs App'
    });

    let currentY = 15;

    // Add title to PDF
    pdf.setFontSize(16);
    pdf.setTextColor(0, 0, 0);
    pdf.text('System Architecture Diagram', margin, currentY);
    currentY += 8;

    pdf.setFontSize(10);
    pdf.setTextColor(100, 100, 100);
    pdf.text(`Generated on ${dateString}`, margin, currentY);
    currentY += 10;

    // Add user context if available
    if (userContext) {
      currentY = addUserContextToPDF(pdf, userContext, currentY);
    }

    // Add the diagram
    const imgData = canvas.toDataURL('image/png');

    // Check if we need a new page for the diagram
    if (currentY + imgHeight > 280) {
      pdf.addPage();
      currentY = 20;
      pdf.setFontSize(14);
      pdf.setTextColor(0, 0, 0);
      pdf.text('System Architecture Diagram', margin, currentY);
      currentY += 10;
    }

    pdf.addImage(imgData, 'PNG', margin, currentY, imgWidth, imgHeight);
    currentY += imgHeight + 10;

    // Add assessment results if available
    if (assessmentResult) {
      currentY = addAssessmentToPDF(pdf, assessmentResult, currentY);
    }

    // Save the PDF
    pdf.save(`system-design-${date.getTime()}.pdf`);

    // Show success message
    toast.success("Comprehensive PDF exported successfully!");
  } catch (error) {
    console.error('Error exporting PDF:', error);
    toast.error("Failed to export PDF. Please try again.");
  }
};
