import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Testing utilities for keyboard shortcuts functionality
 */

/**
 * Test copy-paste functionality
 */
export const testCopyPaste = () => {
  console.log('🧪 Testing Copy-Paste Functionality:');
  console.log('');

  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const modKey = isMac ? '⌘' : 'Ctrl';

  console.log('📋 Copy-Paste Test Instructions:');
  console.log('1. Add 2-3 components to the canvas');
  console.log('2. Select one or more components (click + drag to multi-select)');
  console.log(`3. Press ${modKey} + C to copy`);
  console.log('   - Should see: "📋 Copied X nodes and Y edges"');
  console.log(`4. Press ${modKey} + V to paste`);
  console.log('   - Should see: "📋 Pasting from clipboard..."');
  console.log('   - Should see: "📋 Pasted X nodes and Y edges"');
  console.log('   - New components should appear offset by 50px');
  console.log('   - New components should be selected (highlighted)');
  console.log('');

  console.log('🔗 Copy-Paste with Connections:');
  console.log('1. Create connections between components');
  console.log('2. Select multiple connected components');
  console.log(`3. Copy (${modKey} + C) and paste (${modKey} + V)`);
  console.log('   - Connections should be preserved between copied components');
  console.log('   - New unique IDs should be generated');
  console.log('');

  console.log('⚠️ Expected Behaviors:');
  console.log('- Copy with nothing selected: "⚠️ Nothing selected to copy"');
  console.log('- Paste with empty clipboard: "📋 Clipboard is empty"');
  console.log('- Copied items get new IDs to avoid conflicts');
  console.log('- Pasted items are automatically selected');
  console.log('- Original items remain unaffected');

  return {
    shortcuts: {
      copy: `${modKey} + C`,
      paste: `${modKey} + V`
    },
    testSteps: [
      'Add components to canvas',
      'Select components',
      'Copy with keyboard shortcut',
      'Paste with keyboard shortcut',
      'Verify new components appear'
    ]
  };
};

/**
 * Test undo-redo functionality
 */
export const testUndoRedo = () => {
  console.log('🧪 Testing Undo-Redo Functionality:');
  console.log('');

  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const modKey = isMac ? '⌘' : 'Ctrl';
  const redoKey = isMac ? `${modKey} + ⇧ + Z` : `${modKey} + Shift + Z`;

  console.log('↩️ Undo-Redo Test Instructions:');
  console.log('1. Perform various actions:');
  console.log('   - Add components');
  console.log('   - Move components');
  console.log('   - Create connections');
  console.log('   - Delete components');
  console.log(`2. Press ${modKey} + Z to undo`);
  console.log('   - Should see: "↩️ Undo action"');
  console.log('   - Last action should be reversed');
  console.log(`3. Press ${redoKey} to redo`);
  console.log('   - Should see: "↪️ Redo action"');
  console.log('   - Action should be restored');
  console.log('');

  console.log('📚 Simple Stack-Based Undo:');
  console.log('- Add component → Undo removes that component');
  console.log('- Add connection → Undo removes that connection');
  console.log('- Paste items → Undo removes pasted items');
  console.log('- Delete items → Undo restores deleted items');
  console.log('- Each action = one stack entry');
  console.log('- No complex state management');
  console.log('');

  console.log('⚠️ Expected Behaviors:');
  console.log('- Undo with no history: "↩️ Nothing to undo"');
  console.log('- Redo with no future: "↪️ Nothing to redo"');
  console.log('- History limited to 50 states');
  console.log('- New actions clear redo history');
  console.log('- Undo/redo actions don\'t create new history entries');

  return {
    shortcuts: {
      undo: `${modKey} + Z`,
      redo: redoKey
    },
    testSteps: [
      'Perform canvas actions',
      'Undo with keyboard shortcut',
      'Verify action was reversed',
      'Redo with keyboard shortcut',
      'Verify action was restored'
    ]
  };
};

/**
 * Test delete functionality
 */
export const testDelete = () => {
  console.log('🧪 Testing Delete Functionality:');
  console.log('');

  console.log('🗑️ Delete Test Instructions:');
  console.log('1. Add components and connections to canvas');
  console.log('2. Select components or connections');
  console.log('3. Press Delete or Backspace key');
  console.log('   - Should see: "🗑️ Deleted X nodes and Y edges"');
  console.log('   - Selected items should be removed');
  console.log('   - Action should be saved to history');
  console.log('4. Press Cmd/Ctrl + Z to undo deletion');
  console.log('   - Should see: "↩️ Undid: Restored X nodes and Y edges"');
  console.log('   - Deleted items should reappear exactly as they were');
  console.log('');

  console.log('🛡️ Protected Elements:');
  console.log('- Tutorial nodes (cannot be deleted)');
  console.log('- Nodes with deletable: false');
  console.log('- Elements being edited (text input active)');
  console.log('');

  console.log('⚠️ Expected Behaviors:');
  console.log('- Delete with nothing selected: No action');
  console.log('- Delete while editing text: No deletion, text editing continues');
  console.log('- Delete tutorial nodes: Ignored');
  console.log('- Delete saves state to history for undo');

  return {
    shortcuts: {
      delete: 'Delete or Backspace'
    },
    testSteps: [
      'Add components to canvas',
      'Select components',
      'Press Delete key',
      'Verify components are removed',
      'Test undo to restore'
    ]
  };
};

/**
 * Comprehensive keyboard shortcuts test
 */
export const testAllKeyboardShortcuts = () => {
  console.log('🚀 Comprehensive Keyboard Shortcuts Test');
  console.log('='.repeat(50));
  console.log('');

  const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  const modKey = isMac ? '⌘' : 'Ctrl';

  console.log('⌨️ Available Keyboard Shortcuts:');
  console.log(`📋 Copy: ${modKey} + C`);
  console.log(`📋 Paste: ${modKey} + V`);
  console.log(`↩️ Undo: ${modKey} + Z`);
  console.log(`↪️ Redo: ${modKey} + ${isMac ? '⇧ + Z' : 'Shift + Z'}`);
  console.log(`🗑️ Delete: Delete or Backspace`);
  console.log(`🔘 Select All: ${modKey} + A (React Flow built-in)`);
  console.log('');

  console.log('🧪 Running All Tests:');
  console.log('');

  // Run individual tests
  testCopyPaste();
  console.log('');
  testUndoRedo();
  console.log('');
  testDelete();

  console.log('');
  console.log('✅ All keyboard shortcut tests ready!');
  console.log('Follow the instructions above to test each feature.');

  return {
    platform: isMac ? 'Mac' : 'Windows/Linux',
    shortcuts: {
      copy: `${modKey} + C`,
      paste: `${modKey} + V`,
      undo: `${modKey} + Z`,
      redo: `${modKey} + ${isMac ? '⇧ + Z' : 'Shift + Z'}`,
      delete: 'Delete or Backspace',
      selectAll: `${modKey} + A`
    },
    features: [
      'Cross-platform keyboard shortcuts',
      'Copy-paste with connection preservation',
      'Undo-redo with 50-state history',
      'Smart delete with protection',
      'Clipboard management',
      'History tracking'
    ]
  };
};

/**
 * Test accidental deletion recovery
 */
export const testAccidentalDeletion = () => {
  console.log('🧪 Testing Accidental Deletion Recovery:');
  console.log('');

  console.log('😱 Accidental Deletion Scenario:');
  console.log('1. Create a complex design with multiple components');
  console.log('2. Add connections between components');
  console.log('3. Accidentally select and delete important components');
  console.log('4. Press Cmd/Ctrl + Z to recover');
  console.log('   - Should see: "↩️ Undid: Restored X nodes and Y edges"');
  console.log('   - All deleted items should reappear in exact positions');
  console.log('   - All connections should be restored');
  console.log('');

  console.log('🔄 What Gets Restored:');
  console.log('- Component positions and properties');
  console.log('- Component data and labels');
  console.log('- All connections between components');
  console.log('- Selection states (components will be unselected)');
  console.log('');

  console.log('⚡ Quick Test:');
  console.log('1. Add 3 components and connect them');
  console.log('2. Select all (Cmd/Ctrl + A)');
  console.log('3. Press Delete (simulate accident)');
  console.log('4. Press Cmd/Ctrl + Z (recover)');
  console.log('5. Everything should be back!');

  return {
    testSteps: [
      'Create complex design',
      'Accidentally delete components',
      'Press Cmd/Ctrl + Z',
      'Verify everything is restored',
      'Test with connections too'
    ]
  };
};

/**
 * Debug undo restoration issue
 */
export const debugUndoRestoration = () => {
  console.log('🔍 Debug Undo Restoration Issue:');
  console.log('');

  console.log('📋 Steps to debug:');
  console.log('1. Add a component to canvas');
  console.log('2. Select the component');
  console.log('3. Press Delete key');
  console.log('4. Watch console for "🗑️ Tracking deleted items" message');
  console.log('5. Press Cmd/Ctrl + Z to undo');
  console.log('6. Watch console for "🔍 Undoing action" and "🔄 Restoring deleted items" messages');
  console.log('');

  console.log('🔍 What to look for:');
  console.log('- "🗑️ Tracking deleted items" should show the deleted node data');
  console.log('- "🔍 Undoing action" should show type: "delete_items"');
  console.log('- "🔄 Restoring deleted items" should show the nodes being restored');
  console.log('- "📦 Restoring nodes" should show node details');
  console.log('');

  console.log('❌ If you see "Undid: Removed node..." instead of "Undid: Restored..."');
  console.log('   → The wrong undo case is being executed (add_node instead of delete_items)');
  console.log('');

  console.log('✅ If you see "Undid: Restored X nodes" but nothing appears:');
  console.log('   → The restoration logic is working but React Flow isn\'t updating');

  return {
    expectedLogs: [
      '🗑️ Tracking deleted items',
      '🔍 Undoing action',
      '🔄 Restoring deleted items',
      '📦 Restoring nodes',
      '↩️ Undid: Restored X nodes'
    ]
  };
};

/**
 * Expose testing functions globally
 */
if (typeof window !== 'undefined') {
  (window as any).layrsKeyboardTest = {
    testCopyPaste,
    testUndoRedo,
    testDelete,
    testAllKeyboardShortcuts,
    testAccidentalDeletion,
    debugUndoRestoration
  };
}
