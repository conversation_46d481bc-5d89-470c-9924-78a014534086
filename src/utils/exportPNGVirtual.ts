import html2canvas from 'html2canvas';
import { toast } from 'sonner';
import { ReactFlowInstance } from '@xyflow/react';
import { getViewportForBounds } from '@xyflow/react';

/**
 * Export PNG using a virtual offscreen container approach
 */
export const exportToPNGVirtual = async (
  rf: ReactFlowInstance,
  padding = 40           // visual breathing room around the diagram
) => {
  if (!rf) {
    toast.error('No diagram to export. Please add components first.');
    return;
  }

  /* ------------------------------------------------------------------ */
  /* 1 ▸ Grab nodes / edges and bail if empty                           */
  /* ------------------------------------------------------------------ */
  const nodes = rf.getNodes();
  const edges = rf.getEdges();
  if (!nodes.length) {
    toast.error('Nothing to export – canvas is empty.');
    return;
  }

  toast.loading('Preparing export…');

  /* ------------------------------------------------------------------ */
  /* 2 ▸ Calculate bounding box manually to avoid hook warning          */
  /* ------------------------------------------------------------------ */
  const nodesRect = nodes.reduce(
    (bounds, node) => {
      const nodeRight = node.position.x + (node.width || 200);
      const nodeBottom = node.position.y + (node.height || 100);
      
      return {
        x: Math.min(bounds.x, node.position.x),
        y: Math.min(bounds.y, node.position.y),
        width: Math.max(bounds.width, nodeRight),
        height: Math.max(bounds.height, nodeBottom)
      };
    },
    { x: Infinity, y: Infinity, width: -Infinity, height: -Infinity }
  );
  
  // Convert to proper width/height format
  const nodesRectFormatted = {
    x: nodesRect.x,
    y: nodesRect.y,
    width: nodesRect.width - nodesRect.x,
    height: nodesRect.height - nodesRect.y
  };

  const exportBounds = {
    x: nodesRectFormatted.x - padding,
    y: nodesRectFormatted.y - padding,
    width:  nodesRectFormatted.width  + padding * 2,
    height: nodesRectFormatted.height + padding * 2,
  };

  /* 3 ▸ Compute the transform that would fit that rect into viewport   */
  const viewport    = rf.getViewport();            // remember user view
  const { x, y, zoom } = getViewportForBounds(
  exportBounds,           // bounds object
  exportBounds.width,     // viewportWidth
  exportBounds.height,    // viewportHeight
  0,                      // padding  <<–– add this (0 or padding)
  0.1,                    // minZoom   (choose what you like)
  1                       // maxZoom
);

  /* ------------------------------------------------------------------ */
  /* 4 ▸ Apply the transform so everything is visible on screen         */
  /* ------------------------------------------------------------------ */
  rf.setViewport({ x, y, zoom });

  // wait a tick so ReactFlow can re-render at new transform
  await new Promise(r => setTimeout(r, 100));

  /* ------------------------------------------------------------------ */
  /* 5 ▸ Snapshot the *existing* DOM – no cloning needed                */
  /* ------------------------------------------------------------------ */
  const container = document.querySelector('.react-flow') as HTMLElement;

  const canvas = await html2canvas(container, {
    backgroundColor: '#ffffff',
    useCORS: true,
    scale: window.devicePixelRatio,   // crisp on HiDPI
    logging: false,
  });

  /* ------------------------------------------------------------------ */
  /* 6 ▸ Restore the user’s original viewport                           */
  /* ------------------------------------------------------------------ */
  rf.setViewport(viewport);

  /* ------------------------------------------------------------------ */
  /* 7 ▸ Trigger download                                               */
  /* ------------------------------------------------------------------ */
  const link = document.createElement('a');
  link.download = `layrs-design-${new Date().toISOString().slice(0,19)}.png`;
  link.href      = canvas.toDataURL('image/png');
  link.click();

  toast.success(
    `PNG exported! (${nodes.length} components · ${edges.length} connections)`
  );
};