import { supabase } from '@/lib/supabase';

/**
 * Fix questions 26-30 with proper FR/NFR data
 */
export const fixQuestions26to30 = async () => {
  const updates = [
    {
      id: '26',
      title: 'Google Drive',
      functional_requirements: [
        'Upload/download files (up to 10GB)',
        'Share files/folders with permissions',
        'Track edits with version history',
        'Sync changes across devices'
      ],
      non_functional_requirements: [
        'Upload/download latency < 1s',
        'Support 1B+ files per day',
        '99.99% data durability'
      ],
      constraints: [
        'Storage: Use a distributed file system (e.g., Google Colossus)',
        'Sync algorithm: Use rsync-like delta encoding',
        'Permissions: Enforce ACLs (access control lists)',
        'Deduplication: Store unique files once'
      ]
    },
    {
      id: '27',
      title: 'Facebook Messenger',
      functional_requirements: [
        'Support 1:1 and group chats (up to 250 users)',
        'Integrate third-party bots (e.g., weather, shopping)',
        'Sync conversations across web/mobile',
        'Send read receipts'
      ],
      non_functional_requirements: [
        'Message delivery < 500ms',
        'Handle 100M+ daily active users',
        '99.95% message persistence'
      ],
      constraints: [
        'Bots: Use a webhook API for integration',
        'Group chats: Use a pub/sub model for fan-out',
        'Search: Index messages with Elasticsearch',
        'Compliance: Encrypt data at rest (e.g., AES-256)'
      ]
    },
    {
      id: '28',
      title: 'Netflix Playlist',
      functional_requirements: [
        'Stream videos at multiple bitrates',
        'Resume playback from last position',
        'Generate recommendations (e.g., "Because you watched X")',
        'Support multiple profiles per account'
      ],
      non_functional_requirements: [
        'Stream latency < 1s',
        'Handle 100M+ concurrent streams',
        '99.9% recommendation accuracy'
      ],
      constraints: [
        'Adaptive streaming: Use DASH or HLS for bitrate switching',
        'Recommendations: Use collaborative filtering (e.g., Spark MLlib)',
        'DRM: Implement Widevine/PlayReady for piracy protection',
        'A/B testing: Dynamically adjust recommendation algorithms'
      ]
    },
    {
      id: '29',
      title: 'Twitter Notifications',
      functional_requirements: [
        'Trigger notifications for user actions',
        'Support email, push, and in-app alerts',
        'Track read/unread status',
        'Filter spam notifications'
      ],
      non_functional_requirements: [
        'Notification latency < 500ms',
        'Handle 1M+ notifications per second',
        '99.9% delivery success rate'
      ],
      constraints: [
        'Pub/sub: Use Kafka for event streaming',
        'Fan-out: Deliver notifications via Firebase Cloud Messaging (FCM)',
        'Deduplication: Track unique notifications in Redis',
        'Priority queues: Process urgent notifications first'
      ]
    },
    {
      id: '30',
      title: 'Instagram Reels',
      functional_requirements: [
        'Upload 15-60 second videos with effects',
        'Recommend Reels based on user interests',
        'Support duets (side-by-side videos)',
        'Track views, likes, and shares'
      ],
      non_functional_requirements: [
        'Reel load latency < 1s',
        'Handle 100M+ Reel views per hour',
        '99.9% recommendation relevance'
      ],
      constraints: [
        'Video processing: Use FFmpeg for transcoding',
        'Recommendations: Use a hybrid model (collaborative filtering + content-based)',
        'Duets: Store composite videos in a distributed file system',
        'Moderation: Use AI for automated content review'
      ]
    }
  ];

  console.log('🔧 Starting to fix questions 26-30...');
  
  let successCount = 0;
  let errorCount = 0;
  
  for (const update of updates) {
    try {
      console.log(`📝 Updating question ${update.id}: ${update.title}`);
      
      const { data, error } = await supabase
        .from('questions')
        .update({
          functional_requirements: update.functional_requirements,
          non_functional_requirements: update.non_functional_requirements,
          constraints: update.constraints
        })
        .eq('id', update.id)
        .select();
      
      if (error) {
        console.error(`❌ Error updating question ${update.id}:`, error);
        errorCount++;
      } else {
        console.log(`✅ Successfully updated question ${update.id}`);
        successCount++;
        
        // Verify the update
        if (data && data.length > 0) {
          const updated = data[0];
          console.log(`   FR: ${updated.functional_requirements?.length || 0} items`);
          console.log(`   NFR: ${updated.non_functional_requirements?.length || 0} items`);
          console.log(`   Constraints: ${updated.constraints?.length || 0} items`);
        }
      }
    } catch (err) {
      console.error(`❌ Exception updating question ${update.id}:`, err);
      errorCount++;
    }
  }
  
  console.log(`\n📊 Update Summary:`);
  console.log(`✅ Successful: ${successCount}`);
  console.log(`❌ Failed: ${errorCount}`);
  console.log(`📋 Total: ${updates.length}`);
  
  return { success: successCount === updates.length, successCount, errorCount };
};

/**
 * Verify the fixes worked
 */
export const verifyFixes = async () => {
  console.log('🔍 Verifying fixes for questions 26-30...');
  
  const { data: questions, error } = await supabase
    .from('questions')
    .select('id, title, functional_requirements, non_functional_requirements, constraints')
    .in('id', ['26', '27', '28', '29', '30'])
    .order('id');
  
  if (error) {
    console.error('❌ Error fetching questions:', error);
    return { success: false, error };
  }
  
  console.log(`\n📋 Verification Results:`);
  
  questions.forEach(q => {
    const hasFR = q.functional_requirements && q.functional_requirements.length > 0;
    const hasNFR = q.non_functional_requirements && q.non_functional_requirements.length > 0;
    const hasConstraints = q.constraints && q.constraints.length > 0;
    const isComplete = hasFR && hasNFR && hasConstraints;
    
    const status = isComplete ? '✅' : '❌';
    console.log(`${status} Q${q.id}: ${q.title}`);
    console.log(`   FR: ${q.functional_requirements?.length || 0} items (${hasFR ? 'OK' : 'MISSING'})`);
    console.log(`   NFR: ${q.non_functional_requirements?.length || 0} items (${hasNFR ? 'OK' : 'MISSING'})`);
    console.log(`   Constraints: ${q.constraints?.length || 0} items (${hasConstraints ? 'OK' : 'MISSING'})`);
  });
  
  const completeCount = questions.filter(q => 
    q.functional_requirements?.length > 0 && 
    q.non_functional_requirements?.length > 0 && 
    q.constraints?.length > 0
  ).length;
  
  console.log(`\n📊 Summary: ${completeCount}/${questions.length} questions are complete`);
  
  return { success: completeCount === questions.length, questions, completeCount };
};

/**
 * Run the complete fix and verification process
 */
export const runCompleteFix = async () => {
  console.log('🚀 Starting complete fix process for questions 26-30...\n');
  
  // Step 1: Apply fixes
  const fixResult = await fixQuestions26to30();
  
  if (!fixResult.success) {
    console.log('❌ Some updates failed. Continuing with verification...\n');
  }
  
  // Step 2: Verify fixes
  const verifyResult = await verifyFixes();
  
  if (verifyResult.success) {
    console.log('\n🎉 All questions 26-30 are now properly configured with FR/NFR separation!');
  } else {
    console.log('\n⚠️ Some questions still need attention. Check the verification results above.');
  }
  
  return { fixResult, verifyResult };
};

// Export for console use
(window as any).fixQuestions26to30 = fixQuestions26to30;
(window as any).verifyFixes = verifyFixes;
(window as any).runCompleteFix = runCompleteFix;
