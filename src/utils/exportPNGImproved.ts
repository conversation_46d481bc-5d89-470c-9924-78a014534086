import html2canvas from 'html2canvas';
import { toast } from 'sonner';
import { ReactFlowInstance } from '@xyflow/react';

/**
 * Improved PNG export that properly captures edges/connections
 */
export const exportToPNGImproved = async (reactFlowInstance: ReactFlowInstance) => {
  if (!reactFlowInstance) {
    toast.error("No diagram to export. Please add components to the canvas first.");
    return;
  }

  try {
    toast.loading('Preparing diagram for export...');

    // Get all nodes and edges to ensure we have content
    const nodes = reactFlowInstance.getNodes();
    const edges = reactFlowInstance.getEdges();
    
    if (nodes.length === 0) {
      toast.error("No components to export. Please add components to the canvas first.");
      return;
    }

    // Get the React Flow viewport (contains the actual diagram)
    const viewport = document.querySelector('.react-flow__viewport') as HTMLElement;
    const container = document.querySelector('.react-flow') as HTMLElement;
    
    if (!viewport || !container) {
      toast.error('Diagram not found. Please ensure the diagram is loaded.');
      return;
    }

    // Get current viewport and nodes to calculate proper bounds
    const originalViewport = reactFlowInstance.getViewport();
    const nodesBounds = reactFlowInstance.getNodes().reduce((bounds, node) => {
      const nodeRight = node.position.x + (node.width || 200);
      const nodeBottom = node.position.y + (node.height || 100);
      
      return {
        minX: Math.min(bounds.minX, node.position.x),
        minY: Math.min(bounds.minY, node.position.y),
        maxX: Math.max(bounds.maxX, nodeRight),
        maxY: Math.max(bounds.maxY, nodeBottom)
      };
    }, { minX: Infinity, minY: Infinity, maxX: -Infinity, maxY: -Infinity });

    console.log('Nodes bounds:', nodesBounds);
    console.log('Original viewport:', originalViewport);

    // Fit the view to show all content with generous padding
    reactFlowInstance.fitView({
      padding: 0.2, // 20% padding
      includeHiddenNodes: true,
      minZoom: 0.1,
      maxZoom: 1.5,
      duration: 0
    });

    // Wait longer for the view to update and settle
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Force visibility of all edges and their components
    const edgeElements = document.querySelectorAll('.react-flow__edge');
    const edgePaths = document.querySelectorAll('.react-flow__edge-path');
    const edgeLabels = document.querySelectorAll('.react-flow__edge-text');
    
    // Make edges visible and prominent
    edgeElements.forEach(edge => {
      const el = edge as HTMLElement;
      el.style.visibility = 'visible';
      el.style.opacity = '1';
      el.style.display = 'block';
    });

    edgePaths.forEach(path => {
      const el = path as SVGPathElement;
      el.style.stroke = '#2563eb'; // Blue color for better visibility
      el.style.strokeWidth = '2';
      el.style.visibility = 'visible';
      el.style.opacity = '1';
      el.style.display = 'block';
    });

    edgeLabels.forEach(label => {
      const el = label as HTMLElement;
      el.style.visibility = 'visible';
      el.style.opacity = '1';
      el.style.display = 'block';
    });

    // Force a repaint by temporarily changing a style
    container.style.transform = 'translateZ(0)';

    toast.loading('Capturing diagram...');

    // Calculate the required canvas dimensions based on node bounds and zoom
    const currentViewport = reactFlowInstance.getViewport();
    const contentWidth = nodesBounds.maxX - nodesBounds.minX + 400; // Add 200px padding on each side
    const contentHeight = nodesBounds.maxY - nodesBounds.minY + 400; // Add 200px padding on top/bottom
    
    console.log('Content dimensions needed:', { width: contentWidth, height: contentHeight });
    console.log('Current zoom:', currentViewport.zoom);

    // Temporarily set a zoom level that fits everything in a reasonable canvas size
    const targetZoom = Math.min(
      Math.min(2000 / contentWidth, 1500 / contentHeight), // Don't exceed 2000x1500 base size
      1 // Don't zoom in beyond 1:1
    );
    
    console.log('Target zoom for export:', targetZoom);

    // Set the zoom and position to capture everything
    reactFlowInstance.setViewport({
      x: -(nodesBounds.minX - 200) * targetZoom, // Position to show leftmost content with padding
      y: -(nodesBounds.minY - 200) * targetZoom, // Position to show topmost content with padding  
      zoom: targetZoom
    });

    // Wait for viewport change to settle
    await new Promise(resolve => setTimeout(resolve, 500));

    // Get the actual dimensions of the content after viewport adjustment
    const viewportRect = viewport.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    
    console.log('After adjustment - Viewport rect:', viewportRect);
    console.log('After adjustment - Container rect:', containerRect);

    // Calculate canvas dimensions based on content size and zoom
    const canvasWidth = Math.min(contentWidth * targetZoom, 3000); // Cap at 3000px
    const canvasHeight = Math.min(contentHeight * targetZoom, 3000); // Cap at 3000px

    console.log('Canvas dimensions:', { canvasWidth, canvasHeight });

    // Temporarily expand the container to fit all content
    const originalContainerStyle = {
      width: container.style.width,
      height: container.style.height,
      overflow: container.style.overflow,
      position: container.style.position
    };

    // Set container to accommodate the full content
    container.style.width = `${canvasWidth}px`;
    container.style.height = `${canvasHeight}px`;
    container.style.overflow = 'visible';
    container.style.position = 'relative';

    // Wait for container resize to take effect
    await new Promise(resolve => setTimeout(resolve, 300));

    // Capture the container (which includes viewport and edges)
    const canvas = await html2canvas(container, {
      backgroundColor: '#ffffff',
      scale: 1, // Use scale 1 since we're already controlling size with zoom
      logging: false,
      useCORS: true,
      allowTaint: false,
      foreignObjectRendering: true, // Important for SVG elements
      imageTimeout: 15000,
      width: canvasWidth,
      height: canvasHeight,
      scrollX: 0,
      scrollY: 0,
      onclone: (clonedDoc) => {
        // Ensure styles are preserved in the cloned document
        const clonedEdges = clonedDoc.querySelectorAll('.react-flow__edge-path');
        clonedEdges.forEach(path => {
          const el = path as SVGPathElement;
          el.style.stroke = '#2563eb';
          el.style.strokeWidth = '2';
          el.style.visibility = 'visible';
          el.style.opacity = '1';
        });
        
        // Ensure the viewport is visible in the clone
        const clonedViewport = clonedDoc.querySelector('.react-flow__viewport') as HTMLElement;
        if (clonedViewport) {
          clonedViewport.style.transform = 'none';
          clonedViewport.style.position = 'static';
        }
      },
      ignoreElements: (element) => {
        // Only ignore UI controls, not diagram content
        if (element.classList && (
          element.classList.contains('react-flow__controls') ||
          element.classList.contains('react-flow__minimap') ||
          element.classList.contains('react-flow__panel') ||
          element.classList.contains('react-flow__attribution') ||
          element.classList.contains('react-flow__resize-control')
        )) {
          return true;
        }
        return false;
      }
    });

    // Reset the transform
    container.style.transform = '';

    // Restore original container styles
    container.style.width = originalContainerStyle.width;
    container.style.height = originalContainerStyle.height;
    container.style.overflow = originalContainerStyle.overflow;
    container.style.position = originalContainerStyle.position;

    // Restore the original viewport
    reactFlowInstance.setViewport(originalViewport);

    toast.loading('Downloading image...');

    // Create and trigger download
    const link = document.createElement('a');
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.download = `system-design-${timestamp}.png`;
    link.href = canvas.toDataURL('image/png', 1.0);

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success(`PNG exported successfully! (${nodes.length} components, ${edges.length} connections)`);

  } catch (error) {
    console.error('Export failed:', error);
    toast.error('Export failed. Please try again or check the browser console.');
  }
};