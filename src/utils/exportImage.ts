import html2canvas from 'html2canvas';
import { toast } from 'sonner';

export const exportToImage = async (reactFlowInstance: any, format: 'png' | 'jpg' = 'png') => {
  if (!reactFlowInstance) {
    toast.error("No diagram to export. Please add components to the canvas first.");
    return;
  }

  try {
    // Show a loading toast
    toast.loading(`Generating ${format.toUpperCase()} image...`);

    // Get the react-flow viewport element (more specific than the container)
    const viewportElement = document.querySelector('.react-flow__viewport') || document.querySelector('.react-flow');
    if (!viewportElement) {
      toast.error("Could not find the diagram canvas element.");
      return;
    }

    // Get the current nodes and fit the view to include all of them
    reactFlowInstance.fitView({ padding: 0.2 });

    // Wait a moment for the view to adjust
    await new Promise((resolve) => setTimeout(resolve, 300));

    // Make sure all elements are visible, especially edges
    const edgeElements = document.querySelectorAll('.react-flow__edge');
    edgeElements.forEach(edge => {
      (edge as HTMLElement).style.visibility = 'visible';
      (edge as HTMLElement).style.opacity = '1';
    });

    // Convert the viewport element to a canvas
    const canvas = await html2canvas(viewportElement as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      logging: false,
      useCORS: true,
      allowTaint: false,
      // Ensure we capture all elements including those that might be partially hidden
      ignoreElements: (element) => {
        // Ignore controls and minimap but keep everything else
        if (element.classList &&
            (element.classList.contains('react-flow__controls') ||
             element.classList.contains('react-flow__minimap'))) {
          return true;
        }
        // Don't ignore any diagram elements
        return false;
      }
    });

    // Convert canvas to image and download
    const date = new Date();
    const fileName = `system-design-${date.getTime()}.${format}`;
    const imageType = format === 'jpg' ? 'image/jpeg' : 'image/png';

    // Create a link element to download the image
    const link = document.createElement('a');
    link.download = fileName;
    link.href = canvas.toDataURL(imageType, 0.8); // 0.8 quality for JPEG
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Show success message
    toast.success(`${format.toUpperCase()} image exported successfully!`);
  } catch (error) {
    console.error(`Error exporting ${format.toUpperCase()} image:`, error);
    toast.error(`Failed to export ${format.toUpperCase()} image. Please try again.`);
  }
};
