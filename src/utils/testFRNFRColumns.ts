import { supabase } from '@/lib/supabase';

/**
 * Test if the new FR/NFR columns exist and work properly
 */
export const testFRNFRColumns = async () => {
  try {
    console.log('🔍 Testing FR/NFR columns...');
    
    // Test 1: Check if columns exist by querying a question
    const { data: question, error: queryError } = await supabase
      .from('questions')
      .select('id, title, functional_requirements, non_functional_requirements, requirements, constraints')
      .eq('id', '11')
      .single();
    
    if (queryError) {
      console.error('❌ Error querying question:', queryError);
      return { success: false, error: queryError.message };
    }
    
    console.log('✅ Successfully queried question with FR/NFR columns');
    console.log('Question data:', {
      id: question.id,
      title: question.title,
      hasFR: !!question.functional_requirements,
      hasNFR: !!question.non_functional_requirements,
      frCount: question.functional_requirements?.length || 0,
      nfrCount: question.non_functional_requirements?.length || 0
    });
    
    // Test 2: Update a question with FR/NFR data if not already present
    if (!question.functional_requirements || !question.non_functional_requirements) {
      console.log('🔄 Updating question 11 with FR/NFR data...');
      
      const { error: updateError } = await supabase
        .from('questions')
        .update({
          functional_requirements: [
            'Publish tweets with text, images, or videos',
            'Follow/unfollow users',
            'Display a chronological or ranked feed',
            'Add likes, retweets, and replies'
          ],
          non_functional_requirements: [
            'Load feed in < 500ms',
            'Handle 100k+ tweets per second',
            '99.95% availability'
          ],
          constraints: [
            'Fan-out writes: Push tweets to all followers\' timelines',
            'Caching: Use Redis for hot data (e.g., trending tweets)',
            'Load balancing: Distribute traffic across multiple servers',
            'Data denormalization: Store redundant data for faster reads'
          ]
        })
        .eq('id', '11');
      
      if (updateError) {
        console.error('❌ Error updating question:', updateError);
        return { success: false, error: updateError.message };
      }
      
      console.log('✅ Successfully updated question 11 with FR/NFR data');
    }
    
    return { success: true, question };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Batch update all medium questions with proper FR/NFR separation
 */
export const batchUpdateMediumQuestions = async () => {
  const updates = [
    {
      id: '12',
      functional_requirements: [
        'Upload images/videos with captions',
        'Follow/unfollow users',
        'Display a ranked feed (e.g., by engagement)',
        'Add comments and likes'
      ],
      non_functional_requirements: [
        'Load feed in < 1s',
        'Handle 1M+ uploads daily',
        '99.9% durability (no data loss)'
      ],
      constraints: [
        'Image compression: Use JPEG/WebP for efficient storage',
        'CDN integration: Cache images globally',
        'Consistency: Eventual consistency for likes/comments',
        'Search: Use Elasticsearch for hashtag-based queries'
      ]
    },
    {
      id: '13',
      functional_requirements: [
        '1:1 and group messaging',
        'End-to-end encryption (e.g., Signal Protocol)',
        'Online/offline status updates',
        'File sharing (images, videos, documents)'
      ],
      non_functional_requirements: [
        'Message delivery in < 1s',
        'Support 100M+ daily active users',
        '99.99% message delivery success rate'
      ],
      constraints: [
        'E2E encryption: Use asymmetric encryption (e.g., RSA) for key exchange',
        'Presence detection: Use WebSocket heartbeats',
        'Media optimization: Compress files before upload',
        'Store-and-forward: Temporarily store messages for offline users'
      ]
    }
    // Add more updates as needed
  ];
  
  try {
    let successCount = 0;
    
    for (const update of updates) {
      const { error } = await supabase
        .from('questions')
        .update({
          functional_requirements: update.functional_requirements,
          non_functional_requirements: update.non_functional_requirements,
          constraints: update.constraints
        })
        .eq('id', update.id);
      
      if (error) {
        console.error(`❌ Failed to update question ${update.id}:`, error);
      } else {
        console.log(`✅ Updated question ${update.id}`);
        successCount++;
      }
    }
    
    return { success: true, updated: successCount, total: updates.length };
    
  } catch (error) {
    console.error('❌ Batch update failed:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

/**
 * Console helper to run tests
 */
export const runFRNFRTests = async () => {
  console.log('🚀 Starting FR/NFR column tests...');
  
  const testResult = await testFRNFRColumns();
  if (!testResult.success) {
    console.log('❌ Test failed, stopping here');
    return;
  }
  
  console.log('🔄 Running batch updates...');
  const batchResult = await batchUpdateMediumQuestions();
  
  if (batchResult.success) {
    console.log(`✅ All tests completed! Updated ${batchResult.updated}/${batchResult.total} questions`);
  } else {
    console.log('❌ Batch update failed');
  }
};
