import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Debug utility for testing auto-save functionality
 */

export const debugAutoSave = () => {
  debugLog('🔍 Auto-Save Debug Information:');
  
  // Check localStorage for auto-save data
  const layrsKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('layrs-design-')) {
      layrsKeys.push(key);
    }
  }
  
  debugLog('📦 LocalStorage keys found:', layrsKeys);
  
  // Check each key
  layrsKeys.forEach(key => {
    try {
      const data = localStorage.getItem(key);
      if (data) {
        const parsed = JSON.parse(data);
        debugLog(`📋 ${key}:`, {
          timestamp: parsed.timestamp ? new Date(parsed.timestamp).toLocaleString() : (parsed.lastModified ? new Date(parsed.lastModified).toLocaleString() : 'No timestamp'),
          nodeCount: (parsed.data?.nodes?.length || parsed.nodes?.length || 0),
          edgeCount: (parsed.data?.edges?.length || parsed.edges?.length || 0),
          hasUserJourneys: !!(parsed.data?.userJourneys || parsed.userJourneys),
          hasAssumptions: !!(parsed.data?.assumptions || parsed.assumptions),
          hasConstraints: !!(parsed.data?.constraints || parsed.constraints),
          contextType: (parsed.data?.contextType || parsed.contextType),
          contextId: (parsed.data?.contextId || parsed.contextId)
        });
      }
    } catch (error) {
      debugError(`❌ Error parsing ${key}:`, error);
    }
  });
  
  // Check current page context
  const currentPath = window.location.pathname;
  debugLog('🌐 Current path:', currentPath);
  
  // Check if React Flow instance exists
  const reactFlowContainer = document.querySelector('.react-flow');
  debugLog('🔄 React Flow container found:', !!reactFlowContainer);
  
  // Check for auto-save related elements
  const saveIndicator = document.querySelector('[data-testid="save-status-indicator"]');
  debugLog('💾 Save status indicator found:', !!saveIndicator);
  
  return {
    layrsKeys,
    currentPath,
    hasReactFlow: !!reactFlowContainer,
    hasSaveIndicator: !!saveIndicator
  };
};

// Test auto-save by simulating changes
export const testAutoSave = () => {
  debugLog('🧪 Testing auto-save functionality...');
  
  // Try to add a component to the canvas
  const componentButton = document.querySelector('[data-component-type="load-balancer"]');
  if (componentButton) {
    debugLog('🎯 Found Load Balancer component button, clicking...');
    (componentButton as HTMLElement).click();
    
    setTimeout(() => {
      debugLog('⏰ Checking for auto-save after component addition...');
      debugAutoSave();
    }, 3000);
  } else {
    debugLog('❌ Component button not found');
  }
  
  // Try to modify text fields
  const userJourneysTextarea = document.querySelector('textarea[placeholder*="user journey"]');
  if (userJourneysTextarea) {
    debugLog('📝 Found user journeys textarea, adding test content...');
    (userJourneysTextarea as HTMLTextAreaElement).value = 'Test user journey for auto-save';
    userJourneysTextarea.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      debugLog('⏰ Checking for auto-save after text change...');
      debugAutoSave();
    }, 1000);
  } else {
    debugLog('❌ User journeys textarea not found');
  }
};

// Clear all auto-save data
export const clearAutoSaveData = () => {
  debugLog('🧹 Clearing all auto-save data...');
  
  const layrsKeys = [];
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('layrs-design-')) {
      layrsKeys.push(key);
    }
  }
  
  layrsKeys.forEach(key => {
    localStorage.removeItem(key);
    debugLog(`🗑️ Removed ${key}`);
  });
  
  debugLog(`✅ Cleared ${layrsKeys.length} auto-save entries`);
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).debugAutoSave = debugAutoSave;
  (window as any).testAutoSave = testAutoSave;
  (window as any).clearAutoSaveData = clearAutoSaveData;
}
