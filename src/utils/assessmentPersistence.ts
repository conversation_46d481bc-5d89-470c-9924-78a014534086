import { AssessmentResult } from '@/services/assessmentService';
import { LocalStorageManager } from './localStorageManager';
import { debugLog, debugWarn } from './debugLogger';
import { DesignContextCategory } from '@/contexts/DesignContext';

/**
 * Utility for persisting assessment results to localStorage
 * Follows the same pattern as design storage
 */

/**
 * Generate the localStorage key for assessment results
 */
export const getAssessmentKey = (
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): string => {
  const id = contextId || questionId;
  return `layrs-assessment-${contextType}-${id}`;
};

/**
 * Save assessment result to localStorage
 */
export const saveAssessmentResult = (
  assessmentResult: AssessmentResult,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): boolean => {
  try {
    const key = getAssessmentKey(questionId, contextType, contextId);
    const success = LocalStorageManager.save(key, assessmentResult);
    
    if (success) {
      debugLog(`💾 Assessment result saved for ${contextType} ${contextId || questionId}`);
    }
    
    return success;
  } catch (error) {
    debugWarn('Failed to save assessment result:', error);
    return false;
  }
};

/**
 * Load assessment result from localStorage
 */
export const loadAssessmentResult = (
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): AssessmentResult | null => {
  try {
    const key = getAssessmentKey(questionId, contextType, contextId);
    const result = LocalStorageManager.load<AssessmentResult>(key);
    
    if (result) {
      debugLog(`📥 Assessment result loaded for ${contextType} ${contextId || questionId}`);
    }
    
    return result;
  } catch (error) {
    debugWarn('Failed to load assessment result:', error);
    return null;
  }
};

/**
 * Remove assessment result from localStorage
 */
export const removeAssessmentResult = (
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): boolean => {
  try {
    const key = getAssessmentKey(questionId, contextType, contextId);
    const success = LocalStorageManager.remove(key);
    
    if (success) {
      debugLog(`🗑️ Assessment result removed for ${contextType} ${contextId || questionId}`);
    }
    
    return success;
  } catch (error) {
    debugWarn('Failed to remove assessment result:', error);
    return false;
  }
};

/**
 * Check if assessment result exists in localStorage
 */
export const hasAssessmentResult = (
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): boolean => {
  const key = getAssessmentKey(questionId, contextType, contextId);
  return LocalStorageManager.exists(key);
};