
import { Node, Edge } from '@xyflow/react';
import { CourseStep } from '@/types/course';

// Add debugging for step 3 validation
export const checkBuildAuthServiceCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking build-auth-service completion");

  // Look for a composite node named "Authentication Service"
  const authServiceNode = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Authentication Service'
  );

  console.log("Found auth service node:", authServiceNode);

  if (!authServiceNode) return false;

  // Check if it has the required internal components
  const childNodes = nodes.filter(node => node.parentId === authServiceNode.id);
  console.log("Child nodes:", childNodes);

  // For step 3, we'll be more lenient - just require the composite with at least 2 components
  const hasEnoughComponents = childNodes.length >= 2;

  // Check for connections between internal components
  const internalEdges = edges.filter(edge =>
    childNodes.some(node => node.id === edge.source) &&
    childNodes.some(node => node.id === edge.target)
  );
  console.log("Internal edges:", internalEdges);

  // Require at least one internal connection
  const hasInternalConnections = internalEdges.length > 0;

  const isCompleted = authServiceNode && hasEnoughComponents && hasInternalConnections;
  console.log("Step 3 completion result:", isCompleted);

  return isCompleted;
}

// Check if Auth Service is connected to API Gateway
export const checkConnectAuthServiceCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking connect-auth-service completion");

  // Find the Authentication Service composite node
  const authServiceNode = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Authentication Service'
  );

  console.log("Found auth service node:", authServiceNode);

  if (!authServiceNode) return false;

  // Find any API Gateway node
  const apiGatewayNodes = nodes.filter(node =>
    (node.data as any)?.type === 'apigateway' &&
    !node.parentId // Not inside a composite
  );

  console.log("API Gateway nodes:", apiGatewayNodes);

  if (apiGatewayNodes.length === 0) return false;

  // Check for a connection between any API Gateway and the Auth Service
  const hasConnection = edges.some(edge =>
    (apiGatewayNodes.some(node => node.id === edge.source) && edge.target === authServiceNode.id) ||
    (apiGatewayNodes.some(node => node.id === edge.target) && edge.source === authServiceNode.id)
  );

  console.log("Has API Gateway to Auth connection:", hasConnection);

  return hasConnection;
}

// Check if Media Service is added and connected to Auth Service
export const checkAddMediaServiceCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking add-media-service completion");

  // Find the Authentication Service composite node
  const authServiceNode = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Authentication Service'
  );

  console.log("Found auth service node:", authServiceNode);

  if (!authServiceNode) return false;

  // Find a Media Service node (server component labeled as Media Service)
  const mediaServiceNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId // Not inside a composite
  );

  console.log("Found media service node:", mediaServiceNode);

  if (!mediaServiceNode) return false;

  // Check for a connection between Auth Service and Media Service
  const hasConnection = edges.some(edge =>
    (edge.source === authServiceNode.id && edge.target === mediaServiceNode.id) ||
    (edge.source === mediaServiceNode.id && edge.target === authServiceNode.id)
  );

  console.log("Has Auth to Media connection:", hasConnection);

  return hasConnection;
}

// Check if Queue is placed between Auth Service and Media Service
export const checkQueuePlacementCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking queue placement completion");

  // Find the Authentication Service composite node
  const authServiceNode = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Authentication Service'
  );

  console.log("Found auth service node:", authServiceNode);

  if (!authServiceNode) return false;

  // Find a Media Service node
  const mediaServiceNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId
  );

  console.log("Found media service node:", mediaServiceNode);

  if (!mediaServiceNode) return false;

  // Find a Queue node
  const queueNode = nodes.find(node =>
    (node.data as any)?.type === 'queue' &&
    !node.parentId
  );

  console.log("Found queue node:", queueNode);

  if (!queueNode) return false;

  // Check if the queue has the required maxQueueSize property
  const queueData = queueNode.data as any;
  const customProps = queueData.metadata?.customProperties || [];
  const hasMaxQueueSize = customProps.some((prop: any) =>
    prop.key.toLowerCase() === 'maxqueuesize' &&
    prop.value === '50'
  );

  console.log("Queue has maxQueueSize=50:", hasMaxQueueSize);

  if (!hasMaxQueueSize) return false;

  // Check for a connection from Auth Service to Queue
  const authToQueueConnection = edges.some(edge =>
    edge.source === authServiceNode.id &&
    edge.target === queueNode.id
  );

  console.log("Has Auth to Queue connection:", authToQueueConnection);

  // Check for a connection from Queue to Media Service
  const queueToMediaConnection = edges.some(edge =>
    edge.source === queueNode.id &&
    edge.target === mediaServiceNode.id
  );

  console.log("Has Queue to Media connection:", queueToMediaConnection);

  return authToQueueConnection && queueToMediaConnection;
};

// Check if Media Database is added and connected
export const checkMediaDatabaseCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking media database completion");

  // Find the Media Service node
  const mediaServiceNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId
  );

  console.log("Found media service node:", mediaServiceNode);

  if (!mediaServiceNode) return false;

  // Find a Database node
  const databaseNode = nodes.find(node =>
    (node.data as any)?.type === 'database' &&
    !node.parentId
  );

  console.log("Found database node:", databaseNode);

  if (!databaseNode) return false;

  // Check for a connection between Media Service and Database
  const hasConnection = edges.some(edge =>
    (edge.source === mediaServiceNode.id && edge.target === databaseNode.id) ||
    (edge.source === databaseNode.id && edge.target === mediaServiceNode.id)
  );

  console.log("Has Media Service to Database connection:", hasConnection);

  // Check for built-in database properties
  const dbData = databaseNode.data as any;
  const metadata = dbData.metadata || {};

  // Check for engine property (should mention S3, Blob, or Cassandra)
  const engine = metadata.engine || '';
  const hasProperEngine = engine.toLowerCase().includes('s3') ||
                          engine.toLowerCase().includes('blob') ||
                          engine.toLowerCase().includes('cassandra');

  console.log("Database has proper engine:", hasProperEngine, "Engine:", engine);

  // Check for replication property
  const replication = metadata.replication || '';
  const hasShardedReplication = replication === 'Sharded';

  console.log("Database has sharded replication:", hasShardedReplication);

  // Be lenient - just require the connection
  return hasConnection;
};

// Check if CDN is added and connected
export const checkCDNCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking CDN completion");

  // Find a Database node (Media DB)
  const databaseNode = nodes.find(node =>
    (node.data as any)?.type === 'database' &&
    !node.parentId
  );

  console.log("Found database node:", databaseNode);

  if (!databaseNode) return false;

  // Find a CDN node
  const cdnNode = nodes.find(node =>
    (node.data as any)?.type === 'cdn' &&
    !node.parentId
  );

  console.log("Found CDN node:", cdnNode);

  if (!cdnNode) return false;

  // Find a Frontend node
  const frontendNode = nodes.find(node =>
    (node.data as any)?.type === 'frontend' &&
    !node.parentId
  );

  // Check for a connection between Database and CDN
  const hasDbToCdnConnection = edges.some(edge =>
    (edge.source === databaseNode.id && edge.target === cdnNode.id) ||
    (edge.source === cdnNode.id && edge.target === databaseNode.id)
  );

  console.log("Has Database to CDN connection:", hasDbToCdnConnection);

  // Check for a connection between CDN and Frontend (optional)
  const hasCdnToFrontendConnection = frontendNode && edges.some(edge =>
    (edge.source === cdnNode.id && edge.target === frontendNode.id) ||
    (edge.source === frontendNode.id && edge.target === cdnNode.id)
  );

  console.log("Has CDN to Frontend connection:", hasCdnToFrontendConnection);

  // Check if the CDN has the required cacheTTL property
  const cdnData = cdnNode.data as any;
  const metadata = cdnData.metadata || {};

  // Check for built-in cacheTTL property (should be around 300 seconds for 5 minutes)
  const cacheTTL = metadata.cacheTTL;
  const hasCacheTTLProperty = cacheTTL !== undefined &&
    (cacheTTL === 300 || // Exactly 5 minutes
     (cacheTTL >= 250 && cacheTTL <= 350) || // Around 5 minutes
     cacheTTL === 3600); // 1 hour alternative

  console.log("CDN has cacheTTL property:", hasCacheTTLProperty, "Value:", cacheTTL);

  // Check for geoDistribution checkbox
  const hasGeoDistribution = metadata.geoDistribution === true;
  console.log("CDN has geoDistribution enabled:", hasGeoDistribution);

  // Check for contentTypes multi-select
  const contentTypes = metadata.contentTypes || [];
  const hasContentTypes = contentTypes.includes('Images') || contentTypes.includes('Video');
  console.log("CDN has proper contentTypes:", hasContentTypes, "Types:", contentTypes);

  // Be more lenient - require the DB to CDN connection, but make the TTL and Frontend connection optional
  // Give bonus points for having the optional properties
  return hasDbToCdnConnection;
};

// Check if Analytics Service is added and connected
export const checkAnalyticsServiceCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking analytics service completion");

  // Find the Media Service node
  const mediaServiceNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId
  );

  console.log("Found media service node:", mediaServiceNode);

  if (!mediaServiceNode) return false;

  // Find an Analytics Service composite node
  const analyticsNode = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Analytics Service'
  );

  console.log("Found analytics node:", analyticsNode);

  if (!analyticsNode) return false;

  // Check for a connection between Media Service and Analytics
  const hasConnection = edges.some(edge =>
    (edge.source === mediaServiceNode.id && edge.target === analyticsNode.id) ||
    (edge.source === analyticsNode.id && edge.target === mediaServiceNode.id)
  );

  console.log("Has Media Service to Analytics connection:", hasConnection);

  // Check if the Analytics Service has internal components
  const childNodes = nodes.filter(node => node.parentId === analyticsNode.id);
  console.log("Analytics Service child nodes:", childNodes);

  // Check for optional custom properties
  const analyticsData = analyticsNode.data as any;
  const customProps = analyticsData.metadata?.customProperties || [];

  const hasProcessingTypeProperty = customProps.some((prop: any) =>
    prop.key.toLowerCase() === 'processing type' &&
    (prop.value.toLowerCase().includes('real-time') ||
     prop.value.toLowerCase().includes('batch'))
  );

  const hasMetricsProperty = customProps.some((prop: any) =>
    prop.key.toLowerCase() === 'metrics' &&
    (prop.value.toLowerCase().includes('user engagement') ||
     prop.value.toLowerCase().includes('system performance'))
  );

  console.log("Analytics has Processing Type property:", hasProcessingTypeProperty);
  console.log("Analytics has Metrics property:", hasMetricsProperty);

  // Be more lenient - just require the connection and the composite
  // Give bonus points for having internal components and custom properties
  return hasConnection;
};

// Check if Notifications are added and connected
export const checkNotificationsCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking notifications completion");

  // Find the Media Service node
  const mediaServiceNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId
  );

  console.log("Found media service node:", mediaServiceNode);

  if (!mediaServiceNode) return false;

  // Find a new Server node that's not the Media Service
  // This could be named "Notification Service" or something similar
  const notificationNode = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    node.id !== mediaServiceNode.id &&
    !node.parentId &&
    (!(node.data as any)?.label || // Allow unnamed servers
     !(node.data as any)?.label.includes('Media')) // Make sure it's not another media service
  );

  console.log("Found notification service node:", notificationNode);

  if (!notificationNode) return false;

  // Find a Queue node that's not the one between Auth and Media
  const queueNode = nodes.find(node => {
    if ((node.data as any)?.type !== 'queue' || node.parentId) return false;

    // Check if this is not the queue between Auth and Media
    const authNode = nodes.find(n =>
      n.type === 'compositeNode' &&
      (n.data as any)?.label === 'Authentication Service'
    );

    // If we can't find the auth node, assume this is a new queue
    if (!authNode) return true;

    // If this queue is connected to auth, it might be the auth-media queue
    const isConnectedToAuth = edges.some(e =>
      e.source === authNode.id && e.target === node.id
    );

    // If not connected to auth, it's a different queue
    if (!isConnectedToAuth) return true;

    // If connected to auth, check if it's also connected to media
    const isConnectedToMedia = edges.some(e =>
      e.source === node.id && e.target === mediaServiceNode.id
    );

    // If not connected to media, it's a different queue
    return !isConnectedToMedia;
  });

  console.log("Found event queue node:", queueNode);

  if (!queueNode) return false;

  // Check for connections:
  // 1. Media Service to Queue
  const mediaToQueueConnection = edges.some(edge =>
    edge.source === mediaServiceNode.id && edge.target === queueNode.id
  );

  // 2. Queue to Notification Service
  const queueToNotificationConnection = edges.some(edge =>
    edge.source === queueNode.id && edge.target === notificationNode.id
  );

  console.log("Has Media->Queue connection:", mediaToQueueConnection);
  console.log("Has Queue->Notification connection:", queueToNotificationConnection);

  // Check for optional custom properties
  const notificationData = notificationNode.data as any;
  const customProps = notificationData.metadata?.customProperties || [];

  const hasDeliveryModeProperty = customProps.some((prop: any) =>
    prop.key.toLowerCase() === 'delivery mode' &&
    (prop.value.toLowerCase().includes('high volume') ||
     prop.value.toLowerCase().includes('low latency'))
  );

  console.log("Notification has Delivery Mode property:", hasDeliveryModeProperty);

  // Both connections must exist
  return mediaToQueueConnection && queueToNotificationConnection;
};

// Default check for steps that just need components and connections
export const checkDefaultStepCompletion = (
  currentStep: CourseStep,
  nodes: Node[],
  edges: Edge[]
): boolean => {
  console.log(`Checking default completion for step: ${currentStep.id}`);

  // For informational steps, always return true
  if (currentStep.isInformationalOnly) {
    return true;
  }

  // Check if all required component types are present
  const requiredComponents = currentStep.goal.components || [];
  const missingComponents = requiredComponents.filter(compType => {
    if (compType === 'compositeNode') {
      return !nodes.some(node => node.type === 'compositeNode');
    }

    return !nodes.some(node =>
      (node.data as any)?.type === compType && !node.parentId
    );
  });

  console.log("Required components:", requiredComponents);
  console.log("Missing components:", missingComponents);

  // Check if all required connections are present
  const requiredConnections = currentStep.goal.connections || [];
  const missingConnections = requiredConnections.filter(([sourceType, targetType]) => {
    // Find nodes of the source type
    const sourceNodes = nodes.filter(node => {
      if (sourceType === 'compositeNode') {
        return node.type === 'compositeNode';
      }
      return (node.data as any)?.type === sourceType && !node.parentId;
    });

    // Find nodes of the target type
    const targetNodes = nodes.filter(node => {
      if (targetType === 'compositeNode') {
        return node.type === 'compositeNode';
      }
      return (node.data as any)?.type === targetType && !node.parentId;
    });

    // Check if there's at least one connection between any source and target
    return !sourceNodes.some(sourceNode =>
      targetNodes.some(targetNode =>
        edges.some(edge =>
          edge.source === sourceNode.id && edge.target === targetNode.id
        )
      )
    );
  });

  console.log("Required connections:", requiredConnections);
  console.log("Missing connections:", missingConnections);

  // For steps with simulation requirements, we'll be more lenient
  if (currentStep.goal.simulation?.required) {
    return missingComponents.length === 0;
  }

  // For export step, always return true (handled by export button)
  if (currentStep.goal.exportRequired) {
    return true;
  }

  return missingComponents.length === 0 && missingConnections.length === 0;
};

// Check if required custom properties are set on components
export const checkRequiredCustomProperties = (
  nodes: Node[],
  requiredProperties: { componentType: string; key: string; value: string; }[]
): boolean => {
  console.log("Checking required custom properties:", requiredProperties);

  // For each required property
  for (const requirement of requiredProperties) {
    // Find nodes of the required type
    const matchingNodes = nodes.filter(node => {
      if (requirement.componentType === 'compositeNode') {
        return node.type === 'compositeNode';
      }
      return (node.data as any)?.type === requirement.componentType && !node.parentId;
    });

    console.log(`Found ${matchingNodes.length} nodes of type ${requirement.componentType}`);

    // Check if any of the matching nodes has the required property
    const hasProperty = matchingNodes.some(node => {
      const data = node.data as any;
      const customProps = data.metadata?.customProperties || [];
      return customProps.some((prop: any) =>
        prop.key.toLowerCase() === requirement.key.toLowerCase() &&
        prop.value === requirement.value
      );
    });

    console.log(`Property ${requirement.key}=${requirement.value} found: ${hasProperty}`);

    if (!hasProperty) {
      return false;
    }
  }

  return true;
};

// Check if the final simulation step is completed
export const checkFinalSimulationCompletion = (nodes: Node[], edges: Edge[]): boolean => {
  console.log("Checking final simulation completion");

  // Find core components
  const frontend = nodes.find(node =>
    (node.data as any)?.type === 'frontend' && !node.parentId
  );

  const authService = nodes.find(node =>
    node.type === 'compositeNode' &&
    (node.data as any)?.label === 'Authentication Service'
  );

  const mediaService = nodes.find(node =>
    (node.data as any)?.type === 'server' &&
    ((node.data as any)?.label === 'Media Service' ||
     ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
    !node.parentId
  );

  console.log("Core components:", {
    frontend: !!frontend,
    authService: !!authService,
    mediaService: !!mediaService
  });

  if (!frontend || !authService || !mediaService) return false;

  // Check for core connections
  const frontendToAuth = edges.some(edge =>
    (edge.source === frontend.id && edge.target === authService.id) ||
    (edge.source === authService.id && edge.target === frontend.id)
  );

  const authToMedia = edges.some(edge =>
    (edge.source === authService.id && edge.target === mediaService.id) ||
    (edge.source === mediaService.id && edge.target === authService.id)
  );

  console.log("Core connections:", {
    frontendToAuth,
    authToMedia
  });

  if (!frontendToAuth) return false;

  // Check if required services have maxQPS set
  const requiredServicesForQPSSettings = [
    { node: authService, name: 'Authentication Service' },
    { node: mediaService, name: 'Media Service' }
  ];

  // Optional services - only check if they exist
  // We could check for additional services like Feed Generator, but we'll keep it simple for now
  // const feedGenerator = nodes.find(node =>
  //   (node.data as any)?.type === 'server' &&
  //   node.id !== mediaService.id &&
  //   !node.parentId
  // );

  // Only check required services for QPS settings
  const hasRequiredQPSSettings = requiredServicesForQPSSettings.every(service => {
    if (service.node) {
      const data = service.node.data as any;
      const customProps = data.metadata?.customProperties || [];
      const hasQPS = customProps.some((prop: any) =>
        prop.key.toLowerCase() === 'maxqps' && prop.value
      );
      console.log(`${service.name} has maxQPS:`, hasQPS);
      return hasQPS;
    }
    return false;
  });

  // For the final simulation, we need the core components with connections
  // and QPS settings on required services
  return !!frontend && !!authService && !!mediaService &&
         !!frontendToAuth && hasRequiredQPSSettings;
}
