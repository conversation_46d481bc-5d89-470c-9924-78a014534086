import { useCallback, useEffect, useRef } from 'react';
import { SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { loadDesignFromSupabase } from '@/services/designService';
import { useAuth } from '@/contexts/AuthContext';

interface UseDesignSyncProps {
  currentQuestionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  onDesignUpdated?: (design: SavedDesign) => void;
  onConflictDetected?: (localDesign: SavedDesign, serverDesign: SavedDesign) => void;
}

interface DesignTimestamp {
  questionId: string;
  contextType: DesignContextCategory;
  contextId: string;
  timestamp: string;
  source: 'localStorage' | 'supabase';
}

export const useDesignSync = ({
  currentQuestionId,
  contextType = 'question',
  contextId,
  onDesignUpdated,
  onConflictDetected
}: UseDesignSyncProps) => {
  const { user } = useAuth();
  const syncIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastSyncTimestamps = useRef<Map<string, DesignTimestamp>>(new Map());
  const isOnline = useRef(navigator.onLine);

  // Generate design key
  const getDesignKey = useCallback((questionId: string, contextType: DesignContextCategory, contextId?: string) => {
    const id = contextId || questionId;
    return `layrs-design-${contextType}-${id}`;
  }, []);

  // Get design identifier for tracking
  const getDesignId = useCallback((questionId: string, contextType: DesignContextCategory, contextId?: string) => {
    const id = contextId || questionId;
    return `${contextType}-${id}`;
  }, []);

  // Check if localStorage design is stale compared to server
  const checkForStaleData = useCallback(async (
    questionId: string,
    contextType: DesignContextCategory,
    contextId?: string
  ): Promise<{ isStale: boolean; localDesign?: SavedDesign; serverDesign?: SavedDesign }> => {
    if (!user) return { isStale: false };

    const designKey = getDesignKey(questionId, contextType, contextId);
    const designId = getDesignId(questionId, contextType, contextId);

    try {
      // Get localStorage design
      const localDataJson = localStorage.getItem(designKey);
      if (!localDataJson) return { isStale: false };

      const localDesign = JSON.parse(localDataJson) as SavedDesign;
      const localTimestamp = new Date(localDesign.lastModified).getTime();

      // Get server design
      const serverDesign = await loadDesignFromSupabase(user.id, questionId, contextType, contextId);
      if (!serverDesign) return { isStale: false };

      const serverTimestamp = new Date(serverDesign.lastModified).getTime();

      // Check if server is newer than localStorage
      const isStale = serverTimestamp > localTimestamp;

      if (isStale) {
        console.log(`🔄 Stale data detected for ${designId}:`, {
          local: new Date(localTimestamp).toISOString(),
          server: new Date(serverTimestamp).toISOString(),
          difference: serverTimestamp - localTimestamp
        });
      }

      return {
        isStale,
        localDesign: isStale ? localDesign : undefined,
        serverDesign: isStale ? serverDesign : undefined
      };
    } catch (error) {
      console.error('Error checking for stale data:', error);
      return { isStale: false };
    }
  }, [user, getDesignKey, getDesignId]);

  // Sync design with server
  const syncDesign = useCallback(async (
    questionId: string,
    contextType: DesignContextCategory,
    contextId?: string,
    forceUpdate = false
  ) => {
    if (!user || !isOnline.current) return;

    const designId = getDesignId(questionId, contextType, contextId);
    const designKey = getDesignKey(questionId, contextType, contextId);

    try {
      const { isStale, localDesign, serverDesign } = await checkForStaleData(questionId, contextType, contextId);

      if (isStale && localDesign && serverDesign) {
        // Check if there are actual content differences (not just timestamp)
        const localContent = JSON.stringify({
          nodes: localDesign.nodes,
          edges: localDesign.edges,
          userJourneys: localDesign.userJourneys,
          assumptions: localDesign.assumptions,
          constraints: localDesign.constraints
        });

        const serverContent = JSON.stringify({
          nodes: serverDesign.nodes,
          edges: serverDesign.edges,
          userJourneys: serverDesign.userJourneys,
          assumptions: serverDesign.assumptions,
          constraints: serverDesign.constraints
        });

        if (localContent !== serverContent) {
          // Real conflict detected
          console.warn(`⚠️ Design conflict detected for ${designId}`);
          onConflictDetected?.(localDesign, serverDesign);
          return;
        }
      }

      if (isStale || forceUpdate) {
        // Update localStorage with server version
        if (serverDesign) {
          localStorage.setItem(designKey, JSON.stringify(serverDesign));
          console.log(`✅ Updated localStorage with server version for ${designId}`);
          onDesignUpdated?.(serverDesign);
        }
      }

      // Update sync timestamp
      lastSyncTimestamps.current.set(designId, {
        questionId,
        contextType,
        contextId: contextId || questionId,
        timestamp: new Date().toISOString(),
        source: 'supabase'
      });

    } catch (error) {
      console.error(`Error syncing design ${designId}:`, error);
    }
  }, [user, getDesignId, getDesignKey, checkForStaleData, onConflictDetected, onDesignUpdated]);

  // Clear stale localStorage data and repopulate from Supabase
  const clearAndRepopulate = useCallback(async (
    questionId: string,
    contextType: DesignContextCategory,
    contextId?: string
  ) => {
    if (!user) return null;

    const designKey = getDesignKey(questionId, contextType, contextId);
    const designId = getDesignId(questionId, contextType, contextId);

    console.log(`🧹 Clearing localStorage and repopulating from Supabase for ${designId}`);

    try {
      // Remove from localStorage
      localStorage.removeItem(designKey);

      // Load fresh from Supabase
      const serverDesign = await loadDesignFromSupabase(user.id, questionId, contextType, contextId);

      if (serverDesign) {
        // Save fresh data to localStorage
        localStorage.setItem(designKey, JSON.stringify(serverDesign));
        console.log(`✅ Repopulated localStorage with fresh server data for ${designId}`);
        return serverDesign;
      }

      return null;
    } catch (error) {
      console.error(`Error clearing and repopulating ${designId}:`, error);
      return null;
    }
  }, [user, getDesignKey, getDesignId]);

  // Start periodic sync
  const startSync = useCallback(() => {
    if (!user || !currentQuestionId) return;

    // Clear existing interval
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
    }

    // Sync every 30 seconds
    syncIntervalRef.current = setInterval(() => {
      if (isOnline.current && currentQuestionId) {
        syncDesign(currentQuestionId, contextType, contextId);
      }
    }, 30000);

    console.log(`🔄 Started design sync for ${getDesignId(currentQuestionId, contextType, contextId)}`);
  }, [user, currentQuestionId, contextType, contextId, syncDesign, getDesignId]);

  // Stop sync
  const stopSync = useCallback(() => {
    if (syncIntervalRef.current) {
      clearInterval(syncIntervalRef.current);
      syncIntervalRef.current = null;
      console.log('⏹️ Stopped design sync');
    }
  }, []);

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => {
      isOnline.current = true;
      console.log('🌐 Back online - resuming sync');
      if (currentQuestionId) {
        syncDesign(currentQuestionId, contextType, contextId, true);
      }
    };

    const handleOffline = () => {
      isOnline.current = false;
      console.log('📴 Gone offline - pausing sync');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [currentQuestionId, contextType, contextId, syncDesign]);

  // Handle visibility change (tab switching)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && currentQuestionId && isOnline.current) {
        // Tab became visible - check for updates
        console.log('👁️ Tab became visible - checking for updates');
        syncDesign(currentQuestionId, contextType, contextId, false);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [currentQuestionId, contextType, contextId, syncDesign]);

  // Start/stop sync based on current question
  useEffect(() => {
    if (currentQuestionId && user) {
      startSync();
    } else {
      stopSync();
    }

    return stopSync;
  }, [currentQuestionId, user, startSync, stopSync]);

  return {
    checkForStaleData,
    syncDesign,
    clearAndRepopulate,
    startSync,
    stopSync,
    isOnline: isOnline.current
  };
};
