import { useRef, useCallback } from 'react';

// Save status for both manual and auto saves
export enum SaveStatus {
  IDLE = 'idle',
  SAVING = 'saving',
  SAVED = 'saved',
  ERROR = 'error',
  AUTO_SAVING = 'auto_saving',
  AUTO_SAVED = 'auto_saved',
  PENDING = 'pending'
}

// Simple manual save hook interface
interface UseManualSaveProps {
  onSave: (data: any, syncToSupabase?: boolean) => Promise<void>;
  onStatusChange?: (status: SaveStatus, lastSaved?: Date) => void;
}

export const useManualSave = ({
  onSave,
  onStatusChange
}: UseManualSaveProps) => {
  // Status tracking
  const currentStatus = useRef<SaveStatus>(SaveStatus.IDLE);
  const lastSavedDate = useRef<Date | null>(null);

  // Update status and notify
  const updateStatus = useCallback((status: SaveStatus) => {
    currentStatus.current = status;
    onStatusChange?.(status, lastSavedDate.current || undefined);
  }, [onStatusChange]);

  // Simple manual save execution
  const executeSave = useCallback(async (data: any, syncToSupabase = false) => {
    try {
      updateStatus(SaveStatus.SAVING);
      await onSave(data, syncToSupabase);

      lastSavedDate.current = new Date();
      updateStatus(SaveStatus.SAVED);

      console.log(`✅ Manual save completed`);
    } catch (error) {
      console.error(`❌ Manual save failed:`, error);
      updateStatus(SaveStatus.ERROR);
      throw error; // Re-throw so caller can handle
    }
  }, [onSave, updateStatus]);

  // Force immediate save (manual save)
  const forceSave = useCallback(async (data: any, syncToSupabase = true) => {
    console.log('🚀 Manual save called:', {
      syncToSupabase,
      hasData: !!data
    });
    await executeSave(data, syncToSupabase);
  }, [executeSave]);

  return {
    forceSave,
    status: currentStatus.current,
    lastSaved: lastSavedDate.current
  };
};
