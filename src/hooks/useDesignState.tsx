import { useState, useRef, useCallback } from 'react';
import { Node, Edge } from '@xyflow/react';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useDesign } from '@/contexts/DesignContext';
import { toast } from 'sonner';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export const useDesignState = () => {
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);
  const [userJourneys, setUserJourneys] = useState<string>('');
  const [assumptions, setAssumptions] = useState<string>('');
  const [constraints, setConstraints] = useState<string>('');
  const [isAssessing, setIsAssessing] = useState<boolean>(false);

  // Create refs to track state
  const shownToasts = useRef<Set<string>>(new Set());
  const originalDesignRef = useRef<{nodes: Node[], edges: Edge[], timestamp: number} | null>(null);
  const modifiedNodesRef = useRef<Set<string>>(new Set());
  const deletedNodesRef = useRef<Set<string>>(new Set());

  const { currentQuestion } = useQuestions();
  const {
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges
  } = useDesign();

  // Helper function to merge nodes and edges
  const mergeNodesAndEdges = useCallback((currentNodes: Node[], currentEdges: Edge[]) => {
    // If we don't have an original design, just use current state
    if (!originalDesignRef.current) {
      debugLog('No original design reference, using current state');
      return { nodes: currentNodes, edges: currentEdges };
    }

    // Get the original node and edge IDs
    const originalNodeIds = new Set(originalDesignRef.current.nodes.map(n => n.id));
    const originalEdgeIds = new Set(originalDesignRef.current.edges.map(e => e.id));

    // Find nodes that were added since the original design was loaded
    const addedNodes = currentNodes.filter(node => !originalNodeIds.has(node.id));
    debugLog(`Found ${addedNodes.length} new nodes added since original load`);

    // Find edges that were added since the original design was loaded
    const addedEdges = currentEdges.filter(edge => !originalEdgeIds.has(edge.id));
    debugLog(`Found ${addedEdges.length} new edges added since original load`);

    // Create maps for quick lookup
    const currentNodesMap = new Map(currentNodes.map(node => [node.id, node]));

    // Process all nodes from the original design
    const mergedNodes: Node[] = [];

    // First, process original nodes
    for (const originalNodeId of originalNodeIds) {
      // Skip nodes that have been deleted by the user
      if (deletedNodesRef.current.has(originalNodeId)) {
        debugLog(`Skipping deleted node ${originalNodeId}`);
        continue;
      }

      const currentNode = currentNodesMap.get(originalNodeId);

      if (currentNode) {
        // Node exists in current state - use it (preserves all modifications)
        mergedNodes.push(currentNode);
      }
    }

    // Add the new nodes
    mergedNodes.push(...addedNodes);

    // Create maps for edge lookup
    const currentEdgesMap = new Map(currentEdges.map(edge => [edge.id, edge]));

    // Process original edges - keep them if they still exist, with updated properties
    const mergedEdges = originalDesignRef.current.edges
      .filter(edge => currentEdgesMap.has(edge.id)) // Keep only edges that still exist
      .map(edge => currentEdgesMap.get(edge.id)!);  // Use the current version

    // Add the new edges
    mergedEdges.push(...addedEdges);

    return { nodes: mergedNodes, edges: mergedEdges };
  }, [originalDesignRef, deletedNodesRef]);

  return {
    nodes,
    setNodes,
    edges, 
    setEdges,
    userJourneys,
    setUserJourneys,
    assumptions,
    setAssumptions,
    constraints,
    setConstraints,
    isAssessing,
    setIsAssessing,
    shownToasts,
    originalDesignRef,
    modifiedNodesRef,
    deletedNodesRef,
    currentQuestion,
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    mergeNodesAndEdges
  };
};
