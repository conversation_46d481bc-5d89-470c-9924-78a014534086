import { useState, useCallback } from 'react';
import { Node, Edge } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface ClipboardData {
  nodes: Node[];
  edges: Edge[];
  timestamp: number;
}

/**
 * Hook for managing copy-paste functionality
 */
export const useClipboard = () => {
  const [clipboardData, setClipboardData] = useState<ClipboardData | null>(null);
  
  const copyToClipboard = useCallback((nodes: Node[], edges: Edge[]) => {
    if (nodes.length === 0 && edges.length === 0) {
      debugLog('⚠️ Nothing selected to copy');
      return;
    }
    
    const data: ClipboardData = {
      nodes: nodes.map(node => ({
        ...node,
        selected: false, // Don't copy selection state
      })),
      edges: edges.map(edge => ({
        ...edge,
        selected: false, // Don't copy selection state
      })),
      timestamp: Date.now()
    };
    
    setClipboardData(data);
    
    // Also try to copy to system clipboard as JSON (for cross-tab support)
    try {
      navigator.clipboard.writeText(JSON.stringify(data));
    } catch (error) {
      debugLog('📋 System clipboard not available, using internal clipboard');
    }
    
    debugLog(`📋 Copied ${nodes.length} nodes and ${edges.length} edges to clipboard`);
  }, []);
  
  const pasteFromClipboard = useCallback(async (
    currentNodes: Node[],
    currentEdges: Edge[],
    onNodesChange: (nodes: Node[]) => void,
    onEdgesChange: (edges: Edge[]) => void,
    viewport?: { x: number; y: number; zoom: number }
  ) => {
    let dataToUse = clipboardData;
    
    // Try to get from system clipboard first
    try {
      const systemClipboard = await navigator.clipboard.readText();
      const parsedData = JSON.parse(systemClipboard);
      if (parsedData.nodes && parsedData.edges && parsedData.timestamp) {
        dataToUse = parsedData;
      }
    } catch (error) {
      // Fall back to internal clipboard
    }
    
    if (!dataToUse) {
      debugLog('📋 Clipboard is empty');
      return;
    }
    
    // Generate new IDs for copied elements to avoid conflicts
    const idMap = new Map<string, string>();
    
    const newNodes = dataToUse.nodes.map(node => {
      const newId = `${node.type || 'default'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      idMap.set(node.id, newId);
      
      return {
        ...node,
        id: newId,
        position: {
          x: node.position.x + 50, // Offset to avoid overlap
          y: node.position.y + 50
        },
        selected: true, // Select newly pasted items
      };
    });
    
    const newEdges = dataToUse.edges
      .filter(edge => idMap.has(edge.source) && idMap.has(edge.target)) // Only include edges between copied nodes
      .map(edge => {
        const newId = `edge-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        return {
          ...edge,
          id: newId,
          source: idMap.get(edge.source)!,
          target: idMap.get(edge.target)!,
          selected: true, // Select newly pasted items
        };
      });
    
    // Clear selection from existing elements
    const updatedCurrentNodes = currentNodes.map(node => ({
      ...node,
      selected: false
    }));
    
    const updatedCurrentEdges = currentEdges.map(edge => ({
      ...edge,
      selected: false
    }));
    
    // Add new elements
    onNodesChange([...updatedCurrentNodes, ...newNodes]);
    onEdgesChange([...updatedCurrentEdges, ...newEdges]);
    
    debugLog(`📋 Pasted ${newNodes.length} nodes and ${newEdges.length} edges`);
  }, [clipboardData]);
  
  const hasClipboardData = useCallback(() => {
    return clipboardData !== null && clipboardData.nodes.length > 0;
  }, [clipboardData]);
  
  const clearClipboard = useCallback(() => {
    setClipboardData(null);
    debugLog('📋 Clipboard cleared');
  }, []);
  
  return {
    copyToClipboard,
    pasteFromClipboard,
    hasClipboardData,
    clearClipboard,
    clipboardData
  };
};
