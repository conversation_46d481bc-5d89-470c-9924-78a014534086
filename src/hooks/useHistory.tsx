import { useState, useCallback, useRef } from 'react';
import { Node, Edge } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface HistoryState {
  nodes: Node[];
  edges: Edge[];
  timestamp: number;
  action: string;
}

/**
 * Hook for managing undo/redo functionality
 */
export const useHistory = (maxHistorySize: number = 50) => {
  const [history, setHistory] = useState<HistoryState[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const isUndoRedoAction = useRef(false);
  const lastSaveTime = useRef(0);
  const pendingAction = useRef<string | null>(null);

  const saveToHistory = useCallback((
    nodes: Node[],
    edges: Edge[],
    action: string = 'Unknown action',
    forceImmediate: boolean = false
  ) => {
    // Don't save to history if this is an undo/redo action
    if (isUndoRedoAction.current) {
      return;
    }

    const now = Date.now();
    const timeSinceLastSave = now - lastSaveTime.current;

    // Group rapid actions together (within 1 second) unless forced
    if (!forceImmediate && timeSinceLastSave < 1000 && pendingAction.current) {
      // Update pending action to the most recent one
      pendingAction.current = action;
      return;
    }

    const newState: HistoryState = {
      nodes: nodes.map(node => ({ ...node })), // Deep copy
      edges: edges.map(edge => ({ ...edge })), // Deep copy
      timestamp: now,
      action: pendingAction.current || action
    };

    setHistory(prevHistory => {
      // Remove any future history if we're not at the end
      const newHistory = prevHistory.slice(0, currentIndex + 1);

      // Add new state
      newHistory.push(newState);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        setCurrentIndex(prev => Math.max(0, prev));
      } else {
        setCurrentIndex(newHistory.length - 1);
      }

      return newHistory;
    });

    lastSaveTime.current = now;
    pendingAction.current = null;

    debugLog(`💾 Saved to history: ${pendingAction.current || action}`);
  }, [currentIndex, maxHistorySize]);

  const undo = useCallback((
    onNodesChange: (nodes: Node[]) => void,
    onEdgesChange: (edges: Edge[]) => void
  ) => {
    if (currentIndex <= 0) {
      debugLog('↩️ Nothing to undo');
      return false;
    }

    const previousState = history[currentIndex - 1];
    if (!previousState) {
      debugLog('↩️ No previous state found');
      return false;
    }

    isUndoRedoAction.current = true;

    // Restore previous state
    onNodesChange([...previousState.nodes]);
    onEdgesChange([...previousState.edges]);

    setCurrentIndex(prev => prev - 1);

    debugLog(`↩️ Undid: ${history[currentIndex]?.action || 'Unknown action'}`);

    // Reset flag after a brief delay
    setTimeout(() => {
      isUndoRedoAction.current = false;
    }, 100);

    return true;
  }, [history, currentIndex]);

  const redo = useCallback((
    onNodesChange: (nodes: Node[]) => void,
    onEdgesChange: (edges: Edge[]) => void
  ) => {
    if (currentIndex >= history.length - 1) {
      debugLog('↪️ Nothing to redo');
      return false;
    }

    const nextState = history[currentIndex + 1];
    if (!nextState) {
      debugLog('↪️ No next state found');
      return false;
    }

    isUndoRedoAction.current = true;

    // Restore next state
    onNodesChange([...nextState.nodes]);
    onEdgesChange([...nextState.edges]);

    setCurrentIndex(prev => prev + 1);

    debugLog(`↪️ Redid: ${nextState.action}`);

    // Reset flag after a brief delay
    setTimeout(() => {
      isUndoRedoAction.current = false;
    }, 100);

    return true;
  }, [history, currentIndex]);

  const canUndo = useCallback(() => {
    return currentIndex > 0;
  }, [currentIndex]);

  const canRedo = useCallback(() => {
    return currentIndex < history.length - 1;
  }, [currentIndex, history.length]);

  const clearHistory = useCallback(() => {
    setHistory([]);
    setCurrentIndex(-1);
    debugLog('🗑️ History cleared');
  }, []);

  const getHistoryInfo = useCallback(() => {
    return {
      totalStates: history.length,
      currentIndex,
      canUndo: canUndo(),
      canRedo: canRedo(),
      currentAction: history[currentIndex]?.action || 'Initial state',
      previousAction: history[currentIndex - 1]?.action || null,
      nextAction: history[currentIndex + 1]?.action || null
    };
  }, [history, currentIndex, canUndo, canRedo]);

  const forceSaveToHistory = useCallback((
    nodes: Node[],
    edges: Edge[],
    action: string = 'Unknown action'
  ) => {
    saveToHistory(nodes, edges, action, true);
  }, [saveToHistory]);

  return {
    saveToHistory,
    forceSaveToHistory,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory,
    getHistoryInfo,
    historySize: history.length
  };
};
