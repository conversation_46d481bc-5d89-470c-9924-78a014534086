import { useState, useCallback, useRef, useEffect } from 'react';
import { Node, Edge } from '@xyflow/react';
import { processLoadedEdges } from '@/utils/edgeProcessing';

interface UndoAction {
  type: 'add_node' | 'add_edge' | 'add_multiple' | 'delete_items';
  nodeIds?: string[];
  edgeIds?: string[];
  deletedNodes?: Node[];
  deletedEdges?: Edge[];
  timestamp: number;
  description: string;
}

/**
 * Simple stack-based undo system
 * Just tracks what was added/deleted and reverses it
 */
export const useSimpleUndo = (maxStackSize: number = 50) => {
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const [redoStack, setRedoStack] = useState<UndoAction[]>([]);
  const undoStackRef = useRef<UndoAction[]>(undoStack);
  const redoStackRef = useRef<UndoAction[]>(redoStack);
  useEffect(() => { undoStackRef.current = undoStack; }, [undoStack]);
  useEffect(() => { redoStackRef.current = redoStack; }, [redoStack]);
  const isUndoRedoAction = useRef(false);

  // Add action to undo stack
  const pushToUndoStack = useCallback((action: UndoAction) => {
    if (isUndoRedoAction.current) {
      return; // Don't track undo/redo actions
    }
    setUndoStack(prev => {
      const newStack = [...prev, action];
      // Limit stack size
      if (newStack.length > maxStackSize) {
        newStack.shift();
      }
      return newStack;
    });
    // Clear redo stack when new action is performed
    setRedoStack(() => []);
  }, [maxStackSize]);

  // Track when a node is added
  const trackNodeAdded = useCallback((nodeId: string, description: string = 'Add component') => {
    pushToUndoStack({
      type: 'add_node',
      nodeIds: [nodeId],
      timestamp: Date.now(),
      description
    });
  }, [pushToUndoStack]);

  // Track when an edge is added
  const trackEdgeAdded = useCallback((edgeId: string, description: string = 'Add connection') => {
    pushToUndoStack({
      type: 'add_edge',
      edgeIds: [edgeId],
      timestamp: Date.now(),
      description
    });
  }, [pushToUndoStack]);

  // Track when multiple items are added (paste operation)
  const trackMultipleAdded = useCallback((nodeIds: string[], edgeIds: string[], description: string = 'Paste items') => {
    if (nodeIds.length > 0 || edgeIds.length > 0) {
      pushToUndoStack({
        type: 'add_multiple',
        nodeIds,
        edgeIds,
        timestamp: Date.now(),
        description
      });
    }
  }, [pushToUndoStack]);

  // Track when multiple items are deleted (node + connected edges operation)
  const trackMultipleDeleted = useCallback((deletedNodes: Node[], deletedEdges: Edge[], description: string = 'Delete multiple items') => {
    if (deletedNodes.length > 0 || deletedEdges.length > 0) {
      console.log('📦 Tracking multiple deleted items for undo:', {
        nodes: deletedNodes.map(n => n.id),
        edges: deletedEdges.map(e => e.id)
      });
      pushToUndoStack({
        type: 'delete_items',
        deletedNodes,
        deletedEdges,
        timestamp: Date.now(),
        description
      });
    }
  }, [pushToUndoStack]);

  // Track when items are deleted
  const trackItemsDeleted = useCallback((deletedNodes: Node[], deletedEdges: Edge[], description: string = 'Delete items') => {
    // Track each deleted node as a separate undo action
    deletedNodes.forEach((node) => {
      console.log('📦 Tracking deleted node for undo:', node.id, node);
      pushToUndoStack({
        type: 'delete_items',
        deletedNodes: [node],
        timestamp: Date.now(),
        description: `Delete node ${node.id}`
      });
    });
  
    // Track each deleted edge as a separate undo action
    deletedEdges.forEach((edge) => {
      console.log('📦 Tracking deleted edge for undo:', edge.id, edge);
      pushToUndoStack({
        type: 'delete_items',
        deletedEdges: [edge],
        timestamp: Date.now(),
        description: `Delete edge ${edge.id}`
      });
    });
  }, [pushToUndoStack]);
  

  // Undo last action
  const undo = useCallback((
    nodes: Node[],
    edges: Edge[],
    setNodes: (nodes: Node[]) => void,
    setEdges: (edges: Edge[]) => void
  ) => {
    const stack = undoStackRef.current;
    if (stack.length === 0) {
      console.log('❌ Undo: No actions in undo stack');
      return false;
    }
    const lastAction = stack[stack.length - 1];
    console.log('🔄 Undo: Processing action:', lastAction);
    console.log('🔄 Undo: Current state - nodes:', nodes.length, 'edges:', edges.length);
    isUndoRedoAction.current = true;
    try {
      switch (lastAction.type) {
        case 'add_node':
          // Remove the added node
          if (lastAction.nodeIds) {
            setNodes(nodes.filter(node => !lastAction.nodeIds!.includes(node.id)));
          }
          break;

        case 'add_edge':
          // Remove the added edge
          if (lastAction.edgeIds) {
            setEdges(edges.filter(edge => !lastAction.edgeIds!.includes(edge.id)));
          }
          break;

        case 'add_multiple':
          // Remove all added nodes and edges
          if (lastAction.nodeIds) {
            setNodes(nodes.filter(node => !lastAction.nodeIds!.includes(node.id)));
          }
          if (lastAction.edgeIds) {
            setEdges(edges.filter(edge => !lastAction.edgeIds!.includes(edge.id)));
          }
          break;

        case 'delete_items':
          // Restore deleted items
          if (lastAction.deletedNodes) {
            console.log('🔄 Undo: Restoring deleted nodes:', lastAction.deletedNodes.map(n => n.id));
            setNodes([...nodes, ...lastAction.deletedNodes]);
          }
          if (lastAction.deletedEdges) {
            console.log('🔄 Undo: Restoring deleted edges:', lastAction.deletedEdges.map(e => e.id));
            console.log('🔄 Undo: Current edges before restore:', edges.map(e => e.id));
            const newEdges = [...edges, ...lastAction.deletedEdges];
            console.log('🔄 Undo: Combined edges list:', newEdges.map(e => e.id));
            // Use processLoadedEdges to ensure handlers and normalization
            const processed = processLoadedEdges(newEdges, setEdges, nodes);
            console.log('🔄 Undo: Processed edges:', processed.map(e => e.id));
            setEdges(processed);
          }
          break;
      }

      // Move action to redo stack
      setRedoStack(prev => {
        const newStack = [...prev, lastAction];
        return newStack;
      });
      setUndoStack(prev => {
        const newStack = prev.slice(0, -1);
        return newStack;
      });
      return true;
    } finally {
      setTimeout(() => {
        isUndoRedoAction.current = false;
      }, 100);
    }
  }, [undoStack]);

  // Redo last undone action
  const redo = useCallback((
    nodes: Node[],
    edges: Edge[],
    setNodes: (nodes: Node[]) => void,
    setEdges: (edges: Edge[]) => void
  ) => {
    const stack = redoStackRef.current;
    if (stack.length === 0) {
      return false;
    }
    const actionToRedo = stack[stack.length - 1];
    isUndoRedoAction.current = true;
    try {
      switch (actionToRedo.type) {
        case 'add_node':
        case 'add_edge':
        case 'add_multiple':
          // For add actions, we need to re-add the items
          // This is complex because we need the original data
          // For now, just log that redo is not fully implemented for add actions
          break;

        case 'delete_items':
          // For delete actions, remove the items again
          if (actionToRedo.deletedNodes) {
            setNodes(nodes.filter(node => !actionToRedo.deletedNodes!.some(dn => dn.id === node.id)));
          }
          if (actionToRedo.deletedEdges) {
            setEdges(edges.filter(edge => !actionToRedo.deletedEdges!.some(de => de.id === edge.id)));
          }
          break;
      }

      // Move action back to undo stack
      setUndoStack(prev => {
        const newStack = [...prev, actionToRedo];
        return newStack;
      });
      setRedoStack(prev => {
        const newStack = prev.slice(0, -1);
        return newStack;
      });
      return true;
    } finally {
      setTimeout(() => {
        isUndoRedoAction.current = false;
      }, 100);
    }
  }, [redoStack]);

  const canUndo = useCallback(() => undoStack.length > 0, [undoStack.length]);
  const canRedo = useCallback(() => redoStack.length > 0, [redoStack.length]);

  const clearStacks = useCallback(() => {
    setUndoStack([]);
    setRedoStack([]);
  }, []);

  const getStackInfo = useCallback(() => {
    return {
      undoStackSize: undoStack.length,
      redoStackSize: redoStack.length,
      lastAction: undoStack[undoStack.length - 1]?.description || 'None',
      canUndo: canUndo(),
      canRedo: canRedo()
    };
  }, [undoStack, redoStack, canUndo, canRedo]);

  return {
    trackNodeAdded,
    trackEdgeAdded,
    trackMultipleAdded,
    trackMultipleDeleted,
    trackItemsDeleted,
    undo,
    redo,
    canUndo,
    canRedo,
    clearStacks,
    getStackInfo
  };
};
