import { useEffect, useCallback, useRef } from 'react';
import { ReactFlowInstance } from '@xyflow/react';
import { useAutoSave } from '@/hooks/useAutoSave';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { debugLog } from '@/utils/debugLogger';

interface UseAutoSaveIntegrationProps {
  currentQuestionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  reactFlowInstance: ReactFlowInstance | null;
  userJourneys: string;
  assumptions: string;
  constraints: string;
  saveDesign: (questionId: string, design: any, contextType?: DesignContextCategory, contextId?: string, syncToSupabase?: boolean, updateCurrentDesign?: boolean) => Promise<any>;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  mergeNodesAndEdges: (currentNodes: any[], currentEdges: any[]) => { nodes: any[], edges: any[] };
  onStatusChange?: (status: SaveStatus, lastSaved?: Date) => void;
  isOnline?: boolean;
  enabled?: boolean;
}

export const useAutoSaveIntegration = ({
  currentQuestionId,
  contextType = 'question',
  contextId,
  reactFlowInstance,
  userJourneys,
  assumptions,
  constraints,
  saveDesign,
  setHasUnsavedChanges,
  mergeNodesAndEdges,
  onStatusChange,
  isOnline = true,
  enabled = true
}: UseAutoSaveIntegrationProps) => {


  // Initialize auto-save hook
  const autoSave = useAutoSave({
    currentQuestionId,
    contextType,
    contextId,
    reactFlowInstance,
    userJourneys,
    assumptions,
    constraints,
    saveDesign,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    onStatusChange,
    isOnline,
    config: { enabled }
  });

  // Track previous values to detect changes
  const previousValues = useRef({
    userJourneys: '',
    assumptions: '',
    constraints: '',
    nodeCount: 0,
    edgeCount: 0,
    lastNodePositions: new Map<string, { x: number; y: number }>()
  });

  // Debounce timer for change detection
  const changeDetectionTimer = useRef<NodeJS.Timeout | null>(null);
  const isInitialLoad = useRef(true);

  // Get current state for comparison
  const getCurrentState = useCallback(() => {
    const nodes = reactFlowInstance?.getNodes() || [];
    const edges = reactFlowInstance?.getEdges() || [];

    const nodePositions = new Map<string, { x: number; y: number }>();
    nodes.forEach(node => {
      nodePositions.set(node.id, { x: node.position.x, y: node.position.y });
    });

    return {
      userJourneys,
      assumptions,
      constraints,
      nodeCount: nodes.length,
      edgeCount: edges.length,
      lastNodePositions: nodePositions // Use consistent naming
    };
  }, [reactFlowInstance, userJourneys, assumptions, constraints]);

  // Trigger auto-save based on change type
  const triggerAutoSaveForChanges = useCallback((changeType: 'text' | 'canvas' | 'major') => {
    if (!enabled || !currentQuestionId) {
      return;
    }

    switch (changeType) {
      case 'text':
        // Text changes: quick localStorage save, debounced Supabase
        autoSave.triggerFullAutoSave(false); // Not a major change
        break;
      case 'canvas':
        // Canvas changes: quick localStorage save, longer debounced Supabase
        autoSave.triggerAutoSave(false); // Not a major change
        setTimeout(() => autoSave.triggerFullAutoSave(false), 3000);
        break;
      case 'major':
        // Major changes (add/remove nodes): immediate full save with delay
        autoSave.triggerFullAutoSave(true); // Major change - needs delay
        break;
    }
  }, [enabled, currentQuestionId, autoSave]);

  // Throttle canvas changes to prevent excessive triggers
  const lastCanvasChangeTime = useRef<number>(0);
  const CANVAS_CHANGE_THROTTLE = 1000; // 1 second throttle for canvas changes

  // Handle canvas changes (called by parent component)
  const handleCanvasChange = useCallback((changeType: 'canvas' | 'major' = 'canvas') => {
    // Check if auto-save is temporarily disabled (e.g., during undo/redo)
    if ((window as any).__autoSaveDisabled) {
      debugLog(`⏸️ Auto-save disabled, skipping canvas change: ${changeType}`);
      return;
    }
    
    if (!enabled || !currentQuestionId) {
      return;
    }

    const now = Date.now();

    // For major changes, always trigger immediately
    if (changeType === 'major') {
      const currentState = getCurrentState();

      // Update previous values for canvas state
      previousValues.current = {
        ...previousValues.current,
        nodeCount: currentState.nodeCount,
        edgeCount: currentState.edgeCount,
        lastNodePositions: currentState.lastNodePositions
      };

      triggerAutoSaveForChanges(changeType);
      lastCanvasChangeTime.current = now;
      return;
    }

    // For canvas changes (like position), throttle to prevent excessive triggers
    if (now - lastCanvasChangeTime.current < CANVAS_CHANGE_THROTTLE) {
      return;
    }

    const currentState = getCurrentState();

    // Update previous values for canvas state
    previousValues.current = {
      ...previousValues.current,
      nodeCount: currentState.nodeCount,
      edgeCount: currentState.edgeCount,
      lastNodePositions: currentState.lastNodePositions
    };

    // Trigger appropriate auto-save
    triggerAutoSaveForChanges(changeType);
    lastCanvasChangeTime.current = now;
  }, [enabled, currentQuestionId, getCurrentState, triggerAutoSaveForChanges]);

  // Detect text field changes only (canvas changes handled by parent)
  const detectChanges = useCallback(() => {
    if (isInitialLoad.current) {
      isInitialLoad.current = false;
      previousValues.current = getCurrentState();
      return;
    }

    const currentState = getCurrentState();
    const prevState = previousValues.current;

    // Check only text field changes
    if (currentState.userJourneys !== prevState.userJourneys ||
        currentState.assumptions !== prevState.assumptions ||
        currentState.constraints !== prevState.constraints) {

      // Update previous values for text fields
      previousValues.current = {
        ...previousValues.current,
        userJourneys: currentState.userJourneys,
        assumptions: currentState.assumptions,
        constraints: currentState.constraints
      };

      // Trigger text change auto-save
      triggerAutoSaveForChanges('text');
    }
  }, [getCurrentState, triggerAutoSaveForChanges]);

  // Monitor text field changes
  useEffect(() => {
    if (changeDetectionTimer.current) {
      clearTimeout(changeDetectionTimer.current);
    }

    changeDetectionTimer.current = setTimeout(() => {
      detectChanges();
    }, 500); // 500ms debounce for text changes

    return () => {
      if (changeDetectionTimer.current) {
        clearTimeout(changeDetectionTimer.current);
      }
    };
  }, [userJourneys, assumptions, constraints, detectChanges]);

  // Manual trigger for change detection (called by parent component)
  const triggerDetectChanges = useCallback(() => {
    detectChanges();
  }, [detectChanges]);

  // Reset on question change
  useEffect(() => {
    isInitialLoad.current = true;
    previousValues.current = {
      userJourneys: '',
      assumptions: '',
      constraints: '',
      nodeCount: 0,
      edgeCount: 0,
      lastNodePositions: new Map()
    };
  }, [currentQuestionId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (changeDetectionTimer.current) {
        clearTimeout(changeDetectionTimer.current);
      }
    };
  }, []);

  return {
    ...autoSave,
    // Additional methods for manual control
    detectChanges,
    triggerDetectChanges,
    handleCanvasChange,
    isEnabled: enabled && autoSave.isEnabled
  };
};
