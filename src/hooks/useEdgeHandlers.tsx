import React, { useCallback, useState } from 'react';
import { Connection, Edge, Node, addEdge, MarkerType, HandleType } from '@xyflow/react';
import { toast } from 'sonner';
import { useEdgeType } from '@/contexts/EdgeTypeContext';
import { useDialog } from '@/contexts/DialogContext';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface UseEdgeHandlersProps {
  nodes: Node[];
  edges: Edge[];
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  isValidConnection: (connection: Connection) => boolean;
  connectionInfo?: {
    nodeId: string | null;
    handleType: HandleType | null;
    handlePosition: string | null;
  };
  trackItemsDeleted: (deletedNodes: Node[], deletedEdges: Edge[], description?: string) => void;
  handleEdgesChange?: (changes: any[], userInitiated?: boolean) => void;
}

export const useEdgeHandlers = ({
  nodes,
  edges,
  setEdges,
  isValidConnection,
  connectionInfo,
  trackItemsDeleted,
  handleEdgesChange
}: UseEdgeHandlersProps) => {
  // New state for storing a pending connection
  const [pendingConnection, setPendingConnection] = useState<Connection | null>(null);
  const { getEdgeType } = useEdgeType();
  const { showDeleteConnectionDialog } = useDialog();

  const onConnect = useCallback((connection: Connection) => {
    debugLog(`🎯 Connection completed:`, connection);
    debugLog(`📍 SOURCE: Node '${connection.source}' handle '${connection.sourceHandle}'`);
    debugLog(`📍 TARGET: Node '${connection.target}' handle '${connection.targetHandle}'`);
    debugLog(`➡️ Arrow will point from SOURCE to TARGET`);

    // Validate the connection based on composite boundaries
    if (isValidConnection(connection)) {
      debugLog(`✅ Connection is valid, creating edge...`);
      // Create a new edge with a default label
      createEdgeWithNumber(connection, '', '');
    } else {
      debugLog(`❌ Connection is invalid`);
      // Show a toast notification explaining why the connection is invalid
      const sourceNode = nodes.find(n => n.id === connection.source);
      const targetNode = nodes.find(n => n.id === connection.target);

      if (sourceNode?.parentId && !targetNode?.parentId) {
        toast.error("Cannot connect from inside a composite to outside elements");
      } else if (!sourceNode?.parentId && targetNode?.parentId) {
        toast.error("Cannot connect from outside to elements inside a composite");
      } else if (sourceNode?.parentId && targetNode?.parentId && sourceNode.parentId !== targetNode.parentId) {
        toast.error("Cannot connect between elements in different composites");
      } else {
        toast.error("Connection not allowed based on hierarchy rules");
      }

      debugLog('Connection not allowed based on composite boundaries');
    }
  }, [nodes, isValidConnection, getEdgeType]);

  // Function to update edge label in-place
  const updateEdgeLabel = useCallback((edgeId: string, newLabel: string) => {
    debugLog(`Updating edge ${edgeId} label to: ${newLabel}`);
    setEdges(edges =>
      edges.map(edge => {
        if (edge.id === edgeId) {
          return {
            ...edge,
            label: newLabel
          };
        }
        return edge;
      })
    );
  }, [setEdges]);

  // Function to create a new edge with the provided number
  const createEdgeWithNumber = useCallback((connection: Connection, connectionNumber: string, label: string) => {
    // Get source and target nodes to determine if they're internal to same composite
    const sourceNode = nodes.find(n => n.id === connection.source);
    const targetNode = nodes.find(n => n.id === connection.target);

    // Check if both nodes are in the same composite container
    const sameParent = sourceNode?.parentId &&
                      targetNode?.parentId &&
                      sourceNode.parentId === targetNode.parentId;

    // Use different styling for internal vs external connections
    const newEdge = {
      ...connection,
      // Use the selected edge type
      type: getEdgeType(),
      animated: false,
      // Add connection number to edge data
      data: {
        connectionNumber: connectionNumber || '',
        isInternal: sameParent,
        onChange: updateEdgeLabel, // Add the onChange handler for in-place editing
        nodes: nodes // Pass nodes for obstacle avoidance
      },
      // Label to display the number on the edge
      label: label || '',
      labelStyle: {
        fill: '#222',
        fontWeight: 700,
        fontSize: 12
      },
      labelBgStyle: {
        fill: 'white',
        fillOpacity: 0.8,
        stroke: '#ccc',
        strokeWidth: 1
      },
      labelBgPadding: [40, 20] as [number, number],
      // Improved arrow for internal connections - darker and more visible
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: sameParent ? 15 : 20,
        height: sameParent ? 15 : 20,
        color: '#222', // Darker color for better visibility
      },
      // Thicker and darker for internal connections to improve visibility
      style: {
        strokeWidth: sameParent ? 2.5 : 2,
        stroke: '#222',
        // Add a subtle shadow to make edges stand out more
        filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.3))',
        zIndex: sameParent ? 1000 : 0 // Explicitly set higher z-index
      }
    };

    setEdges((eds) => addEdge(newEdge, eds));
    setPendingConnection(null);
  }, [nodes, setEdges, updateEdgeLabel, getEdgeType]);

  // Add a function to handle edge click
  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation();

    // Get the current edge label
    const currentLabel = edge.label || edge.data?.connectionNumber || '';
    const currentType = edge.type || 'smooth';
    // Parse type: e.g. 'dashed-bezier' => ['dashed', 'bezier']
    let visualStyle = 'normal';
    let pathType = 'smooth';
    if (currentType.includes('-')) {
      const [vs, pt] = currentType.split('-');
      visualStyle = vs;
      pathType = pt;
    } else if (currentType === 'bezier' || currentType === 'smooth') {
      pathType = currentType;
      visualStyle = 'normal';
    } else {
      visualStyle = currentType;
      pathType = 'bezier';
    }

    // Use the global dialog context to show the delete dialog
    showDeleteConnectionDialog(
      edge.id, 
      currentLabel as string,
      () => {
        // Use handleEdgesChange if available, otherwise fall back to direct setEdges
        if (handleEdgesChange) {
          debugLog('🗑️ Manual edge deletion via dialog - using handleEdgesChange');
          debugLog('🗑️ Edge to delete:', edge.id, edge);
          
          // Track the edge deletion directly here since we have the edge object
          trackItemsDeleted([], [edge], 'Delete 1 edge via dialog');
          
          const deleteChange = { type: 'remove', id: edge.id };
          handleEdgesChange([deleteChange], true); // Mark as user-initiated
          
          // Force immediate auto-save for edge deletion to prevent tab switch issues
          debugLog('🚀 Forcing immediate auto-save for edge deletion');
          setTimeout(() => {
            // Trigger a canvas change to force immediate processing of auto-save queue
            if ((window as any).__triggerImmediateAutoSave) {
              (window as any).__triggerImmediateAutoSave();
            }
          }, 100); // Small delay to ensure edge removal is processed first
        } else {
          debugLog('🗑️ Manual edge deletion via dialog - using setEdges fallback');
          // Track this edge deletion for undo/redo
          trackItemsDeleted([], [edge], 'Delete 1 edge via dialog');
          setEdges((eds) => eds.filter((e) => e.id !== edge.id));
        }
      },
      (newLabel: string) => {
        setEdges((eds) =>
          eds.map((e) => {
            if (e.id === edge.id) {
              return {
                ...e,
                data: { ...e.data, connectionNumber: newLabel },
                label: newLabel
              };
            }
            return e;
          })
        );
        toast.success(`Connection label updated to: "${newLabel}"`);
      },
      currentType,
      (newStyle: string) => {
        // Update only the visual style, keep pathType
        const newType = newStyle.includes('-') ? newStyle : `${newStyle}-${pathType}`;
        setEdges((eds) =>
          eds.map((e) => {
            if (e.id === edge.id) {
              return {
                ...e,
                type: newType
              };
            }
            return e;
          })
        );
        toast.success(`Connection style updated to: "${newStyle}"`);
      },
      pathType,
      (newPathType: string) => {
        // Update only the path type, keep visualStyle
        const newType = visualStyle === 'normal' ? newPathType : `${visualStyle}-${newPathType}`;
        setEdges((eds) =>
          eds.map((e) => {
            if (e.id === edge.id) {
              return {
                ...e,
                type: newType
              };
            }
            return e;
          })
        );
        toast.success(`Connection path type updated to: "${newPathType}"`);
      }
    );
  }, [showDeleteConnectionDialog, setEdges, trackItemsDeleted]);

  return {
    onConnect,
    onEdgeClick
  };
};
