import { useCallback, useRef } from 'react';
import { Node, OnNodesChange, XYPosition } from '@xyflow/react';
import { toast } from 'sonner';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface UseNodesHandlersProps {
  nodes: Node[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  onNodesChange: OnNodesChange;
  modifiedNodesRef?: React.RefObject<Set<string>>;
  deletedNodesRef?: React.RefObject<Set<string>>;
}

export const useNodesHandlers = ({
  nodes,
  setNodes,
  onNodesChange,
  modifiedNodesRef,
  deletedNodesRef
}: UseNodesHandlersProps) => {
  // Create default empty sets if refs not provided
  const defaultModifiedNodes = new Set<string>();
  const defaultDeletedNodes = new Set<string>();

  // Flag to temporarily disable tracking during bulk operations
  const isResetting = useRef(false);

  // Track if we're currently dragging a node to avoid excessive updates
  const isDraggingRef = useRef<boolean>(false);
  // Track the last position of nodes being dragged to debounce position updates
  const lastPositionsRef = useRef<Record<string, { x: number, y: number }>>({});
  // Track the nodes that were modified during the current drag operation
  const dragModifiedNodesRef = useRef<Set<string>>(new Set());
  // Check if a point is inside a composite node
  const isPointInsideNode = useCallback((point: XYPosition, node: Node): boolean => {
    // Get node dimensions from the DOM
    const nodeElement = document.querySelector(`[data-id="${node.id}"]`);
    if (!nodeElement) {
      debugLog(`DOM element for node ${node.id} not found`);
      return false;
    }
    const bounds = nodeElement.getBoundingClientRect();

    // Calculate if the point is within the node's bounds in screen coordinates
    const isInside = (
      point.x >= bounds.left &&
      point.x <= bounds.right &&
      point.y >= bounds.top &&
      point.y <= bounds.bottom
    );

    if (isInside) {
      debugLog(`Point ${point.x},${point.y} is inside node ${node.id} bounds:`, bounds);
    }

    return isInside;
  }, []);

  // Find appropriate composite node at a point, considering hierarchy constraints
  const findCompositeAtPoint = useCallback(
    (point: XYPosition, draggedNode: Node | null = null): Node | null => {
      debugLog('findCompositeAtPoint called with point:', point, 'draggedNode:', draggedNode?.id);

      // Filter for composite nodes only
      const compositeNodes = nodes.filter(node => node.type === 'compositeNode');
      debugLog('Available composite nodes:', compositeNodes.map(n => ({ id: n.id, parentId: n.parentId })));

      // Find all composites that contain the point
      const matchingComposites = compositeNodes.filter(node =>
        isPointInsideNode(point, node)
      );
      debugLog('Matching composites at point:', matchingComposites.map(n => ({ id: n.id, parentId: n.parentId })));

      if (matchingComposites.length === 0) {
        debugLog('No matching composites found at point');
        return null;
      }

      // If we have a dragged node, filter for composites at the same hierarchy level
      if (draggedNode) {
        // Get the parent ID of the dragged node (null if top-level)
        const draggedNodeParentId = draggedNode.parentId || null;
        debugLog('Dragged node parent ID:', draggedNodeParentId);

        // Filter for composites that share the same parent as the dragged node
        // and are not the dragged node itself
        const sameHierarchyComposites = matchingComposites.filter(composite =>
          composite.id !== draggedNode.id &&
          (composite.parentId || null) === draggedNodeParentId
        );
        debugLog('Same hierarchy composites:', sameHierarchyComposites.map(n => ({ id: n.id, parentId: n.parentId })));

        if (sameHierarchyComposites.length > 0) {
          // If we have matches at the same hierarchy level, return the first one
          debugLog('Returning same hierarchy composite:', sameHierarchyComposites[0].id);
          return sameHierarchyComposites[0];
        } else {
          debugLog('No same hierarchy composites found, falling back to first match');
        }
      }

      // If no dragged node specified or no same-hierarchy matches,
      // return the first matching composite
      debugLog('Returning first matching composite:', matchingComposites[0].id);
      return matchingComposites[0];
    },
    [nodes, isPointInsideNode]
  );

  // Custom nodes change handler that maintains relative positions of child nodes when parent is moved
  const handleNodesChange: OnNodesChange = useCallback(
    (changes) => {
      debugLog('handleNodesChange called with changes:', changes);

      // First apply changes normally
      onNodesChange(changes);

      // For each change, track modifications and deletions
      // For each position change, update direct child nodes if their parent moved
      changes.forEach(change => {
        // Handle position changes with debouncing
        if (change.type === 'position' && change.position && change.id) {
          // Add to temporary tracking during drag
          dragModifiedNodesRef.current.add(change.id);

          // Only track significant position changes to reduce updates
          const lastPosition = lastPositionsRef.current[change.id];
          const currentPosition = change.position;

          // Skip tracking during active drag operations
          if (change.dragging) {
            // Mark that we're in a dragging operation
            isDraggingRef.current = true;

            // Update the last position
            lastPositionsRef.current[change.id] = {
              x: currentPosition.x,
              y: currentPosition.y
            };

            // Don't mark as modified during drag, only at the end
            return;
          } else if (isDraggingRef.current) {
            // This is the end of a drag operation
            isDraggingRef.current = false;

            // Now we can add all nodes modified during this drag to the tracking
            if (modifiedNodesRef?.current) {
              dragModifiedNodesRef.current.forEach(nodeId => {
                modifiedNodesRef.current.add(nodeId);
              });
              debugLog(`Added ${dragModifiedNodesRef.current.size} nodes to modified tracking after drag`);
              // Clear the temporary tracking
              dragModifiedNodesRef.current.clear();
            }
          } else if (lastPosition) {
            // For non-drag position changes, check if the change is significant
            const dx = Math.abs(currentPosition.x - lastPosition.x);
            const dy = Math.abs(currentPosition.y - lastPosition.y);

            // Only track if position changed by more than 5 pixels
            if (dx > 5 || dy > 5) {
              if (modifiedNodesRef?.current) {
                modifiedNodesRef.current.add(change.id);
                debugLog(`Node ${change.id} position modified significantly`);
              }

              // Update the last position
              lastPositionsRef.current[change.id] = {
                x: currentPosition.x,
                y: currentPosition.y
              };
            }
          } else {
            // First position change for this node
            if (modifiedNodesRef?.current) {
              modifiedNodesRef.current.add(change.id);
              debugLog(`Node ${change.id} position modified for the first time`);
            }

            // Initialize the last position
            lastPositionsRef.current[change.id] = {
              x: currentPosition.x,
              y: currentPosition.y
            };
          }
        } else if (change.type === 'dimensions' && change.id) {
          // Track that this node's dimensions have been modified
          if (modifiedNodesRef?.current) {
            modifiedNodesRef.current.add(change.id);
            debugLog(`Node ${change.id} dimensions modified by user`);
          }
        } else if (change.type === 'remove' && change.id) {
          // Only track deletions if we're not in a reset operation
          if (!isResetting.current && deletedNodesRef?.current) {
            deletedNodesRef.current.add(change.id);
            debugLog(`Node ${change.id} deleted by user`);

            // Remove from modified nodes if it was there
            if (modifiedNodesRef?.current) {
              modifiedNodesRef.current.delete(change.id);
            }

            // Remove from last positions tracking
            delete lastPositionsRef.current[change.id];
          } else if (isResetting.current) {
            debugLog(`Node ${change.id} removed during reset (not tracking as user deletion)`);
          }
        }

        // For position changes, update child nodes if their parent moved
        if (change.type === 'position' && change.position && change.id) {
          debugLog(`Processing position change for node ${change.id}:`, change.position);

          setNodes(currentNodes => {
            // Find any nodes that have this node as a parent
            const childNodes = currentNodes.filter(n => n.parentId === change.id);
            debugLog(`Found ${childNodes.length} child nodes for node ${change.id}:`,
              childNodes.map(n => ({ id: n.id, position: n.position })));

            if (childNodes.length === 0) return currentNodes;

            // Get the position delta from the change
            if (!change.positionAbsolute) {
              debugLog('No positionAbsolute in change, skipping child updates');
              return currentNodes;
            }

            const dx = change.position.x - change.positionAbsolute.x;
            const dy = change.position.y - change.positionAbsolute.y;
            debugLog(`Position delta: dx=${dx}, dy=${dy}`);

            // Update all direct child nodes to maintain their positions relative to parent
            return currentNodes.map(node => {
              if (node.parentId === change.id) {
                const newPosition = {
                  x: node.position.x - dx,
                  y: node.position.y - dy
                };
                debugLog(`Updating child node ${node.id} position from`,
                  node.position, 'to', newPosition);

                return {
                  ...node,
                  position: newPosition
                };
              }
              return node;
            });
          });
        }
      });
    },
    [onNodesChange, setNodes, modifiedNodesRef, deletedNodesRef]
  );

  // Ensure proper node ordering (parents before children)
  const ensureProperNodeOrder = useCallback(() => {
    debugLog('Ensuring proper node order');

    setNodes(currentNodes => {
      // First, create a map of parent-child relationships
      const childrenMap: Record<string, string[]> = {};
      const parentMap: Record<string, string> = {};

      // Populate the maps
      currentNodes.forEach(node => {
        if (node.parentId) {
          // Add to parent's children list
          if (!childrenMap[node.parentId]) {
            childrenMap[node.parentId] = [];
          }
          childrenMap[node.parentId].push(node.id);

          // Record this node's parent
          parentMap[node.id] = node.parentId;
        }
      });

      debugLog('Parent-child relationships:', { childrenMap, parentMap });

      // Create a topologically sorted array
      const orderedNodes: Node[] = [];
      const visited = new Set<string>();
      const temp = new Set<string>();

      // DFS function to detect cycles and build ordered list
      const visit = (nodeId: string): boolean => {
        // If node is in temporary set, we have a cycle
        if (temp.has(nodeId)) {
          debugError(`Cycle detected involving node ${nodeId}`);
          return false;
        }

        // If node is already visited, skip
        if (visited.has(nodeId)) return true;

        // Mark node as being visited
        temp.add(nodeId);

        // Visit all children first
        const children = childrenMap[nodeId] || [];
        for (const childId of children) {
          if (!visit(childId)) return false;
        }

        // Remove from temporary set and add to visited
        temp.delete(nodeId);
        visited.add(nodeId);

        // Add node to ordered list
        const node = currentNodes.find(n => n.id === nodeId);
        if (node) orderedNodes.unshift(node); // Add to front of array

        return true;
      };

      // Start with nodes that have no parents
      const rootNodes = currentNodes.filter(node => !node.parentId);
      debugLog('Root nodes:', rootNodes.map(n => n.id));

      // Visit each root node
      for (const node of rootNodes) {
        visit(node.id);
      }

      // Check if we visited all nodes
      if (orderedNodes.length !== currentNodes.length) {
        debugError('Not all nodes were visited. There might be orphaned nodes or cycles.');

        // Add any remaining nodes
        const remainingNodes = currentNodes.filter(node => !visited.has(node.id));
        debugLog('Remaining nodes:', remainingNodes.map(n => n.id));

        // Add them to the end
        orderedNodes.push(...remainingNodes);
      }

      debugLog('Ordered nodes:', orderedNodes.map(n => n.id));
      return orderedNodes;
    });
  }, [setNodes]);

  // Method to temporarily disable tracking during bulk operations
  const setResetting = useCallback((resetting: boolean) => {
    isResetting.current = resetting;
    debugLog(`Node tracking ${resetting ? 'disabled' : 'enabled'} for bulk operations`);
  }, []);

  return {
    handleNodesChange,
    findCompositeAtPoint,
    isPointInsideNode,
    ensureProperNodeOrder,
    setResetting,
    isResetting
  };
};
