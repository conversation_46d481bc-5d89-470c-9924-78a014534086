
import { useState, useEffect, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { chatAnalytics, assessmentAnalytics, sessionAnalytics } from '@/services/analyticsService';

// Hook for chat analytics
export const useChatAnalytics = (questionId?: string, contextType: 'free' | 'question' | 'guided' = 'free', contextId?: string) => {
  const { user } = useAuth();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [messageCount, setMessageCount] = useState(0);
  const sessionStartTime = useRef<number>(Date.now());

  // Start chat session
  const startChatSession = useCallback(async () => {
    if (!user) return;
    
    try {
      const newSessionId = await chatAnalytics.startChatSession(
        user.id,
        questionId,
        contextType,
        contextId
      );
      setSessionId(newSessionId);
      sessionStartTime.current = Date.now();
      setMessageCount(0);
    } catch (error) {
      console.error('Error starting chat session:', error);
    }
  }, [user, questionId, contextType, contextId]);

  // Track message sent
  const trackMessage = useCallback(async () => {
    if (!sessionId) return;
    
    const newCount = messageCount + 1;
    setMessageCount(newCount);
    
    const durationSeconds = Math.floor((Date.now() - sessionStartTime.current) / 1000);
    
    try {
      await chatAnalytics.updateChatSession(sessionId, newCount, durationSeconds);
    } catch (error) {
      console.error('Error tracking message:', error);
    }
  }, [sessionId, messageCount]);

  // End chat session
  const endChatSession = useCallback(async () => {
    if (!sessionId) return;
    
    const durationSeconds = Math.floor((Date.now() - sessionStartTime.current) / 1000);
    
    try {
      await chatAnalytics.endChatSession(sessionId, messageCount, durationSeconds);
      setSessionId(null);
      setMessageCount(0);
    } catch (error) {
      console.error('Error ending chat session:', error);
    }
  }, [sessionId, messageCount]);

  return {
    sessionId,
    messageCount,
    startChatSession,
    trackMessage,
    endChatSession,
    isActive: !!sessionId
  };
};

// Hook for assessment analytics
export const useAssessmentAnalytics = () => {
  const { user } = useAuth();
  const [isTracking, setIsTracking] = useState(false);
  const assessmentStartTime = useRef<number>(0);

  // Start assessment tracking
  const startAssessment = useCallback(() => {
    assessmentStartTime.current = Date.now();
    setIsTracking(true);
  }, []);

  // Record assessment completion
  const recordAssessment = useCallback(async (
    questionId: string,
    contextType: 'question' | 'guided',
    contextId: string,
    score: number,
    nodeCount: number,
    edgeCount: number,
    componentsUsed: string[],
    isBestScore: boolean = false
  ) => {
    if (!user || !isTracking) return;

    const durationSeconds = Math.floor((Date.now() - assessmentStartTime.current) / 1000);

    try {
      await assessmentAnalytics.recordAssessment(
        user.id,
        questionId,
        contextType,
        contextId,
        score,
        durationSeconds,
        nodeCount,
        edgeCount,
        componentsUsed,
        isBestScore
      );
      setIsTracking(false);
    } catch (error) {
      console.error('Error recording assessment:', error);
    }
  }, [user, isTracking]);

  // Get user's assessment statistics
  const getAssessmentStats = useCallback(async () => {
    if (!user) return null;
    
    try {
      return await assessmentAnalytics.getAssessmentStats(user.id);
    } catch (error) {
      console.error('Error getting assessment stats:', error);
      return null;
    }
  }, [user]);

  return {
    isTracking,
    startAssessment,
    recordAssessment,
    getAssessmentStats
  };
};

// Hook for session analytics
export const useSessionAnalytics = () => {
  const { user } = useAuth();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [pageViews, setPageViews] = useState(0);
  const [actionsPerformed, setActionsPerformed] = useState(0);
  const [questionsAttempted, setQuestionsAttempted] = useState<string[]>([]);

  // Start session
  const startSession = useCallback(async () => {
    if (!user) return;
    
    try {
      const newSessionId = await sessionAnalytics.startSession(user.id);
      setSessionId(newSessionId);
      setPageViews(1);
      setActionsPerformed(0);
      setQuestionsAttempted([]);
    } catch (error) {
      console.error('Error starting session:', error);
    }
  }, [user]);

  // Track page view
  const trackPageView = useCallback(async () => {
    if (!user || !sessionId) return;
    
    const newPageViews = pageViews + 1;
    setPageViews(newPageViews);
    
    try {
      await sessionAnalytics.updateSession(
        user.id,
        newPageViews,
        actionsPerformed,
        questionsAttempted
      );
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }, [user, sessionId, pageViews, actionsPerformed, questionsAttempted]);

  // Track action
  const trackAction = useCallback(async () => {
    if (!user || !sessionId) return;
    
    const newActions = actionsPerformed + 1;
    setActionsPerformed(newActions);
    
    try {
      await sessionAnalytics.updateSession(
        user.id,
        pageViews,
        newActions,
        questionsAttempted
      );
    } catch (error) {
      console.error('Error tracking action:', error);
    }
  }, [user, sessionId, pageViews, actionsPerformed, questionsAttempted]);

  // Track question attempt
  const trackQuestionAttempt = useCallback(async (questionId: string) => {
    if (!user || !sessionId || questionsAttempted.includes(questionId)) return;
    
    const newQuestions = [...questionsAttempted, questionId];
    setQuestionsAttempted(newQuestions);
    
    try {
      await sessionAnalytics.updateSession(
        user.id,
        pageViews,
        actionsPerformed,
        newQuestions
      );
    } catch (error) {
      console.error('Error tracking question attempt:', error);
    }
  }, [user, sessionId, pageViews, actionsPerformed, questionsAttempted]);

  // End session
  const endSession = useCallback(async () => {
    if (!user) return;
    
    try {
      await sessionAnalytics.endSession(user.id);
      setSessionId(null);
    } catch (error) {
      console.error('Error ending session:', error);
    }
  }, [user]);

  // Auto-start session when user is available
  useEffect(() => {
    if (user && !sessionId) {
      startSession();
    }
  }, [user, sessionId, startSession]);

  // Auto-end session on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (user) {
        // Use sendBeacon for reliable tracking on page unload
        navigator.sendBeacon('/api/analytics/end-session', JSON.stringify({ userId: user.id }));
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [user]);

  return {
    sessionId,
    pageViews,
    actionsPerformed,
    questionsAttempted,
    trackPageView,
    trackAction,
    trackQuestionAttempt,
    endSession
  };
};

// Hook for component usage analytics
export const useComponentAnalytics = () => {
  const { user } = useAuth();

  const trackComponentUsage = useCallback(async (
    componentType: string,
    questionId?: string,
    contextType?: 'free' | 'question' | 'guided',
    contextId?: string
  ) => {
    if (!user) return;

    try {
      // This would be implemented in the analyticsService
      // For now, we'll track it via Vercel Analytics
      const { track } = await import('@vercel/analytics');
      track('component_used', {
        componentType,
        questionId,
        contextType,
        contextId
      });
    } catch (error) {
      console.error('Error tracking component usage:', error);
    }
  }, [user]);

  return {
    trackComponentUsage
  };
};
