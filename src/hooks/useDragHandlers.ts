import { useCallback, useState, useRef } from 'react';
import { Node, ReactFlowInstance, XYPosition } from '@xyflow/react';
import { toast } from 'sonner';
import { componentTypes } from '../components/ComponentTypes';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface UseDragHandlersProps {
  nodes: Node[];
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  reactFlowRef: React.RefObject<HTMLDivElement>;
  reactFlowInstance: React.MutableRefObject<ReactFlowInstance | null>;
  findCompositeAtPoint: (point: XYPosition, node?: Node | null) => Node | null;
  ensureProperNodeOrder: () => void;
}

interface DragData {
  type: string;
  className: string;
  label: string;
}

export const useDragHandlers = ({
  nodes,
  setNodes,
  reactFlowRef,
  reactFlowInstance,
  findCompositeAtPoint,
  ensureProperNodeOrder
}: UseDragHandlersProps) => {
  const [isDraggingNode, setIsDraggingNode] = useState<boolean>(false);
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);

  // Use a ref to track the last time we updated node visuals during drag over
  // This helps us throttle visual updates during drag operations
  const lastDragOverUpdateRef = useRef<number>(0);
  // Throttle interval in milliseconds (update visuals at most every 100ms)
  const DRAG_OVER_THROTTLE = 100;

  // Get all descendant nodes of a given node
  const getAllDescendantIds = useCallback((nodeId: string): string[] => {
    const descendants: string[] = [];

    // Find direct children
    const directChildren = nodes.filter(n => n.parentId === nodeId);

    directChildren.forEach(child => {
      descendants.push(child.id);
      // If this child is a composite, get its descendants too
      if (child.type === 'compositeNode') {
        descendants.push(...getAllDescendantIds(child.id));
      }
    });

    return descendants;
  }, [nodes]);

  // Updated handler for starting node drag
  const onNodeDragStart = useCallback((_event: React.MouseEvent, node: Node) => {
    setIsDraggingNode(true);
    setDraggedNodeId(node.id);

    // Note: Whether the node is a child of a composite affects
    // which composite nodes can be valid drop targets

    // Highlight all composite nodes as potential drop targets
    // Skip highlighting the node being dragged and its children to prevent circular references
    setNodes((prevNodes) => {
      return prevNodes.map((n) => {
        if (n.type === 'compositeNode' && n.id !== node.id && !getAllDescendantIds(node.id).includes(n.id)) {
          // Check if the node being dragged is not already an ancestor of this node
          // to prevent circular references
          let isChildOrDescendant = false;
          let currentParentId = n.parentId;

          // Check if this node is a child/descendant of the dragged node
          while (currentParentId) {
            if (currentParentId === node.id) {
              isChildOrDescendant = true;
              break;
            }
            // Find the parent node
            const parentNode = prevNodes.find(pn => pn.id === currentParentId);
            currentParentId = parentNode ? parentNode.parentId : null;
          }

          // Only highlight as dropzone if it's not a child/descendant of the dragged node
          if (!isChildOrDescendant) {
            return {
              ...n,
              data: {
                ...n.data,
                isDropzoneActive: true,
                // If this is the parent of the dragging node, mark it specially
                isDraggingChild: n.id === node.parentId
              },
            };
          }
        }
        return n;
      });
    });
  }, [setNodes, getAllDescendantIds]);

  // Improved node drag stop handler with enhanced support for nested composites
  const onNodeDragStop = useCallback((event: React.MouseEvent, node: Node) => {
    const currentPoint = { x: event.clientX, y: event.clientY };

    // If the node has a parent, check if the drop point is within the parent's bounds
    if (node.parentId) {
      const parentNode = nodes.find(n => n.id === node.parentId);
      if (parentNode) {
        // Get the parent node's DOM element
        const parentElement = document.querySelector(`[data-id="${parentNode.id}"]`);
        if (parentElement) {
          const parentBounds = parentElement.getBoundingClientRect();

          // Check if the drop point is outside the parent's bounds
          const isOutsideParent =
            currentPoint.x < parentBounds.left ||
            currentPoint.x > parentBounds.right ||
            currentPoint.y < parentBounds.top ||
            currentPoint.y > parentBounds.bottom;

          if (isOutsideParent) {
            debugLog('Drop point is outside parent bounds, ignoring drop');

            // Reset dropzone visuals
            setNodes((prev) =>
              prev.map((n) =>
                n.type === 'compositeNode'
                  ? { ...n, data: { ...n.data, isDropzoneActive: false, isDraggingChild: false } }
                  : n
              )
            );

            // End dragging state
            setIsDraggingNode(false);
            setDraggedNodeId(null);

            return; // Exit early, don't process the drop
          }

          // If we're still inside the same parent, just update the position
          // and don't do any nesting/unnesting operations
          if (reactFlowRef.current && reactFlowInstance.current) {
            // Get the current node's position from React Flow
            // This preserves the exact position where the user dropped it
            const nodePosition = node.position;

            // We'll keep this position as is, without any additional constraints
            // This allows free movement within the parent composite

            // Update the node position while keeping it in the same parent
            setNodes((nds) => nds.map((n) => {
              if (n.id === node.id) {
                return {
                  ...n,
                  // Keep the exact position where the user dropped it
                  position: nodePosition,
                  // Keep the same parent
                  parentId: node.parentId,
                  extent: 'parent' as const, // Use 'as const' to ensure correct type
                  data: {
                    ...n.data,
                    parentCompositeId: node.parentId
                  }
                };
              }
              return n;
            }));

            // End dragging state
            setIsDraggingNode(false);
            setDraggedNodeId(null);

            // Reset dropzone visuals
            setNodes((prev) =>
              prev.map((n) =>
                n.type === 'compositeNode'
                  ? { ...n, data: { ...n.data, isDropzoneActive: false, isDraggingChild: false } }
                  : n
              )
            );

            return; // Exit early, we've handled the repositioning
          }
        }
      }
    }

    // The rest of the function for handling drops onto other composites
    const compositeNode = findCompositeAtPoint(currentPoint, node);

    if (compositeNode && node.id !== compositeNode.id) {
      // Prevent circular nesting
      const draggedDescendants = getAllDescendantIds(node.id);
      if (draggedDescendants.includes(compositeNode.id)) {
        toast.error("Cannot nest into a descendant (circular reference)");
        return;
      }

      // Get the composite node's DOM element to calculate its actual dimensions
      const compositeElement = document.querySelector(`[data-id="${compositeNode.id}"]`);
      if (compositeElement && reactFlowRef.current) {
        const compositeBounds = compositeElement.getBoundingClientRect();

        // Calculate the relative position within the composite
        const relativeX = event.clientX - compositeBounds.left;
        const relativeY = event.clientY - compositeBounds.top;

        // Convert to flow coordinates based on the composite's dimensions
        // This gives us the exact position where the user dropped the node
        const relativePos = {
          x: relativeX * (compositeNode.width || 250) / compositeBounds.width,
          y: relativeY * (compositeNode.height || 200) / compositeBounds.height,
        };

        // We'll use minimal padding constraints to prevent nodes from being
        // positioned completely outside the parent's bounds
        const minPadding = 5; // Reduced padding to allow more freedom
        const nodeWidth = node.style?.width || 150;
        const nodeHeight = node.style?.height || 80;

        // Only constrain if the node would be completely outside the parent
        if (relativePos.x < -nodeWidth + minPadding) {
          relativePos.x = -nodeWidth + minPadding;
        } else if (relativePos.x > (compositeNode.width || 250) - minPadding) {
          relativePos.x = (compositeNode.width || 250) - minPadding;
        }

        if (relativePos.y < -nodeHeight + minPadding) {
          relativePos.y = -nodeHeight + minPadding;
        } else if (relativePos.y > (compositeNode.height || 200) - minPadding) {
          relativePos.y = (compositeNode.height || 200) - minPadding;
        }

        // Get all descendants of the dragged node
        const descendantIds = node.type === 'compositeNode' ? getAllDescendantIds(node.id) : [];

        setNodes((nds) => {
          // First, update the dragged node itself
          const updatedNodes = nds.map((n) => {
            if (n.id === node.id) {
              // This is the node being dragged
              return {
                ...n,
                parentId: compositeNode.id,
                extent: 'parent' as const, // Use 'as const' to ensure correct type
                position: relativePos,
                data: {
                  ...n.data,
                  parentCompositeId: compositeNode.id
                }
              };
            }
            return n;
          });

          // Now update all descendants
          return updatedNodes.map((n) => {
            if (descendantIds.includes(n.id)) {
              // This is a descendant of the dragged node
              return {
                ...n,
                // Ensure extent is set for all descendants
                extent: 'parent' as const, // Use 'as const' to ensure correct type
                // Keep the existing parentId relationship
                data: {
                  ...n.data,
                  // Ensure parentCompositeId is set correctly
                  parentCompositeId: n.parentId
                }
              };
            }
            return n;
          });
        });
      } else {
        // Fallback if we can't find the DOM element
        const reactFlowBounds = reactFlowRef.current?.getBoundingClientRect();
        const flowInstance = reactFlowInstance.current;
        if (!reactFlowBounds || !flowInstance) return;

        const flowPos = flowInstance.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        });

        const relativePos = {
          x: flowPos.x - compositeNode.position.x,
          y: flowPos.y - compositeNode.position.y,
        };

        // Get all descendants of the dragged node
        const descendantIds = node.type === 'compositeNode' ? getAllDescendantIds(node.id) : [];

        setNodes((nds) => {
          // First, update the dragged node itself
          const updatedNodes = nds.map((n) => {
            if (n.id === node.id) {
              // This is the node being dragged
              return {
                ...n,
                parentId: compositeNode.id,
                extent: 'parent' as const, // Use 'as const' to ensure correct type
                position: relativePos,
                data: {
                  ...n.data,
                  parentCompositeId: compositeNode.id
                }
              };
            }
            return n;
          });

          // Now update all descendants
          return updatedNodes.map((n) => {
            if (descendantIds.includes(n.id)) {
              // This is a descendant of the dragged node
              return {
                ...n,
                // Ensure extent is set for all descendants
                extent: 'parent' as const, // Use 'as const' to ensure correct type
                // Keep the existing parentId relationship
                data: {
                  ...n.data,
                  // Ensure parentCompositeId is set correctly
                  parentCompositeId: n.parentId
                }
              };
            }
            return n;
          });
        });
      }

      // Ensure proper node ordering after nesting
      ensureProperNodeOrder();
      toast.success("Composite nested successfully");
    } else if (node.parentId) {
      const parentNode = nodes.find(n => n.id === node.parentId);
      if (parentNode && reactFlowRef.current && reactFlowInstance.current) {
        const reactFlowBounds = reactFlowRef.current.getBoundingClientRect();
        const flowPos = reactFlowInstance.current.screenToFlowPosition({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        const descendantIds = node.type === 'compositeNode' ? getAllDescendantIds(node.id) : [];

        setNodes((nds) => nds.map((n) => {
          if (n.id === node.id) {
            return {
              ...n,
              position: flowPos,
              parentId: undefined,
              extent: undefined,
              data: {
                ...n.data,
                parentCompositeId: undefined
              }
            };
          } else if (descendantIds.includes(n.id)) {
            return {
              ...n,
              extent: 'parent' as const, // Use 'as const' to ensure correct type
              data: {
                ...n.data,
                parentCompositeId: n.parentId
              }
            };
          }
          return n;
        }));
      }

      // Ensure proper node ordering after unnesting
      ensureProperNodeOrder();
    }

    setIsDraggingNode(false);
    setDraggedNodeId(null);

    // Reset dropzone visuals
    setNodes((prev) =>
      prev.map((n) =>
        n.type === 'compositeNode'
          ? { ...n, data: { ...n.data, isDropzoneActive: false, isDraggingChild: false } }
          : n
      )
    );
  }, [findCompositeAtPoint, getAllDescendantIds, nodes, reactFlowInstance, reactFlowRef, setNodes, ensureProperNodeOrder]);


  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';

    // Throttle visual updates during drag over to reduce state updates
    const now = Date.now();
    if (now - lastDragOverUpdateRef.current < DRAG_OVER_THROTTLE) {
      // Skip this update if we've updated recently
      return;
    }

    // Update our timestamp
    lastDragOverUpdateRef.current = now;

    // Get the current mouse position
    const currentPoint = { x: event.clientX, y: event.clientY };

    // For onDragOver, we don't have a node reference since it's coming from palette
    // So we pass null, which will just find any composite at the point
    const compositeNode = findCompositeAtPoint(currentPoint, null);

    // If we found a composite node, determine its hierarchy level
    let targetParentId = null;
    if (compositeNode) {
      // If the composite has a parent, we should only highlight composites at the same level
      targetParentId = compositeNode.parentId;
    }
    // Update dropzone visual feedback for all composite nodes
    setNodes((prevNodes) => {
      return prevNodes.map((node) => {
        if (node.type !== 'compositeNode') return node;

        // Only highlight composites at the same hierarchy level
        // If the found composite has a parent, only highlight nodes with the same parent
        // If the found composite has no parent, only highlight top-level composites
        const isValidTarget = node.parentId === targetParentId;

        // Set isDropzoneActive true only for the composite node we're hovering over
        // AND only if it's at the correct hierarchy level
        const isActive = compositeNode?.id === node.id && isValidTarget;
        // Keep the isDraggingChild value if it was already set
        const isDraggingChild = node.data.isDraggingChild || false;

        // Only update if the state is actually changing
        if (node.data.isDropzoneActive === isActive &&
            node.data.isDraggingChild === isDraggingChild) {
          return node; // No change needed
        }

        return {
          ...node,
          data: {
            ...node.data,
            isDropzoneActive: isActive,
            isDraggingChild
          },
        };
      });
    });
  }, [findCompositeAtPoint, setNodes, DRAG_OVER_THROTTLE]);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowRef.current?.getBoundingClientRect();
      const dataTransfer = event.dataTransfer.getData('application/reactflow');

      if (!dataTransfer || !reactFlowBounds || !reactFlowInstance.current) return;

      // Alternative approach: Try to get the actual ReactFlow viewport element
      const reactFlowViewport = reactFlowRef.current?.querySelector('.react-flow__viewport');
      const actualReactFlowBounds = reactFlowViewport?.getBoundingClientRect() || reactFlowBounds;

      debugLog('🎯 Bounds comparison:');
      debugLog('wrapperBounds:', {
        left: reactFlowBounds.left,
        top: reactFlowBounds.top,
        width: reactFlowBounds.width,
        height: reactFlowBounds.height
      });
      debugLog('viewportBounds:', {
        left: actualReactFlowBounds.left,
        top: actualReactFlowBounds.top,
        width: actualReactFlowBounds.width,
        height: actualReactFlowBounds.height
      });

      // Parse the JSON data from dataTransfer
      let dragData: DragData;
      try {
        dragData = JSON.parse(dataTransfer);
      } catch (e) {
        // Fallback to old behavior if parsing fails
        dragData = {
          type: dataTransfer,
          className: '',
          label: dataTransfer.charAt(0).toUpperCase() + dataTransfer.slice(1)
        };
      }

      // Log the raw event coordinates and React Flow bounds
      debugLog('🎯 DROP EVENT DEBUG:');
      debugLog('Raw mouse coordinates:', {
        clientX: event.clientX,
        clientY: event.clientY
      });
      debugLog('React Flow bounds:', {
        left: reactFlowBounds.left,
        top: reactFlowBounds.top,
        width: reactFlowBounds.width,
        height: reactFlowBounds.height,
        right: reactFlowBounds.right,
        bottom: reactFlowBounds.bottom
      });

      // Get the current viewport transformation
      const { zoom, x: panX, y: panY } = reactFlowInstance.current.getViewport();
      debugLog('Current viewport:', { zoom, panX, panY });

      // Calculate the screen coordinates relative to the actual React Flow viewport
      // Use the actual ReactFlow viewport bounds instead of the wrapper bounds
      const screenX = event.clientX;
      const screenY = event.clientY;

      debugLog('Screen coordinates relative to React Flow viewport:', { screenX, screenY });
      debugLog('React Flow viewport offset from browser viewport:', {
        leftOffset: actualReactFlowBounds.left,
        topOffset: actualReactFlowBounds.top
      });

      // Let's also check if the mouse is actually inside the React Flow viewport bounds
      const isInsideBounds = screenX >= 0 && screenX <= actualReactFlowBounds.width &&
                            screenY >= 0 && screenY <= actualReactFlowBounds.height;
      debugLog('Mouse inside React Flow viewport bounds?', isInsideBounds);

      // Check if dropping inside a composite node
      const dropPoint = { x: event.clientX, y: event.clientY };
      // For new nodes from palette, we pass null as the dragged node
      const compositeNode = findCompositeAtPoint(dropPoint, null);
      let parentId = compositeNode?.id;

      const nodeType = dragData.type;

      // Get default dimensions for this node type
      const defaultDimensions = nodeType === 'composite'
        ? { width: 250, height: 200 }
        : { width: 150, height: 80 };

      // Convert screen coordinates to flow coordinates (zoom and pan aware)
      const nodePosition = reactFlowInstance.current.screenToFlowPosition({
        x: screenX,
        y: screenY,
      });

      debugLog('🎯 Drop position calculated:');
      debugLog('screenCoords:', { x: screenX, y: screenY });
      debugLog('flowPosition:', nodePosition);
      debugLog('zoom:', zoom);

      // Additional debugging: Let's also try the direct approach
      const directFlowPosition = reactFlowInstance.current.screenToFlowPosition({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });
      debugLog('🎯 Direct flow position (should be same):');
      debugLog('directFlowPosition:', directFlowPosition);

      // Check if there's a difference
      const positionDiff = {
        x: Math.abs(nodePosition.x - directFlowPosition.x),
        y: Math.abs(nodePosition.y - directFlowPosition.y)
      };
      debugLog('🎯 Position difference (should be 0):');
      debugLog('positionDiff:', positionDiff);

      // Create the new node with a more robust ID
      const newNode = {
        id: `${nodeType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        type: nodeType === 'composite' ? 'compositeNode' : 'customNode',
        position: nodePosition, // Use the exact drop position
        data: {
          type: nodeType,
          label: componentTypes[nodeType]?.label || dragData.label,
          className: componentTypes[nodeType]?.className || dragData.className,
          parentCompositeId: parentId,
          metadata: {
            customLogic: '', // Initialize with empty custom logic
            customProperties: [] // Initialize with empty custom properties array
          },
          // Pass setNodes to the node data so it can update its label
          setNodes: setNodes,
          // For composite nodes, initialize the components array
          ...(nodeType === 'composite' && { components: [] }),
        },
        // If this node has a parent, set the parentId
        ...(parentId && { parentId }),
        // Set default dimensions
        style: defaultDimensions,
      } as Node; // Type assertion to Node

      // If dropping inside a composite node, set the parent and adjust position
      if (compositeNode) {
        newNode.parentId = compositeNode.id;
        newNode.data.parentCompositeId = compositeNode.id;
        newNode.extent = 'parent' as const; // Use 'as const' to ensure correct type

        // Get the drop position in flow coordinates using the correct viewport bounds
        const dropFlowPosition = reactFlowInstance.current.screenToFlowPosition({
          x: event.clientX - actualReactFlowBounds.left,
          y: event.clientY - actualReactFlowBounds.top,
        });

        // Calculate position relative to the composite node's position in flow coordinates
        newNode.position = {
          x: dropFlowPosition.x - compositeNode.position.x,
          y: dropFlowPosition.y - compositeNode.position.y,
        };

        debugLog('Composite node flow-relative position:');
        debugLog('newNode.position:', newNode.position);

        // Use minimal padding constraints to prevent nodes from being
        // positioned completely outside the parent's bounds
        const minPadding = 5; // Reduced padding to allow more freedom
        const nodeWidth = (newNode.style as any)?.width || 150;
        const nodeHeight = (newNode.style as any)?.height || 80;

        // Only constrain if the node would be completely outside the parent
        if (newNode.position.x < -nodeWidth + minPadding) {
          newNode.position.x = -nodeWidth + minPadding;
        } else if (newNode.position.x > (compositeNode.width || 250) - minPadding) {
          newNode.position.x = (compositeNode.width || 250) - minPadding;
        }

        if (newNode.position.y < -nodeHeight + minPadding) {
          newNode.position.y = -nodeHeight + minPadding;
        } else if (newNode.position.y > (compositeNode.height || 200) - minPadding) {
          newNode.position.y = (compositeNode.height || 200) - minPadding;
        }

        debugLog(`Positioned new node at flow-relative coordinates: (${newNode.position.x}, ${newNode.position.y}) within composite`);
      }

      // Add the new node and clear dropzone highlights in a single atomic operation
      setNodes((nds) => {
        debugLog(`Adding new node ${newNode.id} of type ${newNode.type} to canvas`);

        // First add the new node
        const nodesWithNewNode = nds.concat(newNode);

        // Then clear dropzone highlights and dragging child status on all composite nodes
        const finalNodes = nodesWithNewNode.map((n) =>
          n.type === 'compositeNode'
            ? { ...n, data: { ...n.data, isDropzoneActive: false, isDraggingChild: false } }
            : n
        );

        return finalNodes;
      });

      // Ensure proper node ordering (this only reorders, doesn't change positions)
      ensureProperNodeOrder();
    },
    [setNodes, findCompositeAtPoint, reactFlowRef, reactFlowInstance, ensureProperNodeOrder]
  );

  return {
    isDraggingNode,
    draggedNodeId,
    onNodeDragStart,
    onNodeDragStop,
    onDragOver,
    onDrop
  };
};
