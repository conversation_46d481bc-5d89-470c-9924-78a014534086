import { useRef, useCallback, useEffect } from 'react';
import { ReactFlowInstance } from '@xyflow/react';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { DesignContextCategory, SavedDesign } from '@/contexts/DesignContext';
import { LocalStorageManager } from '@/utils/localStorageManager';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface AutoSaveConfig {
  enabled: boolean;
  localStorageDelay: number; // ms
  supabaseDelay: number; // ms
  maxRetries: number;
}

interface UseAutoSaveProps {
  currentQuestionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  reactFlowInstance: ReactFlowInstance | null;
  userJourneys: string;
  assumptions: string;
  constraints: string;
  saveDesign: (questionId: string, design: any, contextType?: DesignContextCategory, contextId?: string, syncToSupabase?: boolean, updateCurrentDesign?: boolean) => Promise<any>;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  mergeNodesAndEdges: (currentNodes: any[], currentEdges: any[]) => { nodes: any[], edges: any[] };
  onStatusChange?: (status: SaveStatus, lastSaved?: Date) => void;
  isOnline?: boolean;
  config?: Partial<AutoSaveConfig>;
}

interface SaveOperation {
  id: string;
  data: any;
  timestamp: number;
  toSupabase: boolean;
  retryCount: number;
}

const DEFAULT_CONFIG: AutoSaveConfig = {
  enabled: true,
  localStorageDelay: 500, // 500ms for localStorage
  supabaseDelay: 2000, // 2s for Supabase
  maxRetries: 3
};

export const useAutoSave = ({
  currentQuestionId,
  contextType = 'question',
  contextId,
  reactFlowInstance,
  userJourneys,
  assumptions,
  constraints,
  saveDesign,
  setHasUnsavedChanges,
  mergeNodesAndEdges,
  onStatusChange,
  isOnline = true,
  config = {}
}: UseAutoSaveProps) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // Status tracking
  const currentStatus = useRef<SaveStatus>(SaveStatus.IDLE);
  const lastSavedDate = useRef<Date | null>(null);
  const lastAutoSavedDate = useRef<Date | null>(null);

  // Save operation queue and locks
  const saveQueue = useRef<SaveOperation[]>([]);
  const isProcessingQueue = useRef(false);
  const localStorageTimer = useRef<NodeJS.Timeout | null>(null);
  const supabaseTimer = useRef<NodeJS.Timeout | null>(null);
  const statusResetTimer = useRef<NodeJS.Timeout | null>(null);
  const lastStatusChangeTime = useRef<number>(0);

  // Track last saved state to avoid unnecessary saves
  const lastSavedState = useRef<string>('');

  // Generate unique operation ID
  const generateOperationId = useCallback(() => {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // Update status and notify
  const updateStatus = useCallback((status: SaveStatus) => {
    const now = Date.now();
    currentStatus.current = status;
    onStatusChange?.(status, lastSavedDate.current || undefined);
    lastStatusChangeTime.current = now;

    // Clear any existing status reset timer
    if (statusResetTimer.current) {
      clearTimeout(statusResetTimer.current);
      statusResetTimer.current = null;
    }

    // Schedule reset to IDLE for success states
    if (status === SaveStatus.SAVED || status === SaveStatus.AUTO_SAVED) {
      statusResetTimer.current = setTimeout(() => {
        // Only reset if enough time has passed and no new status changes occurred
        if (Date.now() - lastStatusChangeTime.current >= 1000) {
          currentStatus.current = SaveStatus.IDLE;
          onStatusChange?.(SaveStatus.IDLE, lastSavedDate.current || undefined);
          statusResetTimer.current = null;
        }
      }, 1000); // Reset to IDLE after 1 second
    }
  }, [onStatusChange]);

  // Create design data from current state
  const createDesignData = useCallback(() => {
    if (!reactFlowInstance || !currentQuestionId) {
      return null;
    }

    const currentNodes = reactFlowInstance.getNodes();
    const currentEdges = reactFlowInstance.getEdges();
    const { nodes: mergedNodes, edges: mergedEdges } = mergeNodesAndEdges(currentNodes, currentEdges);

    debugLog(`🔍 Autosave capturing data:`, {
      currentNodesCount: currentNodes.length,
      currentEdgesCount: currentEdges.length,
      mergedNodesCount: mergedNodes.length,
      mergedEdgesCount: mergedEdges.length,
      nodeIds: mergedNodes.map(n => n.id),
      contextType,
      contextId: contextId || currentQuestionId
    });

    return {
      nodes: mergedNodes,
      edges: mergedEdges,
      userJourneys,
      assumptions,
      constraints,
      contextType,
      contextId: contextId || currentQuestionId,
      lastModified: new Date().toISOString()
    };
  }, [reactFlowInstance, userJourneys, assumptions, constraints, contextType, contextId, currentQuestionId, mergeNodesAndEdges]);

  // Create design data with delay to allow React Flow to update
  const createDesignDataDelayed = useCallback(async (delay = 0) => {
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
    return createDesignData();
  }, [createDesignData]);

  // Get localStorage key for current design
  const getLocalStorageKey = useCallback(() => {
    if (!currentQuestionId) {
      return null;
    }
    const id = contextId || currentQuestionId;
    return `layrs-design-${contextType}-${id}`;
  }, [currentQuestionId, contextType, contextId]);

  // Save to localStorage immediately (using same format as manual save)
  const saveToLocalStorage = useCallback(async (data: any) => {
    const key = getLocalStorageKey();
    if (!key) {
      return false;
    }

    try {
      // Use the same format as manual save (direct JSON, not wrapped in LocalStorageManager format)
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      debugError(`❌ Auto-save to localStorage failed for key ${key}:`, error);
      return false;
    }
  }, [getLocalStorageKey]);

  // Save to Supabase with retry logic
  const saveToSupabase = useCallback(async (data: any, retryCount = 0): Promise<boolean> => {
    if (!currentQuestionId || !isOnline) {
      debugLog(`⏭️ Skipping Supabase save: questionId=${currentQuestionId}, online=${isOnline}`);
      return false;
    }

    try {
      debugLog(`☁️ Auto-saving to Supabase for question ${currentQuestionId}...`);
      await saveDesign(currentQuestionId, data, contextType, contextId, true, true); // Update current design state after successful save
      debugLog(`✅ Auto-saved to Supabase for question ${currentQuestionId}`);
      lastSavedDate.current = new Date();
      // Don't call setHasUnsavedChanges(false) during auto-save to avoid race conditions
      return true;
    } catch (error) {
      debugError(`❌ Auto-save to Supabase failed for question ${currentQuestionId} (attempt ${retryCount + 1}):`, error);

      if (retryCount < finalConfig.maxRetries) {
        // Exponential backoff: 2s, 4s, 8s
        const delay = finalConfig.supabaseDelay * Math.pow(2, retryCount);
        setTimeout(() => {
          saveToSupabase(data, retryCount + 1);
        }, delay);
      }
      return false;
    }
  }, [currentQuestionId, isOnline, saveDesign, contextType, contextId, setHasUnsavedChanges, finalConfig.maxRetries, finalConfig.supabaseDelay]);

  // Process save queue
  const processQueue = useCallback(async () => {
    if (isProcessingQueue.current || saveQueue.current.length === 0) return;

    isProcessingQueue.current = true;
    updateStatus(SaveStatus.AUTO_SAVING);

    try {
      // Get the latest operation (discard older ones)
      const latestOp = saveQueue.current[saveQueue.current.length - 1];
      saveQueue.current = []; // Clear queue

      debugLog(`🔄 Processing auto-save operation: ${latestOp.id}`);

      // Always save to localStorage first
      await saveToLocalStorage(latestOp.data);

      // Save to Supabase if requested and online
      if (latestOp.toSupabase && isOnline) {
        const success = await saveToSupabase(latestOp.data);
        if (success) {
          updateStatus(SaveStatus.AUTO_SAVED);
        } else {
          updateStatus(SaveStatus.ERROR);
        }
      } else {
        updateStatus(SaveStatus.AUTO_SAVED);
        lastAutoSavedDate.current = new Date();
      }

    } catch (error) {
      debugError('❌ Auto-save queue processing failed:', error);
      updateStatus(SaveStatus.ERROR);
    } finally {
      isProcessingQueue.current = false;

      // Process any new operations that were queued during processing
      if (saveQueue.current.length > 0) {
        setTimeout(processQueue, 100);
      }
    }
  }, [saveToLocalStorage, saveToSupabase, isOnline, updateStatus]);

  // Queue a save operation
  const queueSave = useCallback(async (toSupabase = false, isMajorChange = false) => {
    if (!finalConfig.enabled) return;

    // For major changes (add/remove), add a small delay to let React Flow update
    const delay = isMajorChange ? 100 : 0;
    const data = await createDesignDataDelayed(delay);
    if (!data) return;

    // Check if data has actually changed
    const dataString = JSON.stringify(data);
    if (dataString === lastSavedState.current) {
      return; // Skip if no changes
    }
    lastSavedState.current = dataString;

    const operation: SaveOperation = {
      id: generateOperationId(),
      data,
      timestamp: Date.now(),
      toSupabase,
      retryCount: 0
    };

    saveQueue.current.push(operation);
    debugLog(`📝 Queued auto-save operation: ${operation.id} (toSupabase: ${toSupabase}, major: ${isMajorChange})`);

    // Clear existing timers
    if (localStorageTimer.current) clearTimeout(localStorageTimer.current);
    if (supabaseTimer.current) clearTimeout(supabaseTimer.current);

    if (toSupabase) {
      // Debounce Supabase saves
      supabaseTimer.current = setTimeout(() => {
        processQueue();
      }, finalConfig.supabaseDelay);
    } else {
      // Quick localStorage save
      localStorageTimer.current = setTimeout(() => {
        processQueue();
      }, finalConfig.localStorageDelay);
    }
  }, [finalConfig.enabled, finalConfig.localStorageDelay, finalConfig.supabaseDelay, createDesignDataDelayed, generateOperationId, processQueue]);

  // Trigger auto-save (localStorage only)
  const triggerAutoSave = useCallback((isMajorChange = false) => {
    queueSave(false, isMajorChange);
  }, [queueSave]);

  // Trigger full auto-save (localStorage + Supabase)
  const triggerFullAutoSave = useCallback((isMajorChange = false) => {
    queueSave(true, isMajorChange);
  }, [queueSave]);

  // Force immediate save (bypass queue)
  const forceSave = useCallback(async () => {
    const data = await createDesignDataDelayed(50); // Small delay for manual saves too
    if (!data) return false;

    updateStatus(SaveStatus.SAVING);

    try {
      // Save to localStorage immediately
      await saveToLocalStorage(data);

      // Save to Supabase if online
      if (isOnline) {
        const success = await saveToSupabase(data);
        if (success) {
          updateStatus(SaveStatus.SAVED);
          return true;
        } else {
          updateStatus(SaveStatus.ERROR);
          return false;
        }
      } else {
        updateStatus(SaveStatus.SAVED);
        return true;
      }
    } catch (error) {
      debugError('❌ Force save failed:', error);
      updateStatus(SaveStatus.ERROR);
      return false;
    }
  }, [createDesignDataDelayed, saveToLocalStorage, saveToSupabase, isOnline, updateStatus]);

  // Check for unsaved changes in localStorage
  const checkForUnsavedChanges = useCallback((): SavedDesign | null => {
    const key = getLocalStorageKey();
    if (!key) return null;

    try {
      const localDataJson = localStorage.getItem(key);
      if (!localDataJson) return null;

      const localData = JSON.parse(localDataJson) as SavedDesign;
      if (!localData) return null;

      // Check if localStorage version is newer than last Supabase save
      const localTimestamp = new Date(localData.lastModified).getTime();
      const lastSavedTimestamp = lastSavedDate.current?.getTime() || 0;

      if (localTimestamp > lastSavedTimestamp) {
        return localData;
      }

      return null;
    } catch (error) {
      debugError('❌ Error checking for unsaved changes:', error);
      return null;
    }
  }, [getLocalStorageKey]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (localStorageTimer.current) clearTimeout(localStorageTimer.current);
      if (supabaseTimer.current) clearTimeout(supabaseTimer.current);
      if (statusResetTimer.current) clearTimeout(statusResetTimer.current);
    };
  }, []);

  return {
    triggerAutoSave,
    triggerFullAutoSave,
    forceSave,
    checkForUnsavedChanges,
    getLocalStorageKey,
    status: currentStatus.current,
    lastSaved: lastSavedDate.current,
    lastAutoSaved: lastAutoSavedDate.current,
    isEnabled: finalConfig.enabled,
    queueLength: saveQueue.current.length
  };
};
