
import { useState, useRef } from 'react';
import { useDesign } from '@/contexts/DesignContext';
import { useQuestions } from '@/contexts/QuestionsContext';
import { Node, Edge } from '@xyflow/react';

/**
 * Hook to access the current design state
 * @returns The current design state
 */
export const useDesignState = () => {
  const {
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges
  } = useDesign();

  const { currentQuestion } = useQuestions();

  // State for flow nodes and edges
  const [nodes, setNodes] = useState<Node[]>([]);
  const [edges, setEdges] = useState<Edge[]>([]);

  // State for user inputs
  const [userJourneys, setUserJourneys] = useState<string>(currentDesign?.userJourneys || '');
  const [assumptions, setAssumptions] = useState<string>(currentDesign?.assumptions || '');
  const [constraints, setConstraints] = useState<string>(currentDesign?.constraints || '');
  const [isAssessing, setIsAssessing] = useState<boolean>(false);

  // Refs to track changes
  const originalDesignRef = useRef<any>(null);
  const modifiedNodesRef = useRef<Set<string>>(new Set());
  const deletedNodesRef = useRef<Set<string>>(new Set());

  // Track which toasts have been shown
  const shownToasts = useRef<Set<string>>(new Set());

  // Function to merge nodes and edges
  const mergeNodesAndEdges = (nodes: Node[], edges: Edge[]) => {
    return { nodes, edges };
  };

  return {
    nodes,
    setNodes,
    edges,
    setEdges,
    userJourneys,
    setUserJourneys,
    assumptions,
    setAssumptions,
    constraints,
    setConstraints,
    isAssessing,
    setIsAssessing,
    currentQuestion,
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    shownToasts,
    originalDesignRef,
    modifiedNodesRef,
    deletedNodesRef,
    mergeNodesAndEdges
  };
};
