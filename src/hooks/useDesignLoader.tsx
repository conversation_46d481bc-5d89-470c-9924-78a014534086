import { useEffect } from 'react';
import { ReactFlowInstance } from '@xyflow/react';
import { toast } from 'sonner';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { processLoadedEdges } from '@/utils/edgeProcessing';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface UseDesignLoaderProps {
  currentQuestionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  loadDesign: (questionId: string, contextType?: DesignContextCategory, contextId?: string) => Promise<any>;
  setCurrentDesign: (design: any) => void;
  setUserJourneys: (journeys: string) => void;
  setAssumptions: (assumptions: string) => void;
  setConstraints: (constraints: string) => void;
  setNodes: (nodes: any[]) => void;
  setEdges: (edges: any[]) => void;
  shownToasts: React.MutableRefObject<Set<string>>;
  originalDesignRef: React.MutableRefObject<{nodes: any[], edges: any[], timestamp: number} | null>;
  modifiedNodesRef: React.MutableRefObject<Set<string>>;
  deletedNodesRef: React.MutableRefObject<Set<string>>;
  reactFlowInstance: ReactFlowInstance | null;
  currentDesign: any;
  nodes: any[];
  edges: any[];
}

export const useDesignLoader = ({
  currentQuestionId,
  contextType = 'question',
  contextId,
  loadDesign,
  setCurrentDesign,
  setUserJourneys,
  setAssumptions,
  setConstraints,
  setNodes,
  setEdges,
  shownToasts,
  originalDesignRef,
  modifiedNodesRef,
  deletedNodesRef,
  reactFlowInstance,
  currentDesign,
  nodes,
  edges
}: UseDesignLoaderProps) => {
  // Effect to load design when currentQuestion changes - using minimized loading strategy
  useEffect(() => {
    const loadDesignData = async () => {
      if (currentQuestionId) {
        const id = contextId || currentQuestionId;
        debugLog(`🔄 Context switching: Loading design data for ${contextType} ${id}`);

        // Clear current design state when switching contexts to prevent stale data
        // This ensures we don't return early from loadDesign with wrong context data
        if (currentDesign &&
            (currentDesign.questionId !== currentQuestionId ||
             currentDesign.contextType !== contextType ||
             currentDesign.contextId !== id)) {
          debugLog(`🧹 Clearing stale design state. Old: ${currentDesign.contextType}-${currentDesign.contextId}, New: ${contextType}-${id}`);
          
          // Check if we have current edges that should be preserved
          const currentEdgesCount = edges.length;
          if (currentEdgesCount > 0) {
            debugLog(`🛡️ Context change detected but preserving ${currentEdgesCount} current edges`);
            // Only clear text fields and design state, keep nodes and edges
            setCurrentDesign(null);
            setUserJourneys('');
            setAssumptions('');
            setConstraints('');
            originalDesignRef.current = null;
            // Don't clear nodes and edges
          } else {
            setCurrentDesign(null);
            // Also clear the UI state immediately to prevent visual artifacts
            setUserJourneys('');
            setAssumptions('');
            setConstraints('');
            setNodes([]);
            console.log('🚨 useDesignLoader clearing edges - context change');
            setEdges([]);
            originalDesignRef.current = null;
          }

          // Clear any incorrectly saved design data from localStorage for the new context
          // This prevents loading stale data that was saved during context switching
          const newDesignKey = `layrs-design-${contextType}-${id}`;
          const existingData = localStorage.getItem(newDesignKey);
          if (existingData) {
            try {
              const parsedData = JSON.parse(existingData);

              // Only remove data if it's actually contaminated (wrong context info)
              // Don't remove legitimate auto-saved data for the correct context
              // Handle both auto-save format (direct properties) and manual save format (nested under data)
              const dataContextType = parsedData.data?.contextType || parsedData.contextType;
              const dataContextId = parsedData.data?.contextId || parsedData.contextId;

              if (dataContextType && dataContextId &&
                  (dataContextType !== contextType || dataContextId !== id)) {
                debugLog(`🧹 Removing contaminated design data: expected ${contextType}-${id}, found ${dataContextType}-${dataContextId}`);
                localStorage.removeItem(newDesignKey);
              } else {
                debugLog(`✅ Keeping legitimate design data for ${contextType}-${id}`);
              }
            } catch (e) {
              debugLog(`🧹 Removing corrupted design data for ${contextType}-${id}`);
              localStorage.removeItem(newDesignKey);
            }
          }
        }

        try {
          // Load the design - our updated loadDesign function will only load from
          // Supabase/localStorage once per design and then use the cache
          const savedDesign = await loadDesign(currentQuestionId, contextType, contextId);

          if (savedDesign) {
            // Store the original design for reference
            const timestamp = new Date(savedDesign.lastModified).getTime();
            originalDesignRef.current = {
              nodes: savedDesign.nodes || [],
              edges: savedDesign.edges || [],
              timestamp
            };
            debugLog('Stored original design state:', originalDesignRef.current);

            // Load the saved design
            setCurrentDesign(savedDesign);
            setUserJourneys(savedDesign.userJourneys || '');
            setAssumptions(savedDesign.assumptions || '');
            setConstraints(savedDesign.constraints || '');

            // We'll set nodes and edges when the reactFlowInstance is available
            setNodes(savedDesign.nodes || []);

            // Process loaded edges to add the onChange handler for label editing
            const processedEdges = processLoadedEdges(savedDesign.edges || [], setEdges);
            setEdges(processedEdges);

            // Show toast only if we haven't shown it for this design before
            const toastKey = `loaded-design-${contextType}-${id}`;
            if (!shownToasts.current.has(toastKey)) {
              // Use a short delay to prevent layout shifts during initial render
              setTimeout(() => {
                const contextLabel = contextType === 'question' ? 'question' :
                                    contextType === 'course' ? 'course' : 'canvas';
                toast.info(`Loaded design for ${contextLabel} #${id}`, {
                  duration: 3000, // Show for 3 seconds only
                });
                shownToasts.current.add(toastKey);
              }, 500);
            }
          } else {
            // If there's no saved design, initialize with empty values
            debugLog(`📭 No saved design found for ${contextType} ${id}, clearing canvas`);
            setCurrentDesign(null);
            setUserJourneys('');
            setAssumptions('');
            setConstraints('');
            setNodes([]);
            console.log('🚨 useDesignLoader clearing edges - no design found');
            setEdges([]);

            // Clear the original design reference
            originalDesignRef.current = null;
          }
        } catch (error) {
          debugError('Error loading design:', error);
          toast.error('Failed to load design');

          // Clear the canvas on error to prevent showing stale data
          debugLog(`❌ Error loading design, clearing canvas for ${contextType} ${id}`);
          setCurrentDesign(null);
          setUserJourneys('');
          setAssumptions('');
          setConstraints('');
          setNodes([]);
          console.log('🚨 useDesignLoader clearing edges - error loading');
          setEdges([]);
          originalDesignRef.current = null;
        }
      }
    };

    loadDesignData();

    // When unmounting or changing designs, clear tracking for the previous design
    return () => {
      if (currentQuestionId) {
        // Clear modification tracking when changing designs
        modifiedNodesRef.current.clear();
        deletedNodesRef.current.clear();
      }
    };
  }, [currentQuestionId, contextType, contextId, loadDesign]);
  // Note: State setters (setCurrentDesign, setNodes, etc.) and refs are intentionally
  // NOT in dependencies to prevent infinite re-renders

  // Effect to update reactFlowInstance with saved nodes and edges
  useEffect(() => {
    if (reactFlowInstance) {
      // Always update the ReactFlow instance, even with empty arrays
      // This ensures the canvas is cleared when no design is found
      debugLog(`🎨 Updating ReactFlow instance with ${nodes.length} nodes and ${edges.length} edges`);
      reactFlowInstance.setNodes(nodes);
      reactFlowInstance.setEdges(edges);
    }
  }, [reactFlowInstance, nodes, edges]);
};
