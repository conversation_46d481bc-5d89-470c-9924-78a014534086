import { useCallback } from 'react';
import { ReactFlowInstance } from '@xyflow/react';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { SaveStatus, useManualSave } from '@/hooks/useSmartAutoSave';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface UseSimpleManualSaveProps {
  currentQuestionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  reactFlowInstance: ReactFlowInstance | null;
  userJourneys: string;
  assumptions: string;
  constraints: string;
  saveDesign: (questionId: string, design: any, contextType?: DesignContextCategory, contextId?: string, syncToSupabase?: boolean, updateCurrentDesign?: boolean) => Promise<any>;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  mergeNodesAndEdges: (currentNodes: any[], currentEdges: any[]) => { nodes: any[], edges: any[] };
  originalDesignRef: React.MutableRefObject<{nodes: any[], edges: any[], timestamp: number} | null>;
  deletedNodesRef: React.MutableRefObject<Set<string>>;
  onStatusChange?: (status: SaveStatus, lastSaved?: Date) => void;
}

export const useSimpleManualSave = ({
  currentQuestionId,
  contextType = 'question',
  contextId,
  reactFlowInstance,
  userJourneys,
  assumptions,
  constraints,
  saveDesign,
  setHasUnsavedChanges,
  mergeNodesAndEdges,
  originalDesignRef,
  deletedNodesRef,
  onStatusChange
}: UseSimpleManualSaveProps) => {

  // Create design data from current state
  const createDesignData = useCallback(() => {
    if (!reactFlowInstance) return null;

    const currentNodes = reactFlowInstance.getNodes();
    const currentEdges = reactFlowInstance.getEdges();

    // Merge the current state with the original state
    const { nodes: mergedNodes, edges: mergedEdges } = mergeNodesAndEdges(currentNodes, currentEdges);

    return {
      nodes: mergedNodes,
      edges: mergedEdges,
      userJourneys,
      assumptions,
      constraints,
      contextType,
      contextId: contextId || currentQuestionId
    };
  }, [reactFlowInstance, userJourneys, assumptions, constraints, contextType, contextId, currentQuestionId, mergeNodesAndEdges]);

  // Wrapper for saveDesign that handles the manual save format
  const handleSave = useCallback(async (data: any, syncToSupabase = false) => {
    if (!currentQuestionId) return;

    const designData = data || createDesignData();
    if (!designData) return;

    await saveDesign(
      currentQuestionId,
      designData,
      contextType,
      contextId,
      syncToSupabase
    );

    // Update tracking references
    if (reactFlowInstance) {
      const now = Date.now();
      originalDesignRef.current = {
        nodes: designData.nodes,
        edges: designData.edges,
        timestamp: now
      };
      deletedNodesRef.current.clear();
    }

    // Clear unsaved changes flag
    setHasUnsavedChanges(false);
  }, [currentQuestionId, createDesignData, saveDesign, contextType, contextId, reactFlowInstance, originalDesignRef, deletedNodesRef, setHasUnsavedChanges]);

  // Initialize manual save hook
  const { forceSave, status, lastSaved } = useManualSave({
    onSave: handleSave,
    onStatusChange
  });

  // Public API for manual saves
  const saveNow = useCallback(async () => {
    debugLog('🔄 Manual save triggered:', {
      currentQuestionId,
      hasReactFlowInstance: !!reactFlowInstance
    });

    if (!currentQuestionId) {
      debugWarn('❌ Cannot save: No currentQuestionId');
      return;
    }

    if (!reactFlowInstance) {
      debugWarn('❌ Cannot save: No reactFlowInstance');
      return;
    }

    const designData = createDesignData();
    debugLog('📊 Design data created:', {
      hasDesignData: !!designData,
      nodeCount: designData?.nodes?.length || 0,
      edgeCount: designData?.edges?.length || 0
    });

    if (designData) {
      console.log('💾 Saving to Supabase...');
      await forceSave(designData, true); // Save to Supabase
    } else {
      console.warn('❌ Cannot save: Design data is null');
    }
  }, [currentQuestionId, reactFlowInstance, createDesignData, forceSave]);

  return {
    saveNow,
    status,
    lastSaved
  };
};
