import { useCallback, useEffect } from 'react';
import { Node, Edge } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface KeyboardShortcutsProps {
  selectedNodes: Node[];
  selectedEdges: Edge[];
  onCopy: (nodes: Node[], edges: Edge[]) => void;
  onPaste: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onDelete: () => void;
}

/**
 * Hook for handling keyboard shortcuts with cross-platform support
 * Mac: Cmd + C/V/Z, Windows/Linux: Ctrl + C/V/Z
 */
export const useKeyboardShortcuts = ({
  selectedNodes,
  selectedEdges,
  onCopy,
  onPaste,
  onUndo,
  onRedo,
  onDelete
}: KeyboardShortcutsProps) => {
  
  // Detect if user is on Mac
  const isMac = typeof navigator !== 'undefined' && navigator.platform.toUpperCase().indexOf('MAC') >= 0;
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Get the modifier key (Cmd on Mac, Ctrl on Windows/Linux)
    const modifierKey = isMac ? event.metaKey : event.ctrlKey;
    
    // Prevent shortcuts when user is typing in input fields
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return;
    }
    
    // Copy (Cmd/Ctrl + C)
    if (modifierKey && event.key.toLowerCase() === 'c' && !event.shiftKey) {
      // Only prevent default and handle copy if there are actually selected canvas elements
      if (selectedNodes.length > 0 || selectedEdges.length > 0) {
        event.preventDefault();
        onCopy(selectedNodes, selectedEdges);
        debugLog(`📋 Copied ${selectedNodes.length} nodes and ${selectedEdges.length} edges`);
      }
      // If no canvas elements are selected, let the browser handle the copy (for text selection)
      return;
    }
    
    // Paste (Cmd/Ctrl + V)
    if (modifierKey && event.key.toLowerCase() === 'v' && !event.shiftKey) {
      // Only prevent default for canvas paste operations
      // Let browser handle paste in text fields naturally
      event.preventDefault();
      onPaste();
      debugLog('📋 Pasting from clipboard...');
      return;
    }
    
    // Undo (Cmd/Ctrl + Z)
    if (modifierKey && event.key.toLowerCase() === 'z' && !event.shiftKey) {
      event.preventDefault();
      onUndo();
      debugLog('↩️ Undo action');
      return;
    }
    
    // Redo (Cmd/Ctrl + Shift + Z or Cmd/Ctrl + Y)
    if (modifierKey && ((event.key.toLowerCase() === 'z' && event.shiftKey) || event.key.toLowerCase() === 'y')) {
      event.preventDefault();
      onRedo();
      debugLog('↪️ Redo action');
      return;
    }
    
    // Delete (Delete or Backspace)
    if (event.key === 'Delete' || event.key === 'Backspace') {
      event.preventDefault();
      if (selectedNodes.length > 0 || selectedEdges.length > 0) {
        onDelete();
        debugLog(`🗑️ Deleted ${selectedNodes.length} nodes and ${selectedEdges.length} edges`);
      }
      return;
    }
    
    // Select All (Cmd/Ctrl + A)
    if (modifierKey && event.key.toLowerCase() === 'a') {
      // Only prevent default when we're in the canvas area, not in text fields
      // React Flow will handle select all for canvas elements
      event.preventDefault();
      debugLog('🔘 Select all (handled by React Flow)');
      return;
    }
    
  }, [selectedNodes, selectedEdges, onCopy, onPaste, onUndo, onRedo, onDelete, isMac]);
  
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  
  // Return keyboard shortcut info for display
  return {
    shortcuts: {
      copy: isMac ? '⌘ + C' : 'Ctrl + C',
      paste: isMac ? '⌘ + V' : 'Ctrl + V',
      undo: isMac ? '⌘ + Z' : 'Ctrl + Z',
      redo: isMac ? '⌘ + ⇧ + Z' : 'Ctrl + Shift + Z',
      delete: 'Delete',
      selectAll: isMac ? '⌘ + A' : 'Ctrl + A'
    },
    isMac
  };
};
