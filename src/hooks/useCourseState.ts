import { useState, useEffect } from 'react';
import { Course, CourseStep, ComponentPropertyRequirement } from '@/types/course';
import { Node, Edge } from '@xyflow/react';
import { sampleCourses } from '@/data/sampleCourses';
import { twitterCourse } from '@/data/twitterCourse';
import { urlShortenerCourse } from '@/data/urlShortenerCourse';
import { ecommerceCourse } from '@/data/ecommerceCourse';
import { useAuth } from '@/contexts/AuthContext';
import {
  checkBuildAuthServiceCompletion,
  checkConnectAuthServiceCompletion,
  checkAddMediaServiceCompletion,
  checkQueuePlacementCompletion,
  checkMediaDatabaseCompletion,
  checkCDNCompletion,
  checkAnalyticsServiceCompletion,
  checkNotificationsCompletion,
  checkDefaultStepCompletion
} from './stepValidations';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface CourseStateReturn {
  courses: Course[];
  currentCourse: Course | null;
  isGuidedMode: boolean;
  setIsGuidedMode: (value: boolean) => void;
  setCurrentCourse: (course: Course | null) => void;
  startCourse: (courseId: string) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  getCurrentStep: () => CourseStep | null;
  checkStepCompletion: (nodes: Node[], edges: Edge[]) => boolean;
  markCurrentStepComplete: () => void;
  getCourseProgress: () => number; // percentage
}

export const useCourseState = (): CourseStateReturn => {
  const [courses, setCourses] = useState<Course[]>([...sampleCourses, twitterCourse, urlShortenerCourse, ecommerceCourse]);
  const [currentCourse, setCurrentCourse] = useState<Course | null>(null);
  const [isGuidedMode, setIsGuidedMode] = useState<boolean>(false);
  const { user } = useAuth();

  // Generate a storage key for course progress based on user ID
  const getProgressStorageKey = (courseId: string): string => {
    const userPrefix = user ? `user-${user.id}` : 'anonymous';
    return `layrs-course-progress-${userPrefix}-${courseId}`;
  };

  // Save course progress to localStorage
  const saveCourseProgress = (course: Course | null) => {
    if (!course) return;

    try {
      const storageKey = getProgressStorageKey(course.id);
      const progressData = {
        courseId: course.id,
        currentStepIndex: course.currentStepIndex,
        steps: course.steps.map(step => ({
          id: step.id,
          completed: step.completed || false
        })),
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem(storageKey, JSON.stringify(progressData));
      debugLog(`Course progress saved for ${course.id}`);
    } catch (error) {
      debugError('Error saving course progress:', error);
    }
  };

  // Load course progress from localStorage
  const loadCourseProgress = (courseId: string): Partial<Course> | null => {
    try {
      const storageKey = getProgressStorageKey(courseId);
      const savedProgressJson = localStorage.getItem(storageKey);

      if (!savedProgressJson) {
        debugLog(`No saved progress found for course ${courseId}`);
        return null;
      }

      const savedProgress = JSON.parse(savedProgressJson);
      debugLog(`Loaded saved progress for course ${courseId}:`, savedProgress);
      return savedProgress;
    } catch (error) {
      debugError('Error loading course progress:', error);
      return null;
    }
  };

  const startCourse = (courseId: string) => {
    debugLog(`Starting course with ID: ${courseId}`);
    const course = courses.find(c => c.id === courseId);
    if (course) {
      debugLog('Course found, checking for saved progress');

      // Check if there's saved progress for this course
      const savedProgress = loadCourseProgress(courseId);

      let courseToSet: Course;

      if (savedProgress && savedProgress.currentStepIndex !== undefined) {
        debugLog('Found saved progress, restoring course state');

        // Create a new course object with the saved progress
        courseToSet = {
          ...course,
          currentStepIndex: savedProgress.currentStepIndex,
          steps: course.steps.map(step => {
            // Find if this step was completed in saved progress
            const savedStep = savedProgress.steps?.find(s => s.id === step.id);
            return {
              ...step,
              completed: savedStep ? savedStep.completed : false
            };
          })
        };

        debugLog('Restored course with progress:', courseToSet);
      } else {
        debugLog('No saved progress found, starting fresh');
        // Reset the course progress when starting fresh
        courseToSet = {
          ...course,
          currentStepIndex: 0,
          steps: course.steps.map(step => ({ ...step, completed: false }))
        };
      }

      setCurrentCourse(courseToSet);
      setIsGuidedMode(true);
      debugLog('isGuidedMode set to:', true);
    } else {
      debugLog('No course found with ID:', courseId);
    }
  };

  const getCurrentStep = (): CourseStep | null => {
    if (!currentCourse) return null;
    return currentCourse.steps[currentCourse.currentStepIndex];
  };

  const goToNextStep = () => {
    if (!currentCourse) return;

    if (currentCourse.currentStepIndex < currentCourse.steps.length - 1) {
      const updatedCourse = {
        ...currentCourse,
        currentStepIndex: currentCourse.currentStepIndex + 1
      };
      setCurrentCourse(updatedCourse);

      // Save progress to localStorage
      saveCourseProgress(updatedCourse);
    }
  };

  const goToPreviousStep = () => {
    if (!currentCourse) return;

    if (currentCourse.currentStepIndex > 0) {
      const updatedCourse = {
        ...currentCourse,
        currentStepIndex: currentCourse.currentStepIndex - 1
      };
      setCurrentCourse(updatedCourse);

      // Save progress to localStorage
      saveCourseProgress(updatedCourse);
    }
  };

  // Mark the current step as complete
  const markCurrentStepComplete = () => {
    if (!currentCourse) return;

    const updatedSteps = [...currentCourse.steps];
    updatedSteps[currentCourse.currentStepIndex].completed = true;

    const updatedCourse = {
      ...currentCourse,
      steps: updatedSteps
    };

    setCurrentCourse(updatedCourse);

    // Save progress to localStorage
    saveCourseProgress(updatedCourse);
  };

  // Calculate the percentage of completed steps
  const getCourseProgress = (): number => {
    if (!currentCourse) return 0;

    const completedSteps = currentCourse.steps.filter(step => step.completed).length;
    return Math.round((completedSteps / currentCourse.steps.length) * 100);
  };

  // Ensure the implementation matches the interface
  const checkStepCompletion = (nodes: Node[], edges: Edge[]): boolean => {
    const currentStep = getCurrentStep();
    if (!currentStep || currentStep.completed) return true;

    // For informational steps, no need to check components/connections
    if (currentStep.isInformationalOnly) return true;

    // For Step 3 specifically, we need custom validation logic
    if (currentStep.id === 'build-auth-service') {
      return checkBuildAuthServiceCompletion(nodes, edges);
    }

    // For Step 4 specifically, we need custom validation logic to connect API Gateway to Auth Service
    if (currentStep.id === 'connect-auth-service') {
      return checkConnectAuthServiceCompletion(nodes, edges);
    }

    // For Step 5, validate Media Service and connection from Auth Service
    if (currentStep.id === 'add-media-service') {
      return checkAddMediaServiceCompletion(nodes, edges);
    }

    // For Step 6, validate traffic simulation with custom properties
    if (currentStep.id === 'simulate-traffic' || currentStep.id === 'final-simulation') {
      // For this step, we'll check if the required custom properties are set
      // The actual simulation success will be handled by the TrafficSimulator component
      if (currentStep.goal.simulation?.customProperties) {
        return checkRequiredCustomProperties(nodes, currentStep.goal.simulation.customProperties);
      }
      return false; // Initially not complete until simulation runs
    }

    // For Step 7, validate queue placement between Auth Service and Media Service
    if (currentStep.id === 'add-queue') {
      return checkQueuePlacementCompletion(nodes, edges);
    }

    // For Step 8, validate media database connection
    if (currentStep.id === 'add-media-database') {
      return checkMediaDatabaseCompletion(nodes, edges);
    }

    // For Step 9, validate CDN connection
    if (currentStep.id === 'add-cdn') {
      return checkCDNCompletion(nodes, edges);
    }

    // For Step 10, validate Analytics Service setup
    if (currentStep.id === 'add-analytics') {
      return checkAnalyticsServiceCompletion(nodes, edges);
    }

    // For Step 11, validate Notifications setup
    if (currentStep.id === 'add-notifications') {
      return checkNotificationsCompletion(nodes, edges);
    }

    // For Step 12, validate final simulation (this is handled by the TrafficSimulator component)
    if (currentStep.id === 'final-simulation') {
      debugLog("Final simulation step detected - checking if system is ready for simulation");

      // For this step, we need to check if the system has the minimum required components
      // to run a meaningful simulation

      // Check for core components
      const frontend = nodes.find(node =>
        (node.data as any)?.type === 'frontend' && !node.parentId
      );

      const authService = nodes.find(node =>
        node.type === 'compositeNode' &&
        (node.data as any)?.label === 'Authentication Service'
      );

      const mediaService = nodes.find(node =>
        (node.data as any)?.type === 'server' &&
        ((node.data as any)?.label === 'Media Service' ||
         ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
        !node.parentId
      );

      // Check for core connections
      const frontendToAuth = frontend && authService && edges.some(edge =>
        (edge.source === frontend.id && edge.target === authService.id) ||
        (edge.source === authService.id && edge.target === frontend.id)
      );

      const authToMedia = authService && mediaService && edges.some(edge =>
        (edge.source === authService.id && edge.target === mediaService.id) ||
        (edge.source === mediaService.id && edge.target === authService.id)
      );

      // Check for required QPS settings
      const hasRequiredQPSSettings = checkRequiredQPSSettings(nodes);

      debugLog("Final simulation validation:", {
        hasFrontend: !!frontend,
        hasAuthService: !!authService,
        hasMediaService: !!mediaService,
        hasFrontendToAuth: !!frontendToAuth,
        hasAuthToMedia: !!authToMedia,
        hasRequiredQPSSettings
      });

      // For the final simulation, we need at least the core components with connections
      // and QPS settings to run a meaningful simulation
      return !!frontend && !!authService && !!mediaService &&
             !!frontendToAuth && !!authToMedia && hasRequiredQPSSettings;
    }

    // For Step 13, validate export (this is handled by the Export button click)
    if (currentStep.id === 'export-system') {
      // This will be completed when the export button is clicked
      return true;
    }

    // Default check for other steps
    return checkDefaultStepCompletion(currentStep, nodes, edges);
  };

  // Check if required custom properties are set on components
  const checkRequiredCustomProperties = (nodes: Node[], requirements: ComponentPropertyRequirement[]): boolean => {
    for (const requirement of requirements) {
      // Find nodes of the required type
      const matchingNodes = nodes.filter(node => {
        if (requirement.componentType === 'compositeNode') {
          return node.type === 'compositeNode';
        }
        return (node.data as any)?.type === requirement.componentType;
      });

      // Check if any node of this type has the required property (case insensitive)
      const hasProperty = matchingNodes.some(node => {
        const nodeData = node.data as any;
        const customProps = nodeData.metadata?.customProperties || [];
        return customProps.some((prop: any) =>
          prop.key.toLowerCase() === requirement.key.toLowerCase() &&
          prop.value === requirement.value
        );
      });

      if (!hasProperty) {
        return false;
      }
    }

    return true;
  };

  // Helper function to check if required QPS settings are present
  function checkRequiredQPSSettings(nodes: Node[]): boolean {
    // Find Auth Service and Media Service
    const authService = nodes.find(node =>
      node.type === 'compositeNode' &&
      (node.data as any)?.label === 'Authentication Service'
    );

    const mediaService = nodes.find(node =>
      (node.data as any)?.type === 'server' &&
      ((node.data as any)?.label === 'Media Service' ||
       ((node.data as any)?.label && (node.data as any)?.label.includes('Media Service'))) &&
      !node.parentId
    );

    // Check if Auth Service has maxQPS property
    const authHasQPS = authService && ((authService.data as any)?.metadata?.customProperties || []).some(
      (prop: any) => prop.key.toLowerCase() === 'maxqps' && prop.value
    );

    // Check if Media Service has maxQPS property
    const mediaHasQPS = mediaService && ((mediaService.data as any)?.metadata?.customProperties || []).some(
      (prop: any) => prop.key.toLowerCase() === 'maxqps' && prop.value
    );

    return !!authHasQPS && !!mediaHasQPS;
  }

  // Save course progress when currentCourse changes
  useEffect(() => {
    if (currentCourse) {
      saveCourseProgress(currentCourse);
    }
  }, [currentCourse]);

  // Save course progress when user leaves the page
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (currentCourse) {
        saveCourseProgress(currentCourse);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [currentCourse]);

  // Make sure the returned function in the hook matches the interface
  return {
    courses,
    currentCourse,
    isGuidedMode,
    setIsGuidedMode,
    setCurrentCourse,
    startCourse,
    goToNextStep,
    goToPreviousStep,
    getCurrentStep,
    checkStepCompletion, // This should be the function that takes both nodes and edges
    markCurrentStepComplete,
    getCourseProgress
  };
};
