import { useAuth } from '@/contexts/AuthContext';

// Define admin emails - in production, this should come from environment variables or database
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Add more admin emails here if needed
];

export const useAdminAccess = () => {
  const { user, loading } = useAuth();

  const isAdmin = user?.email && ADMIN_EMAILS.includes(user.email.toLowerCase());

  return {
    isAdmin: !!isAdmin,
    isLoading: loading,
    user,
    adminEmails: ADMIN_EMAILS,
  };
};

export default useAdminAccess;
