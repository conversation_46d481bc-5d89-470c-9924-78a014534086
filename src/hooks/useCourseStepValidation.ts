import { useEffect, useRef } from 'react';
import { Node, Edge } from '@xyflow/react';
import { useCourse } from '@/contexts/CourseContext';
import { toast } from 'sonner';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export const useCourseStepValidation = (nodes: Node[], edges: Edge[]) => {
  const { isGuidedMode, getCurrentStep, checkStepCompletion, markCurrentStepComplete } = useCourse();
  const currentStep = getCurrentStep();
  const lastCheckRef = useRef<number>(0);
  const completedRef = useRef<boolean>(false);
  const lastStepIdRef = useRef<string | null>(null);

  // Check for step completion with debouncing
  useEffect(() => {
    if (!isGuidedMode || !currentStep) return;

    // Reset completion when step changes
    if (currentStep.id !== lastStepIdRef.current) {
      completedRef.current = !!currentStep.completed;
      lastStepIdRef.current = currentStep.id;
    }

    // If already completed, don't check again
    if (completedRef.current) return;

    const now = Date.now();
    // Only check completion every 1 second to avoid excessive checking
    if (now - lastCheckRef.current < 1000) return;

    lastCheckRef.current = now;

    debugLog("Checking step completion for step:", currentStep.id);
    const isCompleted = checkStepCompletion(nodes, edges);
    debugLog("Step completion result:", isCompleted);
    
    if (isCompleted && !currentStep.completed) {
      debugLog("Marking step as complete:", currentStep.id);
      // Mark step as complete
      markCurrentStepComplete();
      completedRef.current = true;
      
      // Show success toast with step-specific messaging
      let successMessage = `Step completed! ${currentStep.title}`;
      let description = "Great job! You can now move to the next step.";
      
      // Custom messages for specific steps
      if (currentStep.id === 'add-queue') {
        successMessage = "Queue added successfully!";
        description = "Your system is now more resilient to traffic spikes.";
      } else if (currentStep.id === 'add-media-database') {
        successMessage = "Media Database added successfully!";
        description = "Your system can now persistently store uploaded media files.";
      } else if (currentStep.id === 'add-cdn') {
        successMessage = "CDN integration complete!";
        description = "Your media will now load faster for users across the globe.";
      } else if (currentStep.id === 'add-analytics') {
        successMessage = "Analytics Service added!";
        description = "You can now capture valuable user engagement data.";
      } else if (currentStep.id === 'add-notifications') {
        successMessage = "Notification flow created!";
        description = "Users will now be notified about new content.";
      } else if (currentStep.id === 'final-simulation') {
        successMessage = "Final simulation complete!";
        description = "You've successfully tested your system at scale.";
      } else if (currentStep.id === 'build-auth-service') {
        successMessage = "Authentication Service built successfully!";
        description = "You've created a secure authentication system for your application.";
      } else if (currentStep.id === 'connect-auth-service') {
        successMessage = "Authentication Service connected!";
        description = "Your Frontend can now communicate with the Authentication Service.";
      } else if (currentStep.id === 'add-media-service') {
        successMessage = "Media Service added and connected!";
        description = "Your system now has a protected backend service that requires authentication.";
      }
      
      toast.success(successMessage, {
        description: description,
        duration: 4000
      });
    }
  }, [
    isGuidedMode, 
    currentStep, 
    nodes, 
    edges, 
    checkStepCompletion, 
    markCurrentStepComplete
  ]);

  // Reset completion tracking when step changes
  useEffect(() => {
    if (currentStep) {
      completedRef.current = !!currentStep.completed;
    }
  }, [currentStep]);

  return { isStepCompleted: completedRef.current };
};
