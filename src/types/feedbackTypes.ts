/**
 * Chat feedback level types and configurations
 */

export interface FeedbackLevel {
  value: 'interview' | 'production' | 'educational';
  label: string;
  description: string;
}

export const feedbackLevels: FeedbackLevel[] = [
  {
    value: 'interview',
    label: 'Interview Focus',
    description: 'Core architecture, scalability, design decisions'
  },
  {
    value: 'production',
    label: 'Production Ready',
    description: 'Complete system with monitoring, security, ops'
  },
  {
    value: 'educational',
    label: 'Learning Mode',
    description: 'Detailed explanations and best practices'
  }
];

export const DEFAULT_FEEDBACK_LEVEL: FeedbackLevel['value'] = 'educational';

/**
 * Storage key for persisting feedback level preference
 */
export const FEEDBACK_LEVEL_STORAGE_KEY = 'layrs-chat-feedback-level';

/**
 * Get feedback level configuration by value
 */
export const getFeedbackLevelConfig = (value: FeedbackLevel['value']): FeedbackLevel => {
  return feedbackLevels.find(level => level.value === value) || feedbackLevels[2]; // Default to educational
};

/**
 * Get prompt instructions for each feedback level
 */
export const getFeedbackLevelPrompt = (level: FeedbackLevel['value']): string => {
  switch (level) {
    case 'interview':
      return 'Focus on system design interview aspects: core architecture, scalability considerations, key design decisions, and trade-offs. Keep responses concise and interview-appropriate.';
    case 'production':
      return 'Provide production-ready guidance including monitoring, security, operations, deployment strategies, error handling, and real-world implementation details.';
    case 'educational':
      return 'Provide detailed educational explanations with best practices, learning context, examples, and comprehensive coverage of concepts to help understand the topic thoroughly.';
    default:
      return getFeedbackLevelPrompt('educational');
  }
};
