
import { Node, Edge } from '@xyflow/react';

// Define what makes up a course step's completion goal
export interface StepGoal {
  title?: string;
  components: string[];       // Required component types
  connections: [string, string][];  // Required connections [source, target]
  properties?: {              // Optional specific properties
    customProperties?: ComponentPropertyRequirement[];
  };
  simulation?: SimulationRequirement; // Optional simulation requirements
  exportRequired?: boolean;   // Optional requirement for export
}

// Custom property configuration for components
export interface ComponentPropertyRequirement {
  componentType: string;
  key: string;
  value: string;
}

// Define simulation requirements
export interface SimulationRequirement {
  required: boolean;
  minQPS?: number;
  maxQPS?: number;
  minDuration?: number;
  maxDuration?: number;
  payload?: Record<string, any>;
  customProperties?: ComponentPropertyRequirement[];
}

// Define the structure of a course step
export interface CourseStep {
  id: string;
  title: string;
  description?: string;
  explanation?: string;  // Why this component/step is important
  hints?: string[];      // Hints to show if user gets stuck
  goal: StepGoal;
  completed?: boolean;
  isInformationalOnly?: boolean; // Flag to indicate this is just an informational step
  filterComponents?: string[]; // Components to filter by in the component palette
}

// Define the structure of a course
export interface Course {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string; // e.g. "30 minutes"
  category?: string; // Make category optional
  steps: CourseStep[];
  currentStepIndex: number;
}
