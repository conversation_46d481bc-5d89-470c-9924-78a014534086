/**
 * Chat feedback style types and configurations
 */

export enum FeedbackStyle {
  FOCUSED = 'focused',
  EDUCATIONAL = 'educational', 
  ANALYTICAL = 'analytical',
  CREATIVE = 'creative',
  PRACTICAL = 'practical',
  MENTOR = 'mentor'
}

export interface FeedbackStyleConfig {
  id: FeedbackStyle;
  name: string;
  description: string;
  icon: string;
  prompt: string;
  color: string;
}

export const FEEDBACK_STYLES: FeedbackStyleConfig[] = [
  {
    id: FeedbackStyle.FOCUSED,
    name: 'Focused',
    description: 'Concise, direct answers focused on the specific question',
    icon: '🎯',
    color: 'text-blue-600',
    prompt: 'Provide a focused, concise answer that directly addresses the question. Be brief and to the point while maintaining accuracy.'
  },
  {
    id: FeedbackStyle.EDUCATIONAL,
    name: 'Educational',
    description: 'Detailed explanations with learning context and examples',
    icon: '📚',
    color: 'text-green-600',
    prompt: 'Provide a comprehensive educational response with detailed explanations, context, examples, and learning resources. Help the user understand the underlying concepts.'
  },
  {
    id: FeedbackStyle.ANALYTICAL,
    name: 'Analytical',
    description: 'Deep technical analysis with pros/cons and trade-offs',
    icon: '🔍',
    color: 'text-purple-600',
    prompt: 'Provide a thorough analytical response that examines multiple perspectives, discusses trade-offs, pros and cons, and technical implications. Include comparative analysis where relevant.'
  },
  {
    id: FeedbackStyle.CREATIVE,
    name: 'Creative',
    description: 'Brainstorming mode with multiple approaches and alternatives',
    icon: '💡',
    color: 'text-yellow-600',
    prompt: 'Think creatively and provide multiple innovative approaches, alternative solutions, and out-of-the-box ideas. Encourage exploration of different possibilities.'
  },
  {
    id: FeedbackStyle.PRACTICAL,
    name: 'Practical',
    description: 'Implementation-focused with actionable steps and examples',
    icon: '🚀',
    color: 'text-orange-600',
    prompt: 'Focus on practical implementation with actionable steps, code examples, specific tools, and concrete guidance that can be immediately applied.'
  },
  {
    id: FeedbackStyle.MENTOR,
    name: 'Mentor',
    description: 'Socratic method with guiding questions to help you think',
    icon: '🎓',
    color: 'text-indigo-600',
    prompt: 'Act as a mentor using the Socratic method. Ask thoughtful guiding questions to help the user think through the problem themselves. Provide hints and gentle guidance rather than direct answers.'
  }
];

export const DEFAULT_FEEDBACK_STYLE = FeedbackStyle.EDUCATIONAL;

/**
 * Get feedback style configuration by ID
 */
export const getFeedbackStyleConfig = (styleId: FeedbackStyle): FeedbackStyleConfig => {
  return FEEDBACK_STYLES.find(style => style.id === styleId) || FEEDBACK_STYLES[1]; // Default to Educational
};

/**
 * Storage key for persisting feedback style preference
 */
export const FEEDBACK_STYLE_STORAGE_KEY = 'layrs-chat-feedback-style';
