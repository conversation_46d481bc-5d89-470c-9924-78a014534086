
/* Animation for traffic flow on edges */
@keyframes edge-traffic-flow {
  0% {
    stroke-dashoffset: 100;
    stroke-width: 2;
  }
  50% {
    stroke-width: 3;
  }
  100% {
    stroke-dashoffset: 0;
    stroke-width: 2;
  }
}

.animate-edge-traffic {
  stroke: #4caf50 !important;
  stroke-dasharray: 10, 5;
  animation: edge-traffic-flow 0.8s linear;
}

/* Animation for nodes processing - older style with color change */
@keyframes node-processing-old {
  0% {
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
  }
  50% {
    filter: brightness(1.2);
    box-shadow: 0 0 0 5px rgba(76, 175, 80, 0.2);
  }
  100% {
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

.node-processing {
  animation: node-processing-old 0.8s ease-out;
}

/* Animation for node overload - older style with color pulse */
@keyframes node-overload-old {
  0% {
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4);
  }
  50% {
    filter: brightness(1.3);
    box-shadow: 0 0 0 8px rgba(220, 38, 38, 0.2);
  }
  100% {
    filter: brightness(1);
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
  }
}

.node-overload {
  animation: node-overload-old 0.5s ease-out infinite;
}

/* Animation for dropped packets */
@keyframes packet-drop {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateY(10px) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
}

.packet-drop {
  position: absolute;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #dc2626;
  font-weight: bold;
  z-index: 9999;
  animation: packet-drop 1s ease-out forwards;
  text-shadow: 0 0 3px rgba(255, 255, 255, 0.7);
}

/* Queue fill animation */
@keyframes queue-pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 0.4;
  }
}

.queue-fill {
  animation: queue-pulse 2s infinite;
}

/* Queue overflow animation */
@keyframes queue-overflow {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.7);
    transform: scale(1) rotate(0deg);
  }
  50% {
    box-shadow: 0 0 10px 5px rgba(220, 38, 38, 0.4);
    transform: scale(1.2) rotate(5deg);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 38, 38, 0);
    transform: scale(1) rotate(0deg);
  }
}

.queue-overflow {
  animation: queue-overflow 0.8s ease-in-out infinite;
  background-color: rgba(255, 0, 0, 0.1);
  border-radius: 50%;
}

/* Styling for simulation panel */
.simulation-panel {
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 16px;
  max-width: 400px;
}

/* Missing configuration warning */
.missing-config {
  border: 1px dashed #f59e0b;
  background-color: #fef3c7;
  border-radius: 6px;
  padding: 8px 12px;
  color: #9a3412;
  margin-bottom: 12px;
  font-size: 0.9rem;
}

.missing-config-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 4px;
}

/* Component status badge */
.component-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  border-radius: 9999px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 500;
}

.component-status-ok {
  background-color: #d1fae5;
  color: #065f46;
}

.component-status-warning {
  background-color: #fef3c7;
  color: #92400e;
}

.component-status-error {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* Metrics display for simulation results */
.metrics-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-top: 8px;
}

.metric-card {
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 8px;
  text-align: center;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
}

.metric-label {
  font-size: 0.75rem;
  color: #6b7280;
}
