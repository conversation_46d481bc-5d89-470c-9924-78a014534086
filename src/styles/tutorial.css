/* Tutorial highlight effect */
.tutorial-highlight {
  outline: 2px solid #3b82f6; /* Blue outline */
  outline-offset: 4px;
  position: relative;
  z-index: 45; /* High enough to be above regular content but below the tutorial popup */
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  pointer-events: auto !important;
}

/* Additional styling for composite component to ensure it's highlighted properly */
.component-compositeNode.tutorial-highlight {
  outline: 2px solid #3b82f6;
  outline-offset: 4px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  z-index: 45;
}

/* Tutorial connection highlight for source and target nodes */
.tutorial-connection-source,
.tutorial-connection-target {
  outline: 2px solid #3b82f6 !important; /* Blue outline to match regular connections */
  outline-offset: 4px;
  position: relative;
  z-index: 45;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

/* Tutorial node styling */
.tutorial-node {
  border: 2px solid #3b82f6 !important;
  z-index: 900 !important;
  box-shadow: 0 8px 16px rgba(59, 130, 246, 0.4) !important;
  pointer-events: all !important;
  overflow: visible !important;
}

/* Make tutorial nodes impossible to miss */
[data-id^="tutorial-"] {
  z-index: 1000 !important;
  pointer-events: all !important;
  max-width: 150px !important;
  max-height: 50px !important;
}

/* Make tutorial nodes stand out more against the canvas */
.react-flow__node.tutorial-node {
  z-index: 1000 !important; 
  opacity: 1 !important;
  width: 150px !important; /* Force exact width */
  height: 50px !important; /* Force exact height */
  min-width: 150px !important;
  min-height: 50px !important;
  max-width: 150px !important;
  max-height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: all !important;
  cursor: move !important;
  /* Ensure content is properly centered */
  box-sizing: border-box !important;
  padding: 10px !important;
}

.react-flow__node.tutorial-node .react-flow__node-content {
  width: 150px !important;
  height: 50px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transform: none !important;
  pointer-events: all !important;
}

.tutorial-node .react-flow__handle {
  background: #3b82f6;
  border: 2px solid #2563eb;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  z-index: 1001 !important;
  opacity: 1 !important;
  pointer-events: all !important;
  cursor: crosshair !important;
}

/* Pulsing handle highlight to draw attention to connection points */
.tutorial-node .react-flow__handle.connection-point {
  animation: pulse-handle 1.5s infinite;
  cursor: crosshair !important;
  width: 12px !important;
  height: 12px !important;
  pointer-events: all !important;
}

@keyframes pulse-handle {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    transform: scale(1.2);
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Add a hint for the user to drag from this handle */
.tutorial-node .react-flow__handle.source-hint::after {
  content: "Drag from here";
  position: absolute;
  white-space: nowrap;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  font-weight: bold;
  z-index: 1002;
  pointer-events: none;
}

/* Add a hint for the user to connect to this handle */
.tutorial-node .react-flow__handle.target-hint::after {
  content: "Connect here";
  position: absolute;
  white-space: nowrap;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #3b82f6;
  color: #3b82f6;
  font-weight: bold;
  z-index: 1002;
  pointer-events: none;
}

/* Tutorial edge styling - updated to look more like regular edges but highlighted */
.tutorial-edge {
  stroke: #3b82f6 !important;
  stroke-width: 2 !important;
  z-index: 9999 !important; /* Ensure edge is above nodes */
  filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.5));
  opacity: 1 !important;
}

.tutorial-edge path {
  stroke-width: 2 !important;
  opacity: 1 !important;
}

.tutorial-edge .react-flow__edge-label {
  color: #000;
  font-weight: bold;
  background: #f1f5fd;
  padding: 2px 5px;
  border-radius: 3px;
  font-size: 12px;
  z-index: 10000 !important;
  opacity: 1 !important;
  border: 1px solid #3b82f6;
}

.tutorial-edge .react-flow__edge-label div {
  background-color: rgba(241, 245, 253, 0.9) !important;
  z-index: 10000 !important;
  opacity: 1 !important;
}

/* Make sure animation shows even when react-flow is zoomed in/out */
.react-flow__viewport .react-flow__edge.tutorial-edge {
  z-index: 9999 !important;
  opacity: 1 !important;
}

/* Ensure the tutorial edge remains visible */
.react-flow__edge.tutorial-edge .react-flow__edge-path {
  stroke: #3b82f6 !important;
  stroke-width: 2 !important;
  opacity: 1 !important;
}

/* Connection in progress indicator */
.react-flow__connection-path.tutorial-connection {
  stroke: #3b82f6 !important;
  stroke-width: 3 !important;
  stroke-dasharray: 5 !important;
  animation: dash-animation 1s linear infinite !important;
}

/* Adjust the handle positions for tutorial nodes to improve connection appearance */
.tutorial-node .react-flow__handle-left {
  left: -5px !important;
  pointer-events: all !important;
  cursor: crosshair !important;
}

.tutorial-node .react-flow__handle-right {
  right: -5px !important;
  pointer-events: all !important;
  cursor: crosshair !important;
}

.tutorial-node .react-flow__handle-top {
  top: -5px !important;
  pointer-events: all !important;
}

.tutorial-node .react-flow__handle-bottom {
  bottom: -5px !important;
  pointer-events: all !important;
}

/* Animations - smooth and not distracting */
@keyframes dash-animation {
  to {
    stroke-dashoffset: -20;
  }
}

/* Success indicator for connection completion */
.connection-success {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(16, 185, 129, 0.9);
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: bold;
  z-index: 10000;
  animation: fade-in-out 2s forwards;
}

@keyframes fade-in-out {
  0% { opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

/* Special overrides to ensure interactivity */
.react-flow__pane {
  cursor: default !important;
}

.react-flow__node.tutorial-node {
  cursor: grab !important;
}

.react-flow__handle {
  pointer-events: all !important;
}

/* Modified tutorial overlay to allow interaction with specific elements */
.tutorial-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 40;
  pointer-events: auto;
}

/* This is crucial - make the overlay allow clicks to pass through to the tutorial nodes */
.tutorial-overlay.connection-step {
  pointer-events: none;
}

/* Tutorial connection highlight for source and target nodes */
.tutorial-connection-source,
.tutorial-connection-target {
  outline: 2px solid #3b82f6 !important; /* Blue outline to match regular connections */
  outline-offset: 4px;
  position: relative;
  z-index: 45;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

/* Tutorial node styling */
.tutorial-connection-source .react-flow__handle-right,
.tutorial-connection-target .react-flow__handle-left {
  background-color: #3b82f6 !important;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  border: 2px solid white !important;
  box-shadow: 0 0 0 2px #3b82f6 !important;
}

/* Tutorial connection animation */
@keyframes tutorial-connection-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.tutorial-connection-source .react-flow__handle-right,
.tutorial-connection-target .react-flow__handle-left {
  animation: tutorial-connection-pulse 2s infinite;
}

/* Tutorial overlay */
.tutorial-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 40;
  pointer-events: none;
}

/* Special styling for connection step overlay */
.tutorial-overlay.connection-step {
  background-color: rgba(0, 0, 0, 0.3);
}

/* Tutorial popup styling */
.tutorial-popup {
  position: fixed;
  z-index: 50;
  background: white;
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 300px;
  width: 100%;
}

/* Tutorial popup arrow */
.tutorial-popup::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

/* Tutorial popup arrow positions */
.tutorial-popup[data-position="top"]::before {
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: white;
}

.tutorial-popup[data-position="right"]::before {
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: white;
}

.tutorial-popup[data-position="bottom"]::before {
  top: -16px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: white;
}

.tutorial-popup[data-position="left"]::before {
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: white;
}
