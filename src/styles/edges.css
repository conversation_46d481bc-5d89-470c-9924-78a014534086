/* Make edges appear on top of nodes */
.react-flow__edge {
  z-index: 1000 !important;
}

/* Make edge paths even more visible */
.react-flow__edge-path {
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: stroke 0.3s ease, stroke-width 0.3s ease, filter 0.3s ease;
}

/* Make edge labels stand out */
.react-flow__edge-label {
  z-index: 1001 !important;
  cursor: pointer;
  pointer-events: all !important; /* Ensure labels are always clickable */
}

/* Ensure EdgeLabelRenderer content is always on top */
.react-flow__edge-label-renderer {
  z-index: 1002 !important;
  pointer-events: all !important;
}

/* Ensure edge label content is always on top */
.react-flow__edge-label-renderer > div {
  z-index: 1002 !important;
  position: relative !important;
}

.react-flow__edge-label text {
  font-weight: bold;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
  pointer-events: all !important; /* Ensure text is clickable */
}

/* Style for straight edges */
.straight-edge {
  stroke-linecap: butt;
  stroke-linejoin: miter;
}

/* Style for orthogonal edges */
.orthogonal-edge {
  stroke-linecap: square;
  stroke-linejoin: miter;
}

/* Style for bezier edges */
.bezier-edge {
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Style for smooth edges */
.smooth-edge {
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* Ensure arrow markers are properly positioned */
.react-flow__arrowhead {
  fill: #222;
  stroke: none;
}

/* Style for edge label input */
.edge-label-input-container {
  background: white;
  border-radius: 4px;
  border: 1px solid #ccc;
  overflow: hidden;
}

.edge-label-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  text-align: center;
  font-size: 12px;
  padding: 2px;
}

/* Add styles specifically for orthogonal edges */
.react-flow__edge.orthogonal-edge .react-flow__edge-path {
  stroke-linecap: square;
  stroke-linejoin: round;
}

/* Optimize marker placement for orthogonal edges */
.react-flow__edge.orthogonal-edge .react-flow__marker-arrow {
  transform-origin: center;
  transform-box: fill-box;
}

/* Optimize marker placement for bezier edges */
.react-flow__edge.bezier-edge .react-flow__marker-arrow {
  transform-origin: center;
  transform-box: fill-box;
}

/* Optimize marker placement for smooth edges */
.react-flow__edge.smooth-edge .react-flow__marker-arrow {
  transform-origin: center;
  transform-box: fill-box;
}
