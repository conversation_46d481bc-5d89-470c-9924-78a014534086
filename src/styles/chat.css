/* Chat window animations */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-in-out;
}

/* Chat loading animation */
.chat-loading-dots {
  display: flex;
  gap: 4px;
}

.chat-loading-dot {
  width: 6px;
  height: 6px;
  background-color: #9ca3af;
  border-radius: 50%;
  animation: chat-loading-bounce 1.4s ease-in-out infinite both;
}

.chat-loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.chat-loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes chat-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ChatGPT-inspired theme for chat panel */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100%;
  overflow: hidden;
  background-color: #ffffff;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  padding: 0;
  background-color: #ffffff;
}

.chat-input {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
  border-top: 1px solid #e5e7eb;
  margin-top: auto;
  background-color: #ffffff;
}

/* ChatGPT-style message bubbles */
.chat-message-user {
  background-color: #f7f7f8;
  border: none;
  margin: 0;
  padding: 1.5rem 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.chat-message-assistant {
  background-color: #ffffff;
  border: none;
  margin: 0;
  padding: 1.5rem 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  border-bottom: 1px solid #f0f0f0;
}

.chat-message-content {
  max-width: 48rem;
  margin: 0 auto;
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
}

.chat-avatar {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.125rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.625rem;
  margin-top: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.chat-avatar-user {
  background-color: #9b87f5;
  color: white;
}

.chat-avatar-assistant {
  background-color: #7E69AB;
  color: white;
}

.chat-text {
  flex: 1;
  line-height: 1.5;
  color: #000000;
  font-size: 0.8125rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  width: 100%;
}

.chat-text p {
  margin: 0 0 1rem 0;
  font-size: 0.8125rem !important;
  line-height: 1.5 !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.chat-text p:last-child {
  margin-bottom: 0;
}

/* Force smaller font size for all markdown elements */
.chat-text * {
  font-size: 0.8125rem !important;
  line-height: 1.5 !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.chat-text h1, .chat-text h2, .chat-text h3, .chat-text h4, .chat-text h5, .chat-text h6 {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  margin: 0.5rem 0 !important;
}

.chat-text ul, .chat-text ol {
  margin: 0.5rem 0 !important;
  padding-left: 1.5rem !important;
}

.chat-text li {
  margin: 0.25rem 0 !important;
  font-size: 0.8125rem !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Ensure tables don't overflow */
.chat-text table {
  max-width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.chat-text table tbody,
.chat-text table thead,
.chat-text table tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

/* Ensure long URLs and text break properly */
.chat-text a {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
}

.chat-text pre {
  display: flex;
  justify-content: center;
  overflow-x: auto;
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 0.375rem;
  padding: 1rem;
  margin: 1rem 0;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.8125rem;
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre;
}

.chat-text code {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 0.25rem;
  padding: 0.125rem 0.25rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.8125rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.chat-text pre code {
  background: none;
  border: none;
  padding: 0;
}

/* ChatGPT-style input */
.chat-input-container {
  max-width: 48rem;
  margin: 0 auto;
  position: relative;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 0.75rem;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.chat-input-container:focus-within {
  border-color: #9b87f5;
  box-shadow: 0 0 0 1px rgba(155, 135, 245, 0.1);
}

.chat-input-field {
  width: 100%;
  border: none;
  border-radius: 0.75rem;
  padding: 0.875rem 3rem 0.875rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #000000;
  background-color: transparent;
  resize: none;
  min-height: 2.75rem;
  max-height: 12rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-input-field:focus {
  outline: none;
}

.chat-input-field::placeholder {
  color: #9ca3af;
  font-size: 0.875rem;
}

.chat-send-button {
  position: absolute;
  right: 0.5rem;
  bottom: 0.5rem;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 0.25rem;
  background-color: #9b87f5;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

.chat-send-button:hover:not(:disabled) {
  background-color: #7c3aed;
}

.chat-send-button:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

/* Loading indicator - used inline within messages */

.chat-loading-dots {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.chat-loading-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #9ca3af;
  border-radius: 50%;
  animation: chat-loading-bounce 1.4s infinite ease-in-out;
}

.chat-loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.chat-loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes chat-loading-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Feedback Level Controls */
.chat-feedback-controls {
  padding: 0.3rem 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.chat-feedback-controls .relative {
  max-width: 48rem;
  margin: 0 auto;
}

/* Fix for TabsContent height issues */
[data-state="active"].h-full {
  display: flex;
  flex-direction: column;
  max-height: 100%;
  height: 100%;
  overflow: hidden;
}

/* Diagrams and SVGs in chat messages */
.chat-text .mermaid,
.chat-text svg {
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
  height: auto;
  overflow-x: auto;
}

/* For code blocks that contain diagrams (e.g., mermaid) */
.chat-text pre code {
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
  overflow-x: auto;
}
