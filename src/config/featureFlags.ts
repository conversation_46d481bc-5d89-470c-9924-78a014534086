// Feature flags configuration
// Set to false to disable features

export const FEATURE_FLAGS = {
  // Guided courses feature
  GUIDED_COURSES: false,

  // Free canvas feature (allows users to design without a specific question)
  FREE_CANVAS: false,

  // Future features can be added here
  // ADVANCED_ANALYTICS: true,
  // COLLABORATION: false,
} as const;

// Helper function to check if a feature is enabled
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature];
};

// Helper function to get all enabled features
export const getEnabledFeatures = (): string[] => {
  return Object.entries(FEATURE_FLAGS)
    .filter(([_, enabled]) => enabled)
    .map(([feature, _]) => feature);
};
