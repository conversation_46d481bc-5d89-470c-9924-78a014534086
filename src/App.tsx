
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from '@/components/ui/sonner';

// Context Providers
import { AuthProvider } from '@/contexts/AuthContext';
import { DialogProvider } from '@/contexts/DialogContext';
import { QuestionsProvider } from '@/contexts/QuestionsContext';
import { DesignProvider } from '@/contexts/DesignContext';
import { CourseProvider } from '@/contexts/CourseContext';
import { EdgeTypeProvider } from '@/contexts/EdgeTypeContext';

// Pages
import Index from '@/pages/Index';
import Questions from '@/pages/Questions';
import QuestionDetail from '@/pages/QuestionDetail';
import Register from '@/pages/Register';
import Login from '@/pages/Login';
import ForgotPassword from '@/pages/ForgotPassword';
import ResetPassword from '@/pages/ResetPassword';
import VerifyEmail from '@/pages/VerifyEmail';
import Profile from '@/pages/Profile';
import Dashboard from '@/pages/Dashboard';
import NotFound from '@/pages/NotFound';
import PrivacyPolicy from '@/pages/PrivacyPolicy';
import TermsOfService from '@/pages/TermsOfService';
import NewLandingPage from '@/pages/NewLandingPage';
import GuidedMode from '@/pages/GuidedMode';

// Admin Pages
import AnalyticsAdmin from '@/pages/AnalyticsAdmin';

import FeedbackAnalytics from '@/pages/admin/FeedbackAnalytics';

// Test Pages  
import DesignTest from '@/pages/DesignTest';
import AgentTest from '@/pages/AgentTest';

// Components
import DesignMigration from '@/components/DesignMigration';
import ProtectedRoute from '@/components/ProtectedRoute';
import AdminGuard from '@/components/AdminGuard';
import { useAuth } from '@/contexts/AuthContext';

// HomeRedirect component that redirects logged-in users to dashboard
const HomeRedirect: React.FC = () => {
  const { user, loading } = useAuth();

  // Show loading state while auth is being determined
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  // If user is logged in, redirect to dashboard
  if (user) {
    return <Navigate to="/dashboard" replace />;
  }

  // If user is not logged in, show the landing page
  return <NewLandingPage />;
};

function App() {
  return (
    <Router>
      <AuthProvider>
        <DialogProvider>
          <EdgeTypeProvider>
            <QuestionsProvider>
              <DesignProvider>
                <CourseProvider>
                  <DesignMigration />
                  <Toaster />
                  <Routes>
                    {/* <Route path="/" element={<LandingPage />} />
                    <Route path="/inspolandingpage" element={<CleanLandingPage />} /> */}
                    <Route path="/" element={<HomeRedirect />} />
                    {/* <Route path="/development" element={<Development />} /> */}
                    <Route path="/questions" element={<ProtectedRoute><Questions /></ProtectedRoute>} />
                    <Route path="/questions/:questionId" element={<ProtectedRoute><QuestionDetail /></ProtectedRoute>} />
                    <Route path="/design/:questionId" element={<ProtectedRoute><Index /></ProtectedRoute>} />
                    <Route path="/canvas" element={<ProtectedRoute><Index /></ProtectedRoute>} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/forgot-password" element={<ForgotPassword />} />
                    <Route path="/reset-password" element={<ResetPassword />} />
                    <Route path="/verify-email" element={<VerifyEmail />} />
                    <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
                    <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
                    <Route path="/guided/:courseId" element={<GuidedMode />} />
                    <Route path="/beta-invitation" element={<HomeRedirect />} />
                    <Route path="/beta-invite" element={<HomeRedirect />} />

                    {/* Legal Pages */}
                    <Route path="/privacy" element={<PrivacyPolicy />} />
                    <Route path="/terms" element={<TermsOfService />} />

                    {/* Admin Routes */}
                    <Route path="/admin/analytics" element={<AdminGuard><AnalyticsAdmin /></AdminGuard>} />

                    <Route path="/admin/feedback" element={<AdminGuard><FeedbackAnalytics /></AdminGuard>} />

                    {/* Test Routes */}
                    <Route path="/test/design" element={<DesignTest />} />
                    <Route path="/test/agent" element={<AgentTest />} />

                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </CourseProvider>
              </DesignProvider>
            </QuestionsProvider>
          </EdgeTypeProvider>
        </DialogProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
