import React, { useState } from 'react';
import { EdgeProps, MarkerType, getSmoothStepPath } from '@xyflow/react';

// Smooth edge component for smooth step connections with rounded corners
const SmoothEdge = ({
  id,
  source,
  target,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  data,
  markerEnd,
  label,
  labelStyle,
  labelBgStyle,
  labelBgPadding,
}: EdgeProps) => {
  const isInternal = data?.isInternal;
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const connectionNumber = label || '';

  // Generate smooth step path using React Flow's built-in function
  const [edgePath, labelX, labelY] = getSmoothStepPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
  });

  // Apply higher z-index for internal connections
  const edgeStyle = {
    ...style,
    zIndex: isInternal ? 1000 : 0,
    strokeWidth: isInternal ? 2.5 : 2,
  };

  // Handle label click to initiate editing
  const handleLabelClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  // Handle input blur or enter key press to save edit
  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    // Trim and limit the length of the label
    const newValue = e.target.value.trim().substring(0, 50); // Match dialog limit of 50 characters
    if (data?.onChange && typeof data.onChange === 'function') {
      data.onChange(id, newValue);
    }
    setIsEditing(false);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const target = e.target as HTMLInputElement;
      const newValue = target.value.trim().substring(0, 50); // Match dialog limit of 50 characters
      if (data?.onChange && typeof data.onChange === 'function') {
        data.onChange(id, newValue);
      }
      setIsEditing(false);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
    }
  };

  // Create label background style without stroke
  const cleanLabelBgStyle = {
    ...labelBgStyle,
    stroke: 'none',
    strokeWidth: 0
  };

  return (
    <>
      {/* Invisible wider path for easier clicking */}
      <path
        id={`${id}-clickable`}
        style={{
          ...edgeStyle,
          strokeWidth: 12, // Much wider for easier clicking
          stroke: 'transparent', // Invisible
          cursor: 'pointer'
        }}
        className="react-flow__edge-path smooth-edge"
        d={edgePath}
        fill="none"
      />
      {/* Visible edge path */}
      <path
        id={id}
        style={edgeStyle}
        className="react-flow__edge-path smooth-edge"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {/* Fix the label position and make sure it's clickable */}
      <g
        transform={`translate(${labelX - (labelBgPadding?.[0] || 0) / 2}, ${
          labelY - (labelBgPadding?.[1] || 0) / 2
        })`}
        className="react-flow__edge-label"
        style={{ pointerEvents: 'all', cursor: 'pointer' }}
        onClick={handleLabelClick}
      >
        {isEditing ? (
          <foreignObject
            width={60}
            height={30}
            x={-30}
            y={-15}
            className="edge-label-input-container"
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <div>
              <input
                className="edge-label-input"
                autoFocus
                defaultValue={connectionNumber as string}
                onBlur={handleInputBlur}
                onKeyDown={handleInputKeyDown}
                data-editing="true"
                onClick={(e) => e.stopPropagation()}
                maxLength={50}
              />
            </div>
          </foreignObject>
        ) : (
          <>
            <rect
              x={-(labelBgPadding?.[0] || 0) / 2}
              y={-(labelBgPadding?.[1] || 0) / 2}
              width={labelBgPadding?.[0] || 0}
              height={labelBgPadding?.[1] || 0}
              style={cleanLabelBgStyle}
              rx={4}
              ry={4}
              className="edge-label-bg"
              onClick={handleLabelClick}
            />
            <text
              style={labelStyle}
              dominantBaseline="middle"
              textAnchor="middle"
              className="edge-label-text"
              onClick={handleLabelClick}
            >
              {connectionNumber as string}
            </text>
          </>
        )}
      </g>
    </>
  );
};

export default SmoothEdge; 