import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Question } from '@/types/Question';
import { CheckCircle, Ban, Lightbulb, PenTool } from 'lucide-react';
import QuestionTabContent from './QuestionTabContent';

interface QuestionTabsProps {
  question: Question;
}

const QuestionTabs: React.FC<QuestionTabsProps> = ({ question }) => {
  return (
    <Tabs defaultValue="requirements" className="mb-6">
      <TabsList className="grid grid-cols-4">
        <TabsTrigger
          value="requirements"
          className="flex items-center gap-2 data-[state=active]:bg-green-500/80 data-[state=active]:text-white"
        >
          <CheckCircle className="h-4 w-4" />
          Requirements
        </TabsTrigger>
        <TabsTrigger
          value="constraints"
          className="flex items-center gap-2 data-[state=active]:bg-red-500/80 data-[state=active]:text-white"
        >
          <Ban className="h-4 w-4" />
          Constraints
        </TabsTrigger>
        <TabsTrigger
          value="hints"
          className="flex items-center gap-2 data-[state=active]:bg-yellow-500/80 data-[state=active]:text-white"
        >
          <Lightbulb className="h-4 w-4" />
          Hints
        </TabsTrigger>
        <TabsTrigger
          value="solution"
          className="flex items-center gap-2 data-[state=active]:bg-blue-500/80 data-[state=active]:text-white"
        >
          <PenTool className="h-4 w-4" />
          Sample Solution
        </TabsTrigger>
      </TabsList>

      <TabsContent value="requirements" className="mt-4">
        {((question.functional_requirements && question.functional_requirements.length > 0) ||
          (question.non_functional_requirements && question.non_functional_requirements.length > 0)) ? (
          <div className="space-y-6">
            {question.functional_requirements && question.functional_requirements.length > 0 && (
              <QuestionTabContent
                type={"requirements"}
                items={question.functional_requirements}
                customTitle="Functional Requirements"
              />
            )}
            {question.non_functional_requirements && question.non_functional_requirements.length > 0 && (
              <QuestionTabContent
                type={"requirements"}
                items={question.non_functional_requirements}
                customTitle="Non Functional Requirements"
              />
            )}
          </div>
        ) : (
          <QuestionTabContent type="requirements" items={question.requirements || []} />
        )}
      </TabsContent>

      <TabsContent value="constraints" className="mt-4">
        <QuestionTabContent type="context" items={question.constraints || []} />
      </TabsContent>

      <TabsContent value="hints" className="mt-4">
        <QuestionTabContent type="hints" items={question.hints || []} />
      </TabsContent>

      <TabsContent value="solution" className="mt-4">
        <QuestionTabContent type="solution" solution={question.sampleSolution || ''} />
      </TabsContent>
    </Tabs>
  );
};

export default QuestionTabs;
