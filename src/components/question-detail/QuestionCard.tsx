
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Question } from '@/types/Question';
import QuestionTags from './QuestionTags';

interface QuestionCardProps {
  question: Question;
  onStartDesign: () => void;
}

const QuestionCard: React.FC<QuestionCardProps> = ({ question, onStartDesign }) => {
  const navigate = useNavigate();

  return (
    <Card className="mb-6 bg-white border-gray-200">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-gray-900">{question.title}</CardTitle>
        <CardDescription className="text-base text-gray-700">{question.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <QuestionTags tags={question.tags} />
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => navigate('/questions')}
        >
          View All Questions
        </Button>
        <Button
          onClick={onStartDesign}
          className="bg-gradient-to-r from-purple-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
        >
          Start Designing
        </Button>
      </CardFooter>
    </Card>
  );
};

export default QuestionCard;
