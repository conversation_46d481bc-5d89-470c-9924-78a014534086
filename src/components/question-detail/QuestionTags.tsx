import React from 'react';
import { Badge } from '@/components/ui/badge';

interface QuestionTagsProps {
  tags: string[];
}

const QuestionTags: React.FC<QuestionTagsProps> = ({ tags }) => {
  if (!tags || tags.length === 0) return null;

  // Filter out 'System Design' tag
  const filteredTags = tags.filter(tag => tag.toLowerCase() !== 'system design'.toLowerCase());

  if (filteredTags.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      {filteredTags.map((tag) => (
        <Badge
          key={tag}
          variant="outline"
          className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100"
        >
          {tag}
        </Badge>
      ))}
    </div>
  );
};

export default QuestionTags;
