import React from 'react';
import { Badge } from '@/components/ui/badge';

interface QuestionTagsProps {
  tags: string[];
  companies?: string[];
}

const QuestionTags: React.FC<QuestionTagsProps> = ({ tags, companies = [] }) => {
  // Filter out 'System Design' tag
  const filteredTags = tags?.filter(tag => tag.toLowerCase() !== 'system design'.toLowerCase()) || [];

  const hasContent = filteredTags.length > 0 || companies.length > 0;
  if (!hasContent) return null;

  return (
    <div className="space-y-3 mb-4">
      {/* Question Tags */}
      {filteredTags.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Tags</h4>
          <div className="flex flex-wrap gap-2">
            {filteredTags.map((tag) => (
              <Badge
                key={tag}
                variant="outline"
                className="bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100"
              >
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Company Tags */}
      {companies.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Companies</h4>
          <div className="flex flex-wrap gap-2">
            {companies.map((company) => (
              <Badge
                key={company}
                variant="outline"
                className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
              >
                {company}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionTags;
