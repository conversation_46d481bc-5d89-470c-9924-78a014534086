import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { List, Ban, Lightbulb, PenTool } from 'lucide-react';

export interface QuestionTabContentProps {
  type: 'requirements' | 'context' | 'hints' | 'solution';
  items?: string[];
  solution?: string;
  customTitle?: string;
}

const QuestionTabContent: React.FC<QuestionTabContentProps> = ({ type, items = [], solution = '', customTitle }) => {
  if (!items || items.length === 0) return null;

  let title = '';
  if (customTitle) {
    title = customTitle;
  } else {
    switch (type) {
      case 'requirements':
        title = 'Requirements';
        break;
      case 'context':
        title = 'Constraints';
        break;
      case 'hints':
        title = 'Hints';
        break;
      case 'solution':
        title = 'Solution';
        break;
      default:
        title = '';
    }
  }

  const getTabInfo = () => {
    switch (type) {
      case 'requirements':
        return {
          icon: <List className="h-5 w-5 text-primary" />,
          description: 'What your design needs to accomplish',
          emptyMessage: 'No requirements provided for this question.'
        };
      case 'context':
        return {
          icon: <Ban className="h-5 w-5 text-red-500" />,
          description: 'hints and suggestions for your design',
          emptyMessage: 'No specific hints or suggestions provided for this question.'
        };
      case 'hints':
        return {
          icon: <Lightbulb className="h-5 w-5 text-yellow-500" />,
          description: 'Helpful tips to guide your design',
          emptyMessage: 'No hints provided for this question.'
        };
      case 'solution':
        return {
          icon: <PenTool className="h-5 w-5 text-blue-500" />,
          description: 'A reference implementation (try solving it yourself first!)',
          emptyMessage: 'No sample solution provided for this question.'
        };
      default:
        return {
          icon: null,
          description: '',
          emptyMessage: 'No data available.'
        };
    }
  };

  const { icon, description, emptyMessage } = getTabInfo();

  return (
    <Card className="bg-white border-gray-200">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2 text-gray-900 font-semibold">
          {icon}
          {title}
        </CardTitle>
        <CardDescription className="text-gray-700">{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {type === 'solution' ? (
          solution ? (
            <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
              <p className="text-base whitespace-pre-line text-gray-900">{solution}</p>
            </div>
          ) : (
            <p className="text-gray-600">{emptyMessage}</p>
          )
        ) : items && items.length > 0 ? (
          <ul className="list-disc pl-5 space-y-2">
            {items.map((item, index) => (
              <li key={index} className="text-base text-gray-900">{item}</li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-600">{emptyMessage}</p>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionTabContent;
