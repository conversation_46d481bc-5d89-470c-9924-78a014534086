
import React from 'react';
import Header from '@/components/Header';

interface LoadingStateProps {
  onStartAssessment: () => void;
}

const LoadingState: React.FC<LoadingStateProps> = ({ onStartAssessment }) => {
  return (
    <div className="flex flex-col min-h-screen">
      <Header onStartAssessment={onStartAssessment} />
      <div className="flex-1 p-6 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    </div>
  );
};

export default LoadingState;
