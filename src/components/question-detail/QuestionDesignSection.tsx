
import React from 'react';
import DesignManager from '@/components/DesignManager';
import { DesignContextCategory } from '@/contexts/DesignContext';

interface QuestionDesignSectionProps {
  questionId: string;
  contextType?: DesignContextCategory;
  contextId?: string;
}

const QuestionDesignSection: React.FC<QuestionDesignSectionProps> = ({
  questionId,
  contextType = 'question',
  contextId
}) => {
  return (
    <div className="mt-8">
      <h3 className="text-xl font-bold mb-4 text-gray-900">Your Design</h3>
      <DesignManager
        questionId={questionId}
        contextType={contextType}
        contextId={contextId || questionId}
      />
    </div>
  );
};

export default QuestionDesignSection;
