import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Question } from '@/types/Question';

interface QuestionHeaderProps {
  question: Question;
}

const QuestionHeader: React.FC<QuestionHeaderProps> = ({ question }) => {
  const navigate = useNavigate();

  const difficultyColors = {
    easy: 'bg-green-500/80 text-white border-green-400/50',
    medium: 'bg-yellow-500/80 text-white border-yellow-400/50',
    hard: 'bg-red-500/80 text-white border-red-400/50'
  };

  const categoryColors = {
    'system-design': 'bg-blue-500/80 text-white border-blue-400/50',
    'architecture': 'bg-purple-500/80 text-white border-purple-400/50',
    'scalability': 'bg-orange-500/80 text-white border-orange-400/50',
    'database': 'bg-cyan-500/80 text-white border-cyan-400/50',
    'frontend': 'bg-pink-500/80 text-white border-pink-400/50',
    'backend': 'bg-indigo-500/80 text-white border-indigo-400/50'
  };

  return (
    <div className="flex items-center mb-6">
      <Button
        variant="ghost"
        className="mr-4"
        onClick={() => navigate('/questions')}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Questions
      </Button>

      <div className="flex-1">
        <div className="flex items-center gap-2">
          <Badge className={`${difficultyColors[question.difficulty as keyof typeof difficultyColors]}`}>
            {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
          </Badge>
          <div className="text-sm text-gray-600 flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {question.updatedAt ? new Date(question.updatedAt).toLocaleDateString() : 'No date'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuestionHeader;
