import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { generalAnalytics, testAnalytics } from '@/services/analyticsService';
import { MessageSquare, BarChart3, Users, Clock, Trophy, Activity } from 'lucide-react';

interface PlatformStats {
  chats: {
    totalSessions: number;
    totalMessages: number;
    averageMessagesPerSession: number;
    averageSessionDuration: number;
  };
  assessments: {
    totalAssessments: number;
    averageScore: number;
    averageDuration: number;
    bestScoreCount: number;
  };
  sessions: {
    totalSessions: number;
    averagePageViews: number;
    averageActions: number;
  };
}

const AnalyticsDashboard: React.FC = () => {
  const [stats, setStats] = useState<PlatformStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<any>(null);

  useEffect(() => {
    loadStats();
    testConnection();
  }, []);

  const testConnection = async () => {
    try {
      const status = await testAnalytics.testConnection();
      setConnectionStatus(status);
    } catch (err) {
      console.error('Connection test failed:', err);
    }
  };

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const platformStats = await generalAnalytics.getPlatformStats();
      if (platformStats) {
        setStats(platformStats);
      } else {
        setError('No analytics data available or insufficient permissions. Please ensure you are logged in as an admin.');
      }
    } catch (err) {
      console.error('Analytics loading error:', err);
      setError('Error loading analytics: ' + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat().format(Math.round(num));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-2">Error loading analytics</p>
          <p className="text-sm text-gray-500">{error}</p>
          <button
            onClick={loadStats}
            className="mt-4 px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">No analytics data available</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Platform Analytics</h2>
          {connectionStatus && (
            <div className="flex items-center gap-2 mt-1">
              <span className="text-sm text-gray-600">Connection Status:</span>
              <Badge variant={connectionStatus.chatAnalytics ? "default" : "destructive"}>
                Chat: {connectionStatus.chatAnalytics ? "✓" : "✗"}
              </Badge>
              <Badge variant={connectionStatus.assessmentAnalytics ? "default" : "destructive"}>
                Assessment: {connectionStatus.assessmentAnalytics ? "✓" : "✗"}
              </Badge>
              <Badge variant={connectionStatus.sessionAnalytics ? "default" : "destructive"}>
                Session: {connectionStatus.sessionAnalytics ? "✓" : "✗"}
              </Badge>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          <button
            onClick={testConnection}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 text-sm"
          >
            Test Connection
          </button>
          <button
            onClick={loadStats}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 text-sm"
          >
            Refresh Data
          </button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="chats">Chat Analytics</TabsTrigger>
          <TabsTrigger value="assessments">Assessment Analytics</TabsTrigger>
          <TabsTrigger value="sessions">Session Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Chat Sessions</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats.chats.totalSessions)}</div>
                <p className="text-xs text-muted-foreground">
                  {formatNumber(stats.chats.totalMessages)} total messages
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Assessments</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats.assessments.totalAssessments)}</div>
                <p className="text-xs text-muted-foreground">
                  Avg score: {stats.assessments.averageScore.toFixed(1)}/10
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">User Sessions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats.sessions.totalSessions)}</div>
                <p className="text-xs text-muted-foreground">
                  Avg {stats.sessions.averagePageViews.toFixed(1)} page views
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Best Scores</CardTitle>
                <Trophy className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(stats.assessments.bestScoreCount)}</div>
                <p className="text-xs text-muted-foreground">
                  Personal best achievements
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="chats" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Chat Metrics
                </CardTitle>
                <CardDescription>Overall chat usage statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Sessions:</span>
                  <Badge variant="secondary">{formatNumber(stats.chats.totalSessions)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Messages:</span>
                  <Badge variant="secondary">{formatNumber(stats.chats.totalMessages)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Avg Messages/Session:</span>
                  <Badge variant="outline">{stats.chats.averageMessagesPerSession.toFixed(1)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Avg Session Duration:</span>
                  <Badge variant="outline">{formatDuration(stats.chats.averageSessionDuration)}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Engagement Insights
                </CardTitle>
                <CardDescription>Chat engagement patterns</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">
                    {stats.chats.averageMessagesPerSession.toFixed(1)}
                  </div>
                  <p className="text-sm text-gray-600">Average messages per conversation</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {formatDuration(stats.chats.averageSessionDuration)}
                  </div>
                  <p className="text-sm text-gray-600">Average conversation length</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="assessments" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Assessment Metrics
                </CardTitle>
                <CardDescription>Performance and completion statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Assessments:</span>
                  <Badge variant="secondary">{formatNumber(stats.assessments.totalAssessments)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Average Score:</span>
                  <Badge variant="outline">{stats.assessments.averageScore.toFixed(1)}/10</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Average Duration:</span>
                  <Badge variant="outline">{formatDuration(stats.assessments.averageDuration)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Best Scores:</span>
                  <Badge variant="secondary">{formatNumber(stats.assessments.bestScoreCount)}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Performance Insights
                </CardTitle>
                <CardDescription>Assessment quality metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">
                    {stats.assessments.averageScore.toFixed(1)}
                  </div>
                  <p className="text-sm text-gray-600">Average score out of 10</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-orange-600">
                    {((stats.assessments.bestScoreCount / stats.assessments.totalAssessments) * 100).toFixed(1)}%
                  </div>
                  <p className="text-sm text-gray-600">Personal best rate</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Session Metrics
                </CardTitle>
                <CardDescription>User session and engagement data</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Sessions:</span>
                  <Badge variant="secondary">{formatNumber(stats.sessions.totalSessions)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Avg Page Views:</span>
                  <Badge variant="outline">{stats.sessions.averagePageViews.toFixed(1)}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Avg Actions:</span>
                  <Badge variant="outline">{stats.sessions.averageActions.toFixed(1)}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Engagement Insights
                </CardTitle>
                <CardDescription>User interaction patterns</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-indigo-600">
                    {stats.sessions.averagePageViews.toFixed(1)}
                  </div>
                  <p className="text-sm text-gray-600">Average pages per session</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-pink-600">
                    {stats.sessions.averageActions.toFixed(1)}
                  </div>
                  <p className="text-sm text-gray-600">Average actions per session</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
