import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useQuestions } from '@/contexts/QuestionsContext';
import { Database, Upload, RefreshCw, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const QuestionMigrationPanel: React.FC = () => {
  const { checkQuestionsExist, refreshQuestions, getConnectionStatus, questions } = useQuestions();
  const [migrationStatus, setMigrationStatus] = useState<'idle' | 'migrating' | 'success' | 'error'>('idle');
  const [migrationMessage, setMigrationMessage] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<{
    connected: boolean;
    tableExists: boolean;
    recordCount: number;
  } | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check connection status on mount
  useEffect(() => {
    checkConnectionStatus();
  }, []);

  const checkConnectionStatus = async () => {
    try {
      const status = await getConnectionStatus();
      setConnectionStatus(status);
    } catch (error) {
      console.error('Error checking connection status:', error);
      setConnectionStatus({ connected: false, tableExists: false, recordCount: 0 });
    }
  };

  const handleCheckQuestions = async () => {
    setMigrationStatus('migrating');
    setMigrationMessage('Checking questions in database...');

    try {
      const result = await checkQuestionsExist();

      if (result.success) {
        setMigrationStatus('success');
        setMigrationMessage(`Found ${result.count} questions in database!`);

        // Refresh questions and connection status
        await refreshQuestions();
        await checkConnectionStatus();
      } else {
        setMigrationStatus('error');
        setMigrationMessage(result.error || 'Failed to check questions');
      }
    } catch (error) {
      setMigrationStatus('error');
      setMigrationMessage(error instanceof Error ? error.message : 'Unknown error occurred');
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshQuestions();
      await checkConnectionStatus();
    } catch (error) {
      console.error('Error refreshing:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = () => {
    switch (migrationStatus) {
      case 'migrating':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getStatusColor = () => {
    switch (migrationStatus) {
      case 'migrating':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'success':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'error':
        return 'bg-red-50 text-red-700 border-red-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Question Migration Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Connection Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg ${
                connectionStatus?.connected ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
              }`}>
                {connectionStatus?.connected ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <XCircle className="h-4 w-4" />
                )}
                <span className="text-sm font-medium">
                  {connectionStatus?.connected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Database</p>
            </div>

            <div className="text-center">
              <div className={`inline-flex items-center gap-2 px-3 py-2 rounded-lg ${
                connectionStatus?.tableExists ? 'bg-green-50 text-green-700' : 'bg-yellow-50 text-yellow-700'
              }`}>
                {connectionStatus?.tableExists ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <span className="text-sm font-medium">
                  {connectionStatus?.tableExists ? 'Exists' : 'Missing'}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Questions Table</p>
            </div>

            <div className="text-center">
              <div className="inline-flex items-center gap-2 px-3 py-2 rounded-lg bg-blue-50 text-blue-700">
                <Database className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {connectionStatus?.recordCount || 0}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">Records</p>
            </div>
          </div>

          {/* Migration Status */}
          {migrationStatus !== 'idle' && (
            <div className={`flex items-center gap-2 p-3 rounded-lg border ${getStatusColor()}`}>
              {getStatusIcon()}
              <span className="text-sm font-medium">{migrationMessage}</span>
            </div>
          )}

          {/* Current Questions Status */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Current Questions Status</h4>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                Loaded Questions: {questions.length}
              </span>
              <div className="flex gap-2">
                {connectionStatus?.recordCount && connectionStatus.recordCount > 0 && (
                  <Badge variant="outline" className="bg-green-50 text-green-700">Database Powered</Badge>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleCheckQuestions}
              disabled={migrationStatus === 'migrating' || !connectionStatus?.connected}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              {migrationStatus === 'migrating' ? 'Checking...' : 'Check Questions'}
            </Button>

            <Button
              variant="outline"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh Status
            </Button>

            <Button
              variant="outline"
              onClick={checkConnectionStatus}
              className="flex items-center gap-2"
            >
              <Database className="h-4 w-4" />
              Check Connection
            </Button>
          </div>

          {/* Instructions */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-medium text-green-900 mb-2">Database Status</h4>
            <ol className="text-sm text-green-800 space-y-1">
              <li>✅ Questions table created successfully</li>
              <li>✅ Sample questions populated in database</li>
              <li>✅ Application now loads questions from Supabase</li>
              <li>✅ Mock data fallback has been removed</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuestionMigrationPanel;
