
import React from 'react';
import CustomNode from './CustomNode';
import CompositeNode from './CompositeNode';

// Create a wrapper for CustomNode that passes setNodes and onAddProperties from node data to props
export const CustomNodeWrapper = (props: any) => {
  const { data, ...rest } = props;
  return <CustomNode {...rest} data={data} setNodes={data.setNodes} onAddProperties={data.onAddProperties} />;
};

// Create a wrapper for CompositeNode that passes setNodes and onAddProperties from node data to props
export const CompositeNodeWrapper = (props: any) => {
  const { data, ...rest } = props;
  return <CompositeNode {...rest} data={data} setNodes={data.setNodes} onAddProperties={data.onAddProperties} />;
};
