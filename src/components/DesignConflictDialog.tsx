import React from 'react';
import { SavedDesign } from '@/contexts/DesignContext';
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Monitor, Cloud, AlertTriangle } from 'lucide-react';

interface DesignConflictDialogProps {
  isOpen: boolean;
  onClose: () => void;
  localDesign: SavedDesign;
  serverDesign: SavedDesign;
  onChooseServer: () => void;
  onChooseLocal?: () => void;
  onMerge?: () => void;
}

export const DesignConflictDialog: React.FC<DesignConflictDialogProps> = ({
  isOpen,
  onClose,
  localDesign,
  serverDesign,
  onChooseLocal,
  onChooseServer,
  onMerge
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getDesignStats = (design: SavedDesign) => ({
    nodes: design.nodes?.length || 0,
    edges: design.edges?.length || 0,
    hasUserJourneys: !!(design.userJourneys?.trim()),
    hasAssumptions: !!(design.assumptions?.trim()),
    hasConstraints: !!(design.constraints?.trim())
  });

  const localStats = getDesignStats(localDesign);
  const serverStats = getDesignStats(serverDesign);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md w-full max-h-[80vh] overflow-y-auto mx-auto">
        <DialogHeader>
          <div className="flex flex-col items-center text-center gap-2">
            <DialogTitle className="flex items-center gap-2 justify-center">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              <span>Design Updated in Another Session</span>
            </DialogTitle>
            <p className="text-sm text-muted-foreground max-w-xs">
              This design was updated in another session or device.<br />You must use the latest version from the server to avoid conflicts.
            </p>
          </div>
        </DialogHeader>

        <div className="flex flex-col items-center justify-center gap-6">
          {/* Server Version */}
          <Card className="border-green-200 w-full max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-700 justify-center">
                <Cloud className="h-4 w-4" />
                Server Version (Other Session)
              </CardTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground justify-center">
                <Clock className="h-3 w-3" />
                {formatDate(serverDesign.lastModified)}
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-2 justify-center">
                <Badge variant="outline">
                  {serverStats.nodes} Components
                </Badge>
                <Badge variant="outline">
                  {serverStats.edges} Connections
                </Badge>
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>User Journeys</span>
                  <Badge variant={serverStats.hasUserJourneys ? "default" : "secondary"}>
                    {serverStats.hasUserJourneys ? "Present" : "Empty"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Assumptions</span>
                  <Badge variant={serverStats.hasAssumptions ? "default" : "secondary"}>
                    {serverStats.hasAssumptions ? "Present" : "Empty"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Constraints</span>
                  <Badge variant={serverStats.hasConstraints ? "default" : "secondary"}>
                    {serverStats.hasConstraints ? "Present" : "Empty"}
                  </Badge>
                </div>
              </div>
              {serverDesign.userJourneys && (
                <div className="text-xs">
                  <div className="font-medium mb-1">User Journeys Preview:</div>
                  <div className="bg-gray-50 p-2 rounded text-gray-600 max-h-20 overflow-y-auto">
                    {serverDesign.userJourneys.substring(0, 150)}
                    {serverDesign.userJourneys.length > 150 && '...'}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex flex-col items-center gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            variant="outline" 
            onClick={onChooseServer}
            className="border-green-200 text-green-700 hover:bg-green-50"
            autoFocus
          >
            Use Server Version
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
