import React, { useState } from 'react';
import { BaseEdge, EdgeLabelRenderer, getBezierPath, getSmoothStepPath, MarkerType } from '@xyflow/react';
import { EdgeProps } from '@xyflow/react';

interface UnifiedEdgeProps extends EdgeProps {
  pathType?: 'bezier' | 'smooth';
  visualStyle?: 'normal' | 'dashed' | 'dotted' | 'thick' | 'doubleArrow';
}

const UnifiedEdge: React.FC<UnifiedEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  data,
  pathType = 'smooth',
  visualStyle = 'normal',
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const connectionNumber = (data?.connectionNumber || data?.label || '') as string;
  
  // Generate path based on path type
  const [edgePath, labelX, labelY] = pathType === 'bezier' 
    ? getBezierPath({
        sourceX,
        sourceY,
        sourcePosition,
        targetX,
        targetY,
        targetPosition,
      })
    : getSmoothStepPath({
        sourceX,
        sourceY,
        targetX,
        targetY,
        sourcePosition,
        targetPosition,
      });

  // Apply visual style
  const getVisualStyle = () => {
    const baseStyle = {
      ...style,
      strokeWidth: visualStyle === 'thick' ? 4 : 2,
    };

    switch (visualStyle) {
      case 'dashed':
        return {
          ...baseStyle,
          strokeDasharray: '5,5',
          stroke: '#666',
        };
      case 'dotted':
        return {
          ...baseStyle,
          strokeDasharray: '4,6',
          stroke: '#666',
        };
      case 'thick':
        return {
          ...baseStyle,
          stroke: '#333',
        };
      case 'doubleArrow':
        return {
          ...baseStyle,
          stroke: '#333',
        };
      default:
        return baseStyle;
    }
  };

  const visualStyleConfig = getVisualStyle();

  // Handle label editing
  const handleLabelClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const newValue = e.target.value.trim().substring(0, 50);
    if (data?.onChange && typeof data.onChange === 'function') {
      data.onChange(id, newValue);
    }
    setIsEditing(false);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const target = e.target as HTMLInputElement;
      const newValue = target.value.trim().substring(0, 50);
      if (data?.onChange && typeof data.onChange === 'function') {
        data.onChange(id, newValue);
      }
      setIsEditing(false);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
    }
  };

  // Calculate label position with offset to ensure it's above the arrow
  const getLabelPosition = () => {
    // Calculate the direction of the edge
    const dx = targetX - sourceX;
    const dy = targetY - sourceY;
    
    // Determine if the edge is more horizontal or vertical
    const isHorizontal = Math.abs(dx) > Math.abs(dy);
    
    // Apply different offsets based on edge direction
    if (isHorizontal) {
      // For horizontal edges, offset vertically
      return { x: labelX, y: labelY - 15 };
    } else {
      // For vertical edges, offset horizontally
      return { x: labelX + (dx > 0 ? 15 : -15), y: labelY };
    }
  };

  const labelPosition = getLabelPosition();

  // Render double arrow effect
  if (visualStyle === 'doubleArrow') {
    // Single line with arrowheads at both ends
    return (
      <>
        <svg width="0" height="0" style={{ position: 'absolute' }}>
          <defs>
            {/* Start marker: flipped horizontally */}
            <marker
              id="double-arrow-start"
              markerWidth="10"
              markerHeight="10"
              refX="2"
              refY="5"
              orient="auto"
              markerUnits="strokeWidth"
            >
              <path d="M 10 0 L 0 5 L 10 10 z" fill="#222" />
            </marker>
            {/* End marker: normal direction */}
            <marker
              id="double-arrow-end"
              markerWidth="10"
              markerHeight="10"
              refX="8"
              refY="5"
              orient="auto"
              markerUnits="strokeWidth"
            >
              <path d="M 0 0 L 10 5 L 0 10 z" fill="#222" />
            </marker>
          </defs>
        </svg>
        <BaseEdge
          path={edgePath}
          markerStart="url(#double-arrow-start)"
          markerEnd="url(#double-arrow-end)"
          style={visualStyleConfig}
        />
        <EdgeLabelRenderer>
          <div
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
              fontSize: 12,
              fontWeight: 700,
              pointerEvents: 'all',
            }}
            className="nodrag nopan"
          >
            {connectionNumber && (
              <div
                style={{
                  background: 'white',
                  padding: '4px 8px',
                  borderRadius: 4,
                  border: '1px solid #ccc',
                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                  whiteSpace: 'nowrap',
                }}
              >
                {connectionNumber}
              </div>
            )}
          </div>
        </EdgeLabelRenderer>
      </>
    );
  }

  // Render single edge with label
  return (
    <>
      <BaseEdge
        path={edgePath}
        markerEnd={markerEnd}
        style={visualStyleConfig}
      />
      <EdgeLabelRenderer>
        <div
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelPosition.x}px,${labelPosition.y}px)`,
            fontSize: 12,
            fontWeight: 700,
            pointerEvents: 'all',
            zIndex: 1000,
          }}
          className="nodrag nopan"
        >
          {connectionNumber && (
            <div
              style={{
                background: 'white',
                padding: '4px 8px',
                borderRadius: 4,
                border: '1px solid #ccc',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
                whiteSpace: 'nowrap',
                zIndex: 1000,
                position: 'relative',
              }}
              onClick={handleLabelClick}
            >
              {isEditing ? (
                <input
                  autoFocus
                  defaultValue={connectionNumber as string}
                  onBlur={handleInputBlur}
                  onKeyDown={handleInputKeyDown}
                  onClick={(e) => e.stopPropagation()}
                  maxLength={50}
                  style={{
                    border: 'none',
                    outline: 'none',
                    background: 'transparent',
                    fontSize: 'inherit',
                    fontWeight: 'inherit',
                  }}
                />
              ) : (
                connectionNumber
              )}
            </div>
          )}
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default UnifiedEdge; 