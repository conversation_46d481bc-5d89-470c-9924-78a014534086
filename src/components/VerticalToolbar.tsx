import React, { useEffect, useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Download, BarChart3, Trophy, HelpCircle, Play } from 'lucide-react';
import { ReactFlowInstance } from '@xyflow/react';
import { AssessmentResult } from '@/services/assessmentService';
import { exportToPNGImproved } from '@/utils/exportPNGImproved';
import { toast } from 'sonner';
import ResetDesignButton from './ResetDesignButton';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface VerticalToolbarProps {
  onStartAssessment: () => void;
  isAssessing?: boolean;
  reactFlowInstance?: ReactFlowInstance | null;
  assessmentResult?: AssessmentResult | null;
  onViewAssessment?: () => void;
  onLoadBestDesign?: () => void;
  onShowBestSubmission?: () => void; // New prop for showing best submission overlay
  hasBestDesign?: boolean;
  onShowTutorial?: () => void;
  onResetUserInputs?: () => void;
  isQuestionMode?: boolean; // New prop to control visibility of best design button
  showRightPanel?: boolean; // New prop to track right panel state
  rightPanelWidth?: number; // New prop for right panel width percentage
  rightPanelCollapsed?: boolean; // New prop to track if right panel is collapsed
  rightPanelRef?: React.RefObject<HTMLDivElement>; // New prop for right panel ref
  autoSave?: { triggerFullAutoSave: () => void };
}

const VerticalToolbar: React.FC<VerticalToolbarProps> = ({
  onStartAssessment,
  isAssessing = false,
  reactFlowInstance,
  assessmentResult,
  onViewAssessment,
  onLoadBestDesign,
  onShowBestSubmission,
  hasBestDesign = false,
  onShowTutorial,
  onResetUserInputs,
  isQuestionMode = false,
  showRightPanel = false,
  rightPanelWidth = 30,
  rightPanelCollapsed = false,
  rightPanelRef,
  autoSave
}) => {
  // State to track the actual right panel width in pixels
  const [actualRightPanelWidth, setActualRightPanelWidth] = useState<number>(0);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Effect to observe right panel width changes
  useEffect(() => {
    if (!rightPanelRef?.current || !showRightPanel || rightPanelCollapsed) {
      setActualRightPanelWidth(0);
      return;
    }

    // Create ResizeObserver to track right panel width changes
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        setActualRightPanelWidth(width);
      }
    });

    // Start observing the right panel
    resizeObserverRef.current.observe(rightPanelRef.current);

    // Set initial width
    setActualRightPanelWidth(rightPanelRef.current.offsetWidth);

    // Cleanup
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [rightPanelRef, showRightPanel, rightPanelCollapsed]);
  const handleExportPNG = async () => {
    if (!reactFlowInstance) {
      toast.error('Canvas not ready for export');
      return;
    }

    try {
      await exportToPNGImproved(reactFlowInstance);
      toast.success('Design exported as PNG!');
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export design');
    }
  };

  // Calculate the right position based on actual right panel width
  const getRightPosition = () => {
    if (showRightPanel && !rightPanelCollapsed && actualRightPanelWidth > 0) {
      // When right panel is open, position toolbar to the left of the panel
      // Use the actual measured width in pixels plus padding for the collapse button
      return `${actualRightPanelWidth + 80}px`; // 80px = space for collapse button + padding
    }
    // When panel is closed or collapsed, leave space for the expand button
    // Similar to left panel logic: button width + margin + padding = ~72px
    return '72px'; // Leave space for the expand button
  };

  return (
    <div
      className="fixed top-1/2 transform -translate-y-1/2 z-30 flex flex-col gap-2 bg-gray-50 rounded-lg shadow-lg border border-gray-200 p-2 group hover:pr-4 transition-all duration-300"
      style={{
        right: getRightPosition(),
        transition: 'right 0.3s ease-in-out, all 0.2s ease-in-out'
      }}
    >
      {/* Start Assessment Button */}
      <Button
        variant="lightCTA"
        size="sm"
        onClick={() => {
          debugLog("Assessment button clicked, isAssessing:", isAssessing);
          if (!isAssessing && onStartAssessment) {
            onStartAssessment();
          } else {
            debugLog("Button click ignored - already assessing or no handler");
          }
        }}
        disabled={isAssessing}
        className="relative z-20 assessment-button w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-purple-500/80 to-indigo-600/80 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm disabled:opacity-50 hover:w-44 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-44"
      >
        <div className="w-6 flex items-center justify-center flex-shrink-0">
          {isAssessing ? (
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <Play size={14} />
          )}
        </div>
        <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">
          {isAssessing ? "..." : "Run Assessment"}
        </span>
      </Button>

      {/* Assessment Results Button */}
      {assessmentResult && onViewAssessment && (
        <Button
          variant="lightCTA"
          size="sm"
          onClick={onViewAssessment}
          className="w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white border-none backdrop-blur-sm hover:w-44 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-44"
        >
          <div className="w-6 flex items-center justify-center flex-shrink-0">
            <BarChart3 size={14} />
          </div>
          <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">View Assessment</span>
        </Button>
      )}

      {/* Load Best Design Button - Only show when in question mode */}
      {hasBestDesign && onShowBestSubmission && isQuestionMode && (
        <Button
          variant="lightCTA"
          size="sm"
          onClick={onShowBestSubmission}
          className="w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-yellow-500/80 to-orange-600/80 hover:from-yellow-600/90 hover:to-orange-700/90 text-white border-none backdrop-blur-sm hover:w-44 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-44"
        >
          <div className="w-6 flex items-center justify-center flex-shrink-0">
            <Trophy size={14} />
          </div>
          <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">Best Submission</span>
        </Button>
      )}

      {/* Export PNG Button */}
      <Button
        variant="lightCTA"
        size="sm"
        onClick={handleExportPNG}
        className="export-button w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm hover:w-36 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-36"
      >
        <div className="w-6 flex items-center justify-center flex-shrink-0">
          <Download size={14} />
        </div>
        <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">Export PNG</span>
      </Button>

      {/* Help Tutorial Button */}
      {onShowTutorial && (
        <Button
          variant="lightCTA"
          size="sm"
          onClick={onShowTutorial}
          className="w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-gray-500/80 to-gray-600/80 hover:from-gray-600/90 hover:to-gray-700/90 text-white border-none backdrop-blur-sm hover:w-44 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-44"
        >
          <div className="w-6 flex items-center justify-center flex-shrink-0">
            <HelpCircle size={14} />
          </div>
          <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">Help</span>
        </Button>
      )}

      {/* Reset Design Button */}
      <div className="w-10 hover:w-44 transition-all duration-200 group-hover:w-44 overflow-hidden">
        <ResetDesignButton
          reactFlowInstance={reactFlowInstance}
          onResetUserInputs={onResetUserInputs}
          variant="vertical-toolbar"
          className="w-full h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-red-500/80 to-red-600/80 hover:from-red-600/90 hover:to-red-700/90 text-white border-none backdrop-blur-sm transition-all duration-200 overflow-hidden whitespace-nowrap"
          onFullAutoSave={typeof autoSave !== 'undefined' ? autoSave.triggerFullAutoSave : undefined}
        />
      </div>
    </div>
  );
};

export default VerticalToolbar;
