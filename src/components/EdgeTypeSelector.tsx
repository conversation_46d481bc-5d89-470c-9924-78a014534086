import React from 'react';
import { useEdgeType } from '@/contexts/EdgeTypeContext';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { 
  Zap,
  Minus,
  ArrowRightLeft,
  Square,
  Circle,
  ArrowUpDown,
  GripVertical,
  Settings
} from 'lucide-react';

const EdgeTypeSelector: React.FC = () => {
  const { 
    selectedPathType, 
    selectedVisualStyle, 
    setSelectedPathType, 
    setSelectedVisualStyle 
  } = useEdgeType();

  const pathTypeOptions = [
    {
      value: 'bezier' as const,
      label: 'Bezier',
      description: 'Smooth curved connections',
      icon: <Zap size={14} className="text-blue-500" />
    },
    {
      value: 'smooth' as const,
      label: 'Smooth Step',
      description: 'Stepped connections',
      icon: <Minus size={14} className="text-green-500" />
    }
  ];

  const visualStyleOptions = [
    {
      value: 'normal' as const,
      label: 'Normal',
      description: 'Standard line style',
      icon: <Minus size={14} className="text-gray-500" />
    },
    {
      value: 'dashed' as const,
      label: 'Dashed',
      description: 'Dashed line connections for emphasis',
      icon: <Square size={14} className="text-orange-500" />
    },
    {
      value: 'thick' as const,
      label: 'Thick',
      description: 'Bold connections for strong relationships',
      icon: <GripVertical size={14} className="text-red-500" />
    },
    {
      value: 'doubleArrow' as const,
      label: 'Double Arrow',
      description: 'Double arrow connections',
      icon: <ArrowUpDown size={14} className="text-indigo-500" />
    }
  ];

  return (
    <div className="edge-type-selector bg-white/90 backdrop-blur-sm rounded-lg shadow-lg border border-gray-200 p-3 transition-all duration-200 hover:scale-105 hover:shadow-xl">
      <div className="flex flex-col gap-2">
        {/* Path Type Selector */}
        <div className="flex items-center gap-2">
          <Settings size={12} className="text-gray-400" />
          <Select value={selectedPathType} onValueChange={setSelectedPathType}>
            <SelectTrigger className="w-80 h-8 bg-transparent border-0 shadow-none hover:bg-gray-50/50 transition-colors text-xs flex justify-center items-center text-center focus:outline-none focus:ring-0 focus:border-0">
              <SelectValue placeholder="Path" />
            </SelectTrigger>
            <SelectContent className="w-80">
              {pathTypeOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="focus:bg-transparent focus:text-gray-900 focus:outline-none focus:ring-0 data-[state=checked]:bg-transparent data-[state=checked]:text-gray-900 data-[highlighted]:bg-gray-50 data-[highlighted]:text-gray-900"
                >
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex-shrink-0">
                      {option.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 text-sm">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {option.description}
                      </div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Visual Style Selector */}
        <div className="flex items-center gap-2">
          <ArrowRightLeft size={12} className="text-gray-400" />
          <Select value={selectedVisualStyle} onValueChange={setSelectedVisualStyle}>
            <SelectTrigger className="w-80 h-8 bg-transparent border-0 shadow-none hover:bg-gray-50/50 transition-colors text-xs flex justify-center items-center text-center focus:outline-none focus:ring-0 focus:border-0">
              <SelectValue placeholder="Style" />
            </SelectTrigger>
            <SelectContent className="w-80">
              {visualStyleOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  value={option.value}
                  className="focus:bg-transparent focus:text-gray-900 focus:outline-none focus:ring-0 data-[state=checked]:bg-transparent data-[state=checked]:text-gray-900 data-[highlighted]:bg-gray-50 data-[highlighted]:text-gray-900"
                >
                  <div className="flex items-center gap-3 p-2 rounded-md hover:bg-gray-50 transition-colors cursor-pointer">
                    <div className="flex-shrink-0">
                      {option.icon}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 text-sm">
                        {option.label}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {option.description}
                      </div>
                    </div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default EdgeTypeSelector; 