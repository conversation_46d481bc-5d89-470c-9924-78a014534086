import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useDesign } from '@/contexts/DesignContext';
import { useQuestions } from '@/contexts/QuestionsContext';
import { Button } from '@/components/ui/button';
import {
  FileQuestion,
  ArrowLeft,
  Home
} from 'lucide-react';

const DesignStatus: React.FC = () => {
  const navigate = useNavigate();
  const { questionId } = useParams<{ questionId: string }>();
  const { currentDesign } = useDesign();
  const { currentQuestion } = useQuestions(); // Remove getQuestionById since we shouldn't call it synchronously

  // Determine if we're in free canvas mode
  const isFreeCanvas = !questionId;

  // If we're in free canvas mode, show free canvas status
  if (isFreeCanvas) {
    return (
      <div className="flex items-center justify-between px-3 py-1 bg-gradient-to-r from-[#4c1d95] to-[#5b21b6] border-b border-purple-400/30">
        <div className="flex items-center gap-2">
          <Home className="h-4 w-4 text-[#C4B5FD]" />
          <span className="text-sm font-medium text-white">Free Canvas</span>
        </div>
        <Button
          variant="lightCTA"
          size="sm"
          className="text-xs h-6 px-2 py-0.5 bg-gradient-to-r from-indigo-500/70 to-purple-600/70 hover:from-indigo-500/90 hover:to-purple-600/90 text-white border-none backdrop-blur-sm"
          onClick={() => navigate('/questions')}
        >
          Browse Questions
        </Button>
      </div>
    );
  }

  // For question mode, use current question (should be set by the page that loads the question)
  const question = currentQuestion;

  // If no question in question mode, show message
  if (!question) {
    return (
      <div className="flex items-center justify-between px-2 py-1 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center gap-2">
          <FileQuestion className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-500">No question selected</span>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="text-xs"
          onClick={() => navigate('/questions')}
        >
          Browse Questions
        </Button>
      </div>
    );
  }

  // Show question status
  return (
    <div className="flex items-center justify-between px-3 py-1 bg-gradient-to-r from-[#4c1d95] via-[#5b21b6] to-[#4c1d95] border-b border-purple-400/30">
      <div className="flex items-center gap-2">
        <FileQuestion className="h-4 w-4 text-[#C4B5FD]" />
        <span className="text-sm font-medium truncate max-w-[200px] text-white" title={question.title}>
          {question.title}
        </span>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="lightCTA"
          size="sm"
          className="text-xs gap-1 h-6 px-2 py-0.5 bg-gradient-to-r from-purple-500/70 to-indigo-500/70 hover:from-purple-500/90 hover:to-indigo-500/90 text-white border-none backdrop-blur-sm"
          onClick={() => navigate(`/questions/${question.id}`)}
        >
          <ArrowLeft className="h-2.5 w-2.5" />
          Back to Question
        </Button>
      </div>
    </div>
  );
};

export default DesignStatus;
