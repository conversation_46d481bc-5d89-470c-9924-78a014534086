import React from 'react';

const Hero: React.FC = () => {
  return (
    <section className="relative overflow-hidden py-20 md:py-32">
      {/* Background gradient effects */}
      <div className="absolute inset-0 bg-hero-glow opacity-80 z-0"></div>

      {/* Main background blobs */}
      <div className="absolute top-0 inset-0 bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b] z-[-1]"></div>

      {/* Top left blob */}
      <div className="absolute -top-96 -left-96 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-[120px]"></div>

      {/* Bottom right blob */}
      <div className="absolute -bottom-96 -right-96 w-[800px] h-[800px] rounded-full bg-indigo-500/10 blur-[120px]"></div>

      {/* Center glow effect */}
      <div className="absolute top-0 left-0 w-full h-full z-0">
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[90%] max-w-[1200px] h-[500px] rounded-full bg-gradient-to-r from-indigo-600/20 via-purple-600/20 to-blue-600/20 blur-[80px] opacity-70"></div>
      </div>

      <div className="container max-w-6xl mx-auto px-4 relative z-10">
        <div className="flex flex-col items-center text-center">
          {/* Logo with glow effect */}
          <div className="mb-8 animate-float relative group">
            {/* Outer glow effect */}
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-xl blur-md opacity-70 group-hover:opacity-90 transition duration-500"></div>
            {/* Inner glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-blue-500 via-purple-400 to-indigo-500 rounded-xl blur-sm opacity-70 group-hover:opacity-90 transition duration-500"></div>
            {/* Logo container */}
            <div className="relative w-28 h-24 bg-gradient-to-br from-[#2e1065] to-[#1e1b4b] rounded-xl flex items-center justify-center shadow-lg border border-white/10">
              <span className="text-4xl font-bold" style={{
                background: 'linear-gradient(90deg, #4F9BFF 0%, #A78BFA 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>Layrs</span>
            </div>
          </div>

          {/* Heading - Updated typography */}
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 text-[#EDE9FE]">
            <span className="gradient-text">Layrs</span> is coming soon
          </h1>

          {/* Subheading - Updated typography */}
          <p className="text-xl md:text-2xl text-[#CBD5E1] max-w-3xl mb-8">
            All things system design.
          </p>

          {/* Status Badge - Yellow to indicate in development */}
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-yellow-500/10 text-yellow-300 text-sm font-medium mb-10 backdrop-blur-sm border border-yellow-500/20">
            <span className="w-2 h-2 bg-yellow-300 rounded-full mr-2 animate-pulse"></span>
            Launching Summer 2025
          </div>
        </div>
      </div>

      {/* Parabolic arc gradient transition */}
      <div className="absolute bottom-0 left-0 right-0 z-10">
        <div className="w-full h-36 bg-gradient-to-b from-transparent to-[#0f0a29]" style={{
          maskImage: 'radial-gradient(ellipse at top, transparent 40%, black 70%)',
          WebkitMaskImage: 'radial-gradient(ellipse at top, transparent 40%, black 70%)'
        }}></div>
      </div>
    </section>
  );
};

export default Hero;
