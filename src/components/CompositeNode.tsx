
import React, { memo, useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Position, NodeResizer } from '@xyflow/react';
import { Edit, Box, Settings } from 'lucide-react';
import type { Node } from '@xyflow/react';

export interface CompositeNodeData {
  type: 'composite';
  label: string;
  className?: string;
  components?: Node[];
  isDropzoneActive?: boolean;
  isDraggingChild?: boolean;
  parentCompositeId?: string;
  metadata?: {
    customLogic?: string;
    customProperties?: Array<{ key: string; value: string }>;
    [key: string]: any;
  };
}

interface CompositeNodeProps {
  id: string;
  data: CompositeNodeData;
  selected: boolean;
  isConnectable?: boolean;
  setNodes?: React.Dispatch<React.SetStateAction<any[]>>;
  onAddProperties?: (nodeId: string) => void; // Callback for add properties button
}

const CompositeNode: React.FC<CompositeNodeProps> = memo(({ id, data, selected, isConnectable, setNodes, onAddProperties }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [labelText, setLabelText] = useState(data.label);
  const inputRef = useRef<HTMLInputElement>(null);

  // Determine if this node is part of a composite
  const isCompositeChild = !!data.parentCompositeId;

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleLabelClick = () => {
    setIsEditing(true);

    // Deselect the node when entering edit mode to prevent accidental deletion
    if (setNodes) {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === id
            ? { ...node, selected: false }
            : node
        )
      );
    }
  };

  const handleLabelChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLabelText(e.target.value);
  };

  const handleLabelBlur = () => {
    // Validate and sanitize the label
    let finalLabel = labelText.trim();

    // Use default if empty
    if (!finalLabel) {
      finalLabel = 'Unnamed Composite';
    }

    if (setNodes) {
      setNodes((nds) =>
        nds.map((node) =>
          node.id === id
            ? {
                ...node,
                data: {
                  ...node.data,
                  label: finalLabel
                }
              }
            : node
        )
      );
    }
    setIsEditing(false);
  };

  // Handle Enter key to confirm edit or Escape to cancel
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      inputRef.current?.blur();
    } else if (e.key === 'Escape') {
      // Reset to original label and exit edit mode
      setLabelText(data.label);
      setIsEditing(false);
      e.preventDefault();
      e.stopPropagation();
    }
  };

  // Only show dropzone message when a new component is being dragged in,
  // not when a child component is being rearranged
  const showDropzoneMessage = data.isDropzoneActive && !data.isDraggingChild;

  return (
    <div
      className={`composite-node group px-4 py-2 shadow-md rounded-md border-2 ${data.className} ${
        selected ? 'border-2 border-layrs-primary' : 'border border-gray-500'
      } ${data.isDropzoneActive ? 'bg-green-50 border-green-300' : ''}`}
      style={{
        position: 'relative',
        overflow: 'visible', // Allow children to overflow
        width: '100%',
        height: '100%',
        // Make the background semi-transparent to see connections through it
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
      }}
    >
      {/* Make the header and content have pointer events */}
      <div style={{ pointerEvents: 'auto' }}>
        <NodeResizer
           minWidth={150}
           minHeight={100}
           isVisible={selected}
           lineClassName="border-layrs-primary"
           handleClassName="h-3 w-3 bg-white border-2 border-layrs-primary rounded"
           handleStyle={{ zIndex: 1000 }}
         />

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {isEditing ? (
              <input
                ref={inputRef}
                type="text"
                value={labelText}
                onChange={handleLabelChange}
                onBlur={handleLabelBlur}
                onKeyDown={handleKeyDown}
                className="text-sm font-medium bg-white border border-gray-300 px-1 py-0.5 rounded w-full"
                onClick={(e) => e.stopPropagation()}
                data-editing="true"
              />
            ) : (
              <div
                className="text-sm font-medium flex items-center gap-1 cursor-text"
                onClick={(e) => {
                  e.stopPropagation();
                  handleLabelClick();
                }}
              >
                <Box size={14} className="text-gray-700" />
                <span>{data.label}</span>
                {selected && <Edit size={12} className="text-gray-500 hover:text-gray-700" />}
              </div>
            )}
          </div>

          {/* Add Properties Button */}
          {onAddProperties && !isEditing && (
            <button
              data-properties-button="true"
              className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded hover:bg-gray-100 flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800"
              style={{ pointerEvents: 'auto' }}
              onClick={(e) => {
                e.stopPropagation(); // Prevent node selection
                onAddProperties(id);
              }}
              title="Add Properties"
            >
              <Settings size={12} />
              <span>Properties</span>
            </button>
          )}
        </div>

        <div className="mt-2 text-xs text-gray-500">
          Composite Container
        </div>

        {isCompositeChild && (
          <div className="absolute top-0 right-0 bg-gray-200 text-xs px-1 rounded-bl">
            Internal
          </div>
        )}

        {showDropzoneMessage && (
          <div className="mt-2 p-2 text-center text-xs text-green-700 bg-green-50 border border-green-200 rounded">
            Drop component here
          </div>
        )}
      </div>

      {/* Handles that act as both source and target */}
      {/* Top handle - dual type */}
      <Handle
        type="source"
        position={Position.Top}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="top"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3"
        id="top"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Bottom handle - dual type */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="bottom"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Bottom}
        className="w-3 h-3"
        id="bottom"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Right handle - dual type */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="right"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Right}
        className="w-3 h-3"
        id="right"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Left handle - dual type */}
      <Handle
        type="source"
        position={Position.Left}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="left"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3"
        id="left"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />
    </div>
  );
});

export default CompositeNode;
