
import React, { useEffect, useState } from 'react';
import { Database, Server, Globe, Zap, ArrowRight, Users } from 'lucide-react';

interface AnimatedNode {
  id: string;
  icon: React.ReactNode;
  label: string;
  x: number;
  y: number;
  delay: number;
}

const AnimatedSystemPreview: React.FC = () => {
  const [activeConnections, setActiveConnections] = useState<string[]>([]);
  const [visibleNodes, setVisibleNodes] = useState<string[]>([]);

  const nodes: AnimatedNode[] = [
    { id: 'client', icon: <Users className="h-5 w-5" />, label: 'Users', x: 10, y: 50, delay: 0 },
    { id: 'lb', icon: <Zap className="h-4 w-4" />, label: 'Load Balancer', x: 35, y: 30, delay: 500 },
    { id: 'server', icon: <Server className="h-5 w-5" />, label: 'App Server', x: 60, y: 20, delay: 1000 },
    { id: 'db', icon: <Database className="h-5 w-5" />, label: 'Database', x: 85, y: 40, delay: 1500 },
    { id: 'cache', icon: <Globe className="h-4 w-4" />, label: 'Cache', x: 60, y: 70, delay: 2000 }
  ];

  const connections = [
    { from: 'client', to: 'lb', path: 'M 10 50 L 35 30' },
    { from: 'lb', to: 'server', path: 'M 35 30 L 60 20' },
    { from: 'server', to: 'db', path: 'M 60 20 L 85 40' },
    { from: 'server', to: 'cache', path: 'M 60 20 L 60 70' }
  ];

  useEffect(() => {
    // Animate nodes appearing
    nodes.forEach((node) => {
      setTimeout(() => {
        setVisibleNodes(prev => [...prev, node.id]);
      }, node.delay);
    });

    // Animate connections after all nodes are visible
    const connectionTimer = setTimeout(() => {
      let currentConnection = 0;
      
      const addConnection = () => {
        if (currentConnection < connections.length) {
          const conn = connections[currentConnection];
          const key = `${conn.from}-${conn.to}`;
          
          setActiveConnections(prev => [...prev, key]);
          currentConnection++;
          
          // Add next connection after a delay
          setTimeout(addConnection, 800);
        } else {
          // Reset and repeat the animation
          setTimeout(() => {
            setActiveConnections([]);
            currentConnection = 0;
            setTimeout(addConnection, 1000);
          }, 3000);
        }
      };
      
      addConnection();
    }, 2500);

    return () => clearTimeout(connectionTimer);
  }, []);

  return (
    <div className="relative w-full h-64 overflow-hidden">
      <svg className="absolute inset-0 w-full h-full" viewBox="0 0 100 100">
        {/* Connection paths */}
        {connections.map((conn) => {
          const key = `${conn.from}-${conn.to}`;
          const isActive = activeConnections.includes(key);
          
          return (
            <g key={key}>
              {/* Base line */}
              <path
                d={conn.path}
                stroke={isActive ? "#8b5cf6" : "#e5e7eb"}
                strokeWidth="2"
                fill="none"
                style={{
                  transition: 'stroke 0.5s ease',
                }}
              />
              
              {/* Animated data flow */}
              {isActive && (
                <>
                  <path
                    d={conn.path}
                    stroke="#8b5cf6"
                    strokeWidth="2"
                    fill="none"
                    strokeDasharray="6,4"
                    className="animate-pulse"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(139, 92, 246, 0.5))'
                    }}
                  />
                  
                  {/* Moving data packet */}
                  <circle
                    r="2"
                    fill="#8b5cf6"
                    className="opacity-80"
                  >
                    <animateMotion
                      dur="2s"
                      repeatCount="indefinite"
                      path={conn.path}
                    />
                  </circle>
                </>
              )}
            </g>
          );
        })}
      </svg>

      {/* Nodes */}
      {nodes.map((node) => {
        const isVisible = visibleNodes.includes(node.id);
        
        return (
          <div
            key={node.id}
            className={`absolute transition-all duration-500 ${
              isVisible 
                ? 'opacity-100 scale-100 translate-y-0' 
                : 'opacity-0 scale-75 translate-y-4'
            }`}
            style={{
              left: `${node.x}%`,
              top: `${node.y}%`,
              transform: `translate(-50%, -50%) ${isVisible ? 'scale(1)' : 'scale(0.75) translateY(16px)'}`
            }}
          >
            <div className="flex flex-col items-center gap-1">
              <div className="w-10 h-10 rounded-lg bg-white/90 backdrop-blur-sm border border-purple-200 shadow-lg flex items-center justify-center text-purple-600 hover:scale-110 transition-transform">
                {node.icon}
              </div>
              <span className="text-xs font-medium text-gray-700 whitespace-nowrap">
                {node.label}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default AnimatedSystemPreview;
