import React from 'react';

const Manifesto: React.FC = () => {
  // Reordered teaser elements as requested
  const elements = [
    {
      title: "Design Simulators",
      description: "Step into live problem spaces. Architect, refine, and grow."
    },
    {
      title: "Precision Feedback",
      description: "Not \"good job\": but surgical feedback at every decision point."
    },
    {
      title: "Skill Graph",
      description: "Visualize your architectural mind expanding, one node at a time."
    },
    {
      title: "Growth Blueprints",
      description: "Personalized learning tracks that evolve with you."
    },
    {
      title: "Layered Progression",
      description: "Systems aren't built in a day. Neither is expertise."
    },
    {
      title: "Design Duel",
      description: "Compete with your past self or with others — not to win, but to evolve."
    }
  ];

  return (
    <section id="manifesto" className="py-20 md:py-32">
      <div className="container max-w-6xl mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          {/* Updated Manifesto title typography */}
          <h2 className="text-3xl md:text-5xl font-bold mb-8 text-center text-[#A78BFA] gradient-text">Our Manifesto</h2>

          <div className="prose prose-lg max-w-none">
            <p className="text-2xl font-bold mb-6 text-center gradient-text">
              Layrs: Master the unseen architecture of great systems. One layer at a time.
            </p>

            {/* Updated body text typography */}
            <p className="text-lg leading-7 mb-6 text-[#94A3B8]">
              Layrs exists to reshape how we learn to design.
            </p>

            <p className="text-lg leading-7 mb-6 text-[#94A3B8]">
              No more scattered theories. No more endless jargon.
            </p>

            <p className="text-lg leading-7 mb-6 text-[#94A3B8]">
              In a world obsessed with syntax, Layrs focuses on what truly matters:
              Building thinking frameworks that scale — just like the systems you dream to design.
            </p>

            <p className="text-lg font-medium mb-4 text-blue-400">We believe:</p>

            <ul className="space-y-4 mb-8">
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span className="text-lg text-[#6280c0]"><strong className="text-blue-400">Learning:</strong> dynamic, not static.</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span className="text-lg text-[#6280c0]"><strong className="text-blue-400">Feedback:</strong> surgical, not superficial.</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-400 mr-2">•</span>
                <span className="text-lg text-[#6280c0]"><strong className="text-blue-400">Growth:</strong> structured, not accidental.</span>
              </li>
            </ul>

            <p className="text-lg leading-7 mb-4 text-[#94A3B8]">
              Layrs is not just another learning platform.
              It's a systematic ascent — from foundations to blueprints, from first sketch to final structure.
            </p>

            <p className="text-lg leading-7 mb-10 font-medium text-[#94A3B8]">
              We don't just teach designs. We architect designers.
            </p>

            {/* <h3 className="text-2xl font-bold mb-6 text-center text-blue-400">Teasers</h3> */}

            {/* Updated teaser card typography */}
            {/* <div className="grid md:grid-cols-2 gap-8 mb-10">
              {elements.map((element, index) => (
                <div
                  key={index}
                  className="p-6 rounded-xl glass-card hover:scale-[1.02] hover:shadow-lg hover:shadow-blue-500/10 transition-all duration-150 ease-in-out"
                >
                  <h3 className="text-lg font-semibold text-[#E0E7FF] mb-3">{element.title}</h3>
                  <p className="text-base text-[#94A3B8]">{element.description}</p>
                </div>
              ))}
            </div> */}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Manifesto;
