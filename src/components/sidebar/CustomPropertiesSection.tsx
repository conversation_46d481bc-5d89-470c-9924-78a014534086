
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Plus, Trash, Code } from 'lucide-react';
import CodeEditor from '../CodeEditor';
import { CustomProperty } from './types';

interface CustomPropertiesSectionProps {
  customProperties: CustomProperty[];
  onAddProperty: () => void;
  onRemoveProperty: (index: number) => void;
  onPropertyChange: (index: number, field: 'key' | 'value', newValue: string) => void;
  onToggleCodeEditor: (index: number) => void;
}

const CustomPropertiesSection: React.FC<CustomPropertiesSectionProps> = ({
  customProperties,
  onAddProperty,
  onRemoveProperty,
  onPropertyChange,
  onToggleCodeEditor,
}) => {
  return (
    <div className="mt-4 border-t pt-3">
      <h3 className="text-sm font-semibold mb-2">Custom Properties</h3>
      <div className="space-y-2">
        {customProperties.map((property, index) => (
          <div key={index} className="flex gap-2 items-start mb-2">
            <Input
              className="w-1/3 px-2 py-1 text-sm border rounded"
              placeholder="Property name"
              value={property.key}
              onChange={(e) => onPropertyChange(index, 'key', e.target.value)}
            />
            <div className="w-2/3 flex flex-col">
              <div className="flex items-center justify-end mb-1">
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {property.isCode ? "Code Editor" : "Text"}
                  </span>
                  <Switch
                    id={`code-toggle-${index}`}
                    checked={property.isCode || false}
                    onCheckedChange={() => onToggleCodeEditor(index)}
                  />
                  <Code className="h-3 w-3 text-gray-500" />
                </div>
              </div>

              {property.isCode ? (
                <div className="min-h-[200px] border rounded mt-0">
                  <CodeEditor
                    value={property.value || ''}
                    onChange={(value) => onPropertyChange(index, 'value', value)}
                    height="300px"
                    minHeight="200px"
                  />
                </div>
              ) : (
                <Textarea
                  className="px-2 py-1 text-sm border rounded resize-y min-h-[120px]"
                  placeholder="Property value"
                  value={property.value}
                  onChange={(e) => onPropertyChange(index, 'value', e.target.value)}
                />
              )}
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              className="p-1 h-8 w-8"
              onClick={() => onRemoveProperty(index)}
            >
              <Trash className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="text-sm text-emerald-600 hover:underline mt-2 px-0"
          onClick={onAddProperty}
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Property
        </Button>
      </div>
    </div>
  );
};

export default CustomPropertiesSection;
