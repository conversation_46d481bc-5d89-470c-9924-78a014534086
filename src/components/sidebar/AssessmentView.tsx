
import React from 'react';

import { AssessmentResult } from './types';
import { Badge } from '@/components/ui/badge';

interface AssessmentViewProps {
  assessmentResult: AssessmentResult | null;
}

const AssessmentView: React.FC<AssessmentViewProps> = ({ assessmentResult }) => {
  if (!assessmentResult) return null;

  // Function to render text with **bold** formatting
  const renderTextWithBold = (text: string) => {
    if (!text) return text;

    // Split text by **bold** patterns and render accordingly
    const parts = text.split(/(\*\*.*?\*\*)/g);

    return parts.map((part, index) => {
      // Check if this part is bold (wrapped in **)
      const boldMatch = part.match(/^\*\*(.*?)\*\*$/);
      if (boldMatch) {
        return (
          <strong key={index} className="font-semibold">
            {boldMatch[1]}
          </strong>
        );
      }
      return part;
    });
  };





  // Function to render a section with a specific background color
  const renderSection = (title: string, content: string | string[], bgColor: string, textColor: string = "text-gray-800") => {
    if (!content || (Array.isArray(content) && content.length === 0)) return null;

    return (
      <div className="mb-3">
        <h4 className="text-sm font-medium mb-1.5 flex items-center">
          <Badge className={`${bgColor} ${textColor} mr-2 text-xs px-2 py-1`}>{title}</Badge>
        </h4>
        <div className={`prose max-w-none text-sm ${bgColor} ${textColor} p-2.5 rounded-md`}>
          {Array.isArray(content) ? (
            <ul className="list-disc pl-4 space-y-1">
              {content.map((item, index) => (
                <li key={index} className="text-sm leading-relaxed">{renderTextWithBold(item)}</li>
              ))}
            </ul>
          ) : (
            // Display text content with proper formatting
            <div className="text-sm leading-relaxed whitespace-pre-line">
              {renderTextWithBold(content)}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Function to render a score with stars
  const renderScore = (score: number, label: string) => {
    const filledStars = Math.round(score);
    const emptyStars = 10 - filledStars;

    return (
      <div className="flex items-center justify-between mb-2">
        <span className="text-sm font-medium capitalize">{label.replace(/_/g, ' ')}</span>
        <div className="flex items-center">
          <div className="flex mr-2">
            {[...Array(filledStars)].map((_, i) => (
              <span key={`filled-${i}`} className="text-yellow-400 mr-0.5 text-sm">★</span>
            ))}
            {[...Array(emptyStars)].map((_, i) => (
              <span key={`empty-${i}`} className="text-gray-300 mr-0.5 text-sm">★</span>
            ))}
          </div>
          <span className="text-sm font-medium">{score}/10</span>
        </div>
      </div>
    );
  };

  return (
    <div>
      <h2 className="text-lg font-semibold mb-3 text-layrs-dark">Assessment Result</h2>

      {/* Star Ratings Display */}
      {assessmentResult.score && (
        <div className="p-3 bg-white border border-gray-200 rounded-lg shadow-sm mb-3">
          <h3 className="text-sm font-medium text-layrs-dark mb-3">Scores</h3>
          {Object.entries(assessmentResult.score).map(([key, value]) => {
            // Only show 0-10 scale scores, skip debugging scores (0-100 scale) and undefined values
            // Skip 'rating' field and only show 'total_rating'
            if (value === undefined || Number(value) > 10 || key === 'rating') return null;
            return renderScore(Math.round(Number(value)), key);
          })}
        </div>
      )}

      <div className="p-3 bg-white border border-gray-200 rounded-lg shadow-sm">
        <h3 className="text-sm font-medium text-layrs-dark mb-2">Assessment Feedback</h3>

        {/* Display all assessment sections */}
        {assessmentResult.validationResponse && (
          <div className="space-y-3">
            {/* Overall Assessment */}
            {assessmentResult.validationResponse.overall_assessment && (
              renderSection(
                "Analysis",
                assessmentResult.validationResponse.overall_assessment,
                "bg-blue-50",
                "text-blue-800"
              )
            )}

            {/* Strengths */}
            {assessmentResult.validationResponse.strengths && assessmentResult.validationResponse.strengths.length > 0 && (
              renderSection(
                "Strengths",
                assessmentResult.validationResponse.strengths,
                "bg-green-50",
                "text-green-800"
              )
            )}

            {/* Weaknesses */}
            {assessmentResult.validationResponse.weaknesses && assessmentResult.validationResponse.weaknesses.length > 0 && (
              renderSection(
                "Weaknesses",
                assessmentResult.validationResponse.weaknesses,
                "bg-red-50",
                "text-red-800"
              )
            )}

            {/* Micro Analysis */}
            {assessmentResult.validationResponse.micro_analysis && (
              renderSection(
                "Micro Analysis",
                assessmentResult.validationResponse.micro_analysis,
                "bg-indigo-50",
                "text-indigo-800"
              )
            )}

            {/* Questions */}
            {assessmentResult.validationResponse.questions && assessmentResult.validationResponse.questions.length > 0 && (
              renderSection(
                "Questions",
                assessmentResult.validationResponse.questions,
                "bg-purple-50",
                "text-purple-800"
              )
            )}

            {/* Interview Questions */}
            {assessmentResult.validationResponse.interview_questions && assessmentResult.validationResponse.interview_questions.length > 0 && (
              renderSection(
                "Interview Follow-up Questions",
                assessmentResult.validationResponse.interview_questions,
                "bg-orange-50",
                "text-orange-800"
              )
            )}

            {/* Scoring Explanation */}
            {assessmentResult.validationResponse.scoring_explanation && (
              renderSection(
                "Scoring Explanation",
                assessmentResult.validationResponse.scoring_explanation,
                "bg-gray-50",
                "text-gray-800"
              )
            )}
          </div>
        )}

        {/* Fallback to context if no validation response */}
        {!assessmentResult.validationResponse && (
          <div className="prose max-w-none text-sm whitespace-pre-line bg-gray-50 p-2.5 rounded-md">
            {assessmentResult.context}
          </div>
        )}

        {/* Display validation warnings if any */}
        {assessmentResult.validationWarnings && assessmentResult.validationWarnings.length > 0 && (
          <div className="mt-3">
            <h4 className="text-sm font-medium mb-1.5">Validation Warnings</h4>
            <div className="prose max-w-none text-sm whitespace-pre-line bg-orange-50 text-orange-800 p-2.5 rounded-md">
              <ul className="list-disc pl-4 space-y-1">
                {assessmentResult.validationWarnings.map((warning, index) => (
                  <li key={index} className="text-sm">{warning}</li>
                ))}
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AssessmentView;
