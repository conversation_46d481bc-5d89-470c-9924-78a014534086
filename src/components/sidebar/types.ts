
import { Node as XYFlowNode } from '@xyflow/react';
import { ComponentType, ComponentMetadata } from '@/components/ComponentTypes';

export interface ComponentMetadataSidebarProps {
  selectedComponent: XYFlowNode | null;
  componentTypes: Record<ComponentType, ComponentMetadata>;
  assessmentResult?: AssessmentResult | null;
  onStartAssessment?: () => void;
  onDeleteNode?: (nodeId: string) => void;
  onClose?: () => void;
  onCanvasChange?: (changeType: 'canvas' | 'major') => void;
}

// Adding any missing types that other components might be importing
export interface NodeData {
  label: string;
  type: string;
  icon?: string;
  description?: string;
  metadata?: NodeDataMetadata;
  customProperties?: CustomProperty[];
}

export interface NodeDataMetadata {
  description?: string;
  icon?: string;
  documentation?: string;
  schemaTables?: string;
  customLogic?: string;
  customProperties?: CustomProperty[];
  contentTypes?: string[];
  recordTypes?: string[];
  [key: string]: any; // Allow for additional properties
}

export interface CustomProperty {
  key: string;
  value: string;
  type: 'text' | 'number' | 'boolean' | 'select';
  options?: string[];
  isCode?: boolean;
}

export interface AssessmentScore {
  rating: number;
  [key: string]: number;
}

export interface AssessmentResult {
  context: string;
  score: AssessmentScore;
  validationWarnings?: string[];
  validationResponse?: any;
}
