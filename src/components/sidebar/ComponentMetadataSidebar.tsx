
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ComponentMetadataSidebarProps, CustomProperty, NodeData, AssessmentResult, NodeDataMetadata } from './types';
import { SchemaTable } from '../SchemaBuilder';
import NodeMetadataView from './NodeMetadataView';
import AssessmentView from './AssessmentView';
import { X } from 'lucide-react';
import { ComponentType, ComponentMetadata } from '@/components/ComponentTypes';
import { Node as XYFlowNode, useReactFlow } from '@xyflow/react';

const ComponentMetadataSidebar: React.FC<ComponentMetadataSidebarProps> = ({
  selectedComponent,
  componentTypes,
  assessmentResult,
  onStartAssessment,
  onDeleteNode,
  onClose,
  onCanvasChange
}) => {
  const { setNodes } = useReactFlow();
  const [metadata, setMetadata] = useState<Record<string, any>>({});
  const [schemaTables, setSchemaTables] = useState<SchemaTable[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string[]>>({});
  const [customLogic, setCustomLogic] = useState<string>("");
  const [customProperties, setCustomProperties] = useState<CustomProperty[]>([]);
  const [componentName, setComponentName] = useState<string>("");



  useEffect(() => {
    // Reset metadata when a new component is selected
    if (selectedComponent?.data) {
      // Type assertion to any first, then to our expected type
      const nodeData = selectedComponent.data as any as NodeData;

      // Handle tables if this is a database component
      if (nodeData.type === 'database' && nodeData.metadata?.schemaTables) {
        try {
          const parsedTables = JSON.parse(nodeData.metadata.schemaTables);
          setSchemaTables(Array.isArray(parsedTables) ? parsedTables : []);
        } catch (e) {
          setSchemaTables([]);
        }
      } else {
        setSchemaTables([]);
      }

      setMetadata(nodeData.metadata || {});

      // Initialize multi-select options
      const multiSelectOptions: Record<string, string[]> = {};
      if (nodeData.type === 'cdn' && nodeData.metadata?.contentTypes) {
        multiSelectOptions.contentTypes = Array.isArray(nodeData.metadata.contentTypes)
          ? nodeData.metadata.contentTypes
          : [];
      }

      if (nodeData.type === 'dns' && nodeData.metadata?.recordTypes) {
        multiSelectOptions.recordTypes = Array.isArray(nodeData.metadata.recordTypes)
          ? nodeData.metadata.recordTypes
          : [];
      }

      setSelectedOptions(multiSelectOptions);

      // Set customLogic from metadata if available
      setCustomLogic(nodeData.metadata?.customLogic || "");

      // Set customProperties from metadata if available with proper deep clone
      setCustomProperties(
        Array.isArray(nodeData.metadata?.customProperties)
          ? JSON.parse(JSON.stringify(nodeData.metadata.customProperties))
          : []
      );

      // Set component name from node data
      setComponentName(nodeData.label || '');
    } else {
      setMetadata({});
      setSchemaTables([]);
      setSelectedOptions({});
      setCustomLogic("");
      setCustomProperties([]);
      setComponentName('');
    }
  }, [selectedComponent]);

  const handleFieldChange = (fieldName: string, value: any) => {
    // Handle special case for component label
    if (fieldName === '__label__') {
      // Update local state immediately for responsive UI
      setComponentName(value);

      // Update the node's label in React Flow state
      setNodes((nodes) =>
        nodes.map((node) =>
          node.id === selectedComponent?.id
            ? { ...node, data: { ...node.data, label: value } }
            : node
        )
      );

      // Trigger autosave for component label change
      onCanvasChange?.('major');
      return;
    }

    const updatedMetadata = {
      ...metadata,
      [fieldName]: value
    };

    setMetadata(updatedMetadata);

    // Update the node data
    if (selectedComponent && selectedComponent.data) {
      selectedComponent.data.metadata = updatedMetadata;
    }

    // Trigger autosave for metadata change
    onCanvasChange?.('major');
  };

  const handleTablesChange = (tables: SchemaTable[]) => {
    setSchemaTables(tables);

    // Store tables as JSON string in metadata
    const updatedMetadata = {
      ...metadata,
      schemaTables: JSON.stringify(tables)
    };

    setMetadata(updatedMetadata);

    // Update the node data
    if (selectedComponent && selectedComponent.data) {
      selectedComponent.data.metadata = updatedMetadata;
    }

    // Trigger autosave for schema table changes
    onCanvasChange?.('major');
  };

  const handleCustomLogicChange = (value: string) => {
    setCustomLogic(value);

    // Update the metadata with the new customLogic
    handleFieldChange('customLogic', value);
  };

  const handleCustomPropertyChange = (index: number, field: 'key' | 'value', newValue: string) => {
    const updatedProperties = [...customProperties];
    updatedProperties[index][field] = newValue;
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const toggleCodeEditor = (index: number) => {
    const updatedProperties = [...customProperties];
    updatedProperties[index].isCode = !updatedProperties[index].isCode;
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const addCustomProperty = () => {
    const newProperty: CustomProperty = { key: '', value: '', type: 'text', isCode: false };
    const updatedProperties = [...customProperties, newProperty];
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const removeCustomProperty = (index: number) => {
    const updatedProperties = [...customProperties];
    updatedProperties.splice(index, 1);
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const handleMultiSelectChange = (fieldName: string, option: string) => {
    const currentOptions = selectedOptions[fieldName] || [];
    let newOptions: string[];

    if (currentOptions.includes(option)) {
      newOptions = currentOptions.filter(item => item !== option);
    } else {
      newOptions = [...currentOptions, option];
    }

    // Update selected options state
    setSelectedOptions({
      ...selectedOptions,
      [fieldName]: newOptions
    });

    // Update metadata
    handleFieldChange(fieldName, newOptions);
  };

  const handleCheckboxChange = (fieldName: string, checked: boolean) => {
    handleFieldChange(fieldName, checked);
  };

  const handleDeleteNode = () => {
    if (selectedComponent && onDeleteNode) {
      onDeleteNode(selectedComponent.id);
    }
  };

  // If there's no assessment result and no selected component, show empty state
  if (!assessmentResult && !selectedComponent) {
    return (
      <div className="h-full bg-layrs-light border-l border-gray-200 p-4 flex items-center justify-center text-gray-400 text-sm">
        <p>Select a component to view and edit its metadata</p>
      </div>
    );
  }

  return (
    <div className="h-full bg-layrs-light border-l border-gray-200 p-4 overflow-y-auto">
      {/* Header with close button */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Component Properties</h3>
        {onClose && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Show component properties when a component is selected */}
      {selectedComponent && (
        <NodeMetadataView
          selectedComponent={selectedComponent}
          componentTypes={componentTypes}
          metadata={metadata}
          schemaTables={schemaTables}
          selectedOptions={selectedOptions}
          customLogic={customLogic}
          customProperties={customProperties}
          componentName={componentName}
          onFieldChange={handleFieldChange}
          onTablesChange={handleTablesChange}
          onMultiSelectChange={handleMultiSelectChange}
          onCheckboxChange={handleCheckboxChange}
          onCustomLogicChange={handleCustomLogicChange}
          onAddCustomProperty={addCustomProperty}
          onRemoveCustomProperty={removeCustomProperty}
          onCustomPropertyChange={handleCustomPropertyChange}
          onToggleCodeEditor={toggleCodeEditor}
          onDeleteNode={handleDeleteNode}
        />
      )}

      {/* Show assessment result when no component is selected */}
      {!selectedComponent && assessmentResult && (
        <AssessmentView assessmentResult={assessmentResult} />
      )}

    </div>
  );
};

export default ComponentMetadataSidebar;
