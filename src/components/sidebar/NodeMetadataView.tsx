
import React from 'react';
import { Node as XYFlowNode } from '@xyflow/react';
import { ComponentMetadata, ComponentType } from '../ComponentTypes';
// import CodeEditor from '../CodeEditor';
import { SchemaTable } from '../SchemaBuilder';
import { NodeData, NodeDataMetadata, CustomProperty } from './types';
import ComponentFields from './ComponentFields';
import CustomPropertiesSection from './CustomPropertiesSection';

interface NodeMetadataViewProps {
  selectedComponent: XYFlowNode | null;
  componentTypes: Record<ComponentType, ComponentMetadata>;
  metadata: NodeDataMetadata;
  schemaTables: SchemaTable[];
  selectedOptions: Record<string, string[]>;
  customLogic: string;
  customProperties: CustomProperty[];
  componentName: string;
  onFieldChange: (fieldName: string, value: any) => void;
  onTablesChange: (tables: SchemaTable[]) => void;
  onMultiSelectChange: (fieldName: string, option: string) => void;
  onCheckboxChange: (fieldName: string, checked: boolean) => void;
  onCustomLogicChange: (value: string) => void;
  onAddCustomProperty: () => void;
  onRemoveCustomProperty: (index: number) => void;
  onCustomPropertyChange: (index: number, field: 'key' | 'value', newValue: string) => void;
  onToggleCodeEditor: (index: number) => void;
  onDeleteNode: () => void;
}

const NodeMetadataView: React.FC<NodeMetadataViewProps> = ({
  selectedComponent,
  componentTypes,
  metadata,
  schemaTables,
  selectedOptions,
  customLogic,
  customProperties,
  componentName,
  onFieldChange,
  onTablesChange,
  onMultiSelectChange,
  onCheckboxChange,
  onCustomLogicChange,
  onAddCustomProperty,
  onRemoveCustomProperty,
  onCustomPropertyChange,
  onToggleCodeEditor,
  onDeleteNode,
}) => {
  if (!selectedComponent) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400 text-sm">
        <p>Select a component to view and edit its metadata</p>
      </div>
    );
  }

  const nodeData = selectedComponent.data as any as NodeData;
  const nodeType = nodeData.type;
  const componentType = componentTypes[nodeType];

  if (!componentType) {
    return (
      <div className="flex items-center justify-center h-full text-gray-400 text-sm">
        <p>Unknown component type</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2 mb-3">
        <div className={`w-8 h-8 rounded flex items-center justify-center ${componentType.className}`}>
          {componentType.iconComponent}
        </div>
        <h2 className="text-lg font-semibold text-layrs-dark">{componentType.label} Metadata</h2>
      </div>

      <p className="text-sm text-gray-500 mb-2">{componentType.description}</p>

      {/* Component Name Field - Always first */}
      <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
        <label htmlFor="componentName" className="component-label font-semibold">
          Component Name
        </label>
        <input
          id="componentName"
          type="text"
          className="component-input"
          placeholder="Enter component name"
          value={componentName}
          onChange={(e) => onFieldChange('__label__', e.target.value)}
        />
        <p className="text-xs text-gray-500">This name will be displayed on the canvas</p>
      </div>

      <ComponentFields
        nodeType={nodeType}
        componentType={componentType}
        metadata={metadata}
        selectedOptions={selectedOptions}
        schemaTables={schemaTables}
        onFieldChange={onFieldChange}
        onMultiSelectChange={onMultiSelectChange}
        onCheckboxChange={onCheckboxChange}
        onTablesChange={onTablesChange}
      />

      <CustomPropertiesSection
        customProperties={customProperties}
        onAddProperty={onAddCustomProperty}
        onRemoveProperty={onRemoveCustomProperty}
        onPropertyChange={onCustomPropertyChange}
        onToggleCodeEditor={onToggleCodeEditor}
      />

      {/* Add custom logic textarea for all component types */}
      <div style={{ marginTop: '16px', borderTop: '1px solid #e5e7eb', paddingTop: '12px' }}>
        <div style={{ marginBottom: '0px', padding: '0px', lineHeight: '1.2' }}>
          <span className="text-sm font-semibold">
            {componentType.customLogicConfig?.label || "Custom Logic"}
          </span>
        </div>
        <div
          className="custom-logic-editor-container border rounded"
          style={{
            padding: '0px',
            margin: '0px',
            minHeight: '200px',
            marginTop: '0px',
            paddingTop: '0px'
          }}
        >
          {/* Temporarily replace CodeEditor with textarea to test if Monaco is the issue */}
          <textarea
            value={customLogic}
            onChange={(e) => onCustomLogicChange(e.target.value)}
            className="w-full h-[300px] p-2 font-mono text-sm resize-none border-0 rounded"
            style={{
              margin: '0px',
              padding: '8px',
              minHeight: '200px',
              height: '300px'
            }}
            placeholder={componentType.customLogicConfig?.placeholder || "Enter custom logic here"}
          />
        </div>
      </div>

      {/* Delete button */}
      <button
        onClick={onDeleteNode}
        className="mt-4 text-sm text-red-600 font-medium hover:underline"
      >
        Delete Component
      </button>
    </div>
  );
};

export default NodeMetadataView;
