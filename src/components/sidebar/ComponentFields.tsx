
import React from 'react';
import { Checkbox } from "@/components/ui/checkbox";
import { ComponentMetadata } from '../ComponentTypes';
import SchemaBuilder, { SchemaTable } from '../SchemaBuilder';
import { NodeData, NodeDataMetadata } from './types';

interface ComponentFieldsProps {
  nodeType: string;
  componentType: ComponentMetadata;
  metadata: NodeDataMetadata;
  selectedOptions: Record<string, string[]>;
  schemaTables: SchemaTable[];
  onFieldChange: (fieldName: string, value: any) => void;
  onMultiSelectChange: (fieldName: string, option: string) => void;
  onCheckboxChange: (fieldName: string, checked: boolean) => void;
  onTablesChange: (tables: SchemaTable[]) => void;
}

const ComponentFields: React.FC<ComponentFieldsProps> = ({
  nodeType,
  componentType,
  metadata,
  selectedOptions,
  schemaTables,
  onFieldChange,
  onMultiSelectChange,
  onCheckboxChange,
  onTablesChange,
}) => {
  return (
    <div className="space-y-2">
      {Object.entries(componentType.fields).map(([fieldName, field]) => {
        // Skip the schema field for database components as we'll render the SchemaBuilder instead
        if (nodeType === 'database' && fieldName === 'schema') {
          return null;
        }

        return (
          <div key={fieldName}>
            <label htmlFor={fieldName} className="component-label">
              {field.label}
            </label>

            {field.type === 'text' && (
              <input
                id={fieldName}
                type="text"
                className="component-input"
                placeholder={field.placeholder}
                value={metadata[fieldName] || ''}
                onChange={(e) => onFieldChange(fieldName, e.target.value)}
              />
            )}

            {field.type === 'number' && (
              <input
                id={fieldName}
                type="number"
                className="component-input"
                placeholder={field.placeholder}
                value={metadata[fieldName] || ''}
                onChange={(e) => onFieldChange(fieldName, e.target.valueAsNumber || 0)}
              />
            )}

            {field.type === 'textarea' && fieldName !== 'schema' && (
              <textarea
                id={fieldName}
                className="component-textarea h-24"
                placeholder={field.placeholder}
                value={metadata[fieldName] || ''}
                onChange={(e) => onFieldChange(fieldName, e.target.value)}
              />
            )}

            {field.type === 'checkbox' && (
              <div className="flex items-center space-x-2 pt-2">
                <Checkbox
                  id={fieldName}
                  checked={metadata[fieldName] || false}
                  onCheckedChange={(checked) => onCheckboxChange(fieldName, !!checked)}
                />
                <label
                  htmlFor={fieldName}
                  className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {field.label}
                </label>
              </div>
            )}

            {field.type === 'select' && field.options && (
              <select
                id={fieldName}
                className="component-input"
                value={metadata[fieldName] || ''}
                onChange={(e) => onFieldChange(fieldName, e.target.value)}
              >
                <option value="">Select {field.label}</option>
                {field.options.map(option => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            )}

            {field.type === 'multi-select' && field.options && (
              <div className="pt-1 space-y-2">
                {field.options.map(option => (
                  <div key={option} className="flex items-center space-x-2">
                    <Checkbox
                      id={`${fieldName}-${option}`}
                      checked={selectedOptions[fieldName]?.includes(option) || false}
                      onCheckedChange={() => onMultiSelectChange(fieldName, option)}
                    />
                    <label
                      htmlFor={`${fieldName}-${option}`}
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option}
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}

      {/* Show SchemaBuilder only for database components */}
      {nodeType === 'database' && (
        <div className="mt-5">
          <SchemaBuilder
            tables={schemaTables}
            onTablesChange={onTablesChange}
          />
        </div>
      )}
    </div>
  );
};

export default ComponentFields;
