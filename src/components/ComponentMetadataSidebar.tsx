import React, { useState, useEffect, useRef } from 'react';
import { Node } from '@xyflow/react';
import { ComponentMetadata, ComponentType } from './ComponentTypes';
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import SchemaBuilder, { SchemaTable } from './SchemaBuilder';

import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Code, Plus, Trash, ChevronDown, ChevronRight, Send, BarChart, Loader2 } from 'lucide-react';
import CodeEditor from './CodeEditor';
import { Switch } from "@/components/ui/switch";
import { toast } from 'sonner';

// Collapsible section component for validation results
interface CollapsibleSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  titleColor?: string;
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  children,
  defaultOpen = false,
  titleColor = "text-gray-700"
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="mb-3">
      <div
        className="flex items-center cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ?
          <ChevronDown className="h-4 w-4 mr-1 text-gray-500" /> :
          <ChevronRight className="h-4 w-4 mr-1 text-gray-500" />
        }
        <h4 className={`text-sm font-medium ${titleColor}`}>{title}</h4>
      </div>

      {isOpen && (
        <div className="mt-1 ml-5">
          {children}
        </div>
      )}
    </div>
  );
};

interface AssessmentScore {
  rating: number;
  [key: string]: number;
}

interface ValidationResponse {
  overall_assessment: string;
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  questions: string[];
  rating: number;
  scoring_explanation: string;
}

interface AssessmentResult {
  context: string;
  score: AssessmentScore;
  validationWarnings?: string[];
  validationResponse?: ValidationResponse; // Properly typed validation response
}

// Chat message interface
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

// Chat state interface
interface ChatState {
  messages: ChatMessage[];
  isLoading: boolean;
}

interface ComponentMetadataSidebarProps {
  selectedComponent: Node | null;
  componentTypes: Record<ComponentType, ComponentMetadata>;
  assessmentResult?: AssessmentResult | null;
  onStartAssessment?: () => void;
  onDeleteNode?: (nodeId: string) => void;
  onCanvasChange?: (changeType: 'canvas' | 'major') => void;
}

interface NodeDataMetadata {
  schemaTables?: string;
  customLogic?: string;
  customProperties?: CustomProperty[];
  [key: string]: any;
}

interface CustomProperty {
  key: string;
  value: string;
  isCode?: boolean;
}

interface NodeData {
  type: ComponentType;
  label?: React.ReactNode;
  metadata?: NodeDataMetadata;
}

const ComponentMetadataSidebar: React.FC<ComponentMetadataSidebarProps> = ({
  selectedComponent,
  componentTypes,
  assessmentResult,
  onStartAssessment,
  onDeleteNode,
  onCanvasChange
}) => {
  const [metadata, setMetadata] = useState<Record<string, any>>({});
  const [schemaTables, setSchemaTables] = useState<SchemaTable[]>([]);
  const [selectedOptions, setSelectedOptions] = useState<Record<string, string[]>>({});
  const [activeTab, setActiveTab] = useState<string>("node");
  const [customLogic, setCustomLogic] = useState<string>("");
  const [customProperties, setCustomProperties] = useState<CustomProperty[]>([]);

  // Chat state
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Set default tab when assessment result changes or node selection changes
    if (assessmentResult && !selectedComponent) {
      setActiveTab("assessment");
    } else if (selectedComponent) {
      setActiveTab("node");
    }
  }, [assessmentResult, selectedComponent]);

  // Handle tab changes within the assessment result tabs
  const handleAssessmentTabChange = (value: string) => {
    setActiveTab(value);
  };

  useEffect(() => {
    // Reset metadata when a new component is selected
    if (selectedComponent?.data) {
      // Type assertion to any first, then to our expected type
      const nodeData = selectedComponent.data as any as NodeData;

      // Handle tables if this is a database component
      if (nodeData.type === 'database' && nodeData.metadata?.schemaTables) {
        try {
          const parsedTables = JSON.parse(nodeData.metadata.schemaTables);
          setSchemaTables(Array.isArray(parsedTables) ? parsedTables : []);
        } catch (e) {
          setSchemaTables([]);
        }
      } else {
        setSchemaTables([]);
      }

      setMetadata(nodeData.metadata || {});

      // Initialize multi-select options
      const multiSelectOptions: Record<string, string[]> = {};
      if (nodeData.type === 'cdn' && nodeData.metadata?.contentTypes) {
        multiSelectOptions.contentTypes = Array.isArray(nodeData.metadata.contentTypes)
          ? nodeData.metadata.contentTypes
          : [];
      }

      if (nodeData.type === 'dns' && nodeData.metadata?.recordTypes) {
        multiSelectOptions.recordTypes = Array.isArray(nodeData.metadata.recordTypes)
          ? nodeData.metadata.recordTypes
          : [];
      }

      setSelectedOptions(multiSelectOptions);

      // Set customLogic from metadata if available
      setCustomLogic(nodeData.metadata?.customLogic || "");

      // Set customProperties from metadata if available
      setCustomProperties(
        Array.isArray(nodeData.metadata?.customProperties)
          ? nodeData.metadata.customProperties
          : []
      );
    } else {
      setMetadata({});
      setSchemaTables([]);
      setSelectedOptions({});
      setCustomLogic("");
      setCustomProperties([]);
    }
  }, [selectedComponent]);

  const handleFieldChange = (fieldName: string, value: any) => {
    const updatedMetadata = {
      ...metadata,
      [fieldName]: value
    };

    setMetadata(updatedMetadata);

    // Update the node data
    if (selectedComponent && selectedComponent.data) {
      selectedComponent.data.metadata = updatedMetadata;
    }

    // Trigger autosave for metadata change
    onCanvasChange?.('major');
  };

  const handleTablesChange = (tables: SchemaTable[]) => {
    setSchemaTables(tables);

    // Store tables as JSON string in metadata
    const updatedMetadata = {
      ...metadata,
      schemaTables: JSON.stringify(tables)
    };

    setMetadata(updatedMetadata);

    // Update the node data
    if (selectedComponent && selectedComponent.data) {
      selectedComponent.data.metadata = updatedMetadata;
    }

    // Trigger autosave for schema table changes
    onCanvasChange?.('major');
  };

  const handleCustomLogicChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setCustomLogic(newValue);

    // Update the metadata with the new customLogic
    handleFieldChange('customLogic', newValue);
  };

  const handleCustomPropertyChange = (index: number, field: 'key' | 'value', newValue: string) => {
    const updatedProperties = [...customProperties];
    updatedProperties[index][field] = newValue;
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const toggleCodeEditor = (index: number) => {
    const updatedProperties = [...customProperties];
    updatedProperties[index].isCode = !updatedProperties[index].isCode;
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const addCustomProperty = () => {
    const newProperty: CustomProperty = { key: '', value: '', isCode: false };
    const updatedProperties = [...customProperties, newProperty];
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const removeCustomProperty = (index: number) => {
    const updatedProperties = [...customProperties];
    updatedProperties.splice(index, 1);
    setCustomProperties(updatedProperties);

    // Update metadata
    handleFieldChange('customProperties', updatedProperties);
  };

  const handleMultiSelectChange = (fieldName: string, option: string) => {
    const currentOptions = selectedOptions[fieldName] || [];
    let newOptions: string[];

    if (currentOptions.includes(option)) {
      newOptions = currentOptions.filter(item => item !== option);
    } else {
      newOptions = [...currentOptions, option];
    }

    // Update selected options state
    setSelectedOptions({
      ...selectedOptions,
      [fieldName]: newOptions
    });

    // Update metadata
    handleFieldChange(fieldName, newOptions);
  };

  const handleCheckboxChange = (fieldName: string, checked: boolean) => {
    handleFieldChange(fieldName, checked);
  };



  const handleDeleteNode = () => {
    if (selectedComponent && onDeleteNode) {
      onDeleteNode(selectedComponent.id);
    }
  };

  const renderNodeMetadata = () => {
    if (!selectedComponent) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400 text-sm">
          <p>Select a component to view and edit its metadata</p>
        </div>
      );
    }

    const nodeData = selectedComponent.data as any as NodeData;
    const nodeType = nodeData.type;
    const componentType = componentTypes[nodeType];

    if (!componentType) {
      return (
        <div className="flex items-center justify-center h-full text-gray-400 text-sm">
          <p>Unknown component type</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <div className={`w-8 h-8 rounded flex items-center justify-center ${componentType.className}`}>
            {componentType.iconComponent}
          </div>
          <h2 className="text-lg font-semibold text-layrs-dark">{componentType.label} Metadata</h2>
        </div>

        <p className="text-sm text-gray-500 mb-4">{componentType.description}</p>

        <div className="space-y-4">
          {Object.entries(componentType.fields).map(([fieldName, field]) => {
            // Skip the schema field for database components as we'll render the SchemaBuilder instead
            if (nodeType === 'database' && fieldName === 'schema') {
              return null;
            }

            return (
              <div key={fieldName} className="space-y-1">
                <label htmlFor={fieldName} className="component-label">
                  {field.label}
                </label>

                {field.type === 'text' && (
                  <input
                    id={fieldName}
                    type="text"
                    className="component-input"
                    placeholder={field.placeholder}
                    value={metadata[fieldName] || ''}
                    onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                  />
                )}

                {field.type === 'number' && (
                  <input
                    id={fieldName}
                    type="number"
                    className="component-input"
                    placeholder={field.placeholder}
                    value={metadata[fieldName] || ''}
                    onChange={(e) => handleFieldChange(fieldName, e.target.valueAsNumber || 0)}
                  />
                )}

                {field.type === 'textarea' && fieldName !== 'schema' && (
                  <textarea
                    id={fieldName}
                    className="component-textarea h-24"
                    placeholder={field.placeholder}
                    value={metadata[fieldName] || ''}
                    onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                  />
                )}

                {field.type === 'checkbox' && (
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id={fieldName}
                      checked={metadata[fieldName] || false}
                      onCheckedChange={(checked) => handleCheckboxChange(fieldName, !!checked)}
                    />
                    <label
                      htmlFor={fieldName}
                      className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {field.label}
                    </label>
                  </div>
                )}

                {field.type === 'select' && field.options && (
                  <select
                    id={fieldName}
                    className="component-input"
                    value={metadata[fieldName] || ''}
                    onChange={(e) => handleFieldChange(fieldName, e.target.value)}
                  >
                    <option value="">Select {field.label}</option>
                    {field.options.map(option => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                )}

                {field.type === 'multi-select' && field.options && (
                  <div className="pt-1 space-y-2">
                    {field.options.map(option => (
                      <div key={option} className="flex items-center space-x-2">
                        <Checkbox
                          id={`${fieldName}-${option}`}
                          checked={selectedOptions[fieldName]?.includes(option) || false}
                          onCheckedChange={() => handleMultiSelectChange(fieldName, option)}
                        />
                        <label
                          htmlFor={`${fieldName}-${option}`}
                          className="text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {option}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            );
          })}

          {/* Show SchemaBuilder only for database components */}
          {nodeType === 'database' && (
            <div className="mt-5">
              <SchemaBuilder
                tables={schemaTables}
                onTablesChange={handleTablesChange}
              />
            </div>
          )}

          {/* Custom Properties Section */}
          <div className="mt-6 border-t pt-4">
            <h3 className="text-sm font-semibold mb-3">Custom Properties</h3>
            <div className="space-y-2">
              {customProperties.map((property, index) => (
                <div key={index} className="flex gap-2 items-start mb-2">
                  <Input
                    className="w-1/3 px-2 py-1 text-sm border rounded"
                    placeholder="Property name"
                    value={property.key}
                    onChange={(e) => handleCustomPropertyChange(index, 'key', e.target.value)}
                  />
                  <div className="w-2/3 flex flex-col">
                    <div className="flex items-center justify-end mb-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {property.isCode ? "Code Editor" : "Text"}
                        </span>
                        <Switch
                          id={`code-toggle-${index}`}
                          checked={property.isCode || false}
                          onCheckedChange={() => toggleCodeEditor(index)}
                        />
                        <Code className="h-3 w-3 text-gray-500" />
                      </div>
                    </div>

                    {property.isCode ? (
                      <div className="h-[200px] border rounded">
                        <CodeEditor
                          value={property.value || ''}
                          onChange={(value) => handleCustomPropertyChange(index, 'value', value)}
                          height="200px"
                        />
                      </div>
                    ) : (
                      <Textarea
                        className="px-2 py-1 text-sm border rounded resize-y min-h-[120px]"
                        placeholder="Property value"
                        value={property.value}
                        onChange={(e) => handleCustomPropertyChange(index, 'value', e.target.value)}
                      />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="p-1 h-8 w-8"
                    onClick={() => removeCustomProperty(index)}
                  >
                    <Trash className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="text-sm text-emerald-600 hover:underline mt-2 px-0"
                onClick={addCustomProperty}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Property
              </Button>
            </div>
          </div>

          {/* Add custom logic textarea for all component types */}
          <div className="mt-6 border-t pt-4">
            <label htmlFor="customLogic" className="block text-sm font-semibold mb-1">
              {componentType.customLogicConfig?.label || "Custom Logic"}
            </label>
            <Textarea
              id="customLogic"
              className="code-textarea"
              placeholder={componentType.customLogicConfig?.placeholder || "Enter custom logic here"}
              value={customLogic}
              onChange={handleCustomLogicChange}
            />
          </div>
        </div>

        {/* Delete button */}
        <button
          onClick={handleDeleteNode}
          className="mt-4 text-sm text-red-600 font-medium hover:underline"
        >
          Delete Component
        </button>
      </div>
    );
  };

  // Function to handle sending a chat message
  const handleSendMessage = async () => {
    if (!chatInput.trim()) return;

    // Create a new user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: chatInput,
      timestamp: new Date()
    };

    // Add user message to chat
    setChatMessages(prev => [...prev, userMessage]);

    // Clear input
    setChatInput('');

    // Set loading state
    setIsLoading(true);

    try {
      // Call the chat API
      const response = await fetch('https://api.layrs.me/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          history: chatMessages.map(msg => ({ role: msg.role, content: msg.content }))
        }),
      });

      if (!response.ok) {
        throw new Error(`Chat request failed with status: ${response.status}`);
      }

      const data = await response.json();

      // Create assistant message from response
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: data.response || "I'm sorry, I couldn't process your request.",
        timestamp: new Date()
      };

      // Add assistant message to chat
      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error during chat:', error);
      toast.error('Failed to send message. Please try again.');

      // Add error message
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'assistant',
        content: "I'm sorry, there was an error processing your request. Please try again.",
        timestamp: new Date()
      }]);
    } finally {
      // Clear loading state
      setIsLoading(false);

      // Scroll to bottom
      setTimeout(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  };

  // Function to handle analyze button click
  const handleAnalyzeDesign = async () => {
    // Set loading state
    setIsLoading(true);

    try {
      // Call the analyze-design API
      const response = await fetch('https://api.layrs.me/api/analyze-design', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ enhanced: true }),
      });

      if (!response.ok) {
        throw new Error(`Analysis request failed with status: ${response.status}`);
      }

      const data = await response.json();

      // Format the response data to include all attributes in plain text format
      let formattedContent = "Design Analysis\n\n";

      // Handle feedback/overall assessment
      if (data.feedback) {
        formattedContent += `Feedback: ${data.feedback}\n\n`;
      } else if (data.overall_assessment) {
        formattedContent += `Overall Assessment: ${data.overall_assessment}\n\n`;
      }

      // Handle proposed suggestions (from analyze-design endpoint)
      if (data.proposed_suggestions) {
        formattedContent += "Proposed Suggestions:\n";
        Object.entries(data.proposed_suggestions).forEach(([_, value], index) => {
          formattedContent += `${index + 1}. ${value}\n`;
        });
        formattedContent += "\n";
      }

      // Handle bonus tips (from analyze-design endpoint)
      if (data.bonus_tips) {
        formattedContent += "Bonus Tips:\n\n";

        // Missing components
        if (data.bonus_tips.missing_components && data.bonus_tips.missing_components.length > 0) {
          formattedContent += "Missing Components:\n";
          data.bonus_tips.missing_components.forEach((component: string, index: number) => {
            formattedContent += `${index + 1}. ${component}\n`;
          });
          formattedContent += "\n";
        }

        // Additional connections
        if (data.bonus_tips.additional_connections && data.bonus_tips.additional_connections.length > 0) {
          formattedContent += "Additional Connections:\n";
          data.bonus_tips.additional_connections.forEach((connection: string, index: number) => {
            formattedContent += `${index + 1}. ${connection}\n`;
          });
          formattedContent += "\n";
        }

        // General tips
        if (data.bonus_tips.general_tips && data.bonus_tips.general_tips.length > 0) {
          formattedContent += "General Tips:\n";
          data.bonus_tips.general_tips.forEach((tip: string, index: number) => {
            formattedContent += `${index + 1}. ${tip}\n`;
          });
          formattedContent += "\n";
        }
      }

      // Handle strengths (from validate endpoint)
      if (data.strengths && data.strengths.length > 0) {
        formattedContent += "Strengths:\n";
        data.strengths.forEach((strength: string, index: number) => {
          formattedContent += `${index + 1}. ${strength}\n`;
        });
        formattedContent += "\n";
      }

      // Handle weaknesses (from validate endpoint)
      if (data.weaknesses && data.weaknesses.length > 0) {
        formattedContent += "Weaknesses:\n";
        data.weaknesses.forEach((weakness: string, index: number) => {
          formattedContent += `${index + 1}. ${weakness}\n`;
        });
        formattedContent += "\n";
      }

      // Handle suggestions (from validate endpoint)
      if (data.suggestions && data.suggestions.length > 0) {
        formattedContent += "Suggestions:\n";
        data.suggestions.forEach((suggestion: string, index: number) => {
          formattedContent += `${index + 1}. ${suggestion}\n`;
        });
        formattedContent += "\n";
      }

      // Handle questions (from validate endpoint)
      if (data.questions && data.questions.length > 0) {
        formattedContent += "Questions:\n";
        data.questions.forEach((question: string, index: number) => {
          formattedContent += `${index + 1}. ${question}\n`;
        });
        formattedContent += "\n";
      }

      // Handle rating (from validate endpoint)
      if (data.rating) {
        formattedContent += `Rating: ${data.rating}/10\n\n`;
      }

      // Handle scoring explanation (from validate endpoint)
      if (data.scoring_explanation) {
        formattedContent += `Scoring Explanation: ${data.scoring_explanation}\n`;
      }

      // Create assistant message from response
      const assistantMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: formattedContent || "I've analyzed your design but couldn't generate detailed feedback.",
        timestamp: new Date()
      };

      // Add assistant message to chat
      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error during design analysis:', error);
      toast.error('Failed to analyze design. Please try again.');

      // Add error message
      setChatMessages(prev => [...prev, {
        id: Date.now().toString(),
        role: 'assistant',
        content: "I'm sorry, there was an error analyzing your design. Please try again.",
        timestamp: new Date()
      }]);
    } finally {
      // Clear loading state
      setIsLoading(false);

      // Scroll to bottom
      setTimeout(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  };



  // Helper function to render validation response
  const renderValidationResponse = () => {
    if (!assessmentResult?.validationResponse) return null;

    const response = assessmentResult.validationResponse;
    const hasIssues = response.weaknesses && response.weaknesses.length > 0;

    return (
      <div className="mb-4 border border-gray-200 rounded-lg overflow-hidden">
        <div className="bg-white p-4">
          <h3 className="text-md font-semibold text-layrs-dark mb-2">Design Validation</h3>

          {/* Status section */}
          <div className="mb-2">
            <span className="text-sm font-medium">Status: </span>
            <span className={`text-sm font-semibold ${hasIssues ? 'text-orange-500' : 'text-green-500'}`}>
              {hasIssues ? 'Issues Found' : 'Valid'}
            </span>
          </div>

          {/* Overall assessment section */}
          {response.overall_assessment && (
            <CollapsibleSection title="Overall Assessment" defaultOpen={true}>
              <p className="text-sm text-gray-700">{response.overall_assessment}</p>
            </CollapsibleSection>
          )}

          {/* Strengths section */}
          {response.strengths && response.strengths.length > 0 && (
            <CollapsibleSection title="Strengths" titleColor="text-green-700" defaultOpen={true}>
              <ul className="list-disc pl-5 space-y-1">
                {response.strengths.map((strength, index) => (
                  <li key={index} className="text-green-600 text-sm">{strength}</li>
                ))}
              </ul>
            </CollapsibleSection>
          )}

          {/* Weaknesses section */}
          {response.weaknesses && response.weaknesses.length > 0 && (
            <CollapsibleSection title="Weaknesses" titleColor="text-red-700" defaultOpen={true}>
              <ul className="list-disc pl-5 space-y-1">
                {response.weaknesses.map((weakness, index) => (
                  <li key={index} className="text-red-600 text-sm">{weakness}</li>
                ))}
              </ul>
            </CollapsibleSection>
          )}

          {/* Suggestions section */}
          {response.suggestions && response.suggestions.length > 0 && (
            <CollapsibleSection title="Suggestions" titleColor="text-blue-700" defaultOpen={true}>
              <ul className="list-disc pl-5 space-y-2">
                {response.suggestions.map((suggestion, index) => (
                  <li key={index} className="text-blue-600 text-sm">{suggestion}</li>
                ))}
              </ul>
            </CollapsibleSection>
          )}

          {/* Questions section */}
          {response.questions && response.questions.length > 0 && (
            <CollapsibleSection title="Questions" titleColor="text-purple-700" defaultOpen={false}>
              <ul className="list-disc pl-5 space-y-1">
                {response.questions.map((question, index) => (
                  <li key={index} className="text-purple-600 text-sm">{question}</li>
                ))}
              </ul>
            </CollapsibleSection>
          )}

          {/* Scoring explanation */}
          {response.scoring_explanation && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <CollapsibleSection title="Scoring Explanation" titleColor="text-gray-700" defaultOpen={false}>
                <p className="text-sm text-gray-700">{response.scoring_explanation}</p>
              </CollapsibleSection>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAssessmentResult = () => {
    if (!assessmentResult) return null;

    // Create a tabbed interface for Assessment and Chat
    return (
      <div>
        <Tabs defaultValue="assessment" className="w-full" onValueChange={handleAssessmentTabChange}>
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="assessment">Assessment</TabsTrigger>
            <TabsTrigger value="assessment-chat">Chat</TabsTrigger>
          </TabsList>

          <TabsContent value="assessment" className="mt-0">
            <h2 className="text-lg font-semibold mb-4 text-layrs-dark">Assessment Result</h2>

            {/* Render validation response */}
            {renderValidationResponse()}

            {/* Display local validation warnings if present and no remote validation */}
            {!assessmentResult.validationResponse && assessmentResult.validationWarnings && assessmentResult.validationWarnings.length > 0 && (
              <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <h3 className="text-md font-medium text-amber-800 mb-2">Validation Warnings</h3>
                <ul className="list-disc pl-5 text-sm text-amber-700 space-y-1">
                  {assessmentResult.validationWarnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
                <p className="text-xs text-amber-600 mt-2">
                  These warnings may affect the accuracy of your assessment. Consider addressing them before proceeding.
                </p>
              </div>
            )}

            {/* Score rendering is handled by AssessmentView component */}
          </TabsContent>

          <TabsContent value="assessment-chat" className="mt-0">
            <div className="flex flex-col h-full">
              <h2 className="text-lg font-semibold mb-4 text-layrs-dark">Design Assistant</h2>

              {/* Chat messages */}
              <div className="flex-1 overflow-y-auto mb-4 bg-white border border-gray-200 rounded-lg p-3 min-h-[300px]">
                {chatMessages.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-400 text-sm">
                    <p>No messages yet. Start a conversation or analyze your design.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {chatMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-[80%] rounded-lg p-3 ${
                            message.role === 'user'
                              ? 'bg-blue-500 text-white'
                              : 'bg-gray-100 text-black'
                          }`}
                        >
                          <pre className="text-sm whitespace-pre-wrap font-sans">{message.content}</pre>
                          <p className="text-xs mt-1 opacity-70">
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </p>
                        </div>
                      </div>
                    ))}
                    <div ref={chatEndRef} />
                  </div>
                )}
              </div>

              {/* Input area */}
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Textarea
                    value={chatInput}
                    onChange={(e) => setChatInput(e.target.value)}
                    placeholder="Ask a question about your design..."
                    className="resize-none p-2 pr-10 min-h-[80px]"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    disabled={isLoading}
                  />
                  {isLoading && (
                    <div className="absolute right-3 bottom-3">
                      <Loader2 className="h-5 w-5 animate-spin text-gray-400" />
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={isLoading || !chatInput.trim()}
                  className="p-2 h-10 w-10"
                  variant="default"
                  title="Send message"
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  // If there's no assessment result and no selected component, show empty state
  if (!assessmentResult && !selectedComponent) {
    return (
      <div className="h-full bg-layrs-light border-l border-gray-200 p-4 flex items-center justify-center text-gray-400 text-sm">
        <p>Select a component to view and edit its metadata</p>
      </div>
    );
  }

  return (
    <div className="h-full bg-layrs-light border-l border-gray-200 p-4 overflow-y-auto">
      {(assessmentResult && selectedComponent) && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mb-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="node">Component</TabsTrigger>
            <TabsTrigger value="assessment">Assessment</TabsTrigger>
          </TabsList>
        </Tabs>
      )}

      {/* Show node metadata if there's no assessment result or if node tab is active */}
      {(activeTab === "node" || !assessmentResult) && renderNodeMetadata()}

      {/* Show assessment result if tab is active or if there's no selected node */}
      {(activeTab === "assessment" || !selectedComponent) && assessmentResult && renderAssessmentResult()}

      {/* Show appropriate action button based on the active tab */}
      {activeTab === "assessment-chat" ? (
        <Button
          className="mt-6 w-full"
          variant="light"
          onClick={handleAnalyzeDesign}
          disabled={isLoading}
        >
          {isLoading ? (
            <span className="flex items-center">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Analyzing...
            </span>
          ) : (
            <span className="flex items-center">
              <BarChart className="h-4 w-4 mr-2" />
              Analyze Design
            </span>
          )}
        </Button>
      ) : onStartAssessment && (
        <Button
          className="mt-6 w-full"
          variant="light"
          onClick={onStartAssessment}
        >
          {assessmentResult ? "Re-run Assessment" : "Run Assessment"}
        </Button>
      )}
    </div>
  );
};

export default ComponentMetadataSidebar;
