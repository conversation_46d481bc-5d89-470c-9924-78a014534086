import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Trash2, Edit3 } from 'lucide-react';

interface DeleteConnectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  edgeLabel?: string;
  onUpdateLabel?: (newLabel: string) => void;
  currentStyle?: string;
  onUpdateStyle?: (newStyle: string) => void;
  currentPathType?: string;
  onUpdatePathType?: (newPathType: string) => void;
}

const styleOptions = [
  { value: 'smooth', label: 'Normal' },
  { value: 'dashed-smooth', label: 'Dashed' },
  { value: 'thick-smooth', label: 'Thick' },
  { value: 'doubleArrow-smooth', label: 'Double Arrow' },
];

const pathTypeOptions = [
  { value: 'bezier', label: 'Bezier (Curved)' },
  { value: 'smooth', label: 'Smooth Step' },
];

const DeleteConnectionDialog: React.FC<DeleteConnectionDialogProps> = React.memo(({
  isOpen,
  onClose,
  onDelete,
  edgeLabel = '',
  onUpdateLabel,
  currentStyle,
  onUpdateStyle,
  currentPathType,
  onUpdatePathType
}) => {
  const [label, setLabel] = useState(edgeLabel);

  // Update local state when edgeLabel prop changes
  useEffect(() => {
    setLabel(edgeLabel);
  }, [edgeLabel]);

  const handleSaveLabel = () => {
    if (onUpdateLabel) {
      onUpdateLabel(label.trim());
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveLabel();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5 text-blue-500" />
            Edit Connection
          </DialogTitle>
          <DialogDescription>
            Edit the label for this connection, change its style, or delete it entirely.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="connection-label">Connection Label</Label>
            <Input
              id="connection-label"
              value={label}
              onChange={(e) => setLabel(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Enter connection label..."
              maxLength={50}
              className="w-full"
            />
            <p className="text-xs text-gray-500">
              Press Enter to save or click "Save Label" to update the connection
            </p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="connection-style">Connection Style</Label>
            <select
              id="connection-style"
              value={currentStyle || 'smooth'}
              onChange={e => onUpdateStyle && onUpdateStyle(e.target.value)}
              className="w-full border rounded px-2 py-1"
            >
              {styleOptions.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="connection-path-type">Path Type</Label>
            <select
              id="connection-path-type"
              value={currentPathType || 'smooth'}
              onChange={e => onUpdatePathType && onUpdatePathType(e.target.value)}
              className="w-full border rounded px-2 py-1"
            >
              {pathTypeOptions.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </select>
          </div>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <div className="flex gap-2">
            {onUpdateLabel && (
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleSaveLabel();
                }} 
                variant="secondary"
              >
                Save Label
              </Button>
            )}
            <Button variant="destructive" onClick={onDelete}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

DeleteConnectionDialog.displayName = 'DeleteConnectionDialog';

export default DeleteConnectionDialog; 