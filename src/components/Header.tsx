import React from 'react';
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import AuthNav from "@/components/AuthNav";
import AdminNavigation from "@/components/AdminNavigation";
import GuidedModeButton from './GuidedModeButton';

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuList
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { FileText, Home, User, Play, Trophy, RotateCcw, HelpCircle, Loader2, BarChart3 } from 'lucide-react';
import { isFeatureEnabled } from '@/config/featureFlags';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useState } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { exportToPNGFixed } from '@/utils/exportPNGFixed';
import { toast } from 'sonner';

export interface HeaderProps {
  onStartAssessment: () => void;
  isAssessing?: boolean;
  reactFlowInstance?: any;
  guidedMode?: boolean;
  saveStatusIndicator?: React.ReactNode;
  assessmentResult?: any;
  onViewAssessment?: () => void;
  userContext?: any;
  onLoadBestDesign?: () => void;
  hasBestDesign?: boolean;
  onShowTutorial?: () => void;
  onResetUserInputs?: () => void;
  onShowBestSubmission?: () => void;
  setResetting?: ((resetting: boolean) => void) | null;
}

const Header: React.FC<HeaderProps> = ({ onStartAssessment, isAssessing = false, reactFlowInstance, guidedMode = false, saveStatusIndicator, assessmentResult, onViewAssessment, userContext, onLoadBestDesign, hasBestDesign = false, onShowTutorial, onResetUserInputs, onShowBestSubmission, setResetting }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { questionId } = useParams<{ questionId: string }>();
  const { currentQuestion } = useQuestions(); // Remove getQuestionById since we shouldn't call it synchronously
  const [showResetDialog, setShowResetDialog] = useState(false);

  const isActive = (path: string) => location.pathname === path;

  // Direct navigation to home page
  const handleCanvasClick = () => {
    navigate('/home', { replace: true });
  };

  // Determine if we're in free canvas mode
  const isFreeCanvas = !questionId;

  // Determine if we're in canvas/design mode (where assessment tools should appear)
  // Only show assessment tools when actually in a design canvas, not on question lists or other pages
  const isCanvasMode = (questionId && location.pathname.startsWith('/design/')) ||
                       (isFreeCanvas && location.pathname === '/home') ||
                       location.pathname.startsWith('/guided/');

  // Get question details for header display (use current question set by the page)
  const question = currentQuestion;

  // Handle reset design
  const handleResetDesign = () => {
    if (reactFlowInstance) {
      // Mark this as a reset operation to prevent deletion protection from blocking it
      if (setResetting) {
        setResetting(true);
      }
      
      reactFlowInstance.setNodes([]);
      reactFlowInstance.setEdges([]);
      
      // Re-enable deletion protection after reset
      if (setResetting) {
        setTimeout(() => {
          setResetting(false);
        }, 100);
      }
    }
    if (onResetUserInputs) {
      onResetUserInputs();
    }
    setShowResetDialog(false);
  };

  const handleExportPNG = async () => {
    if (!reactFlowInstance) {
      toast.error("No diagram to export. Please add components to the canvas first.");
      return;
    }

    try {
      await exportToPNGFixed(reactFlowInstance);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export design');
    }
  };

  return (
    <header className="flex items-center px-4 py-1 bg-gradient-to-r from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b] border-b border-purple-500/20">
      {/* Left Section - Logo and Navigation */}
      <div className="flex items-center space-x-6">
        <Link to="/dashboard" className="flex items-center">
          <img src="/Layrs.jpeg" alt="Layrs Logo" className="w-8 h-8" />
          <span className="text-xl font-bold ml-2 text-[#EDE9FE]">Layrs</span>
        </Link>

        {/* Navigation Menu */}
        <NavigationMenu>
          <NavigationMenuList>
            {/* Canvas button - only show if free canvas is enabled */}
            {isFeatureEnabled('FREE_CANVAS') && (
              <NavigationMenuItem>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCanvasClick}
                  className={cn(
                    "gap-1 h-8 px-3 py-1 text-sm",
                    (isActive('/home') || isActive('/canvas'))
                      ? "bg-gradient-to-r from-blue-500/90 to-indigo-600/90 text-white border-none backdrop-blur-sm"
                      : "bg-gradient-to-r from-blue-500/70 to-indigo-600/70 hover:from-blue-500/90 hover:to-indigo-600/90 text-white border-none backdrop-blur-sm"
                  )}
                >
                  <Home className="h-4 w-4" />
                  Canvas
                </Button>
              </NavigationMenuItem>
            )}

            <NavigationMenuItem>
              <Link to="/questions">
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "gap-1 h-8 px-3 py-1 text-sm",
                    (isActive('/questions') || location.pathname.startsWith('/questions/'))
                      ? "bg-gradient-to-r from-indigo-500/90 to-purple-600/90 text-white border-none backdrop-blur-sm"
                      : "bg-gradient-to-r from-indigo-500/70 to-purple-600/70 hover:from-indigo-500/90 hover:to-purple-600/90 text-white border-none backdrop-blur-sm"
                  )}
                >
                  <FileText className="h-4 w-4" />
                  Questions
                </Button>
              </Link>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>

        {!guidedMode && isFeatureEnabled('GUIDED_COURSES') && <GuidedModeButton />}
      </div>

      {/* Center Section - Question Name, Run Assessment, Helper Dropdown, Save Button */}
      <div className="flex-1 flex items-center justify-center gap-3">
        {/* Question Name / Page Context */}
        <div className="flex items-center gap-2">
          {isActive('/questions') ? (
            <>
              <FileText className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white">Questions</span>
            </>
          ) : isActive('/profile') ? (
            <>
              <User className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white">Profile</span>
            </>
          ) : location.pathname.startsWith('/guided') ? (
            <>
              <FileText className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white">Guided Course</span>
            </>
          ) : location.pathname.startsWith('/admin') ? (
            <>
              <FileText className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white">Admin</span>
            </>
          ) : question && typeof question === 'object' && 'title' in question ? (
            <>
              <FileText className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white max-w-[200px] truncate" title={question.title}>
                {question.title}
              </span>
            </>
          ) : isFreeCanvas && isFeatureEnabled('FREE_CANVAS') ? (
            <>
              <Home className="h-4 w-4 text-[#C4B5FD]" />
              <span className="text-sm font-medium text-white">Free Canvas</span>
            </>
          ) : null}
        </div>

        {/* Separator - Only show in canvas mode */}
        {isCanvasMode && (
          <div className="text-white/40 text-sm">|</div>
        )}

        {/* Run Assessment Button - Only show in canvas mode */}
        {isCanvasMode && (
          <Button
            onClick={onStartAssessment}
            disabled={isAssessing}
            variant="outline"
            size="sm"
            className="h-8 px-4 py-1 flex items-center gap-2 text-sm bg-gradient-to-r from-purple-500/90 to-indigo-600/90 hover:from-purple-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
          >
            {isAssessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            <span>{isAssessing ? "Running..." : "Run Assessment"}</span>
          </Button>
        )}

        {/* Export PNG Button - Only show in canvas mode */}
        {/* {isCanvasMode && (
          <Button
            onClick={handleExportPNG}
            variant="lightCTA"
            size="sm"
            className="h-8 px-4 py-1 flex items-center gap-2 text-sm bg-gradient-to-r from-blue-500/90 to-cyan-600/90 hover:from-blue-600/90 hover:to-cyan-700/90 text-white border-none backdrop-blur-sm"
          >
            <Download className="h-4 w-4" />
            <span>Export PNG</span>
          </Button>
        )} */}

        {/* View Assessment Button - Only show if assessmentResult exists */}
        {isCanvasMode && assessmentResult && onViewAssessment && (
          <Button
            onClick={onViewAssessment}
            variant="outline"
            size="sm"
            className="h-8 px-4 py-1 flex items-center gap-2 text-sm bg-gradient-to-r from-green-500/90 to-emerald-600/90 hover:from-green-600/90 hover:to-emerald-700/90 text-white border-none backdrop-blur-sm"
          >
            <BarChart3 className="h-4 w-4" />
            <span>View Assessment</span>
          </Button>
        )}

        {/* Reset Design Button - Only show in canvas mode */}
        {isCanvasMode && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowResetDialog(true)}
            className="h-8 px-3 py-1 flex items-center gap-2 text-sm bg-white/10 hover:bg-white/20 text-white border-white/20 hover:border-white/30 backdrop-blur-sm"
          >
            <RotateCcw className="h-4 w-4" />
            <span>Reset</span>
          </Button>
        )}

        {/* My Best Design Button - Only show in canvas mode */}
        {isCanvasMode && hasBestDesign && onShowBestSubmission && (
          <Button
            variant="outline"
            size="sm"
            onClick={onShowBestSubmission}
            className="h-8 px-3 py-1 flex items-center gap-2 text-sm bg-white/10 hover:bg-white/20 text-white border-white/20 hover:border-white/30 backdrop-blur-sm"
          >
            <Trophy className="h-4 w-4" />
            <span>Best Design</span>
          </Button>
        )}

        {/* Help Button - Only show in canvas mode */}
        {isCanvasMode && onShowTutorial && (
          <Button
            variant="outline"
            size="sm"
            onClick={onShowTutorial}
            className="h-8 px-3 py-1 flex items-center gap-2 text-sm bg-white/10 hover:bg-white/20 text-white border-white/20 hover:border-white/30 backdrop-blur-sm"
          >
            <HelpCircle className="h-4 w-4" />
            <span>Help</span>
          </Button>
        )}

        {/* Save Button with Status - Only show in canvas mode */}
        {isCanvasMode && saveStatusIndicator && (
          <div className="flex items-center">
            {saveStatusIndicator}
          </div>
        )}
      </div>

      {/* Right Section - Admin and User */}
      <div className="flex items-center gap-2">
        <AdminNavigation />
        <AuthNav />
      </div>

      {/* Reset Design Confirmation Dialog */}
      <AlertDialog open={showResetDialog} onOpenChange={setShowResetDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset Design</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to reset the design? This will clear all components, connections, and user inputs. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleResetDesign} className="bg-red-600 hover:bg-red-700">
              Reset Design
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </header>
  );
};

export default Header;
