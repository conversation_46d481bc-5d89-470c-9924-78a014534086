import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Question } from '@/types/Question';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, AlertCircle, Ban, Loader2 } from 'lucide-react';

/**
 * Test component to verify FR/NFR column functionality
 * This component can be temporarily added to test the new database structure
 */
const FRNFRTest: React.FC = () => {
  const [question, setQuestion] = useState<Question | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<any>(null);

  const loadTestQuestion = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error: queryError } = await supabase
        .from('questions')
        .select('*')
        .eq('id', '11')
        .single();
      
      if (queryError) {
        setError(`Query error: ${queryError.message}`);
        return;
      }
      
      setQuestion(data);
      
      // Analyze the data structure
      const analysis = {
        hasLegacyRequirements: !!data.requirements && data.requirements.length > 0,
        hasFunctionalRequirements: !!data.functional_requirements && data.functional_requirements.length > 0,
        hasNonFunctionalRequirements: !!data.non_functional_requirements && data.non_functional_requirements.length > 0,
        hasConstraints: !!data.constraints && data.constraints.length > 0,
        legacyCount: data.requirements?.length || 0,
        frCount: data.functional_requirements?.length || 0,
        nfrCount: data.non_functional_requirements?.length || 0,
        constraintsCount: data.constraints?.length || 0
      };
      
      setTestResults(analysis);
      
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const updateTestQuestion = async () => {
    if (!question) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const { error: updateError } = await supabase
        .from('questions')
        .update({
          functional_requirements: [
            'Publish tweets with text, images, or videos',
            'Follow/unfollow users',
            'Display a chronological or ranked feed',
            'Add likes, retweets, and replies'
          ],
          non_functional_requirements: [
            'Load feed in < 500ms',
            'Handle 100k+ tweets per second',
            '99.95% availability'
          ],
          constraints: [
            'Fan-out writes: Push tweets to all followers\' timelines',
            'Caching: Use Redis for hot data (e.g., trending tweets)',
            'Load balancing: Distribute traffic across multiple servers',
            'Data denormalization: Store redundant data for faster reads'
          ]
        })
        .eq('id', '11');
      
      if (updateError) {
        setError(`Update error: ${updateError.message}`);
        return;
      }
      
      // Reload the question to see the changes
      await loadTestQuestion();
      
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTestQuestion();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>FR/NFR Database Test</CardTitle>
          <CardDescription>
            Testing the new functional_requirements and non_functional_requirements columns
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={loadTestQuestion} disabled={loading}>
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Reload Question
            </Button>
            <Button onClick={updateTestQuestion} disabled={loading || !question}>
              Update with FR/NFR
            </Button>
          </div>
          
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle>Database Structure Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {testResults.hasLegacyRequirements ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  }
                  <span>Legacy Requirements: {testResults.legacyCount} items</span>
                </div>
                <div className="flex items-center gap-2">
                  {testResults.hasFunctionalRequirements ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  }
                  <span>Functional Requirements: {testResults.frCount} items</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {testResults.hasNonFunctionalRequirements ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  }
                  <span>Non-Functional Requirements: {testResults.nfrCount} items</span>
                </div>
                <div className="flex items-center gap-2">
                  {testResults.hasConstraints ? 
                    <CheckCircle className="h-4 w-4 text-green-500" /> : 
                    <AlertCircle className="h-4 w-4 text-yellow-500" />
                  }
                  <span>Constraints: {testResults.constraintsCount} items</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {question && (
        <div className="space-y-4">
          {/* Functional Requirements */}
          {question.functional_requirements && question.functional_requirements.length > 0 && (
            <Card className="border-green-200">
              <CardHeader>
                <CardTitle className="text-md flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Functional Requirements (New Column)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 pl-4 list-disc text-sm">
                  {question.functional_requirements.map((req, index) => (
                    <li key={`fr-${index}`} className="text-gray-700">{req}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Non-Functional Requirements */}
          {question.non_functional_requirements && question.non_functional_requirements.length > 0 && (
            <Card className="border-blue-200">
              <CardHeader>
                <CardTitle className="text-md flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                  Non-Functional Requirements (New Column)
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 pl-4 list-disc text-sm">
                  {question.non_functional_requirements.map((req, index) => (
                    <li key={`nfr-${index}`} className="text-gray-700">{req}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Constraints */}
          {question.constraints && question.constraints.length > 0 && (
            <Card className="border-red-200">
              <CardHeader>
                <CardTitle className="text-md flex items-center gap-2">
                  <Ban className="h-4 w-4 text-red-600" />
                  Technical Constraints
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 pl-4 list-disc text-sm">
                  {question.constraints.map((constraint, index) => (
                    <li key={`constraint-${index}`} className="text-gray-700">{constraint}</li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default FRNFRTest;
