
import React from 'react';
import { useCourse } from '@/contexts/CourseContext';
import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';

const LessonStepOverlay: React.FC = () => {
  const { getCurrentStep, goToNextStep, markCurrentStepComplete } = useCourse();
  const currentStep = getCurrentStep();

  if (!currentStep) return null;

  // For informational steps (like step 2), show a simpler UI
  if (currentStep.isInformationalOnly) {
    return (
      <div className="bg-white border rounded-md p-4 mb-4 text-left max-h-[80vh] overflow-y-auto">
        <div className="flex items-center gap-2 mb-2 text-blue-600">
          <div className="h-5 w-5">🔍</div>
          <h3 className="font-medium">Learning Content</h3>
        </div>
        
        <div className="mb-4 text-sm text-gray-700 whitespace-pre-line">
          {currentStep.description}
        </div>
        
        <Button 
          onClick={() => {
            markCurrentStepComplete();
            goToNextStep();
          }}
          className="w-full mt-2 flex items-center justify-center"
        >
          Continue to Next Step
          <ChevronRight className="h-4 w-4 ml-1" />
        </Button>
      </div>
    );
  }

  // For interactive steps, show the regular UI with detailed instructions
  return (
    <div className="bg-white border rounded-md p-4 mb-4">
      <h3 className="font-medium mb-2">{currentStep.title}</h3>
      <p className="text-sm text-gray-700 mb-4 whitespace-pre-line">{currentStep.description}</p>
      
      {currentStep.hints && currentStep.hints.length > 0 && (
        <div className="mt-2">
          <h4 className="text-sm font-medium mb-1">Hints:</h4>
          <ul className="text-xs text-gray-600 list-disc pl-4">
            {currentStep.hints.map((hint, index) => (
              <li key={index}>{hint}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default LessonStepOverlay;
