
import React, { useEffect, useState } from 'react';
import { useCourse } from '@/contexts/CourseContext';
import { Node } from '@xyflow/react';
import { toast } from 'sonner';

interface CourseVisualHintsProps {
  nodes: Node[];
}

const CourseVisualHints: React.FC<CourseVisualHintsProps> = ({ nodes }) => {
  const { isGuidedMode, getCurrentStep } = useCourse();
  const [showHints, setShowHints] = useState<boolean>(false);
  const currentStep = getCurrentStep();

  useEffect(() => {
    if (!isGuidedMode || !currentStep || !showHints) return;

    // Check which components need to be added
    const missingComponents = currentStep.goal.components.filter(
      compType => !nodes.some(node => node.data?.type === compType)
    );

    if (missingComponents.length > 0) {
      // Visual hint for missing components
      toast.info(
        `Hint: Try adding ${missingComponents.map(c => c.replace('_', ' ')).join(' and ')}`,
        {
          id: 'missing-components-hint',
          duration: 5000
        }
      );
    }
  }, [isGuidedMode, currentStep, nodes, showHints]);

  // Public method to trigger hints
  const displayHints = () => {
    setShowHints(true);
    setTimeout(() => setShowHints(false), 5000); // Reset after 5 seconds
  };

  // We'll expose the displayHints method via a ref in the parent component
  return null; // This component doesn't render anything visible
};

export const useVisualHints = () => {
  const hintsRef = React.useRef<{ displayHints: () => void } | null>(null);
  
  const showHints = () => {
    if (hintsRef.current) {
      hintsRef.current.displayHints();
    }
  };

  return { hintsRef, showHints };
};

export default CourseVisualHints;
