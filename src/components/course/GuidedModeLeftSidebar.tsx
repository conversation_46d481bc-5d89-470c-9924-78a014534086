import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, BookOpen, Component, MessageSquare } from 'lucide-react';
import CourseOutlineSidebar from './CourseOutlineSidebar';
import ComponentPalette from '../ComponentPalette';
import ChatPanel from '../ChatPanel';
import { useCourse } from '@/contexts/CourseContext';
import { ReactFlowInstance } from '@xyflow/react';

interface GuidedModeLeftSidebarProps {
  onExitGuidedMode: () => void;
  currentStepId?: string;
  filterComponents?: string[];
  reactFlowInstance?: ReactFlowInstance | null;
  onActiveTabChange?: (tab: string) => void;
}

const GuidedModeLeftSidebar: React.FC<GuidedModeLeftSidebarProps> = ({
  onExitGuidedMode,
  currentStepId,
  filterComponents,
  reactFlowInstance,
  onActiveTabChange
}) => {
  const [activeTab, setActiveTab] = useState<string>("course");
  const { currentCourse, getCurrentStep } = useCourse();
  const currentStep = getCurrentStep();

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    onActiveTabChange?.(tab);
  };

  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Header with Exit Button and Course Title */}
      <div className="p-4 border-b">
        <Button
          variant="ghost"
          size="sm"
          onClick={onExitGuidedMode}
          className="flex items-center"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Exit Course
        </Button>

        {/* Course Title Heading */}
        {currentCourse && (
          <h2 className="text-xl font-bold mt-4 text-gray-800">
            {currentCourse.title}
          </h2>
        )}
      </div>

      {/* Tabbed Content */}
      <Tabs
        defaultValue="course"
        value={activeTab}
        onValueChange={handleTabChange}
        className="flex flex-col flex-1 overflow-hidden"
      >
        <TabsList className="w-full grid grid-cols-3 mx-4 mt-2 mb-2">
          <TabsTrigger value="course" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Course
          </TabsTrigger>
          <TabsTrigger value="components" className="flex items-center gap-2">
            <Component className="h-4 w-4" />
            Components
          </TabsTrigger>
          <TabsTrigger value="chat" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Chat
          </TabsTrigger>
        </TabsList>

        <TabsContent value="course" className="mt-0 flex-1 overflow-y-auto">
          <CourseOutlineSidebar />
        </TabsContent>

        <TabsContent value="components" className="mt-0 flex-1 overflow-y-auto p-4">
          <ComponentPalette
            filterComponents={filterComponents}
            currentStepId={currentStepId}
          />
        </TabsContent>

        <TabsContent value="chat" className="mt-0 flex-1 overflow-hidden">
          <div className="h-full flex flex-col">
            <ChatPanel
              reactFlowInstance={reactFlowInstance}
              contextType="course"
              contextId={currentCourse?.id || 'course-selection'}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default GuidedModeLeftSidebar;
