import React from 'react';
import { useCourse } from '@/contexts/CourseContext';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon, ArrowRightIcon, CheckCircle, CircleIcon, InfoIcon, DownloadIcon, FileQuestion, BookOpenIcon } from 'lucide-react';
import TrafficSimulator from './TrafficSimulator';
import { useReactFlow } from '@xyflow/react';
import { toast } from 'sonner';
import ExportFormatDialog from '@/components/ExportFormatDialog';
import ReferenceArchitecture from '@/components/ReferenceArchitecture';
import TwitterReferenceArchitecture from '@/components/TwitterReferenceArchitecture';
import URLShortenerReferenceArchitecture from '@/components/URLShortenerReferenceArchitecture';
import EcommerceReferenceArchitecture from '@/components/EcommerceReferenceArchitecture';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface CourseStepsPanelProps {
  onStartAssessment?: () => void;
}

const CourseStepsPanel: React.FC<CourseStepsPanelProps> = ({ onStartAssessment }) => {
  const { currentCourse, getCurrentStep, goToPreviousStep, goToNextStep, checkStepCompletion, markCurrentStepComplete } = useCourse();
  const currentStep = getCurrentStep();
  const { getNodes, getEdges } = useReactFlow();

  // Get the nodes and edges for step completion checking
  const nodes = getNodes();
  const edges = getEdges();

  // Call checkStepCompletion with both nodes and edges
  const isCompleted = currentStep ? checkStepCompletion(nodes, edges) : false;

  if (!currentCourse || !currentStep) return null;

  // Special handling for different step types
  const isSimulationStep = currentStep.id === 'simulate-traffic' || currentStep.id === 'final-simulation';
  const simulationRequirements = isSimulationStep && currentStep.goal.simulation?.customProperties;
  const configuredNodes = getNodes();

  // Special handling for Step 7 (queue step)
  const isQueueStep = currentStep.id === 'add-queue';

  // Special handling for Step 13 (export step)
  const isExportStep = currentStep.id === 'export-system';

  const handleExportComplete = () => {
    // Mark the export step as completed after export
    if (isExportStep && !currentStep.completed) {
      markCurrentStepComplete();
      toast.success("System design exported successfully!", {
        description: "You've completed the final step of the course. Well done!"
      });
    }
  };

  // Create a ReactFlow instance for export
  const getReactFlowInstance = () => {
    return {
      getNodes: () => getNodes(),
      getEdges: () => getEdges(),
      fitView: () => {
        debugLog("Mock fitView called");
        return true;
      }
    };
  };

  return (
    <div className="p-4 rounded-lg shadow-md bg-white border min-w-[300px] max-w-[500px] max-h-[90vh] overflow-y-auto">
      {/* Course Title */}
      <div className="mb-3 pb-2 border-b border-gray-100">
        <h2 className="text-lg font-bold text-gray-800">
          {currentCourse.title}
        </h2>
      </div>

      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">{currentStep.title}</h3>
        <span className="flex items-center">
          {currentStep.completed ? (
            <CheckCircle className="h-5 w-5 text-green-500 mr-1" />
          ) : (
            <CircleIcon className="h-5 w-5 text-gray-300 mr-1" />
          )}
          Step {currentCourse.currentStepIndex + 1}/{currentCourse.steps.length}
        </span>
      </div>

      <div className="mb-4 whitespace-pre-line break-words overflow-wrap-anywhere">{currentStep.description}</div>

      {currentStep.explanation && (
        <div className="mb-4 text-sm text-gray-600 break-words overflow-wrap-anywhere">{currentStep.explanation}</div>
      )}

      {/* Display hints if there are any */}
      {currentStep.hints && currentStep.hints.length > 0 && (
        <div className="mb-4">
          <h4 className="font-medium text-sm mb-2">Hints:</h4>
          <ul className="list-disc list-inside text-sm pl-2 space-y-1">
            {currentStep.hints.map((hint, index) => (
              <li key={index} className="text-gray-700 break-words overflow-wrap-anywhere">{hint}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Add special note for simulation step */}
      {isSimulationStep && simulationRequirements && !currentStep.completed && (
        <div className="mb-4 flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <InfoIcon className="h-5 w-5 text-blue-500 mt-0.5 shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Important: Configure component capacity first</p>
            <p>Before running the simulation, set the <code className="bg-blue-100 px-1 py-0.5 rounded">maxQPS</code> custom property on both the Auth Service and Media Service components.</p>
            <p className="mt-1">Use the component inspector panel on the right when selecting each component.</p>
          </div>
        </div>
      )}

      {/* Special note for queue step */}
      {isQueueStep && (
        <div className="mb-4 flex items-start gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <InfoIcon className="h-5 w-5 text-blue-500 mt-0.5 shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Architecture Improvement</p>
            <p>Remove the direct connection between Auth Service and Media Service, then connect them through a Queue to buffer excess requests.</p>
          </div>
        </div>
      )}

      {/* Export Button for the export system step */}
      {isExportStep && (
        <div className="my-6 space-y-4">
          <ExportFormatDialog
            reactFlowInstance={getReactFlowInstance()}
            onExportComplete={handleExportComplete}
            trigger={
              <Button
                className="w-full flex items-center justify-center gap-2"
              >
                <DownloadIcon className="h-4 w-4" />
                Export Design
              </Button>
            }
          />
          <p className="text-xs text-gray-500 mt-2 text-center">
            Choose your preferred format to export your system design
          </p>

          {currentCourse.id === 'design-instagram' ? (
            <>
              <ReferenceArchitecture
                trigger={
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2 mt-4"
                  >
                    <BookOpenIcon className="h-4 w-4" />
                    View Instagram Reference Architecture
                  </Button>
                }
              />
              <p className="text-xs text-gray-500 mt-2 text-center">
                Compare your design with Instagram's actual architecture
              </p>
            </>
          ) : currentCourse.id === 'twitter-architecture' ? (
            <>
              <TwitterReferenceArchitecture
                trigger={
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2 mt-4"
                  >
                    <BookOpenIcon className="h-4 w-4" />
                    View Twitter Reference Architecture
                  </Button>
                }
              />
              <p className="text-xs text-gray-500 mt-2 text-center">
                Compare your design with Twitter's actual architecture
              </p>
            </>
          ) : currentCourse.id === 'url-shortener' ? (
            <>
              <URLShortenerReferenceArchitecture
                trigger={
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2 mt-4"
                  >
                    <BookOpenIcon className="h-4 w-4" />
                    View URL Shortener Reference Architecture
                  </Button>
                }
              />
              <p className="text-xs text-gray-500 mt-2 text-center">
                Compare your design with production-grade URL shorteners
              </p>
            </>
          ) : currentCourse.id === 'ecommerce-platform' ? (
            <>
              <EcommerceReferenceArchitecture
                trigger={
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center gap-2 mt-4"
                  >
                    <BookOpenIcon className="h-4 w-4" />
                    View E-commerce Reference Architecture
                  </Button>
                }
              />
              <p className="text-xs text-gray-500 mt-2 text-center">
                Compare your design with production-grade e-commerce platforms
              </p>
            </>
          ) : null}

          {onStartAssessment && (
            <>
              <Button
                onClick={onStartAssessment}
                className="w-full flex items-center justify-center gap-2 mt-4"
                variant="default"
              >
                <FileQuestion className="h-4 w-4" />
                Start Assessment
              </Button>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Get feedback on your system design
              </p>
            </>
          )}
        </div>
      )}

      {/* Show traffic simulator for simulation steps and queue step */}
      {(isSimulationStep || currentStep.id === 'add-queue') && (
        <div className="my-4">
          <TrafficSimulator nodes={getNodes()} edges={getEdges()} />
        </div>
      )}

      <div className="flex justify-between mt-6">
        <Button
          variant="outline"
          size="sm"
          onClick={goToPreviousStep}
          disabled={currentCourse.currentStepIndex === 0}
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Previous
        </Button>

        <Button
          size="sm"
          onClick={goToNextStep}
          disabled={currentCourse.currentStepIndex === currentCourse.steps.length - 1 || !isCompleted}
        >
          Next
          <ArrowRightIcon className="h-4 w-4 ml-1" />
        </Button>
      </div>
    </div>
  );
};

export default CourseStepsPanel;
