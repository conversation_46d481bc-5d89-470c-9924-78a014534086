
import React from 'react';
import { useCourse } from '@/contexts/CourseContext';
import { Check, ChevronRight, BarChart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';

const CourseOutlineSidebar: React.FC = () => {
  const { currentCourse, getCourseProgress } = useCourse();

  if (!currentCourse) {
    return null;
  }

  // Get the current progress percentage
  const progressPercentage = getCourseProgress();
  const completedSteps = currentCourse.steps.filter(step => step.completed).length;
  const totalSteps = currentCourse.steps.length;

  return (
    <div className="h-full bg-gray-50 border-r p-4 overflow-y-auto">
      {/* Course Progress */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-bold">Course Outline</h3>
          <div className="flex items-center text-sm text-gray-600">
            <BarChart className="h-4 w-4 mr-1" />
            <span>{progressPercentage}% Complete</span>
          </div>
        </div>

        <Progress value={progressPercentage} className="h-2" />

        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{completedSteps}/{totalSteps} steps</span>
          <span>Step {currentCourse.currentStepIndex + 1} of {totalSteps}</span>
        </div>
      </div>

      <div className="space-y-1">
        {currentCourse.steps.map((step, index) => {
          const isCurrent = index === currentCourse.currentStepIndex;
          const isCompleted = step.completed;

          return (
            <div
              key={step.id}
              className={cn(
                "flex items-center p-2 rounded-md",
                isCurrent ? "bg-gray-200" : "",
                isCompleted ? "text-gray-700" : "text-gray-500"
              )}
            >
              <div className="w-6 h-6 mr-2 flex items-center justify-center">
                {isCompleted ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <span className="w-4 h-4 rounded-full border flex items-center justify-center text-xs">
                    {index + 1}
                  </span>
                )}
              </div>
              <span className={cn(
                "text-sm",
                isCurrent ? "font-medium" : ""
              )}>
                {step.title}
              </span>
              {isCurrent && (
                <ChevronRight className="h-4 w-4 ml-auto" />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CourseOutlineSidebar;
