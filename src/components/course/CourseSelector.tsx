import React from 'react';
import { useCourse } from '@/contexts/CourseContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock, Bar<PERSON>hart } from 'lucide-react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const CourseSelector: React.FC = () => {
  const { courses, startCourse, isGuidedMode } = useCourse();
  const navigate = useNavigate();

  if (isGuidedMode) {
    return null; // Don't show course selection when already in guided mode
  }

  const handleStartCourse = (courseId: string) => {
    debugLog('Starting course:', courseId);
    startCourse(courseId);
    navigate(`/guided/${courseId}`);
    toast.success('Course started successfully');
  };

  return (
    <div className="p-6 space-y-6">
      <div className="text-center max-w-2xl mx-auto mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Guided Courses</h1>
        <p className="text-gray-600 mt-2">
          Learn system design step-by-step with our interactive guided courses
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {courses.map((course) => (
          <Card key={course.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-blue-50">
              <CardTitle>{course.title}</CardTitle>
              <CardDescription>{course.description}</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>{course.estimatedTime}</span>
                </div>
                <div className="flex items-center">
                  <BarChart className="h-4 w-4 mr-1" />
                  <span className="capitalize">{course.difficulty}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <Button 
                className="w-full" 
                onClick={() => handleStartCourse(course.id)}
              >
                Start Course
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CourseSelector;
