import React, { useState, useEffect } from 'react';
import { Node, Edge } from '@xyflow/react';
import { useCourse } from '@/contexts/CourseContext';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { SimulationRequirement } from '@/types/course';
import { PlayIcon, CircleStopIcon, AlertTriangleIcon, InfoIcon, ChartBarIcon, LayersIcon } from 'lucide-react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface TrafficSimulatorProps {
  nodes: Node[];
  edges: Edge[];
}

interface SimulationConfig {
  qps: number;
  duration: number;
  payload: Record<string, any>;
}

interface ComponentStatus {
  id: string;
  label: string;
  type: string;
  maxQPS: number;
  currentQPS: number;
  droppedRequests: number;
  queueSize?: number;
  maxQueueSize?: number;
}

const TrafficSimulator: React.FC<TrafficSimulatorProps> = ({ nodes, edges }) => {
  const { getCurrentStep, markCurrentStepComplete } = useCourse();
  const currentStep = getCurrentStep();
  const simulationRequirements = currentStep?.goal.simulation;

  const [simulationConfig, setSimulationConfig] = useState<SimulationConfig>({
    qps: simulationRequirements?.minQPS || 15,
    duration: simulationRequirements?.minDuration || 5,
    payload: simulationRequirements?.payload || { userId: 'abc123', action: 'upload_photo' }
  });

  const [isSimulating, setIsSimulating] = useState(false);
  const [payloadStr, setPayloadStr] = useState(JSON.stringify(simulationConfig.payload, null, 2));
  const [simulationComplete, setSimulationComplete] = useState(false);
  const [simulationSuccess, setSimulationSuccess] = useState(false);
  const [canSimulate, setCanSimulate] = useState(false);
  const [componentStatuses, setComponentStatuses] = useState<ComponentStatus[]>([]);
  const [missingConfigurations, setMissingConfigurations] = useState<{component: string, property: string}[]>([]);

  // Check if the system is ready for simulation
  useEffect(() => {
    const frontend = nodes.find(node =>
      node.data?.type === 'frontend' && !node.parentId
    );

    const apiGateway = nodes.find(node =>
      node.data?.type === 'apigateway' && !node.parentId
    );

    const authService = nodes.find(node =>
      node.type === 'compositeNode' &&
      node.data?.label === 'Authentication Service'
    );

    const mediaService = nodes.find(node =>
      node.data?.type === 'server' &&
      typeof node.data?.label === 'string' &&
      node.data?.label.includes('Media Service') &&
      !node.parentId
    );

    // Check for required connections - following the correct architecture
    const frontendToApiGateway = edges.some(edge =>
      (edge.source === frontend?.id && edge.target === apiGateway?.id) ||
      (edge.source === apiGateway?.id && edge.target === frontend?.id)
    );

    const apiGatewayToAuth = edges.some(edge =>
      (edge.source === apiGateway?.id && edge.target === authService?.id) ||
      (edge.source === authService?.id && edge.target === apiGateway?.id)
    );

    // Find if there's a queue between Auth and Media
    const queueNode = nodes.find(node =>
      node.data?.type === 'queue' && !node.parentId
    );

    // Check for Auth -> Queue connection
    const authToQueue = queueNode && edges.some(edge =>
      (edge.source === authService?.id && edge.target === queueNode.id) ||
      (edge.source === queueNode.id && edge.target === authService?.id)
    );

    // Check for Queue -> Media connection
    const queueToMedia = queueNode && edges.some(edge =>
      (edge.source === queueNode.id && edge.target === mediaService?.id) ||
      (edge.source === mediaService?.id && edge.target === queueNode.id)
    );

    const hasQueuePath = !!authToQueue && !!queueToMedia;

    // Direct connection (used in earlier steps)
    const directAuthToMedia = edges.some(edge =>
      (edge.source === authService?.id && edge.target === mediaService?.id) ||
      (edge.source === mediaService?.id && edge.target === authService?.id)
    );

    // Connection is valid if either direct or via queue
    const authToMediaValid = directAuthToMedia || hasQueuePath;

    // Check component configurations based on current step
    const missingConfigs: {component: string, property: string}[] = [];

    // We don't need to check for advanced components in the basic simulation

    const feedGenerator = nodes.find(node =>
      node.data?.type === 'server' &&
      node.id !== mediaService?.id &&
      !node.parentId
    );

    // Collect all components that might need QPS settings
    const servicesForQPSSettings = [
      { node: authService, name: 'Authentication Service', required: true },
      { node: mediaService, name: 'Media Service', required: true },
      { node: feedGenerator, name: 'Feed Generator', required: false }
    ].filter(item => !!item.node);

    // Check if required services have maxQPS set
    servicesForQPSSettings.forEach(service => {
      if (service.node) {
        const data = service.node.data as any;
        const customProps = data.metadata?.customProperties || [];
        const maxQPSProp = customProps.find((prop: any) =>
          prop.key.toLowerCase() === 'maxqps'
        );

        // Only add to missing configs if this is a required service
        if (!maxQPSProp && service.required) {
          missingConfigs.push({ component: service.name, property: 'maxQPS' });
        }
      }
    });

    // Set missing configurations
    setMissingConfigurations(missingConfigs);

    // For the final system test (step 12), require more components
    if (currentStep?.id === 'final-simulation') {
      const hasRequiredComponents =
        !!frontend &&
        !!authService &&
        !!mediaService;

      // For final simulation we need at least the core components configured with proper connections
      const hasRequiredConnections =
        frontendToApiGateway &&
        apiGatewayToAuth &&
        (hasQueuePath || directAuthToMedia);

      // Check if required services have maxQPS set
      const hasRequiredQPSSettings = servicesForQPSSettings
        .filter(service => service.required) // Only check required services
        .every(service => {
          if (service.node) {
            const data = service.node.data as any;
            const customProps = data.metadata?.customProperties || [];
            return customProps.some((prop: any) =>
              prop.key.toLowerCase() === 'maxqps' && prop.value
            );
          }
          return false;
        });

      debugLog("Final simulation requirements check:", {
        hasRequiredComponents,
        hasRequiredConnections,
        hasRequiredQPSSettings,
        missingConfigs
      });

      setCanSimulate(hasRequiredComponents && hasRequiredConnections && hasRequiredQPSSettings);

      // Mark step as complete if simulation has been run successfully
      if (simulationComplete && hasRequiredComponents && hasRequiredConnections && hasRequiredQPSSettings) {
        if (currentStep && !currentStep.completed) {
          markCurrentStepComplete();
        }
      }
    } else {
      // For earlier steps
      const hasRequiredComponents = !!frontend && !!apiGateway && !!authService && !!mediaService;
      const hasRequiredConnections = frontendToApiGateway && apiGatewayToAuth && (directAuthToMedia || hasQueuePath);

      setCanSimulate(hasRequiredComponents && hasRequiredConnections && missingConfigs.length === 0);
    }
  }, [nodes, edges, currentStep]);

  // Run simulation
  const runSimulation = async () => {
    if (!canSimulate) return;

    try {
      setIsSimulating(true);
      setSimulationComplete(false);
      setComponentStatuses([]);

      // Find relevant components
      const authService = nodes.find(node =>
        node.type === 'compositeNode' &&
        node.data?.label === 'Authentication Service'
      );

      const mediaService = nodes.find(node =>
        node.data?.type === 'server' &&
        typeof node.data?.label === 'string' &&
        node.data?.label.includes('Media Service')
      );

      // Find if there's a queue between Auth and Media
      const queueNode = nodes.find(node =>
        node.data?.type === 'queue' && !node.parentId
      );

      // Check for Auth -> Queue connection
      const authToQueue = queueNode && edges.some(edge =>
        (edge.source === authService?.id && edge.target === queueNode.id) ||
        (edge.source === queueNode.id && edge.target === authService?.id)
      );

      // Check for Queue -> Media connection
      const queueToMedia = queueNode && edges.some(edge =>
        (edge.source === queueNode.id && edge.target === mediaService?.id) ||
        (edge.source === mediaService?.id && edge.target === queueNode.id)
      );

      // Check if there's a complete queue path between Auth and Media
      const hasQueueBetweenAuthAndMedia = !!authToQueue && !!queueToMedia;

      // We don't need to check for advanced components in the basic simulation

      // Get maxQPS values from components
      const authMaxQPS = getMaxQPS(authService);
      const mediaMaxQPS = getMaxQPS(mediaService);

      // Get queue sizes
      const queueMaxSize = getMaxQueueSize(queueNode);

      // Initialize component statuses
      const initialStatuses: ComponentStatus[] = [];

      if (authService) {
        initialStatuses.push({
          id: authService.id,
          label: 'Auth Service',
          type: 'service',
          maxQPS: authMaxQPS,
          currentQPS: 0,
          droppedRequests: 0
        });
      }

      if (queueNode) {
        initialStatuses.push({
          id: queueNode.id,
          label: 'Queue',
          type: 'queue',
          maxQPS: 0,
          currentQPS: 0,
          droppedRequests: 0,
          queueSize: 0,
          maxQueueSize: queueMaxSize
        });
      }

      if (mediaService) {
        initialStatuses.push({
          id: mediaService.id,
          label: 'Media Service',
          type: 'service',
          maxQPS: mediaMaxQPS,
          currentQPS: 0,
          droppedRequests: 0
        });
      }

      // Add other components to initialStatuses...

      setComponentStatuses(initialStatuses);

      // Set the QPS for each component based on the simulation config
      const qps = simulationConfig.qps;
      const duration = simulationConfig.duration;

      // Update component statuses with the configured QPS
      setComponentStatuses(prevStatuses => {
        return prevStatuses.map(status => {
          let newStatus = { ...status };

          // Set Auth Service QPS to the configured value
          if (status.label === 'Auth Service') {
            newStatus.currentQPS = qps;
          }

          // Set Media Service QPS based on whether there's a queue
          if (status.label === 'Media Service') {
            if (hasQueueBetweenAuthAndMedia) {
              // With queue, Media Service receives at most its maxQPS
              newStatus.currentQPS = Math.min(qps, status.maxQPS);
            } else {
              // Without queue, Media Service receives full QPS from Auth
              newStatus.currentQPS = qps;

              // Calculate dropped requests if QPS exceeds maxQPS
              if (qps > status.maxQPS) {
                newStatus.droppedRequests = Math.floor((qps - status.maxQPS) * duration);
              }
            }
          }

          // Set Queue status if present
          if (status.label === 'Queue' && hasQueueBetweenAuthAndMedia) {
            const mediaServiceStatus = prevStatuses.find(s => s.label === 'Media Service');

            if (mediaServiceStatus) {
              // Calculate how many requests are buffered per second
              const incomingRequests = qps;
              const outgoingRequests = Math.min(qps, mediaServiceStatus.maxQPS);
              const bufferRate = Math.max(0, incomingRequests - outgoingRequests);

              // Calculate total buffered requests over the duration
              const totalBuffered = Math.min(bufferRate * duration, status.maxQueueSize || 50);
              newStatus.queueSize = totalBuffered;

              // Calculate dropped requests if queue fills up
              if (totalBuffered >= (status.maxQueueSize || 50)) {
                const excessRequests = (bufferRate * duration) - (status.maxQueueSize || 50);
                newStatus.droppedRequests = Math.max(0, excessRequests);
              }
            }
          }

          return newStatus;
        });
      });

      // Find the path edges for visualization
      const pathEdges: Edge[] = [];

      if (hasQueueBetweenAuthAndMedia) {
        // Auth -> Queue -> Media path
        const authToQueueEdge = edges.find(edge =>
          (edge.source === authService?.id && edge.target === queueNode?.id) ||
          (edge.source === queueNode?.id && edge.target === authService?.id)
        );

        const queueToMediaEdge = edges.find(edge =>
          (edge.source === queueNode?.id && edge.target === mediaService?.id) ||
          (edge.source === mediaService?.id && edge.target === queueNode?.id)
        );

        if (authToQueueEdge) pathEdges.push(authToQueueEdge);
        if (queueToMediaEdge) pathEdges.push(queueToMediaEdge);
      } else {
        // Direct Auth -> Media path
        const authToMediaEdge = edges.find(edge =>
          (edge.source === authService?.id && edge.target === mediaService?.id) ||
          (edge.source === mediaService?.id && edge.target === authService?.id)
        );

        if (authToMediaEdge) pathEdges.push(authToMediaEdge);
      }

      // Run the visual simulation
      await simulateTrafficFlow(pathEdges, qps, duration);

      // Update simulation status
      setIsSimulating(false);
      setSimulationComplete(true);

      // Check if simulation was successful (no dropped requests, or queue helped)
      const totalDroppedRequests = componentStatuses.reduce(
        (total, status) => total + (status.droppedRequests || 0),
        0
      );

      const queueBufferedRequests = componentStatuses.find(
        status => status.label === 'Queue'
      )?.queueSize || 0;

      const isSuccessful = totalDroppedRequests === 0 ||
        (hasQueueBetweenAuthAndMedia && queueBufferedRequests > 0);

      setSimulationSuccess(isSuccessful);

      if (isSuccessful) {
        if (hasQueueBetweenAuthAndMedia && queueBufferedRequests > 0) {
          toast.success("Simulation complete!", {
            description: `Great job! The queue successfully buffered ${Math.floor(queueBufferedRequests)} requests, preventing them from being dropped.`,
            duration: 5000
          });
        } else {
          toast.success("Simulation complete!", {
            description: "All requests were processed successfully.",
            duration: 4000
          });
        }

        // Mark step as complete if simulation was successful
        if (currentStep?.id === 'simulate-traffic' && !currentStep.completed) {
          markCurrentStepComplete();
        }
      } else {
        toast.warning("Simulation complete with dropped requests", {
          description: `${Math.floor(totalDroppedRequests)} requests were dropped. Try adding a queue or increasing service capacity.`,
          duration: 5000
        });
      }
    } catch (error) {
      console.error("Error during simulation:", error);
      setIsSimulating(false);
      toast.error("Simulation failed", {
        description: "An error occurred during the simulation. Please try again.",
        duration: 4000
      });
    }
  };

  // Simulate traffic flow by adding animation classes to edges and nodes
  const simulateTrafficFlow = async (pathEdges: Edge[], qps: number, duration: number) => {
    const intervalMs = 1000 / Math.min(10, qps); // Cap animation rate for visual clarity
    const totalPackets = Math.min(50, qps * duration); // Cap total packets for performance
    let packetsSent = 0;

    // Get the edge elements - add null checks
    const edgeElements = pathEdges.map(edge =>
      document.querySelector(`[data-testid="rf__edge-${edge.id}"]`)
    ).filter(el => el !== null); // Filter out null elements

    // Get node elements for visualization - add error handling
    const nodeElementsMap = new Map<string, Element>();
    componentStatuses.forEach(status => {
      const nodeElement = document.querySelector(`[data-id="${status.id}"]`);
      if (nodeElement) {
        nodeElementsMap.set(status.id, nodeElement);
      }
    });

    // Find queue element for special animation - add null checks
    const queueStatus = componentStatuses.find(s => s.label === 'Queue');
    const queueElement = queueStatus
      ? nodeElementsMap.get(queueStatus.id)
      : null;

    // Create queue fill animation if we have a queue - add null checks
    let queueFillLevel = 0;
    let queueFillElement: HTMLElement | null = null;

    if (queueElement) {
      try {
        // Create a fill element for the queue
        queueFillElement = document.createElement('div');
        queueFillElement.className = 'absolute bottom-0 left-0 right-0 bg-yellow-400 opacity-50 transition-all duration-300';
        queueFillElement.style.height = '0%';
        queueElement.appendChild(queueFillElement);

        // Get the queue status
        if (queueStatus && queueStatus.maxQueueSize) {
          // Calculate what percentage of the queue will be filled
          const fillPercentage = Math.min(100, ((queueStatus.queueSize || 0) / queueStatus.maxQueueSize) * 100);

          // Animate the queue filling up over the duration
          const fillInterval = setInterval(() => {
            queueFillLevel += fillPercentage / (duration * 5); // Fill gradually
            if (queueFillElement) {
              queueFillElement.style.height = `${Math.min(100, queueFillLevel)}%`;
            }

            if (queueFillLevel >= fillPercentage) {
              clearInterval(fillInterval);
            }
          }, 200);
        }
      } catch (error) {
        console.error("Error setting up queue animation:", error);
        // Continue with simulation even if queue animation fails
      }
    }

    return new Promise<void>(resolve => {
      const interval = setInterval(() => {
        try {
          // Add animation class to edges in sequence
          edgeElements.forEach((element) => {
            if (element) {
              element.classList.add('animate-edge-traffic');
              setTimeout(() => {
                if (element && element.classList) {
                  element.classList.remove('animate-edge-traffic');
                }
              }, 800);
            }
          });

          // Visualize processing and overload effects
          componentStatuses.forEach(status => {
            const nodeElement = nodeElementsMap.get(status.id);
            if (!nodeElement) return;

            // Handle queue animation separately
            if (status.label === 'Queue') {
              // If queue is overflowing, show dropped packets
              if (status.droppedRequests > 0 && Math.random() < 0.4) {
                try {
                  const nodeRect = nodeElement.getBoundingClientRect();
                  const dropElement = document.createElement('div');
                  dropElement.className = 'packet-drop queue-overflow';
                  dropElement.style.left = `${nodeRect.left + nodeRect.width / 2}px`;
                  dropElement.style.top = `${nodeRect.bottom}px`;
                  dropElement.textContent = '❌';
                  document.body.appendChild(dropElement);

                  // Remove the dropped packet element after animation completes
                  setTimeout(() => {
                    if (document.body.contains(dropElement)) {
                      document.body.removeChild(dropElement);
                    }
                  }, 1000);
                } catch (error) {
                  console.error("Error creating queue overflow animation:", error);
                }
              }
              return;
            }

            // If QPS exceeds maxQPS, show overload effect
            if (status.currentQPS > status.maxQPS) {
              nodeElement.classList.add('node-overload');

              // Visualize dropped packets with more visible animation
              if (Math.random() < 0.4) { // Increased probability for better visibility
                try {
                  const nodeRect = nodeElement.getBoundingClientRect();
                  const dropElement = document.createElement('div');
                  dropElement.className = 'packet-drop';
                  dropElement.style.left = `${nodeRect.left + nodeRect.width / 2}px`;
                  dropElement.style.top = `${nodeRect.bottom}px`;
                  dropElement.textContent = '❌'; // Add X symbol for dropped packets
                  dropElement.style.color = 'red';
                  dropElement.style.fontWeight = 'bold';
                  document.body.appendChild(dropElement);

                  // Remove the dropped packet element after animation completes
                  setTimeout(() => {
                    if (document.body.contains(dropElement)) {
                      document.body.removeChild(dropElement);
                    }
                  }, 1000);
                } catch (error) {
                  console.error("Error creating drop animation:", error);
                  // Continue with simulation even if drop animation fails
                }
              }
            } else {
              // If not overloaded, add processing visual effect
              nodeElement.classList.add('node-processing');
              setTimeout(() => {
                if (nodeElement && nodeElement.classList) {
                  nodeElement.classList.remove('node-processing');
                }
              }, 500);
            }
          });

          packetsSent++;
          if (packetsSent >= totalPackets) {
            clearInterval(interval);

            // Remove overload visualizations after simulation completes
            componentStatuses.forEach(status => {
              const nodeElement = nodeElementsMap.get(status.id);
              if (nodeElement) {
                nodeElement.classList.remove('node-overload');
                nodeElement.classList.remove('node-processing');
              }
            });

            setTimeout(() => {
              // Clean up queue fill element if it exists
              try {
                if (queueFillElement && queueElement && queueElement.contains(queueFillElement)) {
                  queueElement.removeChild(queueFillElement);
                }
              } catch (error) {
                console.error("Error cleaning up queue animation:", error);
              }
              resolve();
            }, 1000); // Wait for animations to complete
          }
        } catch (error) {
          console.error("Error during simulation animation:", error);
          clearInterval(interval);
          resolve(); // Resolve the promise even if there's an error
        }
      }, intervalMs);
    });
  };

  // Helper function to get maxQPS from component
  function getMaxQPS(node?: Node): number {
    if (!node) return 0;

    const data = node.data as any;
    const customProps = data.metadata?.customProperties || [];
    const maxQPSProp = customProps.find((prop: any) =>
      prop.key.toLowerCase() === 'maxqps'
    );

    return maxQPSProp ? parseInt(maxQPSProp.value) : 0;
  }

  // Helper function to get maxQueueSize from queue component
  function getMaxQueueSize(node?: Node): number {
    if (!node) return 50; // Default

    const data = node.data as any;
    const customProps = data.metadata?.customProperties || [];
    const maxSizeProp = customProps.find((prop: any) =>
      prop.key.toLowerCase() === 'maxqueuesize'
    );

    return maxSizeProp ? parseInt(maxSizeProp.value) : 50;
  }

  // Show component configuration info
  const showConfigInfo = () => {
    // Check for missing components
    const frontend = nodes.find(node => node.data?.type === 'frontend' && !node.parentId);
    const apiGateway = nodes.find(node => node.data?.type === 'apigateway' && !node.parentId);
    const authService = nodes.find(node => node.type === 'compositeNode' && node.data?.label === 'Authentication Service');
    const mediaService = nodes.find(node => node.data?.type === 'server' && typeof node.data?.label === 'string' && node.data?.label.includes('Media Service') && !node.parentId);

    // Check for missing connections - following the correct architecture
    const frontendToApiGateway = edges.some(edge =>
      (edge.source === frontend?.id && edge.target === apiGateway?.id) ||
      (edge.source === apiGateway?.id && edge.target === frontend?.id)
    );

    const apiGatewayToAuth = edges.some(edge =>
      (edge.source === apiGateway?.id && edge.target === authService?.id) ||
      (edge.source === authService?.id && edge.target === apiGateway?.id)
    );

    // Check for either direct connection or connection via queue
    // First, find if there's a queue between Auth and Media
    const queueNode = nodes.find(node =>
      node.data?.type === 'queue' && !node.parentId
    );

    // Check for Auth -> Queue connection
    const authToQueue = queueNode && edges.some(edge =>
      (edge.source === authService?.id && edge.target === queueNode.id) ||
      (edge.source === queueNode.id && edge.target === authService?.id)
    );

    // Check for Queue -> Media connection
    const queueToMedia = queueNode && edges.some(edge =>
      (edge.source === queueNode.id && edge.target === mediaService?.id) ||
      (edge.source === mediaService?.id && edge.target === queueNode.id)
    );

    // Check for direct Auth -> Media connection
    const directAuthToMedia = edges.some(edge =>
      (edge.source === authService?.id && edge.target === mediaService?.id) ||
      (edge.source === mediaService?.id && edge.target === authService?.id)
    );

    // Connection is valid if either direct or via queue
    const authToMediaValid = directAuthToMedia || (authToQueue && queueToMedia);

    const missingComponents = [];
    if (!frontend) missingComponents.push("Frontend");
    if (!apiGateway) missingComponents.push("API Gateway");
    if (!authService) missingComponents.push("Authentication Service");
    if (!mediaService) missingComponents.push("Media Service");

    const missingConnections = [];
    if (frontend && apiGateway && !frontendToApiGateway) missingConnections.push("Frontend to API Gateway");
    if (apiGateway && authService && !apiGatewayToAuth) missingConnections.push("API Gateway to Authentication Service");
    if (authService && mediaService && !authToMediaValid) {
      if (queueNode) {
        // If there's a queue, check which connection is missing
        if (!authToQueue) missingConnections.push("Authentication Service to Queue");
        if (!queueToMedia) missingConnections.push("Queue to Media Service");
      } else {
        // If no queue, suggest direct connection
        missingConnections.push("Authentication Service to Media Service");
      }
    }

    return (
      <>
        {missingComponents.length > 0 && (
          <div className="flex items-start gap-2 p-3 bg-red-50 border border-red-200 rounded-md mb-4">
            <AlertTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 shrink-0" />
            <div className="text-sm">
              <p className="font-medium mb-1">Missing Components</p>
              <ul className="list-disc list-inside">
                {missingComponents.map((item, index) => (
                  <li key={index}>Add <code className="bg-red-100 px-1 py-0.5 rounded">{item}</code> to the canvas</li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {missingConnections.length > 0 && (
          <div className="flex items-start gap-2 p-3 bg-orange-50 border border-orange-200 rounded-md mb-4">
            <AlertTriangleIcon className="h-5 w-5 text-orange-500 mt-0.5 shrink-0" />
            <div className="text-sm">
              <p className="font-medium mb-1">Missing Connections</p>
              <ul className="list-disc list-inside">
                {missingConnections.map((item, index) => (
                  <li key={index}>Connect <code className="bg-orange-100 px-1 py-0.5 rounded">{item}</code></li>
                ))}
              </ul>
            </div>
          </div>
        )}

        {missingConfigurations.length > 0 && (
          <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md mb-4">
            <AlertTriangleIcon className="h-5 w-5 text-amber-500 mt-0.5 shrink-0" />
            <div className="text-sm">
              <p className="font-medium mb-1">Missing Component Configurations</p>
              <ul className="list-disc list-inside">
                {missingConfigurations.map((item, index) => (
                  <li key={index}>Set <code className="bg-amber-100 px-1 py-0.5 rounded">{item.property}</code> on {item.component}</li>
                ))}
              </ul>
              <p className="mt-2 text-xs">Click on each component and add custom properties in the right panel</p>
            </div>
          </div>
        )}
      </>
    );
  };

  // Show component status display
  const renderComponentStatuses = () => {
    return componentStatuses.map((status) => {
      const isOverloaded = status.type !== 'queue' && status.currentQPS > status.maxQPS;
      const queueFillPercentage = status.type === 'queue' && status.maxQueueSize ?
        Math.min(100, Math.round((status.queueSize || 0) / status.maxQueueSize * 100)) : 0;

      return (
        <div
          key={status.id}
          className={`p-3 border rounded-md ${
            isOverloaded ? 'bg-red-50 border-red-300' :
            status.type === 'queue' ? 'bg-blue-50 border-blue-300' :
            'bg-green-50 border-green-300'
          }`}
        >
          <div className="flex justify-between items-center">
            <h4 className="font-medium text-sm">{status.label}</h4>
            {status.type === 'service' && (
              <span className={`text-xs px-2 py-1 rounded ${
                isOverloaded ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800'
              }`}>
                {isOverloaded ? 'OVERLOADED' : 'OK'}
              </span>
            )}
          </div>

          {status.type !== 'queue' ? (
            <div className="mt-2 space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Current QPS:</span>
                <span className="font-mono">{status.currentQPS.toFixed(0)}</span>
              </div>
              <div className="flex justify-between">
                <span>Max QPS:</span>
                <span className="font-mono">{status.maxQPS.toFixed(0)}</span>
              </div>
              {status.droppedRequests > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>Dropped:</span>
                  <span className="font-mono">{status.droppedRequests.toFixed(0)}</span>
                </div>
              )}
            </div>
          ) : (
            <div className="mt-2 space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Buffered:</span>
                <span className="font-mono">{status.queueSize || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>Capacity:</span>
                <span className="font-mono">{status.maxQueueSize || 50}</span>
              </div>

              {/* Queue fill visualization */}
              <div className="h-2 bg-blue-100 rounded-full mt-1 overflow-hidden">
                <div
                  className="h-full bg-blue-500 rounded-full"
                  style={{width: `${queueFillPercentage}%`}}
                ></div>
              </div>

              {status.droppedRequests > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>Overflow:</span>
                  <span className="font-mono">{status.droppedRequests.toFixed(0)}</span>
                </div>
              )}
            </div>
          )}
        </div>
      );
    });
  };

  // Add this function to reset the simulation state
  const resetSimulation = () => {
    setSimulationComplete(false);
    setComponentStatuses([]);

    // Reset simulation config to initial values if needed
    if (simulationRequirements) {
      setSimulationConfig({
        qps: simulationRequirements.minQPS || 15,
        duration: simulationRequirements.minDuration || 5,
        payload: simulationRequirements.payload || { userId: 'abc123', action: 'upload_photo' }
      });
    }

    // Reset payload string
    setPayloadStr(JSON.stringify(simulationConfig.payload, null, 2));
  };

  // Define what to show for simulation results
  const renderSimulationResults = () => {
    if (!simulationComplete) return null;

    const totalDropped = componentStatuses.reduce((total, status) => total + status.droppedRequests, 0);
    const totalRequests = simulationConfig.qps * simulationConfig.duration;
    const successRate = Math.max(0, Math.min(100, 100 - (totalDropped / totalRequests * 100)));

    return (
      <div className={`p-4 border rounded-md ${
        simulationSuccess ? 'bg-green-50 border-green-300' : 'bg-amber-50 border-amber-300'
      } mb-4`}>
        <h4 className="font-medium mb-2">Simulation Results</h4>

        <div className="flex justify-between text-sm mb-2">
          <span>Success Rate:</span>
          <span className="font-medium">{successRate.toFixed(1)}%</span>
        </div>

        <div className="h-2 bg-gray-200 rounded-full mb-3">
          <div
            className={`h-full ${
              simulationSuccess ? 'bg-green-500' : 'bg-amber-500'
            } rounded-full`}
            style={{width: `${successRate}%`}}
          ></div>
        </div>

        {simulationSuccess ? (
          <p className="text-sm text-green-700 flex items-center gap-2">
            <InfoIcon className="h-4 w-4" />
            Your system handled the load successfully.
          </p>
        ) : (
          <p className="text-sm text-amber-700 flex items-center gap-2">
            <AlertTriangleIcon className="h-4 w-4" />
            Your system dropped {totalDropped.toFixed(0)} requests. Consider adding queues or increasing capacity.
          </p>
        )}

        <div className="mt-3">
          <Button
            variant="secondary"
            size="sm"
            className="w-full"
            onClick={resetSimulation}
          >
            Reset Simulation
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="border rounded-md p-4 bg-gray-50">
      <div className="flex items-center mb-4">
        <ChartBarIcon className="h-5 w-5 mr-2" />
        <h3 className="text-lg font-medium">Traffic Simulator</h3>
      </div>

      {!isSimulating && !simulationComplete && (
        <>
          {showConfigInfo()}

          <div className="space-y-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">Traffic Rate (QPS)</label>
              <div className="flex gap-4 items-center">
                <Slider
                  value={[simulationConfig.qps]}
                  min={simulationRequirements?.minQPS || 1}
                  max={simulationRequirements?.maxQPS || 200}
                  step={1}
                  onValueChange={(values) =>
                    setSimulationConfig({...simulationConfig, qps: values[0]})
                  }
                  className="flex-1"
                />
                <span className="font-mono text-sm w-12 text-right">
                  {simulationConfig.qps}
                </span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Duration (seconds)</label>
              <div className="flex gap-4 items-center">
                <Slider
                  value={[simulationConfig.duration]}
                  min={simulationRequirements?.minDuration || 1}
                  max={simulationRequirements?.maxDuration || 30}
                  step={1}
                  onValueChange={(values) =>
                    setSimulationConfig({...simulationConfig, duration: values[0]})
                  }
                  className="flex-1"
                />
                <span className="font-mono text-sm w-12 text-right">
                  {simulationConfig.duration}
                </span>
              </div>
            </div>
          </div>

          <Button
            onClick={runSimulation}
            disabled={!canSimulate}
            className="w-full mb-2"
            title={!canSimulate ? "Configure components before running simulation" : "Run traffic simulation"}
          >
            <PlayIcon className="h-4 w-4 mr-2" />
            Start Simulation
          </Button>

          {!canSimulate && (
            <div className="text-xs text-red-500 text-center p-2 border border-red-200 rounded bg-red-50 mb-2">
              {missingConfigurations.length > 0 ? (
                <p>Please configure the required properties before running the simulation</p>
              ) : (
                <p>Missing required components or connections for simulation</p>
              )}
            </div>
          )}
        </>
      )}

      {isSimulating && (
        <div className="text-center py-6">
          <div className="inline-flex items-center mb-3">
            <LayersIcon className="h-5 w-5 mr-2 text-blue-500 animate-pulse" />
            <span className="font-medium">Simulating Traffic...</span>
          </div>
          <p className="text-sm text-gray-600">
            Processing {simulationConfig.qps} QPS for {simulationConfig.duration} seconds
          </p>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsSimulating(false)}
            className="mt-4"
          >
            <CircleStopIcon className="h-4 w-4 mr-1" />
            Stop
          </Button>
        </div>
      )}

      {simulationComplete && renderSimulationResults()}

      {(isSimulating || simulationComplete) && componentStatuses.length > 0 && (
        <div className="space-y-3 mt-4">
          <h4 className="font-medium">Component Status</h4>
          {renderComponentStatuses()}
        </div>
      )}
    </div>
  );
};

export default TrafficSimulator;
