
import React, { useEffect, useRef } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  targetElementId?: string | null; // ID of element to position near
  position?: 'top' | 'right' | 'bottom' | 'left'; // Position relative to target
  offsetX?: number; // Additional X offset
  offsetY?: number; // Additional Y offset
  isConnectionStep?: boolean; // Flag for connection animation step
  sourceNodeId?: string; // Source node ID for connection step
  targetNodeId?: string; // Target node ID for connection step
  connectionLabel?: string; // Label for the connection
}

interface TutorialPopupProps {
  step: TutorialStep;
  onNext: () => void;
  onSkip: () => void;
  onComplete: () => void;
  isLastStep: boolean;
}

const TutorialPopup: React.FC<TutorialPopupProps> = ({
  step,
  onNext,
  onSkip,
  onComplete,
  isLastStep,
}) => {
  const { title, description, targetElementId, position = 'bottom', offsetX = 0, offsetY = 0 } = step;
  const [popupStyle, setPopupStyle] = React.useState<React.CSSProperties>({
    position: 'fixed',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 50,
  });
  
  const popupRef = useRef<HTMLDivElement>(null);

  // Position the popup near the target element
  useEffect(() => {
    // For connection step, position the popup higher in the center
    if (step.isConnectionStep) {
      setPopupStyle({
        position: 'fixed',
        top: '10%', // Position even higher (10% from the top instead of 20%)
        left: '50%',
        transform: 'translate(-50%, 0)', // No vertical centering
        zIndex: 50,
        maxWidth: '300px',
        width: '100%'
      });
      return;
    }
    
    if (targetElementId) {
      const targetElement = document.getElementById(targetElementId);
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // Wait for the popup to be rendered to get its dimensions
        setTimeout(() => {
          const popupElement = popupRef.current;
          if (!popupElement) return;
          
          // Get actual popup dimensions
          const popupWidth = popupElement.offsetWidth;
          const popupHeight = popupElement.offsetHeight;
          
          let top = 0;
          let left = 0;

          // Calculate position based on target element and desired placement
          switch (position) {
            case 'top':
              top = rect.top - popupHeight - 10 - offsetY;
              left = rect.left + rect.width / 2 + offsetX;
              break;
            case 'right':
              top = rect.top + rect.height / 2 + offsetY;
              left = rect.right + 10 + offsetX;
              break;
            case 'bottom':
              top = rect.bottom + 10 + offsetY;
              left = rect.left + rect.width / 2 + offsetX;
              break;
            case 'left':
              top = rect.top + rect.height / 2 + offsetY;
              left = rect.left - popupWidth - 10 - offsetX;
              break;
          }

          // Apply transformations based on position
          let transform = '';
          if (position === 'left') {
            transform = 'translateY(-50%)';
          } else if (position === 'top') {
            transform = 'translateX(-50%)';
          } else if (position === 'right') {
            transform = 'translateY(-50%)';
          } else { // bottom
            transform = 'translateX(-50%)';
          }
          
          // Constrain horizontal position
          if (position === 'left' || position === 'right') {
            // No additional X transform needed
          } else {
            // For top and bottom positions, consider the horizontal centering transform
            left = Math.max(popupWidth / 2 + 8, left); // At least 8px from left edge + half width for centering
            left = Math.min(viewportWidth - popupWidth / 2 - 8, left); // At least 8px from right edge + half width for centering
          }
          
          // Special case for left position to avoid going off-screen left
          if (position === 'left') {
            if (left < 8) {
              // Switch to right position if there's not enough space on the left
              left = rect.right + 10 + offsetX;
              transform = 'translateY(-50%)';
            }
          }
          
          // Special case for right position to avoid going off-screen right
          if (position === 'right') {
            if (left + popupWidth > viewportWidth - 8) {
              // Switch to left position if there's not enough space on the right
              left = rect.left - 10 - offsetX;
              transform = 'translateY(-50%) translateX(-100%)';
            }
          }
          
          // Constrain vertical position
          if (position === 'top' || position === 'bottom') {
            // For left and right positions, consider the vertical centering transform
            top = Math.max(8, top); // At least 8px from top edge
            top = Math.min(viewportHeight - popupHeight - 8, top); // At least 8px from bottom edge
          } else {
            top = Math.max(8, top); // At least 8px from top edge
            top = Math.min(viewportHeight - popupHeight - 8, top); // At least 8px from bottom edge
          }
          
          // Special case for top position to avoid going off-screen top
          if (position === 'top') {
            if (top < 8) {
              // Switch to bottom position if there's not enough space on top
              top = rect.bottom + 10 + offsetY;
              transform = 'translateX(-50%)';
            }
          }
          
          // Special case for bottom position to avoid going off-screen bottom
          if (position === 'bottom') {
            if (top + popupHeight > viewportHeight - 8) {
              // Switch to top position if there's not enough space at bottom
              top = rect.top - popupHeight - 10 - offsetY;
              transform = 'translateX(-50%)';
            }
          }

          // Set the position and add a nice transition effect
          setPopupStyle({
            position: 'fixed',
            top: `${top}px`,
            left: `${left}px`,
            transform,
            zIndex: 50,
            transition: 'all 0.2s ease-in-out',
            maxWidth: '300px',
            width: '100%'
          });
        }, 0);

        // Add highlight effect to the target element
        targetElement.classList.add('tutorial-highlight');
        
        return () => {
          // Remove highlight when component unmounts or target changes
          targetElement.classList.remove('tutorial-highlight');
        };
      }
    }
  }, [targetElementId, position, offsetX, offsetY, step.isConnectionStep]);

  // Handle the action for the primary button (next or complete)
  const handlePrimaryAction = () => {
    if (isLastStep) {
      onComplete();
    } else {
      onNext();
    }
  };

  // Determine if this is a connection step
  const isConnectionStep = !!step.isConnectionStep;

  return (
    <>
      {/* Use different overlay class for connection steps */}
      <div className={`tutorial-overlay ${isConnectionStep ? 'connection-step' : ''}`}></div>
      <Card ref={popupRef} className="rounded shadow p-4 bg-white border z-50" style={popupStyle}>
        <div className="flex flex-col gap-4">
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
          
          <div className="flex justify-between mt-2">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onSkip}
              className="text-gray-500 hover:text-gray-700"
            >
              Skip
            </Button>
            <Button 
              variant="default" 
              size="sm"
              onClick={handlePrimaryAction}
              className="bg-layrs-primary hover:bg-layrs-secondary text-white"
            >
              {isLastStep ? 'Got it' : 'Next'}
            </Button>
          </div>
        </div>
      </Card>
    </>
  );
};

export default TutorialPopup;
