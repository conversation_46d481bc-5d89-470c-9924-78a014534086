import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { ArrowRight, Share } from 'lucide-react';
import { addToWaitlistSimple } from '@/services/simpleWaitlistService';
import { generateReferralCode } from '@/services/waitlistService';

const WaitlistForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [referralCode, setReferralCode] = useState<string | null>(null);

  // Honeypot fields (hidden from users, but bots might fill them)
  const [website, setWebsite] = useState('');
  const [phone, setPhone] = useState('');
  const [company, setCompany] = useState('');

  // Check for referral code in URL and check if user has already joined
  useEffect(() => {
    // Check for referral code
    const params = new URLSearchParams(window.location.search);
    const ref = params.get('ref');
    if (ref) {
      setReferralCode(ref);
    }

    // Check if user has already joined the waitlist (based on localStorage)
    const savedEmail = localStorage.getItem('layrsWaitlistEmail');
    const savedReferralCode = localStorage.getItem('layrsReferralCode');

    if (savedEmail && savedReferralCode) {
      setIsSubmitted(true);
    }
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim() || !email.includes('@')) {
      toast.error("Invalid email", {
        description: "Please enter a valid email address."
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simple bot detection - if honeypot fields are filled, silently ignore
      if (website || phone || company) {
        // Pretend it worked for bots
        toast.success("You're on the list, architect!", {
          description: "Thank you for joining our waitlist. We'll notify you when Layrs launches."
        });
        setEmail('');
        setIsSubmitting(false);
        setIsSubmitted(true);
        return;
      }

      // Add email to waitlist with simple protection
      const { success, error, retryAfter } = await addToWaitlistSimple(email, referralCode || undefined);

      if (!success) {
        const errorMessage = retryAfter
          ? `${error} Please try again in ${retryAfter} seconds.`
          : error || "Failed to join waitlist. Please try again.";

        toast.error("Error", {
          description: errorMessage
        });
        setIsSubmitting(false);
        return;
      }

      // Generate a unique referral code for sharing
      const userReferralCode = generateReferralCode(email);

      // Set success message based on whether they used a referral code
      const successMessage = referralCode
        ? "You're on the list with Priority Access!"
        : "You're on the list, architect!";

      toast.success(successMessage, {
        description: "Thank you for joining our waitlist. We'll notify you when Layrs launches."
      });

      // Clear form and update state
      setEmail('');
      setIsSubmitting(false);
      setIsSubmitted(true);

      // Store the user's email and referral code for sharing and persistence
      localStorage.setItem('layrsWaitlistEmail', email);
      localStorage.setItem('layrsReferralCode', userReferralCode);

    } catch (error) {
      console.error('Error submitting to waitlist:', error);
      toast.error("Error", {
        description: "An unexpected error occurred. Please try again."
      });
      setIsSubmitting(false);
    }
  };

  const handleShare = () => {
    // Get the user's referral code from localStorage
    const userReferralCode = localStorage.getItem('layrsReferralCode');

    // Create the share URL with the referral code
    const baseUrl = window.location.origin;
    const shareUrl = userReferralCode
      ? `${baseUrl}?ref=${userReferralCode}`
      : window.location.href;

    if (navigator.share) {
      navigator.share({
        title: 'Layrs - System Design Mastery',
        text: 'I just joined the waitlist for Layrs! Learn to architect, not just code. Join with my referral link for priority access:',
        url: shareUrl,
      })
      .catch((error) => console.log('Error sharing:', error));
    } else {
      // Fallback for browsers that don't support navigator.share
      navigator.clipboard.writeText(shareUrl)
        .then(() => {
          toast.success("Referral link copied!", {
            description: "Share it with your network to give them priority access."
          });
        })
        .catch(err => console.log('Error copying link:', err));
    }
  };

  return (
    <section id="waitlist" className="py-16 md:py-24">
      <div className="container max-w-6xl mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <div className="glass-card rounded-2xl p-8 md:p-10 shadow-lg">
            <h2 className="text-3xl md:text-4xl font-bold mb-3 text-center">Join the waitlist</h2>

            {/* Benefit tagline */}
            <p className="text-lg text-gray-300 mb-8 text-center">
              Join hundreds already on the waitlist and get early access to Layrs.
              {referralCode && <span className="ml-1 text-blue-400">Priority Access detected!</span>}
            </p>

            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
                {/* Honeypot fields - hidden from users but visible to bots */}
                <input
                  type="text"
                  name="website"
                  value={website}
                  onChange={(e) => setWebsite(e.target.value)}
                  style={{ display: 'none' }}
                  tabIndex={-1}
                  autoComplete="off"
                />
                <input
                  type="tel"
                  name="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  style={{ display: 'none' }}
                  tabIndex={-1}
                  autoComplete="off"
                />
                <input
                  type="text"
                  name="company"
                  value={company}
                  onChange={(e) => setCompany(e.target.value)}
                  style={{ display: 'none' }}
                  tabIndex={-1}
                  autoComplete="off"
                />

                <Input
                  type="email"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="flex-grow bg-[#0F172A] border-white/10 rounded-lg px-4 py-4 focus:ring-2 focus:ring-layrs-primary/20 transition-all placeholder:text-gray-400 text-[#E2E8F0]"
                  style={{ minHeight: '3rem' }}
                  disabled={isSubmitting}
                  required
                />
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium py-3 px-6 rounded-lg transition-all shadow-md shadow-blue-500/20 hover:shadow-lg flex items-center justify-center gap-2"
                  style={{ minHeight: '3rem' }}
                >
                  {isSubmitting ? 'Joining...' : 'Join Waitlist'}
                  {!isSubmitting && <ArrowRight className="w-4 h-4" />}
                </Button>
              </form>
            ) : (
              <div className="text-center">
                <p className="text-xl text-blue-400 font-semibold mb-4">You're on the list, architect!</p>

                {/* Display referral code if available */}
                {localStorage.getItem('layrsReferralCode') && (
                  <div className="mb-4 p-3 bg-white/5 rounded-lg inline-block">
                    <p className="text-sm text-gray-300 mb-1">Your referral code:</p>
                    <p className="text-lg font-mono font-semibold text-blue-400">
                      {localStorage.getItem('layrsReferralCode')}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Share this code to give your friends priority access
                    </p>
                  </div>
                )}

                <Button
                  onClick={handleShare}
                  variant="outline"
                  className="mx-auto flex items-center gap-2 bg-white/5 hover:bg-white/10 text-blue-400"
                >
                  <Share className="w-4 h-4" />
                  Share with others
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default WaitlistForm;
