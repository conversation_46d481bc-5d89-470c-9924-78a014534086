import { useEffect, useState } from 'react';
import { migrateLocalStorageDesigns } from '@/utils/designMigration';

/**
 * Component that handles migrating designs to the new format
 * This component doesn't render anything, it just runs the migration
 * when the app starts
 */
const DesignMigration: React.FC = () => {
  const [migrationRun, setMigrationRun] = useState(false);

  useEffect(() => {
    // Only run the migration once
    if (!migrationRun) {
      try {
        migrateLocalStorageDesigns();
        setMigrationRun(true);
      } catch (error) {
        console.error('Error running design migration:', error);
      }
    }
  }, [migrationRun]);

  // This component doesn't render anything
  return null;
};

export default DesignMigration;
