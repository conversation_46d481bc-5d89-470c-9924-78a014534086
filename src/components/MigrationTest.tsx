import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuestions } from '@/contexts/QuestionsContext';
import { QuestionService } from '@/services/questionService';
import { Database, RefreshCw, CheckCircle, XCircle } from 'lucide-react';

const MigrationTest: React.FC = () => {
  const { questions, loading, error, refreshQuestions } = useQuestions();
  const [testResults, setTestResults] = useState<{
    connectionTest: 'idle' | 'testing' | 'success' | 'error';
    migrationTest: 'idle' | 'testing' | 'success' | 'error';
    loadTest: 'idle' | 'testing' | 'success' | 'error';
    messages: string[];
  }>({
    connectionTest: 'idle',
    migrationTest: 'idle',
    loadTest: 'idle',
    messages: []
  });

  const addMessage = (message: string) => {
    setTestResults(prev => ({
      ...prev,
      messages: [...prev.messages, `${new Date().toLocaleTimeString()}: ${message}`]
    }));
  };

  const testConnection = async () => {
    setTestResults(prev => ({ ...prev, connectionTest: 'testing' }));
    addMessage('Testing database connection...');

    try {
      const status = await QuestionService.getConnectionStatus();
      
      if (status.connected) {
        setTestResults(prev => ({ ...prev, connectionTest: 'success' }));
        addMessage(`✅ Connection successful! Table exists: ${status.tableExists}, Records: ${status.recordCount}`);
      } else {
        setTestResults(prev => ({ ...prev, connectionTest: 'error' }));
        addMessage('❌ Connection failed');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, connectionTest: 'error' }));
      addMessage(`❌ Connection error: ${error}`);
    }
  };

  const testMigration = async () => {
    setTestResults(prev => ({ ...prev, migrationTest: 'testing' }));
    addMessage('Testing migration...');

    try {
      const result = await QuestionService.checkQuestionsExist();
      
      if (result.success) {
        setTestResults(prev => ({ ...prev, migrationTest: 'success' }));
        addMessage(`✅ Found ${result.count} questions in database!`);
      } else {
        setTestResults(prev => ({ ...prev, migrationTest: 'error' }));
        addMessage(`❌ Check failed: ${result.error}`);
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, migrationTest: 'error' }));
      addMessage(`❌ Check error: ${error}`);
    }
  };

  const testLoad = async () => {
    setTestResults(prev => ({ ...prev, loadTest: 'testing' }));
    addMessage('Testing question loading...');

    try {
      await refreshQuestions();
      setTestResults(prev => ({ ...prev, loadTest: 'success' }));
      addMessage(`✅ Questions loaded successfully! Count: ${questions.length}`);
    } catch (error) {
      setTestResults(prev => ({ ...prev, loadTest: 'error' }));
      addMessage(`❌ Load error: ${error}`);
    }
  };

  const runAllTests = async () => {
    setTestResults({
      connectionTest: 'idle',
      migrationTest: 'idle',
      loadTest: 'idle',
      messages: []
    });

    await testConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testMigration();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testLoad();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'testing':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Question Migration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Test Status */}
          <div className="grid grid-cols-3 gap-4">
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              {getStatusIcon(testResults.connectionTest)}
              <span className="text-sm font-medium">Connection Test</span>
            </div>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              {getStatusIcon(testResults.migrationTest)}
              <span className="text-sm font-medium">Migration Test</span>
            </div>
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              {getStatusIcon(testResults.loadTest)}
              <span className="text-sm font-medium">Load Test</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button onClick={runAllTests} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Run All Tests
            </Button>
            <Button variant="outline" onClick={testConnection}>
              Test Connection
            </Button>
            <Button variant="outline" onClick={testMigration}>
              Check Questions
            </Button>
            <Button variant="outline" onClick={testLoad}>
              Test Load
            </Button>
          </div>

          {/* Current Status */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Current Status</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Questions Loaded:</span>
                <span className="ml-2 font-medium">{questions.length}</span>
              </div>
              <div>
                <span className="text-gray-600">Loading:</span>
                <span className="ml-2 font-medium">{loading ? 'Yes' : 'No'}</span>
              </div>
              <div>
                <span className="text-gray-600">Error:</span>
                <span className="ml-2 font-medium">{error || 'None'}</span>
              </div>
            </div>
          </div>

          {/* Test Messages */}
          {testResults.messages.length > 0 && (
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
              <h4 className="text-white font-medium mb-2">Test Log</h4>
              <div className="space-y-1 max-h-40 overflow-y-auto">
                {testResults.messages.map((message, index) => (
                  <div key={index}>{message}</div>
                ))}
              </div>
            </div>
          )}

          {/* Questions Preview */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Questions Preview</h4>
            <div className="space-y-2">
              {questions.slice(0, 3).map(question => (
                <div key={question.id} className="flex items-center gap-2 text-sm">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    {question.difficulty}
                  </span>
                  <span className="font-medium">{question.title}</span>
                </div>
              ))}
              {questions.length > 3 && (
                <div className="text-sm text-gray-500">
                  ... and {questions.length - 3} more questions
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MigrationTest;
