import React, { useEffect, useState, useRef } from 'react';
import { componentTypes, ComponentType } from '@/components/ComponentTypes';
import { Package, ChevronRight } from 'lucide-react';

interface LeftComponentPaletteProps {
  showComponentPalette?: boolean;
  leftPanelWidth?: number;
  showLeftPanel?: boolean;
  leftPanelRef?: React.RefObject<HTMLDivElement>;
}

const LeftComponentPalette: React.FC<LeftComponentPaletteProps> = ({
  showComponentPalette = true,
  leftPanelWidth = 25,
  showLeftPanel = true,
  leftPanelRef
}) => {
  // State to track the actual left panel width in pixels
  const [actualLeftPanelWidth, setActualLeftPanelWidth] = useState<number>(0);
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  // Effect to observe left panel width changes
  useEffect(() => {
    if (!leftPanelRef?.current || !showLeftPanel) {
      setActualLeftPanelWidth(0);
      return;
    }

    // Create ResizeObserver to track left panel width changes
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.contentRect.width;
        setActualLeftPanelWidth(width);
      }
    });

    // Start observing the left panel
    resizeObserverRef.current.observe(leftPanelRef.current);

    // Set initial width
    setActualLeftPanelWidth(leftPanelRef.current.offsetWidth);

    // Cleanup
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
    };
  }, [leftPanelRef, showLeftPanel]);

  // Handle component drag start
  const onDragStart = (event: React.DragEvent, componentType: ComponentType) => {
    const component = componentTypes[componentType];
    if (component) {
      // Set the drag data with component information
      event.dataTransfer.setData('application/reactflow', JSON.stringify({
        type: component.type,
        className: component.className,
        label: component.label
      }));
      event.dataTransfer.effectAllowed = 'move';
    }
  };

  // Get component items for the palette
  const componentItems = Object.values(componentTypes).map(component => ({
    type: component.type,
    label: component.label,
    icon: component.iconComponent,
    className: component.className
  }));

  if (!showComponentPalette) {
    return null;
  }

  // Calculate the left position based on the actual measured left panel width
  const calculateLeftPosition = () => {
    if (!showLeftPanel || actualLeftPanelWidth === 0) {
      // When left panel is collapsed, position after the expand button
      // The expand button is positioned at left-0 with ml-2 (8px margin)
      // Button width is approximately 36px (size="sm"), so we position at ~72px to give plenty of space
      return '72px'; // Leave space for the expand button (8px margin + 36px button + 28px padding)
    }
    // Use the actual measured width in pixels plus padding
    return `${actualLeftPanelWidth + 16}px`; // 16px = 1rem padding
  };

  const leftPosition = calculateLeftPosition();

  return (
    <div
      className="left-component-palette fixed top-1/2 transform -translate-y-1/2 z-30 flex flex-col gap-2 bg-gray-50 rounded-lg shadow-lg border border-gray-200 p-2 group hover:pl-4 transition-all duration-200 max-h-[80vh] overflow-y-auto"
      style={{
        left: leftPosition,
        transition: 'left 0.2s ease-in-out, padding 0.2s ease-in-out'
      }}
    >


      {/* Components Header */}
      <div className="w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm bg-gradient-to-r from-indigo-500/80 to-purple-600/80 text-white border-none backdrop-blur-sm hover:w-40 transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-40 rounded-md flex-shrink-0">
        <div className="w-6 flex items-center justify-center flex-shrink-0">
          <Package size={14} />
        </div>
        <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100 font-medium">
          Components
        </span>
      </div>

      {/* Component Items */}
      <div className="flex flex-col gap-1 overflow-y-auto">
        {componentItems.map((item) => (
          <div
            key={item.type}
            className={`w-10 h-8 px-3 py-1 flex items-center justify-start gap-1 text-sm border rounded-md cursor-move hover:shadow-md transition-all duration-200 overflow-hidden whitespace-nowrap group-hover:w-44 flex-shrink-0 ${item.className}`}
            draggable
            onDragStart={(event) => onDragStart(event, item.type as ComponentType)}
            title={`Drag to add ${item.label}`}
          >
            <div className="w-6 flex items-center justify-center flex-shrink-0">
              {item.icon}
            </div>
            <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100 font-medium">
              {item.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LeftComponentPalette;
