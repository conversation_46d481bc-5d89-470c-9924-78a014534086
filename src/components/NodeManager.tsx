import React, { useCallback } from 'react';
import { Node, ReactFlowInstance } from '@xyflow/react';

interface NodeManagerProps {
  reactFlowInstance: ReactFlowInstance | null;
  setSelectedComponent: (node: Node | null) => void;
  selectedComponent: Node | null;
  setRightPanelCollapsed?: (collapsed: boolean) => void;
}

// Changed from FC to a custom hook that returns handlers
export const useNodeManager = ({
  reactFlowInstance,
  setSelectedComponent,
  selectedComponent,
  setRightPanelCollapsed
}: NodeManagerProps) => {
  const handleNodeSelect = useCallback((node: Node | null, openRightPanel: boolean = false) => {
    console.log('Node selected in NodeManager component:', node?.id, 'openRightPanel:', openRightPanel);
    
    // Only set selected component when explicitly requesting to open the right panel
    if (openRightPanel) {
      setSelectedComponent(node);
    } else {
      // Don't set selected component when not opening the right panel
      // This prevents automatic right panel opening
    }

    // Only auto-expand right panel when explicitly requested
    if (node && openRightPanel && setRightPanelCollapsed) {
      setRightPanelCollapsed(false);
    }
  }, [setSelectedComponent, setRightPanelCollapsed]);

  const handleDeleteNode = useCallback((nodeId: string) => {
    if (reactFlowInstance) {
      // Get current nodes before deleting
      const currentNodes = reactFlowInstance.getNodes();

      // Find the node to be deleted
      const nodeToDelete = currentNodes.find(node => node.id === nodeId);

      // If this is a composite node, we need to delete all its children first
      if (nodeToDelete && nodeToDelete.type === 'compositeNode') {
        // Get all child nodes of this composite
        const childNodeIds = currentNodes
          .filter(node => node.data?.parentCompositeId === nodeId)
          .map(node => node.id);

        // Remove all child nodes first
        reactFlowInstance.setNodes((nodes) =>
          nodes.filter(node => !childNodeIds.includes(node.id))
        );
      }

      // Now remove the node itself
      reactFlowInstance.setNodes((nodes) => nodes.filter((node) => node.id !== nodeId));

      // Clear the selected component if it was the deleted one
      if (selectedComponent && selectedComponent.id === nodeId) {
        setSelectedComponent(null);
      }
    }
  }, [reactFlowInstance, selectedComponent, setSelectedComponent]);

  return {
    handleNodeSelect,
    handleDeleteNode
  };
};
