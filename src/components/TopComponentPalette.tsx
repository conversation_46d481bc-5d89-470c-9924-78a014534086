import React from 'react';
import { componentTypes, ComponentType } from '@/components/ComponentTypes';

interface TopComponentPaletteProps {
  showComponentPalette?: boolean;
}

const TopComponentPalette: React.FC<TopComponentPaletteProps> = ({
  showComponentPalette = true
}) => {
  // Handle component drag start
  const onDragStart = (event: React.DragEvent, componentType: ComponentType) => {
    const component = componentTypes[componentType];
    if (component) {
      // Set the drag data with component information
      event.dataTransfer.setData('application/reactflow', JSON.stringify({
        type: component.type,
        className: component.className,
        label: component.label
      }));
      event.dataTransfer.effectAllowed = 'move';
    }
  };

  // Get component items for the palette
  const componentItems = Object.values(componentTypes).map(component => ({
    type: component.type,
    label: component.label,
    icon: component.iconComponent,
    className: component.className
  }));

  if (!showComponentPalette) {
    return null;
  }

  return (
    <div className="top-component-palette w-full bg-gray-50 border-b border-gray-200 shadow-sm z-30">
      <div className="flex items-center px-4 py-2">
        {/* Component Items - Always expanded */}
        <div className="flex items-center gap-2 overflow-x-auto">
          {componentItems.map((item) => (
            <div
              key={item.type}
              className={`flex items-center gap-2 px-3 py-1.5 text-sm border rounded-md cursor-move hover:shadow-md transition-all duration-200 flex-shrink-0 ${item.className}`}
              draggable
              onDragStart={(event) => onDragStart(event, item.type as ComponentType)}
              title={`Drag to add ${item.label}`}
            >
              <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                {item.icon}
              </div>
              <span className="text-sm font-medium whitespace-nowrap">
                {item.label}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TopComponentPalette;
