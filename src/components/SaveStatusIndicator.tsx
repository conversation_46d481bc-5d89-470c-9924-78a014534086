import React from 'react';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { Check<PERSON><PERSON>cle, AlertCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface SaveStatusIndicatorProps {
  status: SaveStatus;
  lastSaved?: Date;
  isOnline?: boolean;
  className?: string;
  onClick?: () => void;
}

const SaveStatusIndicator: React.FC<SaveStatusIndicatorProps> = ({
  status,
  lastSaved,
  isOnline = true,
  className = '',
  onClick
}) => {
  const formatLastSaved = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);

    if (diffSeconds < 10) return 'just now';
    if (diffSeconds < 60) return `${diffSeconds}s ago`;
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  const getStatusConfig = () => {
    switch (status) {
      case SaveStatus.SAVING:
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          text: 'Saving...',
          className: 'bg-blue-500/90 hover:bg-blue-600/90 text-white border-none'
        };
      case SaveStatus.AUTO_SAVING:
        return {
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          text: 'saving...',
          className: 'bg-blue-500/90 hover:bg-blue-600/90 text-white border-none'
        };
      case SaveStatus.SAVED:
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'Saved',
          className: 'bg-green-500/90 hover:bg-green-600/90 text-white border-none'
        };
      case SaveStatus.AUTO_SAVED:
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'Saved',
          className: 'bg-green-500/90 hover:bg-green-600/90 text-white border-none'
        };
      case SaveStatus.PENDING:
        return {
          icon: <Loader2 className="h-4 w-4 animate-pulse" />,
          text: 'Pending...',
          className: 'bg-yellow-500/90 hover:bg-yellow-600/90 text-white border-none'
        };
      case SaveStatus.ERROR:
        return {
          icon: <AlertCircle className="h-4 w-4" />,
          text: 'Error',
          className: 'bg-red-500/90 hover:bg-red-600/90 text-white border-none'
        };
      default:
        return {
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'Save',
          className: 'bg-white/10 hover:bg-white/20 text-white border-white/20 hover:border-white/30'
        };
    }
  };

  const statusConfig = getStatusConfig();

  const getTooltipContent = () => {
    const parts = [];

    // Status info
    switch (status) {
      case SaveStatus.SAVING:
        parts.push('Currently saving your changes...');
        break;
      case SaveStatus.AUTO_SAVING:
        parts.push('Auto-saving your changes...');
        break;
      case SaveStatus.SAVED:
        parts.push('All changes saved successfully');
        break;
      case SaveStatus.AUTO_SAVED:
        parts.push('Changes auto-saved successfully');
        break;
      case SaveStatus.PENDING:
        parts.push('Changes pending auto-save...');
        break;
      case SaveStatus.ERROR:
        parts.push('Failed to save changes. Please try again.');
        break;
      default:
        parts.push('Ready to save changes');
    }

    // Last saved info
    if (lastSaved) {
      parts.push(`Last saved: ${formatLastSaved(lastSaved)}`);
    }

    // Click hint
    if (onClick && status !== SaveStatus.SAVING && status !== SaveStatus.AUTO_SAVING) {
      parts.push('💡 Click to save now');
    }

    return parts.join('\n');
  };

  const handleClick = () => {
    debugLog('💾 Save indicator clicked:', {
      hasOnClick: !!onClick,
      status,
      canSave: onClick && status !== SaveStatus.SAVING
    });

    if (onClick && status !== SaveStatus.SAVING && status !== SaveStatus.AUTO_SAVING) {
      debugLog('✅ Triggering save...');
      onClick();
    } else {
      debugLog('❌ Save not triggered - conditions not met');
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className={`h-8 px-3 py-1 flex items-center gap-2 text-sm backdrop-blur-sm ${statusConfig.className} ${className}`}
            onClick={handleClick}
            disabled={status === SaveStatus.SAVING || status === SaveStatus.AUTO_SAVING}
          >
            {statusConfig.icon}
            <span>{statusConfig.text}</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs">
          <div className="text-sm whitespace-pre-line">
            {getTooltipContent()}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default SaveStatusIndicator;
