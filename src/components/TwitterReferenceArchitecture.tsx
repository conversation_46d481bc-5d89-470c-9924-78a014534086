import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  Di<PERSON>Title,
  Di<PERSON>Trigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileIcon, ExternalLinkIcon } from 'lucide-react';

interface TwitterReferenceArchitectureProps {
  trigger?: React.ReactNode;
}

const TwitterReferenceArchitecture: React.FC<TwitterReferenceArchitectureProps> = ({
  trigger
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <FileIcon className="h-4 w-4" />
            View Twitter Reference Architecture
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Twitter Reference Architecture</DialogTitle>
          <DialogDescription>
            Compare your design with Twitter's actual architecture
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Core Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Frontend Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Mobile Apps (iOS, Android)</li>
                  <li>Web Application (React)</li>
                  <li>Progressive Web App</li>
                  <li>TweetDeck</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">API Gateway & Load Balancing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>GraphQL API Gateway</li>
                  <li>REST API Gateway</li>
                  <li>Rate limiting and throttling</li>
                  <li>Traffic routing and load balancing</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Tweet Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Tweet creation and validation</li>
                  <li>Media processing</li>
                  <li>Entity extraction (hashtags, mentions)</li>
                  <li>Tweet storage and retrieval</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Timeline Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Home timeline generation</li>
                  <li>User timeline retrieval</li>
                  <li>Timeline ranking algorithms</li>
                  <li>Timeline caching strategies</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Data Storage & Processing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Tweet Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Manhattan (distributed key-value store)</li>
                  <li>MySQL for relational data</li>
                  <li>Redis for caching</li>
                  <li>Hadoop for batch processing</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Media Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Blobstore for media files</li>
                  <li>CDN for global delivery</li>
                  <li>Media processing pipeline</li>
                  <li>Transcoding services</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Graph Database</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>FlockDB for social graph</li>
                  <li>Follower/following relationships</li>
                  <li>Graph traversal optimizations</li>
                  <li>Cached graph queries</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Search Infrastructure</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Lucene-based search engine</li>
                  <li>Real-time indexing</li>
                  <li>Earlybird for recent tweets</li>
                  <li>Distributed search clusters</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Scalability Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Fan-out Architecture</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Hybrid push/pull model</li>
                  <li>Push for users with few followers</li>
                  <li>Pull for high-follower accounts</li>
                  <li>Queue-based processing</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Caching Strategy</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Multi-level caching</li>
                  <li>Redis for timeline caches</li>
                  <li>Memcached for object caches</li>
                  <li>Cache invalidation strategies</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Messaging & Queues</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Event Bus for service communication</li>
                  <li>Kafka for event streaming</li>
                  <li>Manhattan Streams for change data capture</li>
                  <li>Queue-based workload distribution</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Real-time Processing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Storm for stream processing</li>
                  <li>Heron for real-time analytics</li>
                  <li>Real-time metrics and monitoring</li>
                  <li>Trending topics calculation</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Additional Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Notification System</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Push notification service</li>
                  <li>Email delivery system</li>
                  <li>In-app notification queue</li>
                  <li>Notification preferences management</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Analytics Platform</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Hadoop for batch processing</li>
                  <li>Presto for interactive queries</li>
                  <li>Druid for real-time analytics</li>
                  <li>Machine learning infrastructure</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Content Moderation</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Automated content filtering</li>
                  <li>Human moderation tools</li>
                  <li>Abuse detection algorithms</li>
                  <li>Safety and security features</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Direct Messaging</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Real-time messaging service</li>
                  <li>Message storage and retrieval</li>
                  <li>End-to-end encryption</li>
                  <li>Media sharing in DMs</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            <p>Note: This is a simplified representation of Twitter's architecture. The actual system is more complex and constantly evolving.</p>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <a 
            href="https://blog.twitter.com/engineering/en_us/topics/infrastructure/2017/the-infrastructure-behind-twitter-scale" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
          >
            Learn more from Twitter Engineering <ExternalLinkIcon className="h-3 w-3" />
          </a>
          <DialogClose asChild>
            <Button type="button">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default TwitterReferenceArchitecture;
