import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Di<PERSON><PERSON>rigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileIcon, ExternalLinkIcon } from 'lucide-react';

interface EcommerceReferenceArchitectureProps {
  trigger?: React.ReactNode;
}

const EcommerceReferenceArchitecture: React.FC<EcommerceReferenceArchitectureProps> = ({
  trigger
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <FileIcon className="h-4 w-4" />
            View E-commerce Reference Architecture
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>E-commerce Platform Reference Architecture</DialogTitle>
          <DialogDescription>
            Compare your design with production-grade e-commerce architectures
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Core Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Frontend Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Web Application (React/Next.js)</li>
                  <li>Mobile Apps (iOS, Android)</li>
                  <li>Progressive Web App</li>
                  <li>Content Delivery Network (CDN)</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">API Gateway & BFF</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>API Gateway for routing</li>
                  <li>Backend-for-Frontend (BFF) pattern</li>
                  <li>Rate limiting and throttling</li>
                  <li>Request authentication</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Authentication Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>User authentication</li>
                  <li>OAuth/OIDC integration</li>
                  <li>Session management</li>
                  <li>Multi-factor authentication</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Product Catalog</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Product information management</li>
                  <li>Categorization and taxonomy</li>
                  <li>Pricing and promotion engine</li>
                  <li>Content management system</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Shopping Experience</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Search & Discovery</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Elasticsearch/Algolia for search</li>
                  <li>Faceted navigation</li>
                  <li>Autocomplete and suggestions</li>
                  <li>Relevance tuning and personalization</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Shopping Cart</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Cart management service</li>
                  <li>Redis/DynamoDB for cart storage</li>
                  <li>Cross-device cart persistence</li>
                  <li>Promotion and coupon application</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Recommendation Engine</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Collaborative filtering</li>
                  <li>Content-based recommendations</li>
                  <li>Real-time personalization</li>
                  <li>A/B testing framework</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">User Profiles</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Customer data platform</li>
                  <li>Preference management</li>
                  <li>Wishlist and saved items</li>
                  <li>Purchase history</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Order Processing</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Checkout Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Multi-step checkout orchestration</li>
                  <li>Address validation</li>
                  <li>Tax calculation</li>
                  <li>Shipping options and rates</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Payment Processing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Payment gateway integration</li>
                  <li>Multiple payment methods</li>
                  <li>Fraud detection</li>
                  <li>PCI DSS compliance</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Order Management</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Order lifecycle management</li>
                  <li>Order splitting and routing</li>
                  <li>Status tracking and notifications</li>
                  <li>Returns and refunds processing</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Inventory Management</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Real-time inventory tracking</li>
                  <li>Multi-warehouse management</li>
                  <li>Reservation system</li>
                  <li>Backorder and pre-order handling</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Backend Infrastructure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Data Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Relational databases (PostgreSQL, MySQL)</li>
                  <li>NoSQL databases (MongoDB, DynamoDB)</li>
                  <li>In-memory databases (Redis)</li>
                  <li>Data warehouses (Snowflake, BigQuery)</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Caching Strategy</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>CDN for static assets</li>
                  <li>API response caching</li>
                  <li>Database query caching</li>
                  <li>Distributed caching (Redis, Memcached)</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Messaging & Events</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Message queues (RabbitMQ, SQS)</li>
                  <li>Event streaming (Kafka)</li>
                  <li>Pub/sub systems</li>
                  <li>Webhook delivery</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Scalability Features</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Horizontal scaling</li>
                  <li>Database sharding</li>
                  <li>Read replicas</li>
                  <li>Microservices architecture</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Additional Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Analytics & Reporting</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Business intelligence dashboards</li>
                  <li>Customer behavior analytics</li>
                  <li>Conversion funnel analysis</li>
                  <li>Sales and inventory forecasting</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Content & Marketing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Content management system</li>
                  <li>Email marketing integration</li>
                  <li>SEO optimization tools</li>
                  <li>Social media integration</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Customer Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Help desk integration</li>
                  <li>Live chat systems</li>
                  <li>Chatbot services</li>
                  <li>Customer feedback collection</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Security & Compliance</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>DDoS protection</li>
                  <li>Web application firewall</li>
                  <li>Data encryption</li>
                  <li>Compliance frameworks (GDPR, CCPA)</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            <p>Note: This is a simplified representation of a production e-commerce architecture. Real-world implementations may include additional components for specific business requirements.</p>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <a 
            href="https://aws.amazon.com/blogs/architecture/building-a-scalable-and-secure-multi-tenant-saas-solution-with-aws/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
          >
            Learn more about e-commerce architecture <ExternalLinkIcon className="h-3 w-3" />
          </a>
          <DialogClose asChild>
            <Button type="button">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EcommerceReferenceArchitecture;
