import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

import { Trash2, Database, HardDrive, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

const DebugCacheManager: React.FC = () => {
  const handleClearAllCaches = () => {
    // Clear localStorage (all design-related keys)
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.startsWith('layrs-design-') ||
        key.startsWith('layrs-course-progress-') ||
        key.startsWith('supabase.auth.token')
      )) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      debugLog(`Removed localStorage key: ${key}`);
    });

    toast.success(`Cleared ${keysToRemove.length} storage entries`);
  };

  const handleClearLocalStorage = () => {
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('layrs-')) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      debugLog(`Removed localStorage key: ${key}`);
    });

    toast.success(`Cleared ${keysToRemove.length} localStorage keys`);
  };

  const handleReloadPage = () => {
    window.location.reload();
  };

  const handleClearInstagramData = () => {
    // Clear all Instagram-related data from localStorage
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.toLowerCase().includes('instagram')) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
      debugLog(`Removed Instagram-related key: ${key}`);
    });

    toast.success('🧨 All Instagram data eliminated!');
  };

  const getLocalStorageInfo = () => {
    const layrsKeys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('layrs-')) {
        layrsKeys.push(key);
      }
    }
    return layrsKeys;
  };

  const layrsKeys = getLocalStorageInfo();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Debug: Cache Manager
        </CardTitle>
        <CardDescription>
          Clear various caches and storage to debug data persistence issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* LocalStorage Info */}
        <div className="p-3 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
            <HardDrive className="h-4 w-4" />
            LocalStorage Keys ({layrsKeys.length})
          </h4>
          {layrsKeys.length > 0 ? (
            <div className="text-xs text-gray-600 space-y-1">
              {layrsKeys.map(key => (
                <div key={key} className="font-mono">{key}</div>
              ))}
            </div>
          ) : (
            <div className="text-xs text-gray-500">No Layrs keys found</div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="grid grid-cols-2 gap-3">
          <Button
            variant="outline"
            onClick={handleClearLocalStorage}
            className="flex items-center gap-2"
          >
            <HardDrive className="h-4 w-4" />
            Clear LocalStorage
          </Button>

          <Button
            variant="destructive"
            onClick={handleClearAllCaches}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear Everything
          </Button>

          <Button
            variant="secondary"
            onClick={handleReloadPage}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Reload Page
          </Button>
        </div>

        {/* Nuclear Option */}
        <div className="border-2 border-red-200 rounded-lg p-3 bg-red-50">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-lg">🧨</span>
            <h4 className="font-medium text-red-800">Nuclear Option</h4>
          </div>
          <p className="text-xs text-red-600 mb-3">
            If Instagram design keeps appearing, use this nuclear option to eliminate ALL Instagram-related data from every possible cache:
            <br />• Course progress storage
            <br />• Design storage (localStorage + Supabase)
            <br />• In-memory caches
            <br />• Global variables
          </p>
          <Button
            variant="destructive"
            onClick={handleClearInstagramData}
            className="w-full flex items-center gap-2 bg-red-600 hover:bg-red-700"
          >
            <Trash2 className="h-4 w-4" />
            🧨 ELIMINATE INSTAGRAM DATA
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Memory Cache:</strong> Clears in-memory design cache</p>
          <p><strong>LocalStorage:</strong> Clears all Layrs localStorage keys</p>
          <p><strong>Clear Everything:</strong> Clears all caches + localStorage + current state</p>
          <p><strong>Reload Page:</strong> Hard refresh to clear all JavaScript state</p>
          <p><strong>Find Instagram:</strong> Use <code>layrsDebug.findInstagramData()</code> to locate Instagram data</p>
          <p><strong>Nuclear Clear:</strong> Use <code>layrsDebug.clearInstagramData()</code> to eliminate all Instagram data</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default DebugCacheManager;
