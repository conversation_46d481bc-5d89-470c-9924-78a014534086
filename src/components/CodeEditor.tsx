
import React from 'react';
import Editor, { OnChange, EditorProps } from '@monaco-editor/react';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  height?: string;
  minHeight?: string;
  options?: EditorProps['options'];
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  height = "200px",
  minHeight = "120px",
  options
}) => {
  return (
    <div style={{ padding: 0, margin: 0, height: height }}>
      <Editor
        height={height}
        defaultLanguage="javascript"
        value={value}
        onChange={(value) => onChange(value || '')}
        options={{
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          fontSize: 12,
          lineNumbers: 'on',
          automaticLayout: true,
          wordWrap: 'on',
          padding: { top: 0, bottom: 0 },
          overviewRulerLanes: 0,
          hideCursorInOverviewRuler: true,
          overviewRulerBorder: false,
          scrollbar: {
            vertical: 'auto',
            horizontal: 'auto',
            verticalScrollbarSize: 8,
            horizontalScrollbarSize: 8,
          },
          ...options,
        }}
        className="!p-0 !m-0"
        onMount={(editor) => {
          // Force remove any padding after mount
          const editorElement = editor.getDomNode();
          if (editorElement) {
            const removePadding = () => {
              editorElement.style.padding = '0';
              editorElement.style.margin = '0';

              // Find and modify all child elements
              const allElements = editorElement.querySelectorAll('*');
              allElements.forEach((el: any) => {
                el.style.paddingTop = '0';
                el.style.paddingBottom = '0';
                el.style.marginTop = '0';
                el.style.marginBottom = '0';
              });
            };

            // Remove padding immediately
            removePadding();

            // Set up a MutationObserver to watch for changes and re-apply our styles
            const observer = new MutationObserver(() => {
              removePadding();
            });

            observer.observe(editorElement, {
              childList: true,
              subtree: true,
              attributes: true,
              attributeFilter: ['style', 'class']
            });

            // Also use a timer to periodically check and remove padding
            const intervalId = setInterval(removePadding, 100);

            // Clean up after 5 seconds
            setTimeout(() => {
              observer.disconnect();
              clearInterval(intervalId);
            }, 5000);
          }
        }}
      />
    </div>
  );
};

export default CodeEditor;
