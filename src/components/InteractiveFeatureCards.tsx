
import React, { useState } from 'react';
import { PenTool, Target, TrendingUp, ArrowR<PERSON>, BarChart3, Zap } from 'lucide-react';

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  animation: React.ReactNode;
}

const InteractiveFeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description, animation }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className="relative text-center p-8 bg-white/80 backdrop-blur-sm border border-purple-100 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group overflow-hidden"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background animation */}
      <div className={`absolute inset-0 transition-opacity duration-500 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
        {animation}
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        <div className="w-16 h-16 mx-auto mb-6 rounded-xl bg-gradient-to-r from-purple-100 to-indigo-200 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
          {icon}
        </div>
        <h3 className="text-xl font-semibold text-zinc-900 mb-3">{title}</h3>
        <p className="text-zinc-600 leading-relaxed">{description}</p>
      </div>
    </div>
  );
};

const DragDropAnimation: React.FC = () => (
  <div className="absolute inset-0 flex items-center justify-center">
    <div className="relative">
      <div className="w-8 h-8 bg-purple-200 rounded border-2 border-dashed border-purple-400 animate-bounce">
        <PenTool className="h-4 w-4 text-purple-600 m-1" />
      </div>
      <ArrowRight className="absolute -right-6 top-1 h-4 w-4 text-purple-400 animate-pulse" />
      <div className="absolute -right-12 -top-1 w-10 h-10 bg-purple-100 rounded-lg border border-purple-300 flex items-center justify-center">
        <div className="w-2 h-2 bg-purple-500 rounded-full animate-ping"></div>
      </div>
    </div>
  </div>
);

const MetricsAnimation: React.FC = () => (
  <div className="absolute inset-0 flex items-center justify-center">
    <div className="grid grid-cols-2 gap-2 text-xs">
      <div className="bg-green-100 p-2 rounded text-center">
        <div className="text-green-700 font-bold animate-pulse">99.9%</div>
        <div className="text-green-600">Uptime</div>
      </div>
      <div className="bg-blue-100 p-2 rounded text-center">
        <div className="text-blue-700 font-bold">
          <span className="animate-pulse">1.2k</span>
        </div>
        <div className="text-blue-600">QPS</div>
      </div>
      <div className="bg-yellow-100 p-2 rounded text-center">
        <div className="text-yellow-700 font-bold">
          <span className="animate-bounce">45ms</span>
        </div>
        <div className="text-yellow-600">Latency</div>
      </div>
      <div className="bg-purple-100 p-2 rounded text-center">
        <div className="text-purple-700 font-bold animate-pulse">94%</div>
        <div className="text-purple-600">Cache Hit</div>
      </div>
    </div>
  </div>
);

const FeedbackAnimation: React.FC = () => (
  <div className="absolute inset-0 flex items-center justify-center">
    <div className="relative">
      <div className="w-12 h-8 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center">
        <BarChart3 className="h-4 w-4 text-gray-500" />
      </div>
      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-ping">
        <Zap className="h-3 w-3 text-white" />
      </div>
      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-green-100 text-green-700 text-xs px-2 py-1 rounded whitespace-nowrap animate-fade-in">
        Optimized!
      </div>
    </div>
  </div>
);

const InteractiveFeatureCards: React.FC = () => {
  return (
    <div className="grid md:grid-cols-3 gap-8">
      <InteractiveFeatureCard
        icon={<PenTool className="h-8 w-8 text-purple-600" />}
        title="Interactive Design"
        description="Create system architecture diagrams with our intuitive drag-and-drop interface"
        animation={<DragDropAnimation />}
      />
      <InteractiveFeatureCard
        icon={<Target className="h-8 w-8 text-green-600" />}
        title="Practice Questions"
        description="Solve real interview questions from top tech companies with guided solutions"
        animation={<MetricsAnimation />}
      />
      <InteractiveFeatureCard
        icon={<TrendingUp className="h-8 w-8 text-purple-600" />}
        title="Expert Feedback"
        description="Get AI-powered feedback and suggestions to improve your system designs"
        animation={<FeedbackAnimation />}
      />
    </div>
  );
};

export default InteractiveFeatureCards;
