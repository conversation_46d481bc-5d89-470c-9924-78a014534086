import React, { useState, useEffect, useCallback } from 'react';
import TutorialPopup, { TutorialStep } from './TutorialPopup';
import { Edge, MarkerType, ReactFlowInstance, Connection } from '@xyflow/react';
import '../styles/tutorial.css'; // Import the tutorial styles
import { componentTypes } from './ComponentTypes';
import { toast } from 'sonner';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Define tutorial steps
const tutorialSteps: TutorialStep[] = [
  {
    id: 'component-palette',
    title: 'Component Palette',
    description: 'This is your toolbox. Drag components from here onto the canvas to build your system design.',
    targetElementId: 'component-palette',
    position: 'right',
    offsetX: 10
  },
  {
    id: 'regular-component',
    title: 'Regular Components',
    description: 'Use these pre-defined components like Servers, Databases, and APIs to quickly design your system.',
    targetElementId: 'server-component',
    position: 'bottom',
    offsetY: 10
  },
  {
    id: 'primitive-component',
    title: 'Primitive Component',
    description: 'This is a fully customizable component. You can modify its properties to represent any system element.',
    targetElementId: 'primitive-component',
    position: 'bottom',
    offsetY: 10
  },
  {
    id: 'composite-component',
    title: 'Composite Component',
    description: 'This special component lets you group related components together. Drag other components inside to create logical groupings.',
    targetElementId: 'composite-component',
    position: 'left',
    offsetX: 10
  },
  {
    id: 'connecting-components',
    title: 'Connecting Components',
    description: 'Now create a connection between the Frontend and Server components. Click on the right handle of the Frontend, hold and drag to the left handle of the Server.',
    targetElementId: null,
    position: 'bottom',
    isConnectionStep: true,
    sourceNodeId: 'tutorial-frontend-node',
    targetNodeId: 'tutorial-server-node',
    connectionLabel: 'Request'
  }
];

interface TutorialManagerProps {
  reactFlowInstance?: ReactFlowInstance | null; // Reference to ReactFlow instance
}

const TutorialManager: React.FC<TutorialManagerProps> = ({ reactFlowInstance }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState<number | null>(null);
  const [tutorialCompleted, setTutorialCompleted] = useState<boolean>(false);
  const [canvasHasNodes, setCanvasHasNodes] = useState<boolean>(false);
  const [sourceTargetNodes, setSourceTargetNodes] = useState<{ source: string | null, target: string | null }>({
    source: null, target: null
  });
  const [tutorialNodesCreated, setTutorialNodesCreated] = useState<boolean>(false);
  const [connectionCompleted, setConnectionCompleted] = useState<boolean>(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);
  
  // Track if we're on the connection step
  const isConnectionStep = currentStepIndex !== null && 
    tutorialSteps[currentStepIndex].isConnectionStep;

  // Check if tutorial has been shown before
  useEffect(() => {
    const tutorialShown = localStorage.getItem('layrsTutorialShown') === 'true';
    
    // If tutorial hasn't been shown, initialize it
    if (!tutorialShown && currentStepIndex === null) {
      debugLog('Starting tutorial');
      setCurrentStepIndex(0);
    }
  }, [currentStepIndex]);

  // Setup a connection listener for the tutorial step using a custom event listener
  useEffect(() => {
    if (!reactFlowInstance || !isConnectionStep) return;

    // Create a connection handler function
    const handleConnect = (params: Connection) => {
      debugLog('Connection detected:', params);
      
      // If we are in the connection step, and this is a connection between our tutorial nodes
      if (isConnectionStep && 
          params.source?.startsWith('tutorial-') && 
          params.target?.startsWith('tutorial-')) {
        
        debugLog('Tutorial connection created!');
        setConnectionCompleted(true);
        setShowSuccessMessage(true);
        
        // Show success message then proceed to next step
        setTimeout(() => {
          setShowSuccessMessage(false);
          
          // Remove tutorial nodes after the success message is shown
          if (reactFlowInstance) {
            // Clean up any tutorial nodes
            reactFlowInstance.setNodes((nodes) => 
              nodes.filter(n => !n.id.startsWith('tutorial-'))
            );
            
            // Also remove any tutorial edges
            reactFlowInstance.setEdges((edges) => 
              edges.filter(e => !e.id.startsWith('tutorial-'))
            );
          }
          
          // Auto-advance to next step or complete tutorial
          if (currentStepIndex !== null && currentStepIndex < tutorialSteps.length - 1) {
            setCurrentStepIndex(currentStepIndex + 1);
          } else {
            completeTutorial();
          }
        }, 2000);
        
        toast.success("Great job! You've created your first connection.");
      }
    };

    // Store the event handler in a ref to use in cleanup
    const connectHandler = handleConnect;
    
    // Publish our handler to a global variable that DiagramEditor can access
    (window as any).__tutorialConnectHandler = connectHandler;
    
    // Cleanup
    return () => {
      // Clean up the global handler
      if ((window as any).__tutorialConnectHandler === connectHandler) {
        delete (window as any).__tutorialConnectHandler;
      }
    };
  }, [reactFlowInstance, isConnectionStep, currentStepIndex]);

  // Create tutorial nodes for the connection step
  useEffect(() => {
    if (isConnectionStep && reactFlowInstance && !tutorialNodesCreated) {
      debugLog('Creating sample nodes for tutorial connection demo');
      
      // Create placeholder nodes using actual component types from our palette
      const frontendComponent = componentTypes['frontend'] || componentTypes['client'];
      const serverComponent = componentTypes['server'];
      
      // Use the same Y position for both nodes to ensure perfect alignment
      const centerY = window.innerHeight / 2 - 50;
      
      const frontendNode = {
        id: 'tutorial-frontend-node',
        type: 'customNode', // Use our custom node type
        position: { x: window.innerWidth / 2 - 250, y: centerY },
        data: { 
          label: frontendComponent ? frontendComponent.label : 'Frontend',
          type: frontendComponent ? frontendComponent.type : 'frontend', 
          className: frontendComponent ? frontendComponent.className : 'bg-blue-100',
          isTutorialNode: true // Mark as tutorial node to prevent deletion
        },
        className: 'tutorial-node',
        deletable: false, // Make nodes non-deletable during tutorial
        // Allow dragging during tutorial so users can interact
        // Fix dimensions explicitly
        width: 150,
        height: 50
      };
      
      const serverNode = {
        id: 'tutorial-server-node',
        type: 'customNode', // Use our custom node type
        position: { x: window.innerWidth / 2 + 100, y: centerY }, // Same Y position for alignment
        data: { 
          label: serverComponent ? serverComponent.label : 'Server',
          type: serverComponent ? serverComponent.type : 'server', 
          className: serverComponent ? serverComponent.className : 'bg-green-100',
          isTutorialNode: true // Mark as tutorial node to prevent deletion 
        },
        className: 'tutorial-node',
        deletable: false, // Make nodes non-deletable during tutorial
        // Allow dragging during tutorial so users can interact
        // Fix dimensions explicitly
        width: 150,
        height: 50
      };
      
      // Set the nodes with a slight delay to ensure React Flow is ready
      setTimeout(() => {
        reactFlowInstance.setNodes([frontendNode, serverNode]);
        debugLog('Sample tutorial nodes created');
        setTutorialNodesCreated(true);
        
        // Wait a bit more before marking canvas as having nodes
        setTimeout(() => {
          setCanvasHasNodes(true);
          highlightNodeHandles();
        }, 500);
        
        // Fit view to make sure the nodes are visible
        reactFlowInstance.fitView({ 
          padding: 0.2, 
          includeHiddenNodes: true,
          nodes: [frontendNode, serverNode]
        });
      }, 100);
    }
  }, [currentStepIndex, reactFlowInstance, canvasHasNodes, isConnectionStep, tutorialNodesCreated]);

  // Modified highlight function to target the main left handle instead of a secondary handle
  const highlightNodeHandles = useCallback(() => {
    if (isConnectionStep) {
      try {
        // Find handles on tutorial nodes
        const sourceEl = document.querySelector(`[data-id="tutorial-frontend-node"]`);
        const targetEl = document.querySelector(`[data-id="tutorial-server-node"]`);
        
        if (sourceEl && targetEl) {
          // Highlight the source node's right handle
          const sourceRightHandle = sourceEl.querySelector('.react-flow__handle-right');
          if (sourceRightHandle) {
            sourceRightHandle.classList.add('connection-point', 'source-hint');
            debugLog('Added highlight class to source right handle');
          }
          
          // Highlight the target node's LEFT handle (the main one, not a secondary handle)
          // Here we specifically target the first left handle without any ID suffix
          const targetLeftHandle = targetEl.querySelector('.react-flow__handle-left');
          if (targetLeftHandle) {
            targetLeftHandle.classList.add('connection-point', 'target-hint');
            debugLog('Added highlight class to target left handle (main one)');
          }
        }
      } catch (err) {
        debugError('Error highlighting handles:', err);
      }
    }
  }, [isConnectionStep]);

  // Call highlightNodeHandles whenever connection step is active
  useEffect(() => {
    if (isConnectionStep && tutorialNodesCreated) {
      // Give a moment for the DOM to update
      const intervalId = setInterval(() => {
        highlightNodeHandles();
      }, 1000); // Keep checking/highlighting periodically
      
      return () => clearInterval(intervalId);
    }
  }, [isConnectionStep, tutorialNodesCreated, highlightNodeHandles]);

  const handleNext = () => {
    if (currentStepIndex !== null && currentStepIndex < tutorialSteps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      completeTutorial();
    }
  };

  const handleSkip = () => {
    completeTutorial();
  };

  const completeTutorial = useCallback(() => {
    setCurrentStepIndex(null);
    setTutorialCompleted(true);
    localStorage.setItem('layrsTutorialShown', 'true');
    
    // Clean up any tutorial nodes when tutorial completes
    if (reactFlowInstance) {
      reactFlowInstance.setNodes((nodes) => 
        nodes.filter(n => !n.id.startsWith('tutorial-'))
      );
      
      // Also remove any tutorial edges
      reactFlowInstance.setEdges((edges) => 
        edges.filter(e => !e.id.startsWith('tutorial-'))
      );
      
      setTutorialNodesCreated(false);
    }
  }, [reactFlowInstance]);

  // If tutorial is not active, don't render anything
  if (currentStepIndex === null) {
    return null;
  }

  const currentStep = tutorialSteps[currentStepIndex];
  const isLastStep = currentStepIndex === tutorialSteps.length - 1;

  return (
    <>
      {showSuccessMessage && (
        <div className="connection-success">
          Connection Created Successfully!
        </div>
      )}
      <TutorialPopup
        step={currentStep}
        onNext={handleNext}
        onSkip={handleSkip}
        onComplete={completeTutorial}
        isLastStep={isLastStep}
      />
    </>
  );
};

export default TutorialManager;
