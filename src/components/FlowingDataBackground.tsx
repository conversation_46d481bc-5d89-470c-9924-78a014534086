
import React, { useEffect, useState } from 'react';

interface DataPacket {
  id: string;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  progress: number;
  speed: number;
  size: number;
  color: string;
}

const FlowingDataBackground: React.FC = () => {
  const [packets, setPackets] = useState<DataPacket[]>([]);

  const colors = ['#8b5cf6', '#3b82f6', '#10b981', '#f59e0b'];
  
  const createPacket = (): DataPacket => {
    const startSide = Math.floor(Math.random() * 4);
    let x, y, targetX, targetY;
    
    switch (startSide) {
      case 0: // top
        x = Math.random() * 100;
        y = -5;
        targetX = Math.random() * 100;
        targetY = 105;
        break;
      case 1: // right
        x = 105;
        y = Math.random() * 100;
        targetX = -5;
        targetY = Math.random() * 100;
        break;
      case 2: // bottom
        x = Math.random() * 100;
        y = 105;
        targetX = Math.random() * 100;
        targetY = -5;
        break;
      default: // left
        x = -5;
        y = Math.random() * 100;
        targetX = 105;
        targetY = Math.random() * 100;
    }

    return {
      id: Math.random().toString(36),
      x,
      y,
      targetX,
      targetY,
      progress: 0,
      speed: 0.3 + Math.random() * 0.7,
      size: 2 + Math.random() * 3,
      color: colors[Math.floor(Math.random() * colors.length)]
    };
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setPackets(prevPackets => {
        // Update existing packets
        const updatedPackets = prevPackets
          .map(packet => ({
            ...packet,
            progress: packet.progress + packet.speed
          }))
          .filter(packet => packet.progress < 100);

        // Add new packet occasionally
        if (Math.random() < 0.1 && updatedPackets.length < 20) {
          updatedPackets.push(createPacket());
        }

        return updatedPackets;
      });
    }, 50);

    return () => clearInterval(interval);
  }, []);

  const interpolate = (start: number, end: number, progress: number) => {
    return start + (end - start) * (progress / 100);
  };

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <svg className="w-full h-full opacity-40">
        {packets.map(packet => {
          const currentX = interpolate(packet.x, packet.targetX, packet.progress);
          const currentY = interpolate(packet.y, packet.targetY, packet.progress);
          const opacity = packet.progress < 10 ? packet.progress / 10 : 
                         packet.progress > 90 ? (100 - packet.progress) / 10 : 1;

          return (
            <circle
              key={packet.id}
              cx={`${currentX}%`}
              cy={`${currentY}%`}
              r={packet.size}
              fill={packet.color}
              opacity={opacity}
              className="animate-pulse"
            />
          );
        })}
        
        {/* Static network grid */}
        <defs>
          <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
            <path d="M 50 0 L 0 0 0 50" fill="none" stroke="url(#gridGradient)" strokeWidth="0.5" opacity="0.3"/>
          </pattern>
          <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.1"/>
            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1"/>
          </linearGradient>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>
  );
};

export default FlowingDataBackground;
