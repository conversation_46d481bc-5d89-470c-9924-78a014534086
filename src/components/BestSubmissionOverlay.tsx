import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Trophy, Star, Calendar, Download } from 'lucide-react';
import { ReactFlowProvider, ReactFlow, Background, Controls, useReactFlow } from '@xyflow/react';
import { SavedDesign } from '@/contexts/DesignContext';
import { loadBestDesignFromSupabase } from '@/services/bestDesignService';
import { useAuth } from '@/contexts/AuthContext';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { toast } from 'sonner';
import { nodeTypes, getDefaultEdgeOptions, edgeTypes } from '@/components/DiagramEditorConstants';
import '@xyflow/react/dist/style.css';

interface BestSubmissionOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  onLoadDesign: () => void;
  questionId: string;
  contextType: DesignContextCategory;
  contextId: string;
}

const BestSubmissionOverlay: React.FC<BestSubmissionOverlayProps> = ({
  isOpen,
  onClose,
  onLoadDesign,
  questionId,
  contextType,
  contextId
}) => {
  const { user } = useAuth();
  const [bestDesign, setBestDesign] = useState<SavedDesign | null>(null);
  const [score, setScore] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  // Load best design data when overlay opens
  useEffect(() => {
    if (isOpen && user && questionId) {
      loadBestDesignData();
    }
  }, [isOpen, user, questionId, contextType, contextId]);

  const loadBestDesignData = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const bestDesignData = await loadBestDesignFromSupabase(
        user.id,
        questionId,
        contextType,
        contextId
      );

      if (bestDesignData) {
        setBestDesign(bestDesignData.design);
        setScore(bestDesignData.score);
      } else {
        toast.error('No best design found');
        onClose();
      }
    } catch (error) {
      console.error('Error loading best design:', error);
      toast.error('Failed to load best design');
      onClose();
    } finally {
      setLoading(false);
    }
  };

  const handleLoadDesign = () => {
    onLoadDesign();
    onClose();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderStars = (score: number) => {
    return Array.from({ length: 10 }, (_, i) => (
      <Star
        key={i}
        size={16}
        className={`${
          i < score
            ? 'text-yellow-400 fill-yellow-400'
            : 'text-gray-300'
        }`}
      />
    ));
  };

  // Component for the ReactFlow preview with proper fit view handling
  const DesignPreview: React.FC<{ design: SavedDesign }> = ({ design }) => {
    const { fitView } = useReactFlow();

    useEffect(() => {
      // Apply fit view after a short delay to ensure nodes are rendered
      const timer = setTimeout(() => {
        fitView({
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.1,
          maxZoom: 1.5,
          duration: 800
        });
      }, 100);

      return () => clearTimeout(timer);
    }, [design.nodes, design.edges, fitView]);

    return (
      <ReactFlow
        nodes={design.nodes || []}
        edges={design.edges || []}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        defaultEdgeOptions={getDefaultEdgeOptions('smooth')}
        fitView
        fitViewOptions={{
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.1,
          maxZoom: 1.5
        }}
        attributionPosition="bottom-left"
        proOptions={{ hideAttribution: true }}
        nodesDraggable={false}
        nodesConnectable={false}
        elementsSelectable={false}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={false}
        minZoom={0.1}
        maxZoom={2}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
      >
        <Background />
        <Controls showInteractive={false} />
      </ReactFlow>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Best Submission Preview
          </DialogTitle>
          <DialogDescription>
            Review your best submission before loading it onto the canvas
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto min-h-0">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : bestDesign && score !== null ? (
            <div className="space-y-4 p-1">
              {/* Score and metadata */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg font-semibold text-gray-900">Score: {score}/10</span>
                      <div className="flex gap-1">
                        {renderStars(score)}
                      </div>
                    </div>
                    {bestDesign.lastModified && (
                      <div className="flex items-center gap-1 text-sm text-gray-600">
                        <Calendar size={14} />
                        <span>Achieved on {formatDate(bestDesign.lastModified)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Design preview */}
              <div className="border border-gray-200 rounded-lg overflow-hidden bg-gray-50">
                <div className="h-96 w-full">
                  <ReactFlowProvider>
                    <DesignPreview design={bestDesign} />
                  </ReactFlowProvider>
                </div>
              </div>

              {/* Context information */}
              {(bestDesign.userJourneys || bestDesign.assumptions || bestDesign.constraints) && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  {bestDesign.userJourneys && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <h4 className="font-medium text-blue-900 mb-2">User Journeys</h4>
                      <p className="text-blue-800 text-xs leading-relaxed">{bestDesign.userJourneys}</p>
                    </div>
                  )}
                  {bestDesign.assumptions && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <h4 className="font-medium text-green-900 mb-2">Assumptions</h4>
                      <p className="text-green-800 text-xs leading-relaxed">{bestDesign.assumptions}</p>
                    </div>
                  )}
                  {bestDesign.constraints && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <h4 className="font-medium text-orange-900 mb-2">Constraints</h4>
                      <p className="text-orange-800 text-xs leading-relaxed">{bestDesign.constraints}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <p>No best submission found</p>
            </div>
          )}
        </div>

        <DialogFooter className="flex-shrink-0 flex justify-between mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          {bestDesign && (
            <Button onClick={handleLoadDesign} className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700">
              <Download className="h-4 w-4 mr-2" />
              Load onto Canvas
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BestSubmissionOverlay;
