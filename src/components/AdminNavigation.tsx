import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { Button } from '@/components/ui/button';
import { Shield, Users, Settings, BarChart3, MessageSquare } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const AdminNavigation: React.FC = () => {
  const { isAdmin } = useAdminAccess();
  const location = useLocation();

  // Don't show admin navigation if user is not admin
  if (!isAdmin) {
    return null;
  }

  const adminRoutes = [
    {
      path: '/admin/waitlist',
      label: 'Waitlist Management',
      icon: Users,
      description: 'Manage beta invitations and waitlist'
    },
    {
      path: '/admin/analytics',
      label: 'Platform Analytics',
      icon: BarChart3,
      description: 'View chat and assessment metrics'
    },
    {
      path: '/admin/feedback',
      label: 'Feedback Analytics',
      icon: MessageSquare,
      description: 'Analyze user feedback and beta testing insights'
    },
    // Add more admin routes here as needed
    // {
    //   path: '/admin/settings',
    //   label: 'System Settings',
    //   icon: Settings,
    //   description: 'Configure system settings'
    // }
  ];

  const isCurrentRoute = (path: string) => location.pathname === path;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={location.pathname.startsWith('/admin') ? 'default' : 'ghost'}
          size="sm"
          className="gap-2"
        >
          <Shield className="h-4 w-4" />
          Admin
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Admin Panel
        </DropdownMenuLabel>
        <DropdownMenuSeparator />

        {adminRoutes.map((route) => {
          const Icon = route.icon;
          return (
            <DropdownMenuItem key={route.path} asChild>
              <Link
                to={route.path}
                className={`flex items-start gap-3 p-2 ${
                  isCurrentRoute(route.path) ? 'bg-accent' : ''
                }`}
              >
                <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <div className="flex flex-col">
                  <span className="font-medium">{route.label}</span>
                  <span className="text-xs text-muted-foreground">
                    {route.description}
                  </span>
                </div>
              </Link>
            </DropdownMenuItem>
          );
        })}

        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link to="/home" className="flex items-center gap-2">
            <span>← Back to App</span>
          </Link>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AdminNavigation;
