import React, { useState, useRef, useEffect } from 'react';
import { Send, ChevronDown, RotateCcw, Wifi, WifiOff } from 'lucide-react';
import { ChatMessage, sendChatMessage, getSessionForContext, startNewSessionForContext } from '@/services/chatService';
import { loadChatHistory, addMessageToCache, clearChatCache, isOnline } from '@/services/chatHistoryService';
import { useDesignState } from '@/hooks/useDesignState';
import { useCourse } from '@/contexts/CourseContext';
import { useAuth } from '@/contexts/AuthContext';
import { ReactFlowInstance } from '@xyflow/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  feedbackLevels,
  FeedbackLevel,
  DEFAULT_FEEDBACK_LEVEL,
  FEEDBACK_LEVEL_STORAGE_KEY,
  getFeedbackLevelConfig
} from '@/types/feedbackTypes';
import { useChatAnalytics } from '@/hooks/useAnalytics';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface ChatPanelProps {
  reactFlowInstance?: ReactFlowInstance | null;
  contextType?: string;
  contextId?: string;
}

const ChatPanel: React.FC<ChatPanelProps> = ({
  reactFlowInstance,
  contextType = 'free',
  contextId = 'free-canvas'
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  // Only allow 'educational' (Learning Mode)
  const feedbackLevel: FeedbackLevel['value'] = 'educational';

  // Initialize chat analytics
  const {
    sessionId: analyticsSessionId,
    messageCount,
    startChatSession,
    trackMessage,
    endChatSession,
    isActive: isAnalyticsActive
  } = useChatAnalytics(
    contextType === 'question' ? contextId : undefined,
    contextType as 'free' | 'question' | 'guided',
    contextId
  );
  const [onlineStatus, setOnlineStatus] = useState(isOnline());
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get the current design state
  const {
    userJourneys,
    assumptions,
    constraints,
    currentQuestion
  } = useDesignState();

  // Get the current course state
  const { currentCourse } = useCourse();
  
  // Get the current user
  const { user } = useAuth();

  const currentContextRef = useRef<string>('');

  // Load chat history when context changes
  useEffect(() => {
    const contextKey = `${contextType}-${contextId}`;

    // Skip if same context
    if (currentContextRef.current === contextKey) return;

    currentContextRef.current = contextKey;

    const loadHistory = async () => {
      setIsLoadingHistory(true);
      try {
        debugLog(`📋 Loading chat history for ${contextType}:${contextId}`);
        const history = await loadChatHistory(contextType, contextId);

        if (history.length > 0) {
          setMessages(history);
          debugLog(`✅ Loaded ${history.length} messages from cache/backend`);
        } else {
          // No history - show welcome message
          setMessages([
            {
              id: '1',
              content: `Hello! I'm your design assistant for ${contextType === 'question' ? 'this question' : contextType === 'course' ? 'this course' : 'free canvas mode'}. How can I help you with your system design today?`,
              sender: 'assistant',
              timestamp: new Date(),
            },
          ]);

          // Start analytics session for new conversations
          if (!isAnalyticsActive) {
            await startChatSession();
          }
        }
      } catch (error) {
        console.error('Error loading chat history:', error);
        // Fallback to welcome message
        setMessages([
          {
            id: '1',
            content: `Hello! I'm your design assistant. How can I help you with your system design today?`,
            sender: 'assistant',
            timestamp: new Date(),
          },
        ]);
      } finally {
        setIsLoadingHistory(false);
      }
    };

    loadHistory();
  }, [contextType, contextId]);

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setOnlineStatus(true);
    const handleOffline = () => setOnlineStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Scroll to bottom of messages when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle starting a new session for this context
  const handleNewSession = async () => {
    // End current analytics session if active
    if (isAnalyticsActive) {
      await endChatSession();
    }

    // Clear cache for this context
    clearChatCache(contextType, contextId);

    // Start new session
    startNewSessionForContext(contextType, contextId);

    // Reset messages to welcome message
    setMessages([
      {
        id: '1',
        content: `Hello! I'm your design assistant for ${contextType === 'question' ? 'this question' : contextType === 'course' ? 'this course' : 'free canvas mode'}. How can I help you with your system design today?`,
        sender: 'assistant',
        timestamp: new Date(),
      },
    ]);

    // Start new analytics session
    await startChatSession();

    debugLog(`🆕 Started new session for ${contextType}:${contextId}`);
  };

  const currentFeedbackConfig = getFeedbackLevelConfig(feedbackLevel);

  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!inputValue.trim() || isLoading) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    const messageToSend = inputValue;
    setInputValue('');
    setIsLoading(true);

    try {
      // Get current nodes and edges from ReactFlow instance
      const currentNodes = reactFlowInstance?.getNodes() || [];
      const currentEdges = reactFlowInstance?.getEdges() || [];

      debugLog('ChatPanel sending data to chat service:', {
        nodeCount: currentNodes.length,
        edgeCount: currentEdges.length,
        nodes: currentNodes,
        edges: currentEdges,
        userJourneys: userJourneys,
        assumptions: assumptions,
        constraints: constraints,
        currentQuestion: currentQuestion,
        currentCourse: currentCourse
      });

      // Call the new System Design Agent (no streaming)
      const response = await sendChatMessage(
        messageToSend,
        currentNodes,
        currentEdges,
        userJourneys,
        assumptions,
        constraints,
        currentQuestion,
        currentCourse,
        undefined, // onStream callback
        feedbackLevel, // Pass the feedback level
        contextType, // Pass context type
        contextId, // Pass context ID
        user?.id // Pass user ID
      );

      // Add the assistant response
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);

      // Cache both user and assistant messages
      const sessionId = getSessionForContext(contextType, contextId);
      addMessageToCache(contextType, contextId, userMessage, sessionId);
      addMessageToCache(contextType, contextId, assistantMessage, sessionId);

      // Track message in analytics (count both user and assistant messages)
      await trackMessage();
      await trackMessage();
    } catch (error) {
      console.error('Error getting chat response:', error);

      // Use the error message from the API if available, otherwise use a generic message
      const errorContent = error instanceof Error ? error.message : "I'm sorry, I encountered an error while processing your request. Please try again later.";
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: errorContent,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="chat-container">
      {/* Only show Online/Offline Indicator and New Session Button */}
      <div className="chat-feedback-controls">
        <div className="flex items-center justify-between">
          {/* Online/Offline Indicator */}
          <div className={`flex items-center gap-1 px-2 py-1 rounded text-xs ${
            onlineStatus
              ? 'bg-green-50 text-green-700 border border-green-200'
              : 'bg-red-50 text-red-700 border border-red-200'
          }`}>
            {onlineStatus ? (
              <>
                <Wifi className="h-3 w-3" />
                <span>Online</span>
              </>
            ) : (
              <>
                <WifiOff className="h-3 w-3" />
                <span>Offline</span>
              </>
            )}
          </div>
          {/* New Session Button */}
          <button
            onClick={handleNewSession}
            className="flex items-center gap-1.5 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-md transition-colors text-blue-700"
            title="Start a new chat session"
          >
            <RotateCcw className="h-3 w-3" />
            <span>New Session</span>
          </button>
        </div>
      </div>

      <div className="chat-messages">
        {/* Loading history indicator */}
        {isLoadingHistory && (
          <div className="chat-message-assistant">
            <div className="chat-message-content">
              <div className="chat-avatar chat-avatar-assistant">
                AI
              </div>
              <div className="chat-text">
                <div className="flex items-center gap-2 text-gray-500">
                  <div className="chat-loading-dots">
                    <div className="chat-loading-dot"></div>
                    <div className="chat-loading-dot"></div>
                    <div className="chat-loading-dot"></div>
                  </div>
                  <span>Loading chat history...</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {!isLoadingHistory && messages.map((message) => (
          <div
            key={message.id}
            className={message.sender === 'user' ? 'chat-message-user' : 'chat-message-assistant'}
          >
            <div className="chat-message-content">
              <div className={`chat-avatar ${message.sender === 'user' ? 'chat-avatar-user' : 'chat-avatar-assistant'}`}>
                {message.sender === 'user' ? 'U' : 'AI'}
              </div>
              <div className="chat-text">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {message.content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        ))}

        {/* Loading indicator */}
        {isLoading && (
          <div className="chat-message-assistant">
            <div className="chat-message-content">
              <div className="chat-avatar chat-avatar-assistant">
                AI
              </div>
              <div className="chat-text">
                <div className="chat-loading-dots">
                  <div className="chat-loading-dot"></div>
                  <div className="chat-loading-dot"></div>
                  <div className="chat-loading-dot"></div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="chat-input">
        <div className="chat-input-container">
          <textarea
            placeholder={onlineStatus ? "Ask about system design..." : "Offline - view cached messages only"}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey && onlineStatus) {
                e.preventDefault();
                handleSendMessage();
              }
            }}
            disabled={isLoading || !onlineStatus}
            className="chat-input-field"
            rows={1}
            style={{
              height: 'auto',
              minHeight: '3rem',
              maxHeight: '12rem',
              resize: 'none'
            }}
            onInput={(e) => {
              const target = e.target as HTMLTextAreaElement;
              target.style.height = 'auto';
              target.style.height = Math.min(target.scrollHeight, 192) + 'px';
            }}
          />
          <button
            type="button"
            onClick={handleSendMessage}
            disabled={isLoading || !inputValue.trim() || !onlineStatus}
            className="chat-send-button"
            title={!onlineStatus ? "Cannot send messages while offline" : "Send message"}
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChatPanel;
