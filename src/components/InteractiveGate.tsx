
import React, { useState, useEffect, useCallback } from 'react';
import { Globe, Server } from 'lucide-react';
import {
  ReactFlow,
  Node,
  Edge,
  useNodesState,
  useEdgesState,
  Connection,
  addEdge,
  Handle,
  Position,
  ReactFlowProvider,
  MarkerType
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

interface InteractiveGateProps {
  onComplete: () => void;
}

// Luxurious Frontend Node Component
const FrontendNode = () => (
  <div className="relative group cursor-pointer select-none">
    <Handle
      type="source"
      position={Position.Right}
      className="!w-full !h-full !bg-transparent !border-0 !right-0 !top-0 !transform-none !opacity-0"
      style={{ width: '100%', height: '100%', right: 0, top: 0 }}
    />
    <div className="w-40 h-40 bg-blue-500/10 backdrop-blur-xl rounded-3xl flex items-center justify-center shadow-2xl border border-blue-400/20 hover:bg-blue-500/20 hover:shadow-[0_20px_60px_rgba(59,130,246,0.25)] transition-all duration-700 ease-out pointer-events-none relative overflow-hidden">
      {/* Subtle inner glow */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 to-transparent rounded-3xl"></div>
      <div className="absolute inset-0 bg-gradient-to-tl from-blue-300/5 to-transparent rounded-3xl"></div>
      <Globe className="h-12 w-12 text-blue-300 opacity-90 z-10" strokeWidth={1} />
    </div>
    <div className="mt-8 text-lg font-light text-blue-200 text-center pointer-events-none tracking-wide" style={{ fontFamily: 'Georgia, serif' }}>
      Frontend
    </div>
  </div>
);

// Luxurious Server Node Component
const ServerNode = () => (
  <div className="relative group cursor-pointer select-none">
    <Handle
      type="target"
      position={Position.Left}
      className="!w-full !h-full !bg-transparent !border-0 !left-0 !top-0 !transform-none !opacity-0"
      style={{ width: '100%', height: '100%', left: 0, top: 0 }}
    />
    <div className="w-40 h-40 bg-green-500/10 backdrop-blur-xl rounded-3xl flex items-center justify-center shadow-2xl border border-green-400/20 hover:bg-green-500/20 hover:shadow-[0_20px_60px_rgba(34,197,94,0.25)] transition-all duration-700 ease-out pointer-events-none relative overflow-hidden">
      {/* Subtle inner glow */}
      <div className="absolute inset-0 bg-gradient-to-br from-green-400/10 to-transparent rounded-3xl"></div>
      <div className="absolute inset-0 bg-gradient-to-tl from-green-300/5 to-transparent rounded-3xl"></div>
      <Server className="h-12 w-12 text-green-300 opacity-90 z-10" strokeWidth={1} />
    </div>
    <div className="mt-8 text-lg font-light text-green-200 text-center pointer-events-none tracking-wide" style={{ fontFamily: 'Georgia, serif' }}>
      Server
    </div>
  </div>
);

const nodeTypes = {
  frontend: FrontendNode,
  server: ServerNode,
};

const InteractiveGateContent: React.FC<InteractiveGateProps> = ({ onComplete }) => {
  const [showHint, setShowHint] = useState(false);
  const [autoConnectTimer, setAutoConnectTimer] = useState(8);
  const [isConnected, setIsConnected] = useState(false);
  const [showConnectionEffect, setShowConnectionEffect] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const initialNodes: Node[] = [
    {
      id: 'frontend',
      type: 'frontend',
      position: { x: 150, y: 200 },
      data: {},
      draggable: false,
    },
    {
      id: 'server',
      type: 'server',
      position: { x: 550, y: 200 },
      data: {},
      draggable: false,
    },
  ];

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Elegant chime sound effect
  const playConnectionChime = () => {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      
      // Soft harpsichord-like chord
      oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
      oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
      oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
      
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.05);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 1.5);
      
      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 1.5);
    } catch (error) {
      console.log('Audio not supported');
    }
  };

  // Auto-connect timer
  useEffect(() => {
    if (isConnected) return;
    
    const timer = setInterval(() => {
      setAutoConnectTimer(prev => {
        if (prev <= 1) {
          handleAutoConnect();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Show hint after 3 seconds
    const hintTimer = setTimeout(() => {
      setShowHint(true);
    }, 3000);

    return () => {
      clearInterval(timer);
      clearTimeout(hintTimer);
    };
  }, [isConnected]);

  const handleAutoConnect = () => {
    const newEdge: Edge = {
      id: 'elegant-connection',
      source: 'frontend',
      target: 'server',
      animated: false,
      style: {
        stroke: '#93c5fd',
        strokeWidth: 3,
        strokeDasharray: '0',
        filter: 'drop-shadow(0 0 8px rgba(147,197,253,0.6))',
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: '#93c5fd',
      },
    };
    setEdges([newEdge]);
    setIsConnected(true);
    setShowConnectionEffect(true);
    playConnectionChime();
    
    // Trigger page reveal with delay
    setTimeout(() => {
      onComplete();
    }, 2000);
  };

  const onConnect = useCallback((connection: Connection) => {
    const newEdge = {
      ...connection,
      id: 'user-connection',
      animated: false,
      style: {
        stroke: '#93c5fd',
        strokeWidth: 3,
        strokeDasharray: '0',
        filter: 'drop-shadow(0 0 8px rgba(147,197,253,0.6))',
      },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: '#93c5fd',
      },
    };
    setEdges((eds) => addEdge(newEdge, eds));
    setIsConnected(true);
    setShowConnectionEffect(true);
    playConnectionChime();
    
    // Trigger page reveal with delay
    setTimeout(() => {
      onComplete();
    }, 2000);
  }, [onComplete, setEdges]);

  const onConnectStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const onConnectEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 overflow-hidden">
      
      {/* Glass-like ambient particles */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-blue-200 rounded-full opacity-30 animate-pulse" style={{ animationDelay: '0s', animationDuration: '4s' }}></div>
        <div className="absolute top-3/4 right-1/3 w-0.5 h-0.5 bg-blue-300 rounded-full opacity-20 animate-pulse" style={{ animationDelay: '2s', animationDuration: '6s' }}></div>
        <div className="absolute bottom-1/3 left-1/2 w-1.5 h-1.5 bg-blue-200 rounded-full opacity-25 animate-pulse" style={{ animationDelay: '1s', animationDuration: '5s' }}></div>
      </div>

      {/* Connection effect overlay */}
      {showConnectionEffect && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent animate-pulse pointer-events-none z-10" style={{ animationDuration: '2s' }}></div>
      )}

      {/* Elegant Header */}
      <div className="pt-20 pb-16 text-center z-20 relative">
        <h1 className="text-4xl font-light text-blue-100 mb-6 leading-relaxed tracking-wide" style={{ fontFamily: 'Georgia, serif' }}>
          Design begins with a single link
        </h1>
        
        {/* Subtle hint text */}
        <div className={`transition-opacity duration-1000 ${showHint ? 'opacity-100' : 'opacity-0'}`}>
          <p className="text-sm font-light text-blue-200 mb-2 tracking-wide">
            Connect the elements to continue
          </p>
        </div>

        {/* Elegant countdown */}
        {!isConnected && autoConnectTimer > 0 && (
          <div className="text-xs font-light text-blue-300 tracking-widest">
            {autoConnectTimer}
          </div>
        )}
      </div>

      {/* React Flow Canvas */}
      <div className="w-full h-96 relative z-20">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onConnectStart={onConnectStart}
          onConnectEnd={onConnectEnd}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{ padding: 0.3 }}
          nodesDraggable={false}
          nodesConnectable={true}
          elementsSelectable={false}
          zoomOnScroll={false}
          zoomOnPinch={false}
          panOnDrag={false}
          proOptions={{ hideAttribution: true }}
          style={{ background: 'transparent' }}
          connectionLineStyle={{
            stroke: '#93c5fd',
            strokeWidth: 2,
            opacity: 1,
            filter: 'drop-shadow(0 0 4px rgba(147,197,253,0.4))',
          }}
          className={isDragging ? 'connecting' : ''}
        >
          {/* Define arrow markers */}
          <svg style={{ position: 'absolute', top: 0, left: 0 }}>
            <defs>
              <marker
                id="arrowclosed"
                markerWidth="20"
                markerHeight="20"
                refX="18"
                refY="3"
                orient="auto"
                markerUnits="strokeWidth"
              >
                <path
                  d="M0,0 L0,6 L9,3 z"
                  fill="#93c5fd"
                  stroke="#93c5fd"
                  strokeWidth="1"
                />
              </marker>
            </defs>
          </svg>
        </ReactFlow>
      </div>

      {/* Elegant success message - Fixed positioning and z-index */}
      {isConnected && (
        <div className="fixed inset-x-0 top-1/2 transform -translate-y-1/2 z-50 animate-fade-in text-center">
          <div className="inline-flex items-center gap-4 px-12 py-6 bg-white/10 backdrop-blur-lg border border-white/20 rounded-full shadow-2xl">
            <span className="text-blue-100 text-lg font-light tracking-wide" style={{ fontFamily: 'Georgia, serif' }}>
              Beautifully connected
            </span>
          </div>
          <p className="mt-8 text-base font-light text-blue-200 animate-fade-in tracking-wide" style={{ fontFamily: 'Georgia, serif' }}>
            Welcome to elegant system design
          </p>
        </div>
      )}

      {/* Custom styles for elegant edge animation */}
      <style dangerouslySetInnerHTML={{
        __html: `
          .react-flow.connecting .react-flow__connection-line {
            animation: pulse 1s ease-in-out infinite;
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
          }

          /* Ensure edges are visible */
          .react-flow__edge {
            pointer-events: all;
          }
          
          .react-flow__edge-path {
            stroke-linecap: round;
            stroke-linejoin: round;
          }
        `
      }} />
    </div>
  );
};

const InteractiveGate: React.FC<InteractiveGateProps> = ({ onComplete }) => {
  return (
    <ReactFlowProvider>
      <InteractiveGateContent onComplete={onComplete} />
    </ReactFlowProvider>
  );
};

export default InteractiveGate;
