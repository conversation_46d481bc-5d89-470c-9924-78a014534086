import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Di<PERSON>Trigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileIcon, ExternalLinkIcon } from 'lucide-react';

interface URLShortenerReferenceArchitectureProps {
  trigger?: React.ReactNode;
}

const URLShortenerReferenceArchitecture: React.FC<URLShortenerReferenceArchitectureProps> = ({
  trigger
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <FileIcon className="h-4 w-4" />
            View URL Shortener Reference Architecture
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>URL Shortener Reference Architecture</DialogTitle>
          <DialogDescription>
            Compare your design with production-grade URL shortener architectures
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Core Components</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Frontend Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Web Application (React/Vue.js)</li>
                  <li>Mobile Apps (iOS, Android)</li>
                  <li>API Clients for integrations</li>
                  <li>Browser Extensions</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">API Gateway & Load Balancing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>API Gateway for request routing</li>
                  <li>Rate limiting and throttling</li>
                  <li>Request authentication</li>
                  <li>Load balancers for traffic distribution</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">URL Creation Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>URL validation and normalization</li>
                  <li>Short code generation algorithms</li>
                  <li>Collision detection and handling</li>
                  <li>Custom alias support</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Redirect Service</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>High-performance redirect handling</li>
                  <li>HTTP 301/302 status code management</li>
                  <li>Click tracking and event generation</li>
                  <li>Link validation and security checks</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Data Storage</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">URL Mapping Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>NoSQL databases (DynamoDB, MongoDB)</li>
                  <li>Key-value stores (Redis)</li>
                  <li>Sharded for horizontal scaling</li>
                  <li>Multi-region replication</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Analytics Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Time-series databases (InfluxDB, Prometheus)</li>
                  <li>Data warehouses (Snowflake, BigQuery)</li>
                  <li>Columnar storage for efficient queries</li>
                  <li>Data partitioning by time periods</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Caching Layer</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>In-memory caches (Redis, Memcached)</li>
                  <li>Distributed caching across regions</li>
                  <li>LRU/LFU eviction policies</li>
                  <li>Cache warming for popular URLs</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">User Data Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Relational databases (PostgreSQL, MySQL)</li>
                  <li>User profiles and preferences</li>
                  <li>Authentication data</li>
                  <li>Subscription and billing information</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Scalability Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Short Code Generation</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Base62 encoding (a-z, A-Z, 0-9)</li>
                  <li>Counter-based approaches</li>
                  <li>Hash-based algorithms (MD5, SHA-1)</li>
                  <li>Distributed ID generators</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Traffic Management</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Global load balancing (GeoDNS)</li>
                  <li>CDN integration for static assets</li>
                  <li>Rate limiting and throttling</li>
                  <li>Circuit breakers for service protection</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Analytics Processing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Event streaming (Kafka, Kinesis)</li>
                  <li>Real-time processing (Flink, Spark Streaming)</li>
                  <li>Batch processing for reports</li>
                  <li>Data aggregation pipelines</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Reliability Features</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Multi-region deployment</li>
                  <li>Database replication</li>
                  <li>Graceful degradation</li>
                  <li>Automated failover</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Additional Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Security Features</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Malicious URL detection</li>
                  <li>CAPTCHA for spam prevention</li>
                  <li>Rate limiting by IP/user</li>
                  <li>Link expiration and access controls</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Premium Features</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Custom domains (brand.link/short)</li>
                  <li>Vanity URLs (custom aliases)</li>
                  <li>QR code generation</li>
                  <li>Advanced analytics dashboards</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Monitoring & Operations</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Real-time metrics and dashboards</li>
                  <li>Alerting and incident response</li>
                  <li>Distributed tracing</li>
                  <li>Performance monitoring</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">API & Integration</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>RESTful API for developers</li>
                  <li>Webhooks for event notifications</li>
                  <li>SDK libraries for popular languages</li>
                  <li>OAuth integration for third-party apps</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            <p>Note: This is a simplified representation of a production URL shortener architecture. Real-world implementations may include additional components for specific business requirements.</p>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <a 
            href="https://medium.com/swlh/how-to-build-a-tiny-url-service-that-scales-to-billions-f6fb5ea22e8c" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
          >
            Learn more about URL shortener architecture <ExternalLinkIcon className="h-3 w-3" />
          </a>
          <DialogClose asChild>
            <Button type="button">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default URLShortenerReferenceArchitecture;
