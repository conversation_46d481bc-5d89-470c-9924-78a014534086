import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DownloadIcon, FileIcon, ImageIcon } from 'lucide-react';
import { exportToPDF } from '@/utils/exportPDF';
import { exportToImage } from '@/utils/exportImage';

interface ExportFormatDialogProps {
  reactFlowInstance: any;
  onExportComplete?: () => void;
  trigger?: React.ReactNode;
}

const ExportFormatDialog: React.FC<ExportFormatDialogProps> = ({
  reactFlowInstance,
  onExportComplete,
  trigger
}) => {
  const handleExportPDF = async () => {
    await exportToPDF(reactFlowInstance);
    if (onExportComplete) onExportComplete();
  };

  const handleExportPNG = async () => {
    await exportToImage(reactFlowInstance, 'png');
    if (onExportComplete) onExportComplete();
  };

  const handleExportJPG = async () => {
    await exportToImage(reactFlowInstance, 'jpg');
    if (onExportComplete) onExportComplete();
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="flex items-center gap-2">
            <DownloadIcon className="h-4 w-4" />
            Export Design
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Your System Design</DialogTitle>
          <DialogDescription>
            Choose a format to export your system design
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-3 gap-4 py-4">
          <Button
            variant="outline"
            className="flex flex-col items-center justify-center gap-2 p-4 h-auto"
            onClick={handleExportPDF}
          >
            <FileIcon className="h-8 w-8 text-red-500" />
            <span>PDF</span>
            <span className="text-xs text-gray-500">Best for sharing</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-col items-center justify-center gap-2 p-4 h-auto"
            onClick={handleExportPNG}
          >
            <ImageIcon className="h-8 w-8 text-blue-500" />
            <span>PNG</span>
            <span className="text-xs text-gray-500">High quality</span>
          </Button>
          <Button
            variant="outline"
            className="flex flex-col items-center justify-center gap-2 p-4 h-auto"
            onClick={handleExportJPG}
          >
            <ImageIcon className="h-8 w-8 text-green-500" />
            <span>JPG</span>
            <span className="text-xs text-gray-500">Smaller file size</span>
          </Button>
        </div>
        <DialogFooter className="sm:justify-start">
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              Cancel
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ExportFormatDialog;
