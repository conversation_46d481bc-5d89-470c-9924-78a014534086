/**
 * System Design Agent Chat Component
 * Integrates with the local Gemini-powered agent
 */
import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Loader2, Send, Bot, User, AlertCircle, CheckCircle } from 'lucide-react';
import { Node, Edge } from '@xyflow/react';
import { 
  chatWithAgent, 
  analyzeDesignWithAgent, 
  checkAgentAvailability,
  startNewDesignSession 
} from '@/services/systemDesignAgentService';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
}

interface SystemDesignAgentChatProps {
  nodes?: Node[];
  edges?: Edge[];
  userJourneys?: string;
  assumptions?: string;
  constraints?: string;
  className?: string;
}

const SystemDesignAgentChat: React.FC<SystemDesignAgentChatProps> = ({
  nodes = [],
  edges = [],
  userJourneys,
  assumptions,
  constraints,
  className = ''
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [agentAvailable, setAgentAvailable] = useState<boolean | null>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Check agent availability on mount
  useEffect(() => {
    const checkAvailability = async () => {
      const available = await checkAgentAvailability();
      setAgentAvailable(available);
      
      if (available) {
        // Add welcome message
        setMessages([{
          id: '1',
          content: "Hello! I'm your System Design Agent powered by Gemini. I can help you design, analyze, and improve system architectures. What would you like to work on today?",
          sender: 'assistant',
          timestamp: new Date()
        }]);
      }
    };

    checkAvailability();
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      const response = await chatWithAgent(
        inputMessage,
        nodes,
        edges,
        userJourneys,
        assumptions,
        constraints
      );

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response,
        sender: 'assistant',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "I'm sorry, there was an error processing your message. Please try again.",
        sender: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyzeDesign = async () => {
    if (nodes.length === 0) {
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        content: "Please add some components to your design before requesting analysis.",
        sender: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    setIsLoading(true);

    try {
      const analysis = await analyzeDesignWithAgent(
        nodes,
        edges,
        userJourneys,
        assumptions,
        constraints
      );

      const analysisMessage: ChatMessage = {
        id: Date.now().toString(),
        content: `**Design Analysis:**\n\n${analysis}`,
        sender: 'assistant',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, analysisMessage]);
    } catch (error) {
      console.error('Analysis error:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        content: "I'm sorry, there was an error analyzing your design. Please try again.",
        sender: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewSession = () => {
    startNewDesignSession();
    setMessages([{
      id: Date.now().toString(),
      content: "Started a new design session! How can I help you with your system design?",
      sender: 'assistant',
      timestamp: new Date()
    }]);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (agentAvailable === null) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Connecting to System Design Agent...</span>
        </CardContent>
      </Card>
    );
  }

  if (!agentAvailable) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
            Agent Unavailable
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            The System Design Agent is not available.
          </p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="outline" 
            size="sm"
          >
            Retry Connection
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Bot className="h-5 w-5 mr-2" />
            System Design Agent
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <CheckCircle className="h-3 w-3 mr-1" />
              Connected
            </Badge>
            <Button onClick={handleNewSession} variant="outline" size="sm">
              New Session
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Chat Messages */}
        <ScrollArea className="h-96 w-full border rounded-md p-4" ref={scrollAreaRef}>
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.sender === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    {message.sender === 'assistant' && (
                      <Bot className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    )}
                    {message.sender === 'user' && (
                      <User className="h-4 w-4 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-muted rounded-lg p-3">
                  <div className="flex items-center space-x-2">
                    <Bot className="h-4 w-4" />
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm">Agent is thinking...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Quick Actions */}
        <div className="flex space-x-2">
          <Button
            onClick={handleAnalyzeDesign}
            variant="outline"
            size="sm"
            disabled={isLoading || nodes.length === 0}
          >
            Analyze Design
          </Button>
        </div>

        {/* Message Input */}
        <div className="flex space-x-2">
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask about system design, request analysis, or get suggestions..."
            disabled={isLoading}
            className="flex-1"
          />
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
            size="icon"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemDesignAgentChat;
