import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import { RotateCcw, Loader2 } from 'lucide-react';
import { useDesign } from '@/contexts/DesignContext';
import { useAuth } from '@/contexts/AuthContext';
import { useCourse } from '@/contexts/CourseContext';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useParams } from 'react-router-dom';
import { toast } from 'sonner';
import { ReactFlowInstance } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface ResetDesignButtonProps {
  reactFlowInstance?: ReactFlowInstance | null;
  className?: string;
  onResetUserInputs?: () => void; // Callback to reset user journeys, assumptions, constraints
  variant?: 'default' | 'vertical-toolbar';
  setResetting?: ((resetting: boolean) => void) | null; // Add prop to mark reset operations
  onFullAutoSave?: (isMajorChange?: boolean) => void; // New prop to trigger full autosave after reset
}

const ResetDesignButton: React.FC<ResetDesignButtonProps> = ({
  reactFlowInstance,
  className = '',
  onResetUserInputs,
  variant = 'default',
  setResetting,
  onFullAutoSave
}) => {
  const [isResetting, setIsResetting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { deleteDesign, setCurrentDesign, getDesignKey } = useDesign();
  const { user } = useAuth();
  const { isGuidedMode, currentCourse } = useCourse();
  const { currentQuestion } = useQuestions();
  const { questionId } = useParams<{ questionId: string }>();

  // Determine the current context
  const getContextInfo = () => {
    if (isGuidedMode && currentCourse) {
      return {
        contextType: 'course' as const,
        contextId: currentCourse.id,
        questionId: currentCourse.id,
        displayName: `course "${currentCourse.title}"`
      };
    } else if (questionId && currentQuestion) {
      return {
        contextType: 'question' as const,
        contextId: questionId,
        questionId: questionId,
        displayName: `question "${currentQuestion.title}"`
      };
    } else {
      return {
        contextType: 'free' as const,
        contextId: 'free-canvas',
        questionId: 'free-canvas',
        displayName: 'free canvas'
      };
    }
  };

  const handleResetDesign = async () => {
    if (!user) {
      toast.error('You must be logged in to reset designs');
      return;
    }

    setIsResetting(true);

    try {
      const { contextType, contextId, questionId: effectiveQuestionId, displayName } = getContextInfo();

      debugLog(`🔄 Resetting design for ${displayName}...`);
      debugLog(`Context: ${contextType}, ID: ${contextId}, QuestionID: ${effectiveQuestionId}`);

      // 1. Clear localStorage entries for this specific context first

      // 2. Clear localStorage entries for this specific context
      const designKey = getDesignKey(effectiveQuestionId, contextType, contextId);
      localStorage.removeItem(designKey);
      debugLog(`✅ Removed localStorage key: ${designKey}`);

      // 3. Clear any related localStorage keys
      const keysToRemove: string[] = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (
          key.includes(effectiveQuestionId) ||
          key.includes(contextId) ||
          (contextType === 'course' && key.includes('course-progress-' + contextId))
        )) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        debugLog(`✅ Removed related localStorage key: ${key}`);
      });

      // 4. Delete design from Supabase
      await deleteDesign(effectiveQuestionId, contextType, contextId);
      debugLog('✅ Design deleted from Supabase');

      // 5. Clear the canvas
      if (reactFlowInstance) {
        // Mark this as a reset operation to prevent deletion protection from blocking it
        if (setResetting) {
          setResetting(true);
        }
        
        reactFlowInstance.setNodes([]);
        reactFlowInstance.setEdges([]);
        debugLog('✅ Canvas cleared');
        
        // Re-enable deletion protection after reset
        if (setResetting) {
          setTimeout(() => {
            setResetting(false);
          }, 100);
        }
      }

      // 6. Clear user inputs (journeys, assumptions, constraints)
      if (onResetUserInputs) {
        onResetUserInputs();
        debugLog('✅ User inputs cleared');
      }

      // 7. Clear current design from memory LAST to prevent reload attempts
      setCurrentDesign(null);
      debugLog('✅ Current design cleared from memory');

      // 8. Trigger full autosave to sync empty state to Supabase
      if (onFullAutoSave) {
        onFullAutoSave(true); // true = major change, triggers immediate Supabase sync
        debugLog('✅ Triggered full autosave after reset');
      }

      toast.success(`Design reset successfully for ${displayName}`);
      setIsDialogOpen(false);

    } catch (error) {
      console.error('❌ Error resetting design:', error);
      toast.error('Failed to reset design. Please try again.');
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant={variant === 'vertical-toolbar' ? 'lightCTA' : 'outline'}
          size="sm"
          className={variant === 'vertical-toolbar'
            ? className
            : `flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 ${className}`
          }
          disabled={isResetting}
        >
          {variant === 'vertical-toolbar' ? (
            <>
              <div className="w-6 flex items-center justify-center flex-shrink-0">
                {isResetting ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <RotateCcw size={14} />
                )}
              </div>
              <span className="text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200 delay-100">
                {isResetting ? "Resetting..." : "Reset Design"}
              </span>
            </>
          ) : (
            <>
              {isResetting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RotateCcw className="h-4 w-4" />
              )}
              Reset Design
            </>
          )}
        </Button>
      </AlertDialogTrigger>

      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Reset Design</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the current design and clear all related data including:
            <ul className="list-disc list-inside mt-2 space-y-1">
              <li>All components and connections on the canvas</li>
              <li>User journeys, assumptions, and constraints</li>
              <li>Cached design data</li>
              <li>Saved design in the database</li>
            </ul>
            <br />
            <strong>This action cannot be undone.</strong>
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isResetting}>
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleResetDesign}
            disabled={isResetting}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isResetting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Resetting...
              </>
            ) : (
              'Reset Design'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ResetDesignButton;
