import React from 'react';
import { 
  Monitor, 
  Server, 
  Cylinder, 
  Circle, 
  Square, 
  Laptop, 
  Network, 
  Globe, 
  Link, 
  Inbox, 
  Rss, 
  Hexagon, 
  Box,
  Database,
  HardDrive,
  Layers,
  Cpu,
  MessageSquare,
  BarChart,
  Cloud,
  Lock,
  Users,
  FileText,
  Settings,
  ShieldCheck
} from 'lucide-react';

export interface ComponentMetadata {
  type: ComponentType;
  label: string;
  description: string;
  iconComponent: React.ReactNode;
  className: string;
  fields: {
    [key: string]: {
      type: 'text' | 'textarea' | 'select' | 'checkbox' | 'number' | 'multi-select';
      label: string;
      placeholder?: string;
      options?: string[];
    };
  };
  customLogicConfig: {
    label: string;
    placeholder: string;
  };
}

export type ComponentType = 'frontend' | 'server' | 'database' | 'cache' | 'queue' | 'client' | 
  'loadbalancer' | 'cdn' | 'dns' | 'messagequeue' | 'pubsub' | 'primitive' | 'composite' | 'apigateway';

export const componentTypes: Record<ComponentType, ComponentMetadata> = {
  primitive: {
    type: 'primitive',
    label: 'Custom',
    description: 'Basic component with custom properties',
    iconComponent: <Hexagon size={18} />,
    className: 'bg-white border-gray-400 text-gray-600 shadow-sm rounded-md',
    customLogicConfig: {
      label: 'Custom Logic',
      placeholder: 'Define custom behavior for this component'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this component'
      }
      // componentName: {
      //   type: 'text',
      //   label: 'Component Name',
      //   placeholder: 'Enter component name'
      // }
    }
  },
  composite: {
    type: 'composite',
    label: 'Composite',
    description: 'Container for grouped components',
    iconComponent: <Box size={18} />,
    className: 'bg-white border-gray-500 text-gray-700 shadow-sm',
    customLogicConfig: {
      label: 'Custom Logic',
      placeholder: 'Define custom behavior for this composite component'
    },
    fields: {
      // componentName: {
      //   type: 'text',
      //   label: 'Component Name',
      //   placeholder: 'Enter composite name'
      // },
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this composite component'
      }
    }
  },
  frontend: {
    type: 'frontend',
    label: 'Frontend',
    description: 'User interface components',
    iconComponent: <Monitor size={18} />,
    className: 'bg-blue-100 border-blue-300',
    customLogicConfig: {
      label: 'Component Logic (Pseudo Code)',
      placeholder: 'onSubmit() → validate() → api.post() → showSuccess()'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this frontend component'
      },
      framework: {
        type: 'text',
        label: 'Framework',
        placeholder: 'React, Vue, Angular, etc.'
      },
      platform: {
        type: 'select',
        label: 'Platform',
        options: ['Web', 'Mobile Web', 'iOS', 'Android']
      },
      apiConnection: {
        type: 'text',
        label: 'API Connection',
        placeholder: 'REST, GraphQL, etc.'
      }
    }
  },
  server: {
    type: 'server',
    label: 'Server',
    description: 'Backend server components',
    iconComponent: <Server size={18} />,
    className: 'bg-green-100 border-green-300',
    customLogicConfig: {
      label: 'API Method Signatures',
      placeholder: 'GET /users/:id → returns User\nPOST /users → creates User'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this server component'
      },
      language: {
        type: 'text',
        label: 'Language/Runtime',
        placeholder: 'Node.js, Python, Java, etc.'
      },
      apiType: {
        type: 'select',
        label: 'API Type',
        options: ['REST', 'GraphQL', 'gRPC', 'WebSockets']
      },
      retries: {
        type: 'text',
        label: 'Retry Strategy',
        placeholder: 'Number of retries or strategy'
      },
      scaling: {
        type: 'select',
        label: 'Scaling Strategy',
        options: ['Horizontal', 'Vertical', 'Auto-scaling']
      }
    }
  },
  database: {
    type: 'database',
    label: 'Database',
    description: 'Data storage systems',
    iconComponent: <Database size={18} />,
    className: 'bg-purple-100 border-purple-300',
    customLogicConfig: {
      label: 'Indexing or Query Strategy',
      placeholder: 'CREATE INDEX ON users (email);\nSELECT * FROM users WHERE email = ?'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this database component'
      },
      engine: {
        type: 'text',
        label: 'Engine',
        placeholder: 'PostgreSQL, MongoDB, etc.'
      },
      schema: {
        type: 'textarea',
        label: 'Schema',
        placeholder: 'Describe your database schema here'
      },
      replication: {
        type: 'select',
        label: 'Replication',
        options: ['None', 'Master-Slave', 'Multi-Master', 'Sharded']
      }
    }
  },
  cache: {
    type: 'cache',
    label: 'Cache',
    description: 'Fast data retrieval',
    iconComponent: <HardDrive size={18} />,
    className: 'bg-red-100 border-red-300',
    customLogicConfig: {
      label: 'Eviction Strategy / TTL Logic',
      placeholder: 'if size > 1000 then evict LRU\nset TTL = 3600 for /api/* responses'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this cache component'
      },
      engine: {
        type: 'text',
        label: 'Engine',
        placeholder: 'Redis, Memcached, etc.'
      },
      ttl: {
        type: 'text',
        label: 'TTL',
        placeholder: 'Time to live in seconds'
      },
      evictionPolicy: {
        type: 'select',
        label: 'Eviction Policy',
        options: ['LRU', 'LFU', 'FIFO', 'Random']
      }
    }
  },
  queue: {
    type: 'queue',
    label: 'Queue',
    description: 'Message queuing service',
    iconComponent: <Layers size={18} />,
    className: 'bg-yellow-100 border-yellow-300',
    customLogicConfig: {
      label: 'Message Handling Logic',
      placeholder: 'retry(message, 3);\ndelay(5s);\nroute(message.type)'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this queue component'
      },
      engine: {
        type: 'text',
        label: 'Engine',
        placeholder: 'Kafka, RabbitMQ, SQS, etc.'
      },
      maxDepth: {
        type: 'text',
        label: 'Max Depth',
        placeholder: 'Maximum number of messages'
      },
      timeout: {
        type: 'text',
        label: 'Timeout',
        placeholder: 'Message processing timeout'
      },
      deliveryGuarantee: {
        type: 'select',
        label: 'Delivery Guarantee',
        options: ['At least once', 'At most once', 'Exactly once']
      }
    }
  },
  client: {
    type: 'client',
    label: 'Client',
    description: 'End-user device',
    iconComponent: <Laptop size={18} />,
    className: 'bg-sky-100 border-blue-400 rounded-lg',
    customLogicConfig: {
      label: 'User Interaction or Request Flow',
      placeholder: 'click() → fetch /products → render()'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this client component'
      },
      platform: {
        type: 'select',
        label: 'Platform',
        options: ['Web', 'Mobile', 'Desktop']
      },
      authRequired: {
        type: 'checkbox',
        label: 'Auth Required'
      }
    }
  },
  loadbalancer: {
    type: 'loadbalancer',
    label: 'Load Balancer',
    description: 'Distribute network traffic',
    iconComponent: <Network size={18} />,
    className: 'bg-orange-100 border-orange-400 octagon',
    customLogicConfig: {
      label: 'Routing Logic (Pseudo Code)',
      placeholder: 'if (next >= servers.length) next = 0;\nroute(request, servers[next++]);'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this load balancer component'
      },
      algorithm: {
        type: 'select',
        label: 'Algorithm',
        options: ['Round Robin', 'Least Connections', 'IP Hash']
      },
      stickySessions: {
        type: 'checkbox',
        label: 'Sticky Sessions'
      },
      healthChecks: {
        type: 'checkbox',
        label: 'Health Checks Enabled'
      }
    }
  },
  cdn: {
    type: 'cdn',
    label: 'CDN',
    description: 'Content delivery network',
    iconComponent: <Globe size={18} />,
    className: 'bg-teal-100 border-teal-400 rounded-full',
    customLogicConfig: {
      label: 'Caching Strategy Logic',
      placeholder: 'if path.startsWith("/static") set cache: 7d;\nelse set cache: 4h;'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this CDN component'
      },
      cacheTTL: {
        type: 'number',
        label: 'Cache Expiry TTL (s)',
        placeholder: 'Time in seconds'
      },
      geoDistribution: {
        type: 'checkbox',
        label: 'Geo Distribution'
      },
      contentTypes: {
        type: 'multi-select',
        label: 'Static Content Types',
        options: ['JS', 'CSS', 'Images', 'Fonts', 'Video']
      }
    }
  },
  dns: {
    type: 'dns',
    label: 'DNS',
    description: 'Domain name resolution',
    iconComponent: <Link size={18} />,
    className: 'bg-indigo-100 border-indigo-400 hexagon',
    customLogicConfig: {
      label: 'DNS Resolution Logic',
      placeholder: 'api.example.com → CNAME → lb-east.example.com\nwww → A → ************'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this DNS component'
      },
      ttl: {
        type: 'number',
        label: 'TTL (s)',
        placeholder: 'Time in seconds'
      },
      recordTypes: {
        type: 'multi-select',
        label: 'Record Types',
        options: ['A', 'AAAA', 'CNAME', 'MX', 'TXT']
      },
      routingPolicy: {
        type: 'select',
        label: 'Routing Policy',
        options: ['Simple', 'Weighted', 'Geolocation']
      }
    }
  },
  messagequeue: {
    type: 'messagequeue',
    label: 'Message Queue',
    description: 'Async message processing',
    iconComponent: <MessageSquare size={18} />,
    className: 'bg-purple-200 border-purple-400 rounded-full',
    customLogicConfig: {
      label: 'Message Processing Logic',
      placeholder: 'on.receive(msg) → validate() → process() → ack()\nif error → retry(3) → dlq()'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this message queue component'
      },
      queueType: {
        type: 'select',
        label: 'Queue Type',
        options: ['FIFO', 'Priority', 'Dead Letter']
      },
      maxRetries: {
        type: 'number',
        label: 'Max Retries',
        placeholder: 'Number of retry attempts'
      },
      visibilityTimeout: {
        type: 'number',
        label: 'Visibility Timeout (s)',
        placeholder: 'Time in seconds'
      },
      deliveryGuarantee: {
        type: 'select',
        label: 'Delivery Guarantee',
        options: ['At-least-once', 'Exactly-once']
      }
    }
  },
  apigateway: {
    type: 'apigateway',
    label: 'API Gateway',
    description: 'Manages and secures API requests',
    iconComponent: <ShieldCheck size={18} />,
    className: 'bg-white border-blue-400 text-blue-600 shadow-sm rounded-full',
    customLogicConfig: {
      label: 'Routing & Auth Rules',
      placeholder: '// Rate limit: 100 rps\n// Auth: JWT verification\n// Route: /api/v1 → Service A\n// Route: /api/v2 → Service B'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this API gateway component'
      },
      gatewayType: {
        type: 'select',
        label: 'Gateway Type',
        options: ['Cloud', 'On-Premise', 'Hybrid']
      },
      rateLimit: {
        type: 'text',
        label: 'Rate Limit',
        placeholder: '100 rps'
      },
      authRequired: {
        type: 'checkbox',
        label: 'Auth Required'
      },
      authTypes: {
        type: 'multi-select',
        label: 'Auth Types',
        options: ['JWT', 'OAuth2', 'API Key', 'Basic Auth']
      },
      routingRules: {
        type: 'textarea',
        label: 'Routing Rules',
        placeholder: '/api/v1 → Service A\n/api/v2 → Service B'
      }
    }
  },
  pubsub: {
    type: 'pubsub',
    label: 'Pub/Sub',
    description: 'Publish/subscribe messaging',
    iconComponent: <Rss size={18} />,
    className: 'bg-yellow-200 border-yellow-400',
    customLogicConfig: {
      label: 'Message Publishing Logic',
      placeholder: 'topic = "order.created";\npublish(data);\nif urgent: publish(data, "order.urgent");'
    },
    fields: {
      description: {
        type: 'textarea',
        label: 'Description',
        placeholder: 'Describe this pub/sub component'
      },
      deliveryGuarantee: {
        type: 'select',
        label: 'Delivery Guarantee',
        options: ['At-least-once', 'At-most-once', 'Exactly-once']
      },
      messageRetentionDuration: {
        type: 'number',
        label: 'Message Retention (s)',
        placeholder: 'Time in seconds'
      },
      numberOfSubscribers: {
        type: 'number',
        label: 'Number of Subscribers',
        placeholder: 'Count of subscribers'
      }
    }
  }
};
