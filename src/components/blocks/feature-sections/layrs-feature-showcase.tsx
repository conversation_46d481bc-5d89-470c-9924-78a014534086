// Removed Card import as we're using plain divs with separators

export const LayrsFeatureShowcase = () => {
  const features = [
    {
      title: "Industry Wide Asked Questions",
      description: "Access comprehensive answers to the most common design system questions across the industry.",
      details: "Get instant access to curated questions and expert answers covering design patterns, component libraries, accessibility standards, and best practices that design teams face daily.",
      image: "/company-logos.jpeg",
      highlighted: false
    },
    {
      title: "AI Powered Assessment",
      description: "Let our AI analyze your design system and provide intelligent recommendations.",
      details: "Our advanced AI algorithms evaluate your design components, identify inconsistencies, suggest improvements, and provide actionable insights to enhance your design system's effectiveness and user experience.",
      image: "/layrs-assess.png",
      highlighted: false
    },
    {
      title: "Chat with your design",
      description: "Interact directly with your design system through our intelligent chat interface.",
      details: "Start conversations about your design components, ask questions about implementation, get real-time feedback, and collaborate with your team through an intuitive chat experience tailored for design workflows.",
      image: "/layrs-chat-focused.jpeg",
      highlighted: false
    }
  ]

  return (
    <div id="features" className="w-full max-w-7xl mx-auto px-4 py-16 mb-16">
      {/* Section Header */}
      <div className="text-center mb-16">
        <h2 className="text-center text-xl font-bold text-foreground md:text-3xl font-inter">
          Everything you need to ace system design interviews
        </h2>
        <p className="mx-auto mt-4 max-w-2xl text-center text-base text-muted-foreground font-inter">
          From curated questions to AI-powered assessments, we provide comprehensive tools to help you master system design concepts and excel in technical interviews.
        </p>
      </div>

      <div className="space-y-16">
        {features.map((feature, index) => (
          <div key={index}>
            {/* Feature Row */}
            <div className={`flex flex-col lg:flex-row items-center gap-8 lg:gap-12 ${
              index % 2 === 1 ? 'lg:flex-row-reverse' : ''
            }`}>
              {/* Image Section */}
              <div className="w-full lg:w-1/2">
                <div className="bg-gray-50 rounded-lg overflow-hidden">
                  <img
                    src={feature.image}
                    alt={feature.title}
                    className="object-contain w-full h-auto max-h-[400px] lg:max-h-[500px]"
                  />
                </div>
              </div>

              {/* Text Section */}
              <div className="w-full lg:w-1/2">
                <h3 className="text-2xl lg:text-3xl font-bold text-foreground mb-4 font-inter">
                  {feature.title}
                </h3>
                <p className="text-lg text-muted-foreground mb-6 font-inter">
                  {feature.description}
                </p>
                <p className="text-muted-foreground leading-relaxed font-inter">
                  {feature.details}
                </p>
              </div>
            </div>

            {/* Separator (except for last item) */}
            {index < features.length - 1 && (
              <div className="w-full h-px bg-black mt-16"></div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}