"use client";
import { cn } from "@/lib/utils";
import React from "react";

export function StatsWithGradient() {
  const items = [
    {
      description: "Years in the business trying to build this up.",
      value: "10+",
    },
    {
      description: "People Fought trying to establish our brand.",
      value: "100k+",
    },
    {
      description:
        "Fight Club Attendance so that everyone knows the first rule.",
      value: "100%",
    },
    {
      description: "Spent fighting in the ring, only to forget after.",
      value: "100+",
    },
  ];
  return (
    <section className="group/container relative mx-auto w-full max-w-7xl overflow-hidden rounded-3xl bg-neutral-50 p-10 py-20 dark:bg-neutral-900">
      <div className="relative z-20">
        <h2 className="text-center text-xl font-bold text-neutral-700 dark:text-neutral-100 md:text-3xl">
          Trusted by fighters all over the world
        </h2>
        <p className="mx-auto mt-4 max-w-2xl text-center text-sm text-neutral-800 dark:text-neutral-200 md:text-base">
          We are a team of experienced fighters and boxers who are passionate
          about helping you grow your business.
        </p>
        <div className="mt-10 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
          {items.map((item, index) => (
            <div
              key={"card" + index}
              className={cn(
                "group/card relative overflow-hidden rounded-lg bg-white/50 p-4 backdrop-blur-sm dark:bg-neutral-800/50"
              )}
            >
              <div className="flex items-center gap-2">
                <p className="text-3xl font-bold text-neutral-700 dark:text-neutral-200">
                  {item.value}
                </p>
              </div>
              <p className="text-balance text-balance mt-4 text-base text-neutral-600 dark:text-neutral-300">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
      <GradientBeam />
      <Bars />
    </section>
  );
}

export const GradientBeam = () => {
  return (
    <div className="pointer-events-none absolute -right-20 bottom-0 h-full w-full">
      <div className="group-hover/container-rotate-[10deg] absolute bottom-0 right-0 h-20 w-full -rotate-[5deg] rounded-full bg-gradient-to-r from-[#6E14D5] to-[#5E29FF] blur-3xl transition duration-200 group-hover/container:-translate-y-4 group-hover/container:blur-[80px]" />
    </div>
  );
};

const Bars = () => {
  return (
    <svg
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="absolute left-0 top-0 mx-auto h-full w-full flex-shrink-0"
    >
      <g opacity="0.3">
        <mask
          id="mask0_10_7"
          style={{ maskType: "alpha" }}
          maskUnits="userSpaceOnUse"
          x="-476"
          y="-536"
          width="1207"
          height="1024"
        >
          <rect
            x="-476"
            y="-74.5068"
            width="1035"
            height="627.858"
            transform="rotate(-26.4202 -476 -74.5068)"
            fill="url(#paint0_radial_10_7)"
          />
        </mask>
        <g mask="url(#mask0_10_7)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M340.815 417.958L767.348 -8.57505L766.598 -9.3252L340.065 417.207L340.815 417.958ZM334.449 417.958L760.982 -8.57505L760.231 -9.3252L333.699 417.207L334.449 417.958ZM754.616 -8.57505L328.083 417.958L327.333 417.207L753.865 -9.3252L754.616 -8.57505ZM321.717 417.958L748.249 -8.57505L747.499 -9.3252L320.967 417.207L321.717 417.958ZM741.883 -8.57505L315.351 417.958L314.6 417.207L741.133 -9.3252L741.883 -8.57505ZM308.985 417.958L735.517 -8.57505L734.767 -9.3252L308.234 417.207L308.985 417.958ZM729.151 -8.57505L302.618 417.958L301.868 417.207L728.401 -9.3252L729.151 -8.57505ZM296.252 417.958L722.785 -8.57505L722.035 -9.3252L295.502 417.207L296.252 417.958ZM716.419 -8.57505L289.886 417.958L289.136 417.207L715.668 -9.3252L716.419 -8.57505ZM283.52 417.958L710.052 -8.57505L709.302 -9.3252L282.77 417.207L283.52 417.958ZM703.686 -8.57505L277.154 417.958L276.404 417.207L702.936 -9.3252L703.686 -8.57505ZM270.788 417.958L697.32 -8.57505L696.57 -9.3252L270.037 417.207L270.788 417.958ZM690.954 -8.57505L264.421 417.958L263.671 417.207L690.204 -9.3252L690.954 -8.57505ZM258.055 417.958L684.588 -8.57505L683.838 -9.3252L257.305 417.207L258.055 417.958ZM678.222 -8.57505L251.689 417.958L250.939 417.207L677.471 -9.3252L678.222 -8.57505ZM245.323 417.958L671.856 -8.57505L671.105 -9.3252L244.573 417.207L245.323 417.958ZM665.489 -8.57505L238.957 417.958L238.207 417.207L664.739 -9.3252L665.489 -8.57505ZM232.591 417.958L659.123 -8.57505L658.373 -9.3252L231.84 417.207L232.591 417.958ZM652.757 -8.57505L226.224 417.958L225.474 417.207L652.007 -9.3252L652.757 -8.57505ZM219.858 417.958L646.391 -8.57505L645.641 -9.3252L219.108 417.207L219.858 417.958ZM640.025 -8.57505L213.492 417.958L212.742 417.207L639.275 -9.3252L640.025 -8.57505ZM207.126 417.958L633.659 -8.57505L632.908 -9.3252L206.376 417.207L207.126 417.958ZM627.292 -8.57505L200.76 417.958L200.01 417.207L626.542 -9.3252L627.292 -8.57505ZM194.394 417.958L620.926 -8.57505L620.176 -9.3252L193.644 417.207L194.394 417.958ZM614.56 -8.57505L188.028 417.958L187.277 417.207L613.81 -9.3252L614.56 -8.57505ZM181.661 417.958L608.194 -8.57505L607.444 -9.3252L180.911 417.207L181.661 417.958ZM601.828 -8.57505L175.295 417.958L174.545 417.207L601.078 -9.3252L601.828 -8.57505ZM168.929 417.958L595.462 -8.57505L594.711 -9.3252L168.179 417.207L168.929 417.958ZM589.095 -8.57505L162.563 417.958L161.813 417.207L588.345 -9.3252L589.095 -8.57505ZM156.197 417.958L582.729 -8.57505L581.979 -9.3252L155.447 417.207L156.197 417.958ZM576.363 -8.57505L149.831 417.958L149.08 417.207L575.613 -9.3252L576.363 -8.57505ZM143.464 417.958L569.997 -8.57505L569.247 -9.3252L142.714 417.207L143.464 417.958ZM563.631 -8.57505L137.098 417.958L136.348 417.207L562.881 -9.3252L563.631 -8.57505ZM130.732 417.958L557.265 -8.57505L556.514 -9.3252L129.982 417.207L130.732 417.958ZM550.898 -8.57505L124.366 417.958L123.616 417.207L550.148 -9.3252L550.898 -8.57505ZM118 417.958L544.532 -8.57505L543.782 -9.3252L117.25 417.207L118 417.958ZM538.166 -8.57505L111.634 417.958L110.883 417.207L537.416 -9.3252L538.166 -8.57505ZM105.267 417.958L531.8 -8.57505L531.05 -9.3252L104.517 417.207L105.267 417.958ZM525.434 -8.57505L98.9013 417.958L98.1511 417.207L524.684 -9.3252L525.434 -8.57505ZM92.5352 417.958L519.068 -8.57505L518.318 -9.3252L91.785 417.207L92.5352 417.958ZM512.702 -8.57505L86.169 417.958L85.4188 417.207L511.951 -9.3252L512.702 -8.57505ZM79.8028 417.958L506.335 -8.57505L505.585 -9.3252L79.0526 417.207L79.8028 417.958ZM499.969 -8.57505L73.4367 417.958L72.6865 417.207L499.219 -9.3252L499.969 -8.57505ZM67.0705 417.958L493.603 -8.57505L492.853 -9.3252L66.3203 417.207L67.0705 417.958ZM487.237 -8.57505L60.7044 417.958L59.9542 417.207L486.487 -9.3252L487.237 -8.57505ZM54.3382 417.958L480.871 -8.57505L480.121 -9.3252L53.588 417.207L54.3382 417.958ZM474.505 -8.57505L47.9721 417.958L47.2219 417.207L473.754 -9.3252L474.505 -8.57505ZM41.6059 417.958L468.138 -8.57505L467.388 -9.3252L40.8557 417.207L41.6059 417.958ZM461.772 -8.57505L35.2397 417.958L34.4895 417.207L461.022 -9.3252L461.772 -8.57505ZM28.8736 417.958L455.406 -8.57505L454.656 -9.3252L28.1234 417.207L28.8736 417.958ZM449.04 -8.57505L22.5074 417.958L21.7572 417.207L448.29 -9.3252L449.04 -8.57505ZM16.1412 417.958L442.674 -8.57505L441.924 -9.3252L15.3911 417.207L16.1412 417.958ZM436.308 -8.57505L9.77505 417.958L9.02493 417.207L435.557 -9.3252L436.308 -8.57505ZM3.4089 417.958L429.942 -8.57505L429.191 -9.3252L2.65878 417.207L3.4089 417.958ZM423.575 -8.57505L-2.95724 417.958L-3.7074 417.207L422.825 -9.3252L423.575 -8.57505ZM-9.32339 417.958L417.209 -8.57505L416.459 -9.3252L-10.0735 417.207L-9.32339 417.958ZM410.843 -8.57505L-15.6895 417.958L-16.4397 417.207L410.093 -9.3252L410.843 -8.57505ZM-22.0557 417.958L404.477 -8.57505L403.727 -9.3252L-22.8059 417.207L-22.0557 417.958ZM398.111 -8.57505L-28.4219 417.958L-29.172 417.207L397.361 -9.3252L398.111 -8.57505ZM-34.788 417.958L391.745 -8.57505L390.994 -9.3252L-35.5382 417.207L-34.788 417.958ZM385.378 -8.57505L-41.1542 417.958L-41.9043 417.207L384.628 -9.3252L385.378 -8.57505ZM-47.5204 417.958L379.012 -8.57505L378.262 -9.3252L-48.2705 417.207L-47.5204 417.958ZM372.646 -8.57505L-53.8865 417.958L-54.6367 417.207L371.896 -9.3252L372.646 -8.57505ZM-60.2527 417.958L366.28 -8.57505L365.53 -9.3252L-61.0028 417.207L-60.2527 417.958ZM359.914 -8.57505L-66.6188 417.958L-67.369 417.207L359.164 -9.3252L359.914 -8.57505ZM-72.985 417.958L353.548 -8.57505L352.797 -9.3252L-73.7351 417.207L-72.985 417.958ZM347.181 -8.57505L-79.3511 417.958L-80.1013 417.207L346.431 -9.3252L347.181 -8.57505ZM-85.7173 417.958L340.815 -8.57505L340.065 -9.3252L-86.4674 417.207L-85.7173 417.958ZM334.449 -8.57505L-92.0835 417.958L-92.8336 417.207L333.699 -9.3252L334.449 -8.57505ZM-98.4496 417.958L328.083 -8.57505L327.333 -9.3252L-99.1998 417.207L-98.4496 417.958ZM321.717 -8.57505L-104.816 417.958L-105.566 417.207L320.967 -9.3252L321.717 -8.57505ZM-111.182 417.958L315.351 -8.57505L314.6 -9.3252L-111.932 417.207L-111.182 417.958ZM308.985 -8.57505L-117.548 417.958L-118.298 417.207L308.234 -9.3252L308.985 -8.57505ZM-123.914 417.958L302.618 -8.57505L301.868 -9.3252L-124.664 417.207L-123.914 417.958ZM296.252 -8.57505L-130.28 417.958L-131.031 417.207L295.502 -9.3252L296.252 -8.57505ZM-136.647 417.958L289.886 -8.57505L289.136 -9.3252L-137.397 417.207L-136.647 417.958ZM283.52 -8.57505L-143.013 417.958L-143.763 417.207L282.77 -9.3252L283.52 -8.57505ZM-149.379 417.958L277.154 -8.57505L276.404 -9.3252L-150.129 417.207L-149.379 417.958ZM270.788 -8.57505L-155.745 417.958L-156.495 417.207L270.037 -9.3252L270.788 -8.57505ZM-162.111 417.958L264.421 -8.57505L263.671 -9.3252L-162.861 417.207L-162.111 417.958ZM258.055 -8.57505L-168.477 417.958L-169.227 417.207L257.305 -9.3252L258.055 -8.57505ZM-174.843 417.958L251.689 -8.57505L250.939 -9.3252L-175.594 417.207L-174.843 417.958ZM245.323 -8.57505L-181.21 417.958L-181.96 417.207L244.573 -9.3252L245.323 -8.57505ZM-187.576 417.958L238.957 -8.57505L238.207 -9.3252L-188.326 417.207L-187.576 417.958ZM232.591 -8.57505L-193.942 417.958L-194.692 417.207L231.84 -9.3252L232.591 -8.57505ZM-200.308 417.958L226.224 -8.57505L225.474 -9.3252L-201.058 417.207L-200.308 417.958ZM219.858 -8.57505L-206.674 417.958L-207.424 417.207L219.108 -9.3252L219.858 -8.57505ZM-213.04 417.958L213.492 -8.57505L212.742 -9.3252L-213.791 417.207L-213.04 417.958ZM207.126 -8.57505L-219.407 417.958L-220.157 417.207L206.376 -9.3252L207.126 -8.57505ZM-225.773 417.958L200.76 -8.57505L200.01 -9.3252L-226.523 417.207L-225.773 417.958ZM194.394 -8.57505L-232.139 417.958L-232.889 417.207L193.644 -9.3252L194.394 -8.57505ZM-238.505 417.958L188.028 -8.57505L187.277 -9.3252L-239.255 417.207L-238.505 417.958ZM181.661 -8.57505L-244.871 417.958L-245.621 417.207L180.911 -9.3252L181.661 -8.57505ZM-251.237 417.958L175.295 -8.57505L174.545 -9.3252L-251.988 417.207L-251.237 417.958ZM168.929 -8.57505L-257.604 417.958L-258.354 417.207L168.179 -9.3252L168.929 -8.57505ZM-263.97 417.958L162.563 -8.57505L161.813 -9.3252L-264.72 417.207L-263.97 417.958ZM156.197 -8.57505L-270.336 417.958L-271.086 417.207L155.447 -9.3252L156.197 -8.57505ZM-276.702 417.958L149.831 -8.57505L149.08 -9.3252L-277.452 417.207L-276.702 417.958ZM143.464 -8.57505L-283.068 417.958L-283.818 417.207L142.714 -9.3252L143.464 -8.57505ZM-289.434 417.958L137.098 -8.57505L136.348 -9.3252L-290.184 417.207L-289.434 417.958ZM130.732 -8.57505L-295.8 417.958L-296.551 417.207L129.982 -9.3252L130.732 -8.57505ZM-302.167 417.958L124.366 -8.57505L123.616 -9.3252L-302.917 417.207L-302.167 417.958ZM118 -8.57505L-308.533 417.958L-309.283 417.207L117.25 -9.3252L118 -8.57505ZM-314.899 417.958L111.634 -8.57505L110.883 -9.3252L-315.649 417.207L-314.899 417.958ZM105.267 -8.57505L-321.265 417.958L-322.015 417.207L104.517 -9.3252L105.267 -8.57505ZM-327.631 417.958L98.9013 -8.57505L98.1511 -9.3252L-328.381 417.207L-327.631 417.958ZM92.5352 -8.57505L-333.997 417.958L-334.748 417.207L91.785 -9.3252L92.5352 -8.57505ZM-340.364 417.958L86.169 -8.57505L85.4188 -9.3252L-341.114 417.207L-340.364 417.958ZM79.8028 -8.57505L-346.73 417.958L-347.48 417.207L79.0526 -9.3252L79.8028 -8.57505ZM-353.096 417.958L73.4367 -8.57505L72.6865 -9.3252L-353.846 417.207L-353.096 417.958ZM67.0705 -8.57505L-359.462 417.958L-360.212 417.207L66.3203 -9.3252L67.0705 -8.57505ZM-365.828 417.958L60.7044 -8.57505L59.9542 -9.3252L-366.578 417.207L-365.828 417.958ZM54.3382 -8.57505L-372.194 417.958L-372.945 417.207L53.588 -9.3252L54.3382 -8.57505ZM-378.561 417.958L47.9721 -8.57505L47.2219 -9.3252L-379.311 417.207L-378.561 417.958ZM41.6059 -8.57505L-384.927 417.958L-385.677 417.207L40.8557 -9.3252L41.6059 -8.57505ZM-391.293 417.958L35.2397 -8.57505L34.4895 -9.3252L-392.043 417.207L-391.293 417.958ZM28.8736 -8.57505L-397.659 417.958L-398.409 417.207L28.1234 -9.3252L28.8736 -8.57505ZM-404.025 417.958L22.5074 -8.57505L21.7572 -9.3252L-404.775 417.207L-404.025 417.958ZM16.1412 -8.57505L-410.391 417.958L-411.141 417.207L15.3911 -9.3252L16.1412 -8.57505ZM-416.757 417.958L9.77505 -8.57505L9.02493 -9.3252L-417.508 417.207L-416.757 417.958ZM3.4089 -8.57505L-423.124 417.958L-423.874 417.207L2.65878 -9.3252L3.4089 -8.57505ZM-429.49 417.958L-2.95724 -8.57505L-3.7074 -9.3252L-430.24 417.207L-429.49 417.958ZM-9.32339 -8.57505L-435.856 417.958L-436.606 417.207L-10.0735 -9.3252L-9.32339 -8.57505ZM-442.222 417.958L-15.6895 -8.57505L-16.4397 -9.3252L-442.972 417.207L-442.222 417.958ZM-22.0557 -8.57505L-448.588 417.958L-449.338 417.207L-22.8059 -9.3252L-22.0557 -8.57505ZM-454.954 417.958L-28.4219 -8.57505L-29.172 -9.3252L-455.705 417.207L-454.954 417.958ZM-34.788 -8.57505L-461.321 417.958L-462.071 417.207L-35.5382 -9.3252L-34.788 -8.57505ZM-467.687 417.958L-41.1542 -8.57505L-41.9043 -9.3252L-468.437 417.207L-467.687 417.958ZM-47.5204 -8.57505L-474.053 417.958L-474.803 417.207L-48.2705 -9.3252L-47.5204 -8.57505ZM-480.419 417.958L-53.8865 -8.57505L-54.6367 -9.3252L-481.169 417.207L-480.419 417.958ZM-60.2527 -8.57505L-486.785 417.958L-487.535 417.207L-61.0028 -9.3252L-60.2527 -8.57505ZM-493.151 417.958L-66.6188 -8.57505L-67.369 -9.3252L-493.902 417.207L-493.151 417.958ZM-72.985 -8.57505L-499.518 417.958L-500.268 417.207L-73.7351 -9.3252L-72.985 -8.57505ZM-505.884 417.958L-79.3511 -8.57505L-80.1013 -9.3252L-506.634 417.207L-505.884 417.958ZM-85.7173 -8.57505L-512.25 417.958L-513 417.207L-86.4674 -9.3252L-85.7173 -8.57505Z"
            fill="url(#paint1_linear_10_7)"
            fill-opacity="0.5"
          />
        </g>
      </g>
      <defs>
        <radialGradient
          id="paint0_radial_10_7"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(41.5 239.422) scale(517.5 313.929)"
        >
          <stop />
          <stop offset="1" stop-opacity="0" />
        </radialGradient>
        <linearGradient
          id="paint1_linear_10_7"
          x1="552.801"
          y1="-105.348"
          x2="-40.3128"
          y2="390.682"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="0.758" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};
