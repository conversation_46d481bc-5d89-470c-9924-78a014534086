import { cn } from "@/lib/utils";
import React, { useRef, useEffect } from "react";
import { motion, useInView, useSpring, useTransform } from "framer-motion";

export function StatsWithNumberTicker() {
  const items = [
    {
      description:
        "Comprehensive design problem bank, which are asked in FAANG and other top software companies.",
      value: 30,
      suffix: "+",
      label: "Questions",
    },
    {
      description: "Covering a wide range of companies that ask system design questions.", 
      value: 40,
      suffix: "+",
      label: "Companies",
    },
    {
      description:
        "Active developers and students using Layrs to learn and build scalable system designs.",
      value: 1000,
      suffix: "+",
      label: "Users",
    },
  ];
  return (
    <section className="group/container relative mx-auto w-full max-w-7xl overflow-hidden bg-secondary p-10 py-20 mb-20">
      <div className="relative z-20">
        <h2 className="text-center text-xl font-bold text-foreground md:text-3xl font-inter">
          Trusted by developers worldwide
        </h2>
        <p className="mx-auto mt-4 max-w-2xl text-center text-base text-muted-foreground font-inter">
          Join thousands of developers who build faster and better with our comprehensive design system and resources.
        </p>
        <div className="mx-auto mt-10 grid max-w-5xl grid-cols-1 gap-10 sm:grid-cols-3">
          {items.map((item, index) => (
            <motion.div
              initial={{ y: 20, opacity: 0, filter: "blur(4px)" }}
              animate={{ y: 0, opacity: 1, filter: "blur(0px)" }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              key={"card" + index}
              className={cn("group/card relative overflow-hidden rounded-lg text-center")}
            >
              <div className="flex flex-col items-center justify-center gap-3">
                <span className="stats-number">
                  <AnimatedNumber value={item.value} />
                  {item.suffix}
                </span>
                <span className="stats-label">
                  {item.label}
                </span>
              </div>
              <p className="mt-4 text-balance text-base text-muted-foreground font-inter">
                {item.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}

function AnimatedNumber({
  value,
  initial = 0,
}: {
  value: number;
  initial?: number;
}) {
  const ref = useRef(null);
  const isInView = useInView(ref);

  const spring = useSpring(initial, { mass: 0.8, stiffness: 75, damping: 15 });
  const display = useTransform(spring, (current) =>
    Math.round(current).toLocaleString()
  );

  useEffect(() => {
    if (isInView) {
      spring.set(value);
    } else {
      spring.set(initial);
    }
  }, [isInView, spring, value, initial]);

  return <motion.span ref={ref}>{display}</motion.span>;
}