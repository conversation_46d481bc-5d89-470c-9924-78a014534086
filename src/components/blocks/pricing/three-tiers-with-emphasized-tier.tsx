"use client"

import { Check } from 'lucide-react'

const tiers = [
  {
    id: 'free',
    name: 'Free',
    price: { monthly: '$0', annually: '$0' },
    description: 'Perfect for getting started with coding challenges.',
    features: ['Access to 5 problems', 'Community discussions', 'Basic hints'],
    featured: false,
    cta: 'Get Started',
  },
  {
    id: 'standard',
    name: 'Standard',
    price: { monthly: '$20', annually: '$200' },
    description: 'Ideal for serious learners looking to advance their skills.',
    features: [
      'Access to 50+ problems',
      'AI feedback',
      'Video explanations',
      'Priority support',
    ],
    featured: true,
    cta: 'Start Learning',
  },
  {
    id: 'professional',
    name: 'Professional',
    price: { monthly: '$40', annually: '$400' },
    description: 'Complete toolkit for interview preparation and career growth.',
    features: [
      'Unlimited problems',
      'Advanced AI mentoring',
      'Interview prep',
      'Custom scenarios',
      '1-on-1 sessions',
    ],
    featured: false,
    cta: 'Go Professional',
  },
]

export default function ThreeTiersWithEmphasizedTier() {
  return (
    <form className="group/tiers bg-secondary py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="text-base/7 font-semibold text-accent">Pricing</h2>
          <p className="mt-2 text-5xl font-semibold tracking-tight text-balance text-foreground sm:text-6xl">
            Choose Your Learning Path
          </p>
        </div>
        <p className="mx-auto mt-6 max-w-2xl text-center text-lg font-medium text-pretty text-muted-foreground sm:text-xl/8">
          From beginner-friendly challenges to advanced interview preparation, we have the perfect plan to accelerate your coding journey.
        </p>
        <div className="mt-16 flex justify-center">
          <fieldset aria-label="Payment frequency">
            <div className="grid grid-cols-2 gap-x-1 rounded-full p-1 text-center text-xs/5 font-semibold ring-1 ring-border ring-inset">
              <label className="group relative rounded-full px-2.5 py-1 has-checked:bg-accent">
                <input
                  defaultValue="monthly"
                  defaultChecked
                  name="frequency"
                  type="radio"
                  className="absolute inset-0 appearance-none rounded-full"
                />
                <span className="text-muted-foreground group-has-checked:text-white">Monthly</span>
              </label>
              <label className="group relative rounded-full px-2.5 py-1 has-checked:bg-accent">
                <input
                  defaultValue="annually"
                  name="frequency"
                  type="radio"
                  className="absolute inset-0 appearance-none rounded-full"
                />
                <span className="text-muted-foreground group-has-checked:text-white">Annually</span>
              </label>
            </div>
          </fieldset>
        </div>
        <div className="isolate mx-auto mt-10 grid max-w-md grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              data-featured={tier.featured ? 'true' : undefined}
              className="group/tier rounded-3xl p-8 ring-1 ring-border data-featured:bg-foreground data-featured:ring-foreground xl:p-10"
            >
              <h3
                id={`tier-${tier.id}`}
                className="text-lg/8 font-semibold text-foreground group-data-featured/tier:text-white"
              >
                {tier.name}
              </h3>
              <p className="mt-4 text-sm/6 text-muted-foreground group-data-featured/tier:text-gray-300">{tier.description}</p>
              {typeof tier.price === 'string' ? (
                <p className="mt-6 text-4xl font-semibold tracking-tight text-foreground group-data-featured/tier:text-white">
                  {tier.price}
                </p>
              ) : (
                <>
                  <p className="mt-6 flex items-baseline gap-x-1 group-not-has-[[name=frequency][value=monthly]:checked]/tiers:hidden">
                    <span className="text-4xl font-semibold tracking-tight text-foreground group-data-featured/tier:text-white">
                      {tier.price.monthly}
                    </span>
                    <span className="text-sm/6 font-semibold text-muted-foreground group-data-featured/tier:text-gray-300">
                      /month
                    </span>
                  </p>
                  <p className="mt-6 flex items-baseline gap-x-1 group-not-has-[[name=frequency][value=annually]:checked]/tiers:hidden">
                    <span className="text-4xl font-semibold tracking-tight text-foreground group-data-featured/tier:text-white">
                      {tier.price.annually}
                    </span>
                    <span className="text-sm/6 font-semibold text-muted-foreground group-data-featured/tier:text-gray-300">
                      /year
                    </span>
                  </p>
                </>
              )}

              <button
                value={tier.id}
                name="tier"
                type="submit"
                aria-describedby={`tier-${tier.id}`}
                className="mt-6 block w-full rounded-md bg-accent px-3 py-2 text-center text-sm/6 font-semibold text-white shadow-xs group-data-featured/tier:bg-accent group-data-featured/tier:text-white hover:bg-accent/80 group-data-featured/tier:hover:bg-accent/80 focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-accent group-data-featured/tier:focus-visible:outline-accent"
              >
                {tier.cta}
              </button>
              <ul
                role="list"
                className="mt-8 space-y-3 text-sm/6 text-muted-foreground group-data-featured/tier:text-gray-300 xl:mt-10"
              >
                {tier.features.map((feature) => (
                  <li key={feature} className="flex gap-x-3">
                    <Check
                      aria-hidden="true"
                      className="h-6 w-5 flex-none text-accent group-data-featured/tier:text-accent"
                    />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </form>
  )
}