import React, { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Transition } from "@headlessui/react";

export function TestimonialsGridWithCenteredCarousel() {
  return (
    <div id="testimonials" className="relative w-full max-w-7xl mx-auto px-4 md:px-8 pt-20 mt-20 overflow-hidden h-full bg-secondary">
      <div className="pb-20">
        <h1 className="pt-4 font-bold text-foreground text-lg md:text-2xl font-inter">
          Trusted by engineers worldwide
        </h1>
        <p className="text-base text-muted-foreground font-inter">
          Layrs is used by software engineers preparing for system design interviews and building scalable systems.
        </p>
      </div>

      <div className=" relative">
        <TestimonialsSlider />
        <div className="h-full max-h-screen md:max-h-none overflow-hidden w-full bg-secondary opacity-30 [mask-image:radial-gradient(circle_at_center,transparent_10%,white_99%)]">
          <TestimonialsGrid />
        </div>
      </div>

      <div className="absolute bottom-0 inset-x-0 h-40 w-full bg-gradient-to-t from-secondary to-transparent"></div>
    </div>
  );
}

export const TestimonialsGrid = () => {
  const first = testimonials.slice(0, 3);
  const second = testimonials.slice(3, 6);
  const third = testimonials.slice(6, 9);
  const fourth = testimonials.slice(9, 12);

  const grid = [first, second, third, fourth];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-7xl mx-auto ">
      {grid.map((testimonialsCol, index) => (
        <div key={`testimonials-col-${index}`} className="grid gap-4">
          {testimonialsCol.map((testimonial) => (
            <Card key={`testimonial-${testimonial.src}-${index}`}>
              <Quote>{testimonial.quote}</Quote>
              <div className="flex gap-2 items-center mt-8">
                <img
                  src={testimonial.src}
                  alt={testimonial.name}
                  className="rounded-full w-10 h-10"
                />
                <div className="flex flex-col">
                  <QuoteDescription>{testimonial.name}</QuoteDescription>
                  <QuoteDescription className="text-[10px]">
                    {testimonial.designation}
                  </QuoteDescription>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ))}
    </div>
  );
};

export const Card = ({
  className,
  children,
}: {
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <div
      className={cn(
        "p-8 rounded-xl border border-neutral-100 bg-neutral-200 dark:border-[rgba(255,255,255,0.10)] dark:bg-[rgba(40,40,40,0.30)] shadow-[2px_4px_16px_0px_rgba(248,248,248,0.06)_inset] group",
        className
      )}
    >
      {children}
    </div>
  );
};

export const Quote = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <h3
      className={cn(
        "text-xs font-semibold font-inter py-2",
        className
      )}
      style={{ color: '#6366f1' }}
    >
      {children}
    </h3>
  );
};

export const QuoteDescription = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <p
      className={cn(
        "text-xs font-normal text-muted-foreground font-inter max-w-sm",
        className
      )}
    >
      {children}
    </p>
  );
};

interface Testimonial {
  src: string;
  quote: string;
  name: string;
  designation?: string;
}

export const testimonials: Testimonial[] = [
  {
    name: "Navneet Ujjain",
    quote:
      "Layrs transformed my system design interview preparation. The practice scenarios are incredibly realistic and helped me land my dream job.",
    src: "https://i.pravatar.cc/150?img=1",
    designation: "Software Engineer at Google",
  },
  {
    name: "Marcus Rodriguez",
    quote:
      "The system design patterns and real-world examples on Layrs are exactly what I needed to understand distributed systems architecture.",
    src: "https://i.pravatar.cc/150?img=2",
    designation: "Senior Backend Engineer",
  },
  {
    name: "Emily Watson",
    quote:
      "Best system design learning platform I've used. The interactive diagrams and step-by-step breakdowns make complex concepts easy to grasp.",
    src: "https://i.pravatar.cc/150?img=3",
    designation: "Tech Lead at Netflix",
  },
  {
    name: "David Kim",
    quote:
      "Layrs helped me transition from frontend to full-stack development by teaching me scalable system architecture principles.",
    src: "https://i.pravatar.cc/150?img=4",
    designation: "Full Stack Developer",
  },
  {
    name: "Lisa Thompson",
    quote:
      "The practice interviews and feedback system are game-changers. I felt completely confident during my actual system design rounds.",
    src: "https://i.pravatar.cc/150?img=5",
    designation: "Engineering Manager",
  },
  {
    name: "Alex Johnson",
    quote:
      "Finally, a platform that teaches system design the right way - with hands-on practice and real industry examples.",
    src: "https://i.pravatar.cc/150?img=6",
    designation: "Principal Engineer",
  },
  {
    name: "Maria Santos",
    quote:
      "The microservices and database design modules on Layrs are incredibly thorough. Perfect for understanding production-grade systems.",
    src: "https://i.pravatar.cc/150?img=7",
    designation: "Cloud Architect",
  },
  {
    name: "James Miller",
    quote:
      "Layrs made system design concepts click for me. The visual approach and practical exercises are exactly what I needed.",
    src: "https://i.pravatar.cc/150?img=8",
    designation: "Software Architect",
  },
  {
    name: "Anna Lee",
    quote:
      "Incredible resource for scaling applications. The load balancing and caching strategies taught here saved us weeks of research.",
    src: "https://i.pravatar.cc/150?img=9",
    designation: "DevOps Engineer",
  },
  {
    name: "Robert Taylor",
    quote:
      "The system design interview prep on Layrs is unmatched. Helped me ace interviews at three FAANG companies.",
    src: "https://i.pravatar.cc/150?img=10",
    designation: "Senior SDE at Amazon",
  },
  {
    name: "Jessica Brown",
    quote:
      "Layrs bridges the gap between theory and practice perfectly. The case studies from real companies are invaluable.",
    src: "https://i.pravatar.cc/150?img=11",
    designation: "Engineering Director",
  },
  {
    name: "Michael Davis",
    quote:
      "The distributed systems course helped me design our company's new architecture. Layrs is an essential tool for any engineer.",
    src: "https://i.pravatar.cc/150?img=12",
    designation: "CTO at Startup",
  },
  {
    name: "Rachel Green",
    quote:
      "System design used to intimidate me, but Layrs breaks everything down into digestible chunks. Now I design systems confidently.",
    src: "https://i.pravatar.cc/150?img=13",
    designation: "Backend Developer",
  },
  {
    name: "Thomas Wilson",
    quote:
      "The real-world system design challenges on Layrs prepared me for the complexities of building at scale.",
    src: "https://i.pravatar.cc/150?img=14",
    designation: "Staff Engineer at Uber",
  },
];

export const TestimonialsSlider = () => {
  const [active, setActive] = useState<number>(0);
  const [autorotate, setAutorotate] = useState<boolean>(true);
  const testimonialsRef = useRef<HTMLDivElement>(null);

  const featuredTestimonials = [
    {
      name: "Sarah",
      quote: "Layrs is one of the best system design learning platforms I have ever tried. It's like magic.",
      src: "https://i.pravatar.cc/150?img=1",
      designation: "Software Engineer at Google",
    },
    ...testimonials.slice(1, 3)
  ];

  const slicedTestimonials = testimonials.slice(0, 3);

  useEffect(() => {
    if (!autorotate) return;
    const interval = setInterval(() => {
      setActive(
        active + 1 === slicedTestimonials.length ? 0 : (active) => active + 1
      );
    }, 7000);
    return () => clearInterval(interval);
  }, [active, autorotate, slicedTestimonials.length]);

  const heightFix = () => {
    if (testimonialsRef.current && testimonialsRef.current.parentElement)
      testimonialsRef.current.parentElement.style.height = `${testimonialsRef.current.clientHeight}px`;
  };

  useEffect(() => {
    heightFix();

    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        heightFix();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  return (
    <section className="absolute inset-0 mt-20 md:mt-60">
      <div className="max-w-3xl mx-auto  relative z-40 h-80">
        <div className="relative pb-12 md:pb-20">
          {/* Particles animation */}

          {/* Carousel */}
          <div className="text-center">
            {/* Testimonial image */}
            <div className="relative h-40 [mask-image:_linear-gradient(0deg,transparent,#FFFFFF_30%,#FFFFFF)] md:[mask-image:_linear-gradient(0deg,transparent,#FFFFFF_40%,#FFFFFF)]">
              <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[480px] h-[480px] -z-10 pointer-events-none before:rounded-full rounded-full before:absolute before:inset-0 before:bg-gradient-to-b before:from-neutral-400/20 before:to-transparent before:to-20% after:rounded-full after:absolute after:inset-0 after:bg-card after:m-px before:-z-20 after:-z-20">
                {featuredTestimonials.map((item, index) => (
                  <Transition
                    key={index}
                    show={active === index}
                    enter="transition ease-[cubic-bezier(0.68,-0.3,0.32,1)] duration-700 order-first"
                    enterFrom="opacity-0 -translate-x-10"
                    enterTo="opacity-100 translate-x-0"
                    leave="transition ease-[cubic-bezier(0.68,-0.3,0.32,1)] duration-700"
                    leaveFrom="opacity-100 translate-x-0"
                    leaveTo="opacity-0 translate-x-10"
                    beforeEnter={() => heightFix()}
                  >
                    <div className="absolute inset-0 h-full -z-10">
                      <img
                        className="relative top-11 left-1/2 -translate-x-1/2 rounded-full w-14 h-14"
                        src={item.src}
                        alt={item.name}
                      />
                    </div>
                  </Transition>
                ))}
              </div>
            </div>
            {/* Text */}
            <div className="mb-10 transition-all duration-150 delay-300 ease-in-out px-8 sm:px-6">
              <div className="relative flex flex-col" ref={testimonialsRef}>
                {featuredTestimonials.map((item, index) => (
                  <Transition
                    key={index}
                    show={active === index}
                    enter="transition ease-in-out duration-500 delay-200 order-first"
                    enterFrom="opacity-0 -translate-x-4"
                    enterTo="opacity-100 translate-x-0"
                    leave="transition ease-out duration-300 delay-300 absolute"
                    leaveFrom="opacity-100 translate-x-0"
                    leaveTo="opacity-0 translate-x-4"
                    beforeEnter={() => heightFix()}
                  >
                    <div className="text-base md:text-xl font-bold font-inter" style={{ color: '#6366f1' }}>
                      "{item.quote}"
                    </div>
                  </Transition>
                ))}
              </div>
            </div>
            {/* Buttons */}
            <div className="flex flex-wrap justify-center -m-1.5 px-8 sm:px-6">
              {featuredTestimonials.map((item, index) => (
                <button
                  className={cn(
                    `px-2 py-1 rounded-full m-1.5 text-xs border border-transparent text-muted-foreground font-inter transition duration-150 ease-in-out [background:linear-gradient(theme(colors.card),_theme(colors.card))_padding-box,_conic-gradient(theme(colors.border),_theme(colors.muted)_25%,_theme(colors.muted)_75%,_theme(colors.border)_100%)_border-box] relative before:absolute before:inset-0 before:bg-muted/30 before:rounded-full before:pointer-events-none ${
                      active === index
                        ? ""
                        : "border-transparent opacity-70"
                    }`
                  )}
                  style={active === index ? { borderColor: '#6366f180' } : {}}
                  key={index}
                  onClick={() => {
                    setActive(index);
                    setAutorotate(false);
                  }}
                >
                  <span className="relative">
                    <span className="text-foreground font-bold">
                      {item.name}
                    </span>{" "}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};