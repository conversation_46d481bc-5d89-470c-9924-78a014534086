import React, { useState } from 'react';
import { X, ChevronLeft, ChevronRight, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface TutorialStep {
  id: string;
  title: string;
  content: string;
  target?: string; // CSS selector for highlighting
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
}

interface TutorialOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  onStepChange?: (stepId: string) => void;
  isGuidedMode?: boolean;
}

const tutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to the System Design Canvas',
    content: 'This interactive canvas helps you design system architectures visually. Let\'s walk through the key features to get you started.',
    position: 'center'
  },
  {
    id: 'component-palette',
    title: 'Component Palette',
    content: 'At the top of the canvas, you\'ll find the "Components" button. Click it to expand and see all the components you need to build your system. Then drag and drop components like databases, servers, load balancers, and more onto the canvas below.',
    target: '.top-component-palette',
    position: 'bottom'
  },
  {
    id: 'canvas-area',
    title: 'Design Canvas',
    content: 'This is your main workspace. Drop components here, connect them with arrows, and arrange them to represent your system architecture. You can zoom, pan, and organize your design freely.',
    position: 'top'
  },
  {
    id: 'connections',
    title: 'Making Connections',
    content: 'Connect components by dragging from one component\'s handle to another. These connections represent data flow, API calls, or other relationships in your system.',
    position: 'bottom'
  },
  {
    id: 'composite-components',
    title: 'Composite Components',
    content: 'Group related components together using composite components! First, drag a "Composite" component from the palette onto the canvas. Then drag other components inside the composite to group them. You can expand/collapse composites, move them as a unit, and even nest composites within other composites for complex hierarchies.',
    position: 'center'
  },
  {
    id: 'properties-panel',
    title: 'Properties Panel',
    content: 'When you select a component, this panel opens automatically to show its properties. You can edit the component name, add descriptions, and configure settings. We\'ll open it now to show you!',
    target: '.properties-panel',
    position: 'left'
  },
  {
    id: 'context-inputs',
    title: 'Context & Requirements',
    content: 'Fill in your user journeys, assumptions, and constraints in this context tab. This information helps the AI assessment understand your design decisions and provide better feedback. We\'ll switch to this tab now!',
    target: '.context-inputs',
    position: 'right'
  },
  {
    id: 'assessment',
    title: 'Get AI Assessment',
    content: 'Once your design is ready, click "Start Assessment" to get detailed AI feedback on scalability, reliability, performance, and best practices.',
    target: '.assessment-button',
    position: 'bottom'
  },
  {
    id: 'save-export',
    title: 'Save & Export',
    content: 'Your designs are automatically saved. You can also export your diagrams as PNG images to share or include in presentations.',
    target: '.export-button',
    position: 'bottom'
  },
  {
    id: 'best-practices',
    title: 'Best Practices',
    content: 'Start with high-level components, then add details. Use composite components to group related services. Think about data flow, scalability bottlenecks, and failure scenarios. The AI assessment will guide you toward industry best practices.',
    position: 'center'
  }
];

const TutorialOverlay: React.FC<TutorialOverlayProps> = ({ isOpen, onClose, onStepChange, isGuidedMode = false }) => {
  const [currentStep, setCurrentStep] = useState(0);

  // Trigger step change when tutorial opens
  React.useEffect(() => {
    if (isOpen && onStepChange) {
      onStepChange(tutorialSteps[currentStep].id);
    }
  }, [isOpen, currentStep, onStepChange]);

  if (!isOpen) return null;

  const step = tutorialSteps[currentStep];

  // Customize step content based on mode
  const getStepContent = (step: TutorialStep) => {
    if (step.id === 'context-inputs') {
      if (isGuidedMode) {
        return {
          ...step,
          title: 'Course Context',
          content: 'In guided mode, the course provides predefined context and requirements. You can view the course outline and steps in this panel to understand what you need to build.'
        };
      }
    }
    return step;
  };

  const currentStepContent = getStepContent(step);
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === tutorialSteps.length - 1;

  const handleNext = () => {
    if (!isLastStep) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);

      // Notify parent about step change
      if (onStepChange) {
        onStepChange(tutorialSteps[nextStep].id);
      }
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);

      // Notify parent about step change
      if (onStepChange) {
        onStepChange(tutorialSteps[prevStep].id);
      }
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    onClose();
  };

  const getTooltipPosition = () => {
    // Special positioning for canvas step to avoid conflicts
    if (currentStepContent.id === 'canvas-area') {
      return 'top-20 left-1/2 transform -translate-x-1/2';
    }

    switch (currentStepContent.position) {
      case 'top':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'bottom':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      case 'left':
        return 'left-4 top-1/2 transform -translate-y-1/2';
      case 'right':
        return 'right-4 top-1/2 transform -translate-y-1/2';
      case 'center':
      default:
        return 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
    }
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" style={{ pointerEvents: 'none' }}>
      {/* Highlight target element if specified */}
      {currentStepContent.target && (
        <style>
          {`
            ${currentStepContent.target} {
              position: relative !important;
              z-index: 51 !important;
              box-shadow: 0 0 0 4px rgba(147, 51, 234, 0.5), 0 0 0 8px rgba(147, 51, 234, 0.2) !important;
              border-radius: 8px !important;
            }
            ${step.target} * {
              z-index: inherit !important;
            }
            .react-flow__renderer {
              z-index: inherit !important;
            }
            .react-flow__selection {
              z-index: inherit !important;
            }
            .react-flow__node {
              z-index: inherit !important;
            }
          `}
        </style>
      )}

      {/* Tutorial tooltip */}
      <div className={`absolute ${getTooltipPosition()} z-[60]`} style={{ pointerEvents: 'auto' }}>
        <div className="bg-white rounded-lg shadow-2xl border border-gray-200 max-w-md mx-4" style={{ pointerEvents: 'auto' }}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5 text-purple-600" />
              <h3 className="font-semibold text-gray-900">{currentStepContent.title}</h3>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Content */}
          <div className="p-4">
            <p className="text-gray-700 leading-relaxed">{currentStepContent.content}</p>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-4 border-t border-gray-200">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">
                {currentStep + 1} of {tutorialSteps.length}
              </span>
              <div className="flex gap-1">
                {tutorialSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`h-2 w-2 rounded-full ${
                      index === currentStep ? 'bg-purple-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrevious}
                disabled={isFirstStep}
                className="flex items-center gap-1"
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>

              {isLastStep ? (
                <Button
                  size="sm"
                  onClick={handleClose}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Get Started
                </Button>
              ) : (
                <Button
                  size="sm"
                  onClick={handleNext}
                  className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-1"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorialOverlay;
