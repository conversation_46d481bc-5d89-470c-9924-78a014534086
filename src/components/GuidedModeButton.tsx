
import React from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { BookOpen } from 'lucide-react';

interface GuidedModeButtonProps {
  className?: string;
}

const GuidedModeButton: React.FC<GuidedModeButtonProps> = ({ className }) => {
  const navigate = useNavigate();

  const handleOpenGuidedMode = () => {
    navigate('/guided');
  };

  return (
    <Button
      variant="outline"
      className={className}
      onClick={handleOpenGuidedMode}
    >
      <BookOpen className="h-4 w-4 mr-2" />
      Guided Courses
    </Button>
  );
};

export default GuidedModeButton;
