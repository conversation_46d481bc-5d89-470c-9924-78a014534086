
import React from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2 } from "lucide-react";

export interface SchemaColumn {
  id: string;
  name: string;
  type: string;
}

export interface SchemaTable {
  id: string;
  name: string;
  columns: SchemaColumn[];
}

interface SchemaBuilderProps {
  tables: SchemaTable[];
  onTablesChange: (tables: SchemaTable[]) => void;
}

const SchemaBuilder: React.FC<SchemaBuilderProps> = ({ tables, onTablesChange }) => {
  const columnTypes = ["string", "int", "boolean", "timestamp", "float", "text", "json", "uuid"];
  
  const addTable = () => {
    const newTable: SchemaTable = {
      id: `table-${Date.now()}`,
      name: `Table${tables.length + 1}`,
      columns: []
    };
    onTablesChange([...tables, newTable]);
  };

  const updateTableName = (tableId: string, newName: string) => {
    const updatedTables = tables.map(table => 
      table.id === tableId ? { ...table, name: newName } : table
    );
    onTablesChange(updatedTables);
  };

  const deleteTable = (tableId: string) => {
    const updatedTables = tables.filter(table => table.id !== tableId);
    onTablesChange(updatedTables);
  };

  const addColumn = (tableId: string) => {
    const updatedTables = tables.map(table => {
      if (table.id === tableId) {
        return {
          ...table,
          columns: [...table.columns, {
            id: `column-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`,
            name: `column${table.columns.length + 1}`,
            type: "string"
          }]
        };
      }
      return table;
    });
    onTablesChange(updatedTables);
  };

  const updateColumn = (tableId: string, columnId: string, field: "name" | "type", value: string) => {
    const updatedTables = tables.map(table => {
      if (table.id === tableId) {
        return {
          ...table,
          columns: table.columns.map(column => 
            column.id === columnId ? { ...column, [field]: value } : column
          )
        };
      }
      return table;
    });
    onTablesChange(updatedTables);
  };

  const deleteColumn = (tableId: string, columnId: string) => {
    const updatedTables = tables.map(table => {
      if (table.id === tableId) {
        return {
          ...table,
          columns: table.columns.filter(column => column.id !== columnId)
        };
      }
      return table;
    });
    onTablesChange(updatedTables);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-md font-medium">Schema Builder</h3>
        <Button 
          onClick={addTable} 
          size="sm"
          variant="light"
        >
          Add Table
        </Button>
      </div>

      {tables.length === 0 && (
        <div className="text-sm text-gray-400 text-center py-4">
          No tables defined. Click "Add Table" to start building your schema.
        </div>
      )}

      {tables.map(table => (
        <div key={table.id} className="bg-white border border-gray-200 rounded-md shadow-sm p-3 space-y-3">
          <div className="flex items-center gap-2">
            <Input
              value={table.name}
              onChange={(e) => updateTableName(table.id, e.target.value)}
              placeholder="Table Name"
              className="font-medium"
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => deleteTable(table.id)}
              className="text-gray-400 hover:text-red-500"
            >
              <Trash2 size={16} />
            </Button>
          </div>

          <div className="space-y-2">
            {table.columns.map(column => (
              <div key={column.id} className="flex items-center gap-2">
                <Input
                  value={column.name}
                  onChange={(e) => updateColumn(table.id, column.id, "name", e.target.value)}
                  placeholder="Column name"
                  className="flex-1"
                />
                <select
                  value={column.type}
                  onChange={(e) => updateColumn(table.id, column.id, "type", e.target.value)}
                  className="component-input w-24 text-sm"
                >
                  {columnTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => deleteColumn(table.id, column.id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <Trash2 size={16} />
                </Button>
              </div>
            ))}
            
            <Button 
              onClick={() => addColumn(table.id)} 
              variant="light"
              size="sm"
              className="w-full mt-2 text-xs"
            >
              Add Column
            </Button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default SchemaBuilder;
