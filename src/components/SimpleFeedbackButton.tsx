import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Send, Loader2, Star } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';
import { debugLog, debug<PERSON>arn, debugError } from '@/utils/debugLogger';

interface FeedbackQuestion {
  id: string;
  question_text: string;
  question_type: 'rating' | 'text' | 'textarea' | 'multiple_choice' | 'checkbox';
  options?: string[];
  is_required: boolean;
  display_order: number;
  category: string;
}

interface FeedbackResponse {
  [questionId: string]: {
    response_text?: string;
    response_rating?: number;
    response_options?: string[];
  };
}

interface SimpleFeedbackButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  category?: string; // Optional single category filter
  categories?: string[]; // Optional multiple categories filter
}

const SimpleFeedbackButton: React.FC<SimpleFeedbackButtonProps> = ({
  className,
  variant = 'default',
  size = 'default',
  category,
  categories
}) => {
  const { user } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [questions, setQuestions] = useState<FeedbackQuestion[]>([]);
  const [responses, setResponses] = useState<FeedbackResponse>({});
  const [activeTab, setActiveTab] = useState<string>('');

  // Load questions when dialog opens
  useEffect(() => {
    if (isOpen && questions.length === 0) {
      loadQuestions();
    }
  }, [isOpen]);

  const loadQuestions = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('feedback_questions')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: true }); // Secondary sort for consistent ordering

      // Filter by category/categories if provided
      if (category) {
        query = query.eq('category', category);
      } else if (categories && categories.length > 0) {
        query = query.in('category', categories);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error loading questions:', error);
        toast.error('Failed to load feedback questions');
        return;
      }

      // Remove duplicates based on question_text, keeping the first occurrence
      const uniqueQuestions = (data || []).filter((question, index, array) =>
        array.findIndex(q => q.question_text === question.question_text) === index
      );

      const filterDescription = category ? `category: ${category}` :
                           categories ? `categories: ${categories.join(', ')}` : 'all categories';
      debugLog(`Loaded ${data?.length || 0} questions, ${uniqueQuestions.length} unique questions for ${filterDescription}`);
      setQuestions(uniqueQuestions);

      // Set default active tab to the first category
      if (uniqueQuestions.length > 0 && !activeTab) {
        setActiveTab(uniqueQuestions[0].category);
      }
    } catch (error) {
      console.error('Error loading questions:', error);
      toast.error('Failed to load feedback questions');
    } finally {
      setIsLoading(false);
    }
  };

  // Organize questions by category
  const questionsByCategory = questions.reduce((acc, question) => {
    if (!acc[question.category]) acc[question.category] = [];
    acc[question.category].push(question);
    return acc;
  }, {} as Record<string, FeedbackQuestion[]>);

  // Get category display info
  const getCategoryInfo = (categoryKey: string) => {
    const categoryNames: Record<string, { name: string; icon: string; description: string }> = {
      general: { name: 'General', icon: '💬', description: 'Overall experience and recommendations' },
      usability: { name: 'Usability', icon: '🎨', description: 'User interface and navigation' },
      features: { name: 'Features', icon: '⚡', description: 'Platform features and functionality' },
      suggestions: { name: 'Suggestions', icon: '💡', description: 'Ideas and improvements' },
      technical: { name: 'Technical', icon: '🔧', description: 'Bugs and performance issues' },
      post_assessment: { name: 'Assessment', icon: '📊', description: 'Assessment experience' },
      post_chat: { name: 'Chat Assistant', icon: '🤖', description: 'AI chat experience' },
      content: { name: 'Content', icon: '📚', description: 'Courses and questions' },
      session_end: { name: 'Session', icon: '⏰', description: 'Session experience' },
      feature_usage: { name: 'Feature Usage', icon: '🎯', description: 'Specific feature feedback' }
    };
    return categoryNames[categoryKey] || { name: categoryKey, icon: '📝', description: 'Feedback' };
  };

  const handleResponseChange = (questionId: string, value: any, type: string) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: {
        ...prev[questionId],
        ...(type === 'rating' ? { response_rating: value } :
           type === 'checkbox' ? { response_options: value } :
           { response_text: value })
      }
    }));
  };

  const validateResponses = () => {
    const requiredQuestions = questions.filter(q => q.is_required);
    const missingResponses = requiredQuestions.filter(q => {
      const response = responses[q.id];
      if (!response) return true;

      if (q.question_type === 'rating') {
        return !response.response_rating;
      } else if (q.question_type === 'checkbox') {
        return !response.response_options || response.response_options.length === 0;
      } else {
        return !response.response_text || response.response_text.trim() === '';
      }
    });

    return missingResponses;
  };

  const handleSubmit = async () => {
    if (!user) {
      toast.error('Please log in to submit feedback');
      return;
    }

    const missingResponses = validateResponses();
    if (missingResponses.length > 0) {
      toast.error(`Please answer all required questions`);
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate a unique session ID for this feedback
      const sessionId = `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Insert all responses
      const responseInserts = Object.entries(responses).map(([questionId, response]) => ({
        user_id: user.id,
        session_id: sessionId,
        question_id: questionId,
        response_text: response.response_text || null,
        response_rating: response.response_rating || null,
        response_options: response.response_options || null,
        context_type: 'manual',
        context_data: {
          source: 'feedback_form',
          timestamp: new Date().toISOString(),
          page: window.location.pathname,
          category: category || 'general'
        },
        user_agent: navigator.userAgent
      }));

      const { error: insertError } = await supabase
        .from('beta_feedback')
        .insert(responseInserts);

      if (insertError) {
        console.error('Error inserting feedback:', insertError);
        throw new Error('Failed to submit feedback');
      }

      // Create a feedback session record
      await supabase
        .from('feedback_sessions')
        .insert({
          session_id: sessionId,
          user_id: user.id,
          trigger_type: 'manual',
          trigger_context: {
            source: 'feedback_form',
            page: window.location.pathname,
            category: category || 'general'
          },
          status: 'completed',
          questions_answered: Object.keys(responses).length,
          total_questions: questions.length,
          completion_percentage: (Object.keys(responses).length / questions.length) * 100,
          completed_at: new Date().toISOString()
        });

      toast.success('Thank you for your feedback!');
      setResponses({});
      setIsOpen(false);
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error('Failed to submit feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuestion = (question: FeedbackQuestion) => {
    const response = responses[question.id];

    switch (question.question_type) {
      case 'rating':
        return (
          <div key={question.id} className="space-y-3">
            <Label className="text-sm font-medium">
              {question.question_text}
              {question.is_required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className="flex items-center gap-2">
              {[1, 2, 3, 4, 5].map((rating) => (
                <button
                  key={rating}
                  type="button"
                  onClick={() => handleResponseChange(question.id, rating, 'rating')}
                  className={`p-1 rounded transition-colors ${
                    response?.response_rating === rating
                      ? 'text-yellow-500'
                      : 'text-gray-300 hover:text-yellow-400'
                  }`}
                >
                  <Star className="h-6 w-6 fill-current" />
                </button>
              ))}
              <span className="text-sm text-gray-500 ml-2">
                {response?.response_rating ? `${response.response_rating}/5` : ''}
              </span>
            </div>
          </div>
        );

      case 'textarea':
        return (
          <div key={question.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {question.question_text}
              {question.is_required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              placeholder="Your response..."
              value={response?.response_text || ''}
              onChange={(e) => handleResponseChange(question.id, e.target.value, 'text')}
              className="min-h-[80px] resize-none"
            />
          </div>
        );

      case 'text':
        return (
          <div key={question.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {question.question_text}
              {question.is_required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              placeholder="Your response..."
              value={response?.response_text || ''}
              onChange={(e) => handleResponseChange(question.id, e.target.value, 'text')}
            />
          </div>
        );

      case 'multiple_choice':
        return (
          <div key={question.id} className="space-y-3">
            <Label className="text-sm font-medium">
              {question.question_text}
              {question.is_required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <RadioGroup
              value={response?.response_text || ''}
              onValueChange={(value) => handleResponseChange(question.id, value, 'text')}
            >
              {question.options?.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <RadioGroupItem value={option} id={`${question.id}-${option}`} />
                  <Label htmlFor={`${question.id}-${option}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        );

      case 'checkbox':
        return (
          <div key={question.id} className="space-y-3">
            <Label className="text-sm font-medium">
              {question.question_text}
              {question.is_required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className="space-y-2">
              {question.options?.map((option) => (
                <div key={option} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${question.id}-${option}`}
                    checked={response?.response_options?.includes(option) || false}
                    onCheckedChange={(checked) => {
                      const currentOptions = response?.response_options || [];
                      const newOptions = checked
                        ? [...currentOptions, option]
                        : currentOptions.filter(o => o !== option);
                      handleResponseChange(question.id, newOptions, 'checkbox');
                    }}
                  />
                  <Label htmlFor={`${question.id}-${option}`} className="text-sm">
                    {option}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!user) {
    return null; // Don't show button if user is not logged in
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <MessageSquare className="h-4 w-4 mr-2" />
          Feedback
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-hidden flex flex-col p-6">
        <div className="flex-1 overflow-hidden">{/* Main content container */}

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
            <span className="ml-2 text-gray-600">Loading questions...</span>
          </div>
        ) : questions.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600">No feedback questions available at the moment.</p>
          </div>
        ) : Object.keys(questionsByCategory).length <= 1 ? (
          // Single category - no tabs needed
          <div className="space-y-4 flex flex-col h-full">
            <p className="text-sm text-gray-600 px-1">
              Help us improve Layrs by answering these questions. Fields marked with * are required.
            </p>

            <div className="space-y-4 flex-1 overflow-y-auto px-1 pt-4 pb-6">
              {questions.map(renderQuestion)}
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t mt-4">
              <Button
                variant="outline"
                onClick={() => {
                  setIsOpen(false);
                  setResponses({});
                  setActiveTab('');
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || validateResponses().length > 0}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Feedback
                  </>
                )}
              </Button>
            </div>
          </div>
        ) : (
          // Multiple categories - use tabs
          <div className="space-y-3">
            <p className="text-sm text-gray-600 px-1">
              Help us improve Layrs by answering these questions. Fields marked with * are required.
            </p>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-1 mb-3">
                {Object.keys(questionsByCategory).map((categoryKey) => {
                  const categoryInfo = getCategoryInfo(categoryKey);
                  const categoryQuestions = questionsByCategory[categoryKey];
                  const answeredCount = categoryQuestions.filter(q => responses[q.id]).length;
                  const requiredCount = categoryQuestions.filter(q => q.is_required).length;
                  const requiredAnswered = categoryQuestions.filter(q => q.is_required && responses[q.id]).length;

                  return (
                    <TabsTrigger
                      key={categoryKey}
                      value={categoryKey}
                      className="flex flex-col items-center gap-1 p-2 text-xs relative h-auto"
                    >
                      <span className="text-sm">{categoryInfo.icon}</span>
                      <span className="font-medium text-xs">{categoryInfo.name}</span>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <span>{answeredCount}/{categoryQuestions.length}</span>
                        {requiredCount > 0 && (
                          <span className={`text-xs ${requiredAnswered === requiredCount ? 'text-green-600' : 'text-red-500'}`}>
                            ({requiredAnswered}/{requiredCount}*)
                          </span>
                        )}
                      </div>
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {Object.entries(questionsByCategory).map(([categoryKey, categoryQuestions]) => {
                return (
                  <TabsContent
                    key={categoryKey}
                    value={categoryKey}
                    className="mt-0"
                  >
                    <div className="max-h-96 overflow-y-auto pt-20 pb-6 px-1">
                      <div className="space-y-4">
                        {categoryQuestions.map(renderQuestion)}
                      </div>
                    </div>
                  </TabsContent>
                );
              })}
            </Tabs>

            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button
                variant="outline"
                onClick={() => {
                  setIsOpen(false);
                  setResponses({});
                  setActiveTab('');
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || validateResponses().length > 0}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Submit Feedback ({Object.keys(responses).length}/{questions.length})
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        </div>{/* Close main content container */}
      </DialogContent>
    </Dialog>
  );
};

export default SimpleFeedbackButton;
