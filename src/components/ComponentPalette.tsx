import React from 'react';
import { componentTypes } from './ComponentTypes';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface ComponentDragItem {
  type: string;
  label: string;
  icon: React.ReactNode;
  className: string;
}

interface ComponentPaletteProps {
  filterComponents?: string[]; // Optional array of component types to filter the palette
  currentStepId?: string; // Added to detect informational steps
}

const ComponentPalette: React.FC<ComponentPaletteProps> = ({ filterComponents, currentStepId }) => {
  // Create component items from componentTypes
  let componentItems: ComponentDragItem[] = Object.values(componentTypes).map(component => ({
    type: component.type,
    label: component.label,
    icon: component.iconComponent,
    className: component.className
  }));

  debugLog('All available components:', componentItems.map(item => `${item.type} (${item.label})`));
  debugLog('Current step ID:', currentStepId);

  // Don't show any components for informational or simulation-only steps
  const informationalOrSimulationSteps = [
    // 'learn-about-api-gateway', // Removed as this step now requires components
    // 'simulate-traffic', // Removed to allow component access during simulation
    // 'final-simulation', // Removed to allow component access during simulation
    'export-system'
  ];

  if (informationalOrSimulationSteps.includes(currentStepId || '')) {
    componentItems = [];
    debugLog('Informational or simulation step detected, hiding all components');
  }
  // Filter components if filterComponents array is provided and non-empty
  else if (filterComponents && filterComponents.length > 0) {
    // Log before filtering
    debugLog('Filter Components List:', filterComponents);
    debugLog('Current step ID:', currentStepId);
    debugLog('Component Types Before Filtering:', componentItems.map(item => item.type));

    // Check for the composite component specifically
    const hasComposite = filterComponents.includes('composite');
    if (hasComposite) {
      debugLog('Looking for composite in available components...');
      const compositeComponent = componentItems.find(item =>
        item.type === 'composite' || item.type === 'compositeNode');
      if (compositeComponent) {
        debugLog('Found composite component with type:', compositeComponent.type);
      } else {
        debugLog('No composite component found in available components');
      }
    }

    // Special case handling for specific steps
    if (currentStepId === 'add-queue') {
      debugLog('Queue step detected - ensuring queue component is available');
      // Make sure the queue component is available regardless of filterComponents
      componentItems = componentItems.filter(item => item.type === 'queue');
    }
    else if (currentStepId === 'add-media-database') {
      debugLog('Media DB step detected - ensuring database component is available');
      componentItems = componentItems.filter(item => item.type === 'database');
    }
    else if (currentStepId === 'add-cdn') {
      debugLog('CDN step detected - ensuring CDN component is available');
      componentItems = componentItems.filter(item => item.type === 'cdn');
    }
    else if (currentStepId === 'add-analytics') {
      debugLog('Analytics step detected - ensuring composite and internal components are available');
      // For analytics step, we need the composite component and components to place inside it
      componentItems = componentItems.filter(item =>
        item.type === 'composite' ||
        item.type === 'compositeNode' ||
        item.type === 'server' ||
        item.type === 'database' ||
        item.type === 'queue'
      );
    }
    else if (currentStepId === 'add-notifications') {
      debugLog('Notifications step detected - ensuring queue and server components are available');
      // For notifications step, we need both queue and server components, plus optional components
      componentItems = componentItems.filter(item =>
        item.type === 'queue' ||
        item.type === 'server' ||
        item.type === 'database' ||
        item.type === 'cache'
      );
    }
    else if (currentStepId === 'final-simulation' || currentStepId === 'export-system') {
      debugLog('Final simulation or export step detected - making all components available');
      // For final simulation and export steps, make all components available
      // This allows users to make any final adjustments to their system
      componentItems = componentItems;
    }
    // Handle special case for composite vs compositeNode naming
    else {
      componentItems = componentItems.filter(item => {
        // Special case: if 'composite' is requested, also include 'compositeNode'
        if (item.type === 'compositeNode' && filterComponents.includes('composite')) {
          return true;
        }

        // For step 5 specifically, ensure server is available
        if (currentStepId === 'add-media-service' && item.type === 'server') {
          debugLog('Keeping server component for Media Service step');
          return true;
        }

        return filterComponents.includes(item.type);
      });
    }

    // Debug output to help troubleshoot
    debugLog('Filtered Component Items:', componentItems.map(item => item.type));
  }

  const onDragStart = (event: React.DragEvent, nodeType: string) => {
    const item = componentItems.find(item => item.type === nodeType);
    if (item) {
      // Stringify the component data so we can retrieve all styling info when dropping
      event.dataTransfer.setData('application/reactflow', JSON.stringify({
        type: item.type,
        className: item.className,
        label: item.label
      }));
      event.dataTransfer.effectAllowed = 'move';

      // Set a ghost image for drag preview using HTML5 API
      const dragPreview = document.createElement('div');
      dragPreview.className = 'component-drag-preview';
      dragPreview.style.width = '120px';
      dragPreview.style.height = '40px';
      dragPreview.style.display = 'flex';
      dragPreview.style.alignItems = 'center';
      dragPreview.style.justifyContent = 'flex-start';
      dragPreview.style.padding = '8px';
      dragPreview.style.backgroundColor = item.className ? item.className.split(' ')[0] : 'white';
      dragPreview.style.border = '1px solid #ccc';
      dragPreview.style.borderRadius = '4px';

      // Create icon element
      const iconElement = document.createElement('div');
      iconElement.className = 'w-5 h-5 flex items-center justify-center';
      iconElement.style.width = '20px';
      iconElement.style.height = '20px';
      iconElement.style.marginRight = '8px';
      // We can't directly append React elements, so we create a placeholder
      // that resembles the icon visually
      iconElement.innerHTML = '⬤'; // Simple circle as placeholder
      dragPreview.appendChild(iconElement);

      // Create label element
      const labelElement = document.createElement('span');
      labelElement.className = 'text-sm font-medium';
      labelElement.style.fontSize = '14px';
      labelElement.style.fontWeight = '500';
      labelElement.textContent = item.label;
      dragPreview.appendChild(labelElement);

      // Add to DOM temporarily (invisible) to use as drag image
      document.body.appendChild(dragPreview);

      // Center the drag image on the cursor instead of using fixed offsets
      // This ensures the component appears centered under the cursor during dragging
      // and will be placed exactly where you drop it
      event.dataTransfer.setDragImage(dragPreview, dragPreview.offsetWidth / 2, dragPreview.offsetHeight / 2);

      // Clean up the temporary element
      setTimeout(() => {
        document.body.removeChild(dragPreview);
      }, 0);
    }
  };

  // Function to assign tutorial ID based on component type
  function getTutorialId(type: string) {
    if (type === 'server') return 'server-component';
    if (type === 'primitive') return 'primitive-component';
    if (type === 'compositeNode') return 'composite-component'; // This should be 'compositeNode', not some other value
    return undefined;
  };

  return (
    <div className="mb-6" id="component-palette">
      <h2 className="text-lg font-semibold mb-3 text-layrs-dark">Component Palette</h2>
      {componentItems.length > 0 ? (
        <div className="flex flex-wrap gap-2">
          {componentItems.map((item) => (
            <div
              key={item.type}
              id={getTutorialId(item.type)}
              className={`cursor-move border rounded-md p-2 flex items-center gap-2 ${item.className} hover:shadow-md transition-shadow duration-200 component-${item.type}`}
              draggable
              onDragStart={(event) => onDragStart(event, item.type)}
            >
              <div className="w-5 h-5 flex items-center justify-center">
                {item.icon}
              </div>
              <span className="text-sm font-medium">{item.label}</span>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">No components available for this step.</p>
      )}
    </div>
  );
};

ComponentPalette.defaultProps = {
  filterComponents: undefined, // Default to undefined (show all components)
  currentStepId: undefined, // Default to undefined
};

export default ComponentPalette;
