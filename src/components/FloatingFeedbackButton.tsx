import React from 'react';
import SimpleFeedbackButton from './SimpleFeedbackButton';
import { cn } from '@/lib/utils';

interface FloatingFeedbackButtonProps {
  className?: string;
}

const FloatingFeedbackButton: React.FC<FloatingFeedbackButtonProps> = ({ className }) => {
  return (
    <div className={cn(
      "fixed bottom-6 right-6 z-50",
      className
    )}>
      <SimpleFeedbackButton
        categories={['general', 'usability', 'features', 'suggestions']}
        variant="default"
        size="default"
        className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
      />
    </div>
  );
};

export default FloatingFeedbackButton;
