import React, { useRef, useEffect } from 'react';
import { Play } from 'lucide-react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const DemoVideo: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const video = videoRef.current;
    const section = sectionRef.current;

    if (!video || !section) return;

    // Create intersection observer
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Video is in view - play it
            video.play().catch((error) => {
              // Handle autoplay restrictions gracefully
              debugLog('Autoplay prevented:', error);
            });
          } else {
            // Video is out of view - pause it
            video.pause();
          }
        });
      },
      {
        // Trigger when 50% of the video is visible
        threshold: 0.5,
        // Add some margin to trigger slightly before/after
        rootMargin: '0px 0px -10% 0px'
      }
    );

    // Start observing the section
    observer.observe(section);

    // Cleanup
    return () => {
      observer.disconnect();
    };
  }, []);
  return (
    <section ref={sectionRef} className="py-16 md:py-24 relative">
      {/* Subtle background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#2e1065]/5 to-transparent"></div>

      <div className="container max-w-6xl mx-auto px-4 relative z-10">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">
            See Layrs in Action
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Watch how Layrs simplifies system design with our intuitive drag-and-drop interface
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Video container with glass effect */}
          <div className="relative group">
            {/* Outer glow effect */}
            <div className="absolute -inset-2 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-indigo-500/20 rounded-2xl blur-xl opacity-70 group-hover:opacity-90 transition duration-500"></div>

            {/* Glass card container */}
            <div className="relative glass-card rounded-2xl p-4 md:p-6 shadow-2xl border border-white/10">
              <div className="relative aspect-video rounded-xl overflow-hidden bg-black/20">
                <video
                  ref={videoRef}
                  className="w-full h-full object-cover rounded-xl"
                  controls
                  muted
                  loop
                  preload="metadata"
                  poster="/api/placeholder/800/450" // You can replace this with a custom thumbnail if needed
                >
                  <source
                    src="https://pub-717e04f7ca014094b46161c73fb59cef.r2.dev/Layrs%20Design%20Simplified%20(2).mp4"
                    type="video/mp4"
                  />
                  Your browser does not support the video tag.
                </video>

                {/* Custom play button overlay (optional - browsers have their own) */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                  <div className="bg-white/10 backdrop-blur-sm rounded-full p-4">
                    <Play className="w-8 h-8 text-white" fill="white" />
                  </div>
                </div>
              </div>

              {/* Video description */}
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-400">
                  Demo: Building a system architecture with Layrs' visual design tools
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>


    </section>
  );
};

export default DemoVideo;
