import React from 'react';
import SimpleFeedbackButton from './SimpleFeedbackButton';

interface PostAssessmentFeedbackProps {
  score?: number;
  questionId?: string;
  onComplete?: () => void;
}

const PostAssessmentFeedback: React.FC<PostAssessmentFeedbackProps> = ({
  score,
  questionId,
  onComplete
}) => {
  return (
    <SimpleFeedbackButton
      category="post_assessment"
      variant="outline"
      size="sm"
      className="bg-gradient-to-r from-green-500/20 to-blue-500/20 hover:from-green-500/30 hover:to-blue-500/30 text-gray-700 border-green-400/30 hover:border-green-400/50"
    />
  );
};

export default PostAssessmentFeedback;
