import { MarkerType, getBezierPath, getSmoothStepPath } from '@xyflow/react';
import { CustomNodeWrapper, CompositeNodeWrapper } from './NodeWrappers';
import UnifiedEdge from './UnifiedEdge';

// Define our custom node types
export const nodeTypes = {
  customNode: CustomNodeWrapper,
  compositeNode: CompositeNodeWrapper
};

// Default edge options for better visibility
export const getDefaultEdgeOptions = (edgeType: string = 'smooth') => ({
  type: edgeType, // Use the selected edge type
  markerEnd: {
    type: MarkerType.ArrowClosed,
    width: 20,
    height: 20,
    color: '#222', // Darker color for better visibility
  },
  style: { 
    strokeWidth: 2, 
    stroke: '#222',
    // Add a subtle shadow to make edges stand out more
    filter: 'drop-shadow(0 1px 1px rgba(0,0,0,0.3))'
  },
  animated: false,
  // Default label styling
  labelStyle: { 
    fill: '#222', 
    fontWeight: 700,
    fontSize: 12
  },
  labelBgStyle: {
    fill: 'white',
    fillOpacity: 0.8,
    rx: 4
  },
  labelBgPadding: [40, 20] as [number, number]
});

// Edge types for different connection styles - all using UnifiedEdge
export const edgeTypes = {
  // Basic path types
  bezier: (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="normal" />,
  smooth: (props: any) => <UnifiedEdge {...props} pathType="smooth" visualStyle="normal" />,
  
  // Dashed variations
  'dashed-bezier': (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="dashed" />,
  'dashed-smooth': (props: any) => <UnifiedEdge {...props} pathType="smooth" visualStyle="dashed" />,
  
  // Dotted variations
  'dotted-bezier': (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="dotted" />,
  'dotted-smooth': (props: any) => <UnifiedEdge {...props} pathType="smooth" visualStyle="dotted" />,
  
  // Thick variations
  'thick-bezier': (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="thick" />,
  'thick-smooth': (props: any) => <UnifiedEdge {...props} pathType="smooth" visualStyle="thick" />,
  
  // Double arrow variations
  'doubleArrow-bezier': (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="doubleArrow" />,
  'doubleArrow-smooth': (props: any) => <UnifiedEdge {...props} pathType="smooth" visualStyle="doubleArrow" />,
  
  // Legacy support for old edge types
  dashed: (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="dashed" />,
  dotted: (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="dotted" />,
  thick: (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="thick" />,
  doubleArrow: (props: any) => <UnifiedEdge {...props} pathType="bezier" visualStyle="doubleArrow" />,
};
