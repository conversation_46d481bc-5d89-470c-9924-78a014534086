import { ReactFlowInstance } from '@xyflow/react';
import { toast } from 'sonner';
import {
  AssessmentResult,
  prepareNestedStructure as prepareAssessmentStructure,
  assessDesign
} from '@/services/assessmentService';
import { Question } from '@/types/Question';
import { saveBestDesignToSupabase } from '@/services/bestDesignService';
import { SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { User } from '@supabase/supabase-js';
import { useAssessmentAnalytics } from '@/hooks/useAnalytics';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface AssessmentHandlerProps {
  reactFlowInstance: ReactFlowInstance | null;
  userJourneys: string;
  assumptions: string;
  constraints: string;
  currentQuestion?: Question | null;
  onAssessmentComplete: (result: AssessmentResult | null) => void;
  setIsAssessing: (isAssessing: boolean) => void;
  // Additional props for best design functionality
  user?: User | null;
  questionId?: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  onBestDesignSaved?: () => void; // Callback to refresh best design status
  // Analytics functions
  startAssessment?: () => void;
  recordAssessment?: (
    questionId: string,
    contextType: 'question' | 'guided',
    contextId: string,
    score: number,
    nodeCount: number,
    edgeCount: number,
    componentsUsed: string[],
    isBestScore: boolean
  ) => Promise<void>;
}

// Regular utility function that returns an object with methods
const AssessmentHandler = ({
  reactFlowInstance,
  userJourneys,
  assumptions,
  constraints,
  currentQuestion,
  onAssessmentComplete,
  setIsAssessing,
  user,
  questionId,
  contextType = 'question',
  contextId,
  onBestDesignSaved,
  startAssessment,
  recordAssessment
}: AssessmentHandlerProps) => {

  // Helper function to save best design after assessment
  const saveBestDesignAfterAssessment = async (result: AssessmentResult, nodes: any[], edges: any[]) => {
    if (!user || !questionId || !result.score?.rating) return;

    try {
      // Create design object from current canvas state
      const design: SavedDesign = {
        questionId: questionId,
        nodes: nodes,
        edges: edges,
        userJourneys: userJourneys,
        assumptions: assumptions,
        constraints: constraints,
        lastModified: new Date().toISOString(),
        contextType: contextType,
        contextId: contextId || questionId
      };

      // Save as best design if score is better
      const saveResult = await saveBestDesignToSupabase(
        user.id,
        questionId,
        design,
        result.score.rating
      );

      if (saveResult.saved) {
        const message = saveResult.previousBest
          ? `🏆 New best score! ${result.score.rating}/10 (previous: ${saveResult.previousBest}/10)`
          : `🏆 First score saved! ${result.score.rating}/10`;
        toast.success(message);

        // Notify parent component that a new best design was saved
        if (onBestDesignSaved) {
          onBestDesignSaved();
        }
      }
    } catch (error) {
      console.error('Error saving best design:', error);
      // Don't show error toast to user as this is a background operation
    }
  };

  // Define the assessment handler as a regular async function
  const handleStartAssessment = async () => {
    debugLog("AssessmentHandler.handleStartAssessment called");

    if (!reactFlowInstance) {
      console.error("No reactFlowInstance available");
      toast.error("No diagram to assess. Please add components to the canvas first.");
      setIsAssessing(false);
      return;
    }

    // Get current flow data from React Flow
    const nodes = reactFlowInstance.getNodes();
    const edges = reactFlowInstance.getEdges();

    debugLog(`Found ${nodes.length} nodes and ${edges.length} edges`);

    if (nodes.length === 0) {
      console.error("No nodes found on canvas");
      toast.error("No components to assess. Please add components to the canvas first.");
      setIsAssessing(false);
      return;
    }

    // Start analytics tracking
    if (startAssessment) {
      debugLog("Starting analytics tracking");
      startAssessment();
    }

    // Prepare nested structure payload using the assessment service
    debugLog("Preparing assessment payload...");
    const { components, connections } = prepareAssessmentStructure(nodes, edges);

    // Prepare payload for assessment
    const payload = {
      components,
      connections,
      context: {
        CUJs: userJourneys,
        assumptions,
        constraints
      }
    };

    debugLog("Assessment payload prepared:", {
      componentsCount: components.length,
      connectionsCount: connections.length
    });

    // Note: setIsAssessing(true) is already called in the parent handler
    toast.info("Sending diagram for assessment...");

    try {
      debugLog("Calling assessDesign service...");
      // Use the assessment service to evaluate the design
      const result = await assessDesign(payload, currentQuestion);

      debugLog("Assessment completed successfully:", result);
      // Set the assessment result
      onAssessmentComplete(result);

      // Record assessment analytics and save best design if user is authenticated
      if (user && result && questionId && result.score?.rating) {
        const componentsUsed = nodes.map(node => node.data?.type || node.type).filter(Boolean) as string[];

        try {
          // Record assessment analytics
          if (recordAssessment) {
            await recordAssessment(
              questionId,
              contextType as 'question' | 'guided',
              contextId || questionId,
              result.score.rating,
              nodes.length,
              edges.length,
              componentsUsed,
              false // Will be updated if this becomes a best score
            );
          }
        } catch (analyticsError) {
          console.error('Error recording assessment analytics (non-blocking):', analyticsError);
        }

        try {
          // Save as best design
          await saveBestDesignAfterAssessment(result, nodes, edges);
        } catch (bestDesignError) {
          console.error('Error saving best design (non-blocking):', bestDesignError);
          // Don't let best design saving errors affect the main assessment flow
        }
      }

      toast.success("Assessment complete!");
    } catch (error: any) {
      console.error('Error during assessment:', error);
      toast.error("Assessment failed. Please try again.");

      // Reset assessment result to ensure UI is in a clean state
      onAssessmentComplete(null);

      // Fallback to mock response for demo purposes
      setTimeout(() => {
        const mockResult: AssessmentResult = {
          context: `The system design shows good understanding of core components and their interactions. However, there are areas that could be improved for better system resilience and performance.`,
          score: {
            rating: 7, // On a scale of 10
            scalability: 6, // On a scale of 10
            fault_tolerance: 5, // On a scale of 10
            clarity: 8 // On a scale of 10
          },
          validationResponse: {
            overall_assessment: "The system design shows good understanding of core components and their interactions. However, there are areas that could be improved for better system resilience and performance.",
            strengths: [
              "Good component organization",
              "Clear separation of concerns",
              "Appropriate use of databases"
            ],
            weaknesses: [
              "Limited fault tolerance mechanisms",
              "Potential bottlenecks in high traffic scenarios"
            ],
            interview_questions: [
              "How will the system handle sudden traffic spikes?",
              "What's the disaster recovery plan?"
            ],
            rating: 7,
            scoring_explanation: "The design scores well on clarity and organization but needs improvement in resilience and scalability aspects."
          }
        };

        onAssessmentComplete(mockResult);
        toast.success("Using fallback assessment (offline mode)");
      }, 1500);
    } finally {
      setIsAssessing(false);
    }
  };

  // Return an object with methods
  return {
    handleStartAssessment
  };
};

export default AssessmentHandler;
