import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock, Save, Trash2 } from 'lucide-react';
import { SavedDesign } from '@/contexts/DesignContext';

interface UnsavedChangesDialogProps {
  isOpen: boolean;
  onClose: () => void;
  unsavedDesign: SavedDesign | null;
  onRestore: (design: SavedDesign) => void;
  onDiscard: () => void;
  onSaveAndContinue: () => void;
}

const UnsavedChangesDialog: React.FC<UnsavedChangesDialogProps> = ({
  isOpen,
  onClose,
  unsavedDesign,
  onRestore,
  onDiscard,
  onSaveAndContinue
}) => {
  if (!unsavedDesign) return null;

  const formatLastModified = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMinutes < 1) return 'just now';
    if (diffMinutes < 60) return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`;
    if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
    if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
    return date.toLocaleDateString();
  };

  const getDesignSummary = () => {
    const nodeCount = unsavedDesign.nodes?.length || 0;
    const edgeCount = unsavedDesign.edges?.length || 0;
    const hasUserJourneys = unsavedDesign.userJourneys?.trim().length > 0;
    const hasAssumptions = unsavedDesign.assumptions?.trim().length > 0;
    const hasConstraints = unsavedDesign.constraints?.trim().length > 0;

    const parts = [];
    if (nodeCount > 0) parts.push(`${nodeCount} component${nodeCount !== 1 ? 's' : ''}`);
    if (edgeCount > 0) parts.push(`${edgeCount} connection${edgeCount !== 1 ? 's' : ''}`);
    if (hasUserJourneys) parts.push('user journeys');
    if (hasAssumptions) parts.push('assumptions');
    if (hasConstraints) parts.push('constraints');

    return parts.length > 0 ? parts.join(', ') : 'empty design';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <DialogTitle>Unsaved Changes Found</DialogTitle>
          </div>
          <DialogDescription className="space-y-3">
            <p>
              We found unsaved changes in your browser that haven't been synced to the cloud.
            </p>
            
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium text-amber-800">
                <Clock className="h-4 w-4" />
                Last modified: {formatLastModified(unsavedDesign.lastModified)}
              </div>
              
              <div className="text-sm text-amber-700">
                Contains: {getDesignSummary()}
              </div>
            </div>

            <p className="text-sm text-gray-600">
              Would you like to restore these changes or start fresh?
            </p>
          </DialogDescription>
        </DialogHeader>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => {
              onDiscard();
              onClose();
            }}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Discard Changes
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="default"
              onClick={() => {
                onRestore(unsavedDesign);
                onClose();
              }}
              className="flex items-center gap-2"
            >
              <Save className="h-4 w-4" />
              Restore Changes
            </Button>
            
            <Button
              variant="default"
              onClick={() => {
                onSaveAndContinue();
                onClose();
              }}
              className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4" />
              Save & Continue
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UnsavedChangesDialog;
