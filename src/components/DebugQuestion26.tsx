import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Debug component to test question 26 specifically
 */
const DebugQuestion26: React.FC = () => {
  const [question, setQuestion] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [updateResult, setUpdateResult] = useState<any>(null);

  const loadQuestion = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error: queryError } = await supabase
        .from('questions')
        .select('*')
        .eq('id', '26')
        .single();
      
      if (queryError) {
        setError(`Query error: ${queryError.message}`);
        return;
      }
      
      setQuestion(data);
      debugLog('Loaded question 26:', data);
      
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const updateQuestion = async () => {
    setLoading(true);
    setError(null);
    setUpdateResult(null);
    
    try {
      const updateData = {
        functional_requirements: [
          'Upload/download files (up to 10GB)',
          'Share files/folders with permissions',
          'Track edits with version history',
          'Sync changes across devices'
        ],
        non_functional_requirements: [
          'Upload/download latency < 1s',
          'Support 1B+ files per day',
          '99.99% data durability'
        ],
        constraints: [
          'Storage: Use a distributed file system (e.g., Google Colossus)',
          'Sync algorithm: Use rsync-like delta encoding',
          'Permissions: Enforce ACLs (access control lists)',
          'Deduplication: Store unique files once'
        ]
      };
      
      debugLog('Updating question 26 with:', updateData);
      
      const { data, error: updateError } = await supabase
        .from('questions')
        .update(updateData)
        .eq('id', '26')
        .select();
      
      if (updateError) {
        setError(`Update error: ${updateError.message}`);
        setUpdateResult({ success: false, error: updateError });
        return;
      }
      
      setUpdateResult({ success: true, data });
      debugLog('Update result:', data);
      
      // Reload the question
      await loadQuestion();
      
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(`Error: ${errorMsg}`);
      setUpdateResult({ success: false, error: errorMsg });
    } finally {
      setLoading(false);
    }
  };

  const checkColumns = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Try to query just the new columns
      const { data, error: queryError } = await supabase
        .from('questions')
        .select('id, functional_requirements, non_functional_requirements')
        .eq('id', '26')
        .single();
      
      if (queryError) {
        setError(`Column check error: ${queryError.message}`);
        return;
      }
      
      debugLog('Column check result:', data);
      alert(`Column check result: ${JSON.stringify(data, null, 2)}`);
      
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadQuestion();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Debug Question 26 (Google Drive)</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={loadQuestion} disabled={loading}>
              Reload Question
            </Button>
            <Button onClick={updateQuestion} disabled={loading}>
              Update with FR/NFR
            </Button>
            <Button onClick={checkColumns} disabled={loading}>
              Check Columns
            </Button>
          </div>
          
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
          
          {updateResult && (
            <div className={`p-3 border rounded-md ${updateResult.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <p className={`text-sm ${updateResult.success ? 'text-green-700' : 'text-red-700'}`}>
                Update {updateResult.success ? 'successful' : 'failed'}
              </p>
              <pre className="text-xs mt-2 overflow-auto">
                {JSON.stringify(updateResult, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {question && (
        <Card>
          <CardHeader>
            <CardTitle>Current Question Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold">Basic Info:</h4>
                <p>ID: {question.id}</p>
                <p>Title: {question.title}</p>
              </div>
              
              <div>
                <h4 className="font-semibold">Functional Requirements:</h4>
                <p>Type: {typeof question.functional_requirements}</p>
                <p>Length: {question.functional_requirements?.length || 0}</p>
                {question.functional_requirements && (
                  <ul className="list-disc pl-5">
                    {question.functional_requirements.map((req: string, i: number) => (
                      <li key={i}>{req}</li>
                    ))}
                  </ul>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold">Non-Functional Requirements:</h4>
                <p>Type: {typeof question.non_functional_requirements}</p>
                <p>Length: {question.non_functional_requirements?.length || 0}</p>
                {question.non_functional_requirements && (
                  <ul className="list-disc pl-5">
                    {question.non_functional_requirements.map((req: string, i: number) => (
                      <li key={i}>{req}</li>
                    ))}
                  </ul>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold">Constraints:</h4>
                <p>Length: {question.constraints?.length || 0}</p>
                {question.constraints && (
                  <ul className="list-disc pl-5">
                    {question.constraints.map((constraint: string, i: number) => (
                      <li key={i}>{constraint}</li>
                    ))}
                  </ul>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold">Raw Data:</h4>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(question, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DebugQuestion26;
