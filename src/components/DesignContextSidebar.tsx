import React, { useState } from 'react';
import { Question } from '@/types/Question';
import ProblemContext from './ProblemContext';
import ChatPanel from './ChatPanel';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileQuestion, FileText, Layers, MessageSquare } from 'lucide-react';
import { ReactFlowInstance } from '@xyflow/react';

interface DesignContextSidebarProps {
  userJourneys: string;
  assumptions: string;
  constraints: string;
  onUserJourneysChange: (value: string) => void;
  onAssumptionsChange: (value: string) => void;
  onConstraintsChange: (value: string) => void;
  currentQuestion?: Question | null;
  reactFlowInstance?: ReactFlowInstance | null;
  contextType?: string;
  contextId?: string;
  onActiveTabChange?: (tab: string) => void;
}

const DesignContextSidebar: React.FC<DesignContextSidebarProps> = ({
  userJourneys,
  assumptions,
  constraints,
  onUserJourneysChange,
  onAssumptionsChange,
  onConstraintsChange,
  currentQuestion,
  reactFlowInstance,
  contextType = 'free',
  contextId = 'free-canvas',
  onActiveTabChange
}) => {
  const [activeTab, setActiveTab] = useState<string>("problem");

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    onActiveTabChange?.(tab);
  };

  return (
    <div className="h-full bg-layrs-light px-4 pt-2 pb-4 overflow-hidden flex flex-col">
      <Tabs
        defaultValue="problem"
        value={activeTab}
        onValueChange={handleTabChange}
        className="flex flex-col h-full"
      >
        <TabsList className="w-full grid grid-cols-2 mb-2 bg-white border-gray-200">
          <TabsTrigger
            value="problem"
            className="flex items-center gap-2 text-gray-600 data-[state=active]:bg-orange-500/80 data-[state=active]:text-white"
          >
            <FileQuestion className="h-4 w-4" />
            Problem
          </TabsTrigger>
          <TabsTrigger
            value="chat"
            className="flex items-center gap-2 text-gray-600 data-[state=active]:bg-purple-700/80 data-[state=active]:text-white"
          >
            <MessageSquare className="h-4 w-4" />
            Chat
          </TabsTrigger>
        </TabsList>

        <TabsContent value="problem" className="mt-0 overflow-y-auto flex-grow">
          <ProblemContext question={currentQuestion} />
          <div className="mt-6">
            <label htmlFor="user-journeys" className="component-label">
              Key User Journeys
            </label>
            <textarea
              id="user-journeys"
              className="component-textarea h-32"
              value={userJourneys}
              onChange={(e) => onUserJourneysChange(e.target.value)}
              placeholder="Describe the main user journeys, e.g., 'User uploads a photo and shares it with friends'"
            />
          </div>
        </TabsContent>

        <TabsContent value="chat" className="mt-0 flex-grow h-full overflow-hidden">
          <div className="h-full flex flex-col">
            <ChatPanel
              reactFlowInstance={reactFlowInstance}
              contextType={contextType}
              contextId={contextId}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DesignContextSidebar;
