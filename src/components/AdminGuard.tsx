import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Shield, AlertTriangle, Home } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface AdminGuardProps {
  children: React.ReactNode;
}

// Define admin emails - in production, this should come from environment variables or database
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Add more admin emails here if needed
];

const AdminGuard: React.FC<AdminGuardProps> = ({ children }) => {
  const { user, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Check if user is admin
  const isAdmin = user.email && ADMIN_EMAILS.includes(user.email.toLowerCase());

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-600">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this admin area.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-gray-600">
              <p>This area is restricted to administrators only.</p>
              <p className="mt-2">
                Current user: <span className="font-medium">{user.email}</span>
              </p>
            </div>
            
            <div className="flex flex-col gap-2">
              <Button 
                onClick={() => window.location.href = '/home'} 
                className="w-full"
              >
                <Home className="mr-2 h-4 w-4" />
                Go to Home
              </Button>
              
              <Button 
                variant="outline" 
                onClick={() => window.history.back()}
                className="w-full"
              >
                Go Back
              </Button>
            </div>

            <div className="text-xs text-gray-500 text-center">
              If you believe this is an error, please contact support.
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // User is admin, render children
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin header indicator */}
      <div className="bg-blue-600 text-white px-4 py-2">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="text-sm font-medium">Admin Panel</span>
          </div>
          <div className="text-sm">
            Logged in as: {user.email}
          </div>
        </div>
      </div>
      
      {/* Admin content */}
      <div className="bg-white min-h-[calc(100vh-48px)]">
        {children}
      </div>
    </div>
  );
};

export default AdminGuard;
