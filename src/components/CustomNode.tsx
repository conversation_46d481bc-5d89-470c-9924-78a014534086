import React, { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeResizer } from '@xyflow/react';
import { Settings } from 'lucide-react';
import { componentTypes } from './ComponentTypes';

export interface CustomNodeData {
  type: string;
  label: string;
  className?: string;
  parentCompositeId?: string;
  isTutorialNode?: boolean; // Flag for tutorial nodes
  metadata?: {
    customLogic?: string;
    customProperties?: Array<{ key: string; value: string }>;
    [key: string]: any;
  };
}

interface CustomNodeProps {
  id: string;
  data: CustomNodeData;
  selected: boolean;
  isConnectable?: boolean;
  setNodes?: React.Dispatch<React.SetStateAction<any[]>>;
  className?: string; // Add className as a prop
  onAddProperties?: (nodeId: string) => void; // Callback for add properties button
}

const CustomNode: React.FC<CustomNodeProps> = memo(({ id, data, selected, isConnectable = true, setNodes, className, onAddProperties }) => {

  // Determine if this node is part of a composite
  const isCompositeChild = !!data.parentCompositeId;

  // Determine if this is a tutorial node
  const isTutorialNode = !!data.isTutorialNode;

  // Get the icon component for this node type
  const getIconComponent = () => {
    if (data.type && componentTypes[data.type as keyof typeof componentTypes]) {
      return componentTypes[data.type as keyof typeof componentTypes].iconComponent;
    }
    return null;
  };

  // Special style for tutorial nodes
  const nodeStyle: React.CSSProperties = isTutorialNode ? {
    width: '150px',
    height: '50px',
    position: 'relative',
    overflow: 'visible',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '10px',
    cursor: 'grab',
    pointerEvents: 'all'
  } : {
    position: 'relative',
    overflow: 'visible',
    width: '100%',
    height: '100%'
  };

  // Custom handle classes for tutorial nodes
  const getHandleClassName = (position: string) => {
    return `w-2 h-2 ${isTutorialNode ? 'tutorial-handle' : ''}`;
  };

  // Special styles for tutorial node handles to ensure they're clickable
  const getHandleStyle = (position: string) => {
    if (isTutorialNode) {
      const baseStyle = {
        pointerEvents: 'all',
        cursor: 'crosshair',
        zIndex: 1001
      };

      switch (position) {
        case 'top':
          return { ...baseStyle, top: '-5px' };
        case 'bottom':
          return { ...baseStyle, bottom: '-5px' };
        case 'left':
          return { ...baseStyle, left: '-5px' };
        case 'right':
          return { ...baseStyle, right: '-5px' };
        default:
          return baseStyle;
      }
    }
    return {};
  };

  return (
    <div
      className={`custom-node group relative px-4 py-2 shadow-md rounded-md ${data.className} ${
        selected ? 'border-2 border-layrs-primary' : 'border border-gray-200'
      } ${isCompositeChild ? 'bg-opacity-90' : ''}`}
      style={{
        position: 'relative',
        overflow: 'visible',
        width: '100%',
        height: '100%',
        cursor: 'pointer'
      }}
    >
      <NodeResizer
        minWidth={100}
        minHeight={50}
        isVisible={selected}
        lineClassName="border-layrs-primary"
        handleClassName="h-3 w-3 bg-white border-2 border-layrs-primary rounded"
        handleStyle={{ zIndex: 1000 }}
      />

      <div className="flex items-center justify-between" style={{ pointerEvents: 'none' }}>
        <div className="text-sm font-medium flex items-center gap-1" style={{ pointerEvents: 'none' }}>
          <div className="w-5 h-5 flex items-center justify-center" style={{ pointerEvents: 'none' }}>
            {getIconComponent()}
          </div>
          <span style={{ pointerEvents: 'none' }}>{data.label}</span>
        </div>

        {/* Add Properties Button */}
        {onAddProperties && (
          <button
            data-properties-button="true"
            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 rounded hover:bg-gray-100 flex items-center gap-1 text-xs text-gray-600 hover:text-gray-800"
            style={{ pointerEvents: 'auto' }}
            onClick={(e) => {
              e.stopPropagation(); // Prevent node selection
              onAddProperties(id);
            }}
            title="Add Properties"
          >
            <Settings size={12} />
            <span>Properties</span>
          </button>
        )}
      </div>


      {isCompositeChild && (
        <div className="absolute top-0 right-0 bg-gray-200 text-xs px-1 rounded-bl">
          Internal
        </div>
      )}

      {/* Handles that act as both source and target */}
      {/* Top handle - dual type with smaller size */}
      <Handle
        type="source"
        position={Position.Top}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="top"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3"
        id="top"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Bottom handle - dual type with smaller size */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="bottom"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Bottom}
        className="w-3 h-3"
        id="bottom"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Right handle - dual type with smaller size */}
      <Handle
        type="source"
        position={Position.Right}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="right"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Right}
        className="w-3 h-3"
        id="right"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />

      {/* Left handle - dual type with smaller size */}
      <Handle
        type="source"
        position={Position.Left}
        className="w-3 h-3 border-2 border-gray-400 bg-white hover:border-blue-500 hover:bg-blue-100 transition-colors"
        id="left"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1001 }}
      />
      <Handle
        type="target"
        position={Position.Left}
        className="w-3 h-3"
        id="left"
        isConnectable={isConnectable}
        style={{ pointerEvents: 'auto', zIndex: 1000, opacity: 0 }}
      />
    </div>
  );
});

export default CustomNode;
