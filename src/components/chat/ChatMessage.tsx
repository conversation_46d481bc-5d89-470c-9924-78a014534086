
import React from 'react';
import { cn } from '@/lib/utils';
import { Avatar } from '@/components/ui/avatar';

export type MessageType = 'user' | 'ai';

export interface ChatMessageProps {
  content: string;
  type: MessageType;
  timestamp?: Date;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  content,
  type,
  timestamp = new Date(),
}) => {
  const isUser = type === 'user';
  
  return (
    <div className={cn(
      "flex w-full mb-2 animate-fade-in",
      isUser ? "justify-end" : "justify-start"
    )}>
      <div className={cn(
        "flex max-w-[80%] gap-2",
        isUser ? "flex-row-reverse" : "flex-row"
      )}>
        <Avatar className={cn(
          "h-8 w-8",
          isUser ? "bg-blue-600" : "bg-purple-600"
        )}>
          <span className="text-xs font-bold text-white">
            {isUser ? 'U' : 'AI'}
          </span>
        </Avatar>
        
        <div className={cn(
          "rounded-lg px-4 py-2 text-sm",
          isUser ? "bg-blue-600 text-white" : "bg-gray-100 text-gray-800"
        )}>
          <p className="whitespace-pre-wrap">{content}</p>
          <div className={cn(
            "text-[10px] mt-1 opacity-70",
            isUser ? "text-blue-100" : "text-gray-500"
          )}>
            {timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
