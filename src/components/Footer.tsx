import React from 'react';
import { Link } from 'react-router-dom';
import { Github, Mail, Twitter } from 'lucide-react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="py-12 border-t border-white/10 bg-gradient-to-b from-[#1e1b4b] to-[#0f0a29] relative overflow-hidden">
      {/* Background glow effects */}
      <div className="absolute inset-0 bg-hero-glow opacity-50 z-0"></div>

      {/* Left blob */}
      <div className="absolute -left-64 top-0 w-[300px] h-[300px] rounded-full bg-blue-500/5 blur-[80px]"></div>

      {/* Right blob */}
      <div className="absolute -right-64 bottom-0 w-[300px] h-[300px] rounded-full bg-indigo-500/5 blur-[80px]"></div>

      <div className="container max-w-6xl mx-auto px-4 relative z-10">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-6 md:mb-0">
            {/* Logo with glow effect */}
            <div className="flex items-center relative group">
              {/* Logo glow */}
              <div className="absolute -inset-1 bg-gradient-to-r from-indigo-500 via-purple-500 to-blue-500 rounded-md blur-sm opacity-60 group-hover:opacity-80 transition duration-500"></div>
              {/* Logo container */}
              <div className="relative w-10 h-10 bg-gradient-to-br from-[#2e1065] to-[#1e1b4b] rounded-md flex items-center justify-center mr-3 border border-white/10">
                <span className="text-sm font-bold" style={{
                  background: 'linear-gradient(90deg, #4F9BFF 0%, #A78BFA 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>L</span>
              </div>
              <span className="text-xl font-bold relative z-10" style={{
                background: 'linear-gradient(90deg, #4F9BFF 0%, #A78BFA 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>Layrs</span>
            </div>
          </div>

          {/* Links */}
          <nav className="flex flex-wrap justify-center gap-6 mb-6 md:mb-0">
            <a href="#manifesto" className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors">
              Manifesto
            </a>
            <Link to="/privacy" className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors">
              Privacy
            </Link>
            <Link to="/terms" className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors">
              Terms
            </Link>
          </nav>

          {/* Social Links */}
          <div className="flex items-center space-x-4">
            <a
              href="#"
              className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors"
              aria-label="Twitter"
            >
              <Twitter size={18} />
            </a>
            <a
              href="#"
              className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors"
              aria-label="GitHub"
            >
              <Github size={18} />
            </a>
            <a
              href="mailto:<EMAIL>"
              className="text-[#CBD5E1] hover:text-[#A78BFA] transition-colors"
              aria-label="Email"
            >
              <Mail size={18} />
            </a>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-white/10 text-center text-[#CBD5E1] text-sm">
          <p>© {currentYear} Layrs. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
