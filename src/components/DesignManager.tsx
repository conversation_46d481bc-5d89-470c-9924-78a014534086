import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDesign, SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { useQuestions } from '@/contexts/QuestionsContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { PenTool, Trash2, Clock, ArrowRight, Plus } from 'lucide-react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface DesignManagerProps {
  questionId: string;
  contextType?: DesignContextCategory;
  contextId?: string;
  onDesignSelected?: () => void;
}

const DesignManager: React.FC<DesignManagerProps> = ({
  questionId,
  contextType = 'question',
  contextId,
  onDesignSelected
}) => {
  const navigate = useNavigate();
  const { loadDesign, saveDesign, deleteDesign, setCurrentDesign } = useDesign();
  const { getQuestionById } = useQuestions();
  const [savedDesign, setSavedDesign] = useState<SavedDesign | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Get question and handle Promise properly
  const [question, setQuestion] = useState<any>(null);

  useEffect(() => {
    const fetchQuestion = async () => {
      try {
        const fetchedQuestion = await getQuestionById(questionId);
        setQuestion(fetchedQuestion);
      } catch (error) {
        console.error('Error fetching question:', error);
      }
    };

    fetchQuestion();
  }, [questionId, getQuestionById]);

  useEffect(() => {
    // Check if there's a saved design for this question/context
    const fetchDesign = async () => {
      try {
        const id = contextId || questionId;
        debugLog(`DesignManager: Loading design for ${contextType} ${id}`);
        const design = await loadDesign(questionId, contextType, contextId);
        setSavedDesign(design);
      } catch (error) {
        console.error('Error loading design:', error);
      }
    };

    // Only fetch the design once when the component mounts or context changes
    fetchDesign();
  }, [questionId, contextType, contextId, loadDesign]);

  const handleCreateNewDesign = async () => {
    try {
      const id = contextId || questionId;
      // Create a new design for this question/context
      const newDesign: Partial<SavedDesign> = {
        questionId,
        nodes: [],
        edges: [],
        userJourneys: '',
        assumptions: '',
        constraints: question?.constraints?.join('\n\n') || '',
        contextType,
        contextId: id
      };

      // Save the design - this will also update the cache
      await saveDesign(questionId, newDesign, contextType, contextId);

      // Set the current design directly from what we just saved
      // No need to load it again from the database
      setCurrentDesign({
        ...newDesign,
        lastModified: new Date().toISOString()
      } as SavedDesign);

      toast.success('New design created');

      if (onDesignSelected) {
        onDesignSelected();
      }

      // Navigate to the appropriate route based on context type
      if (contextType === 'question') {
        navigate(`/design/${questionId}`);
      } else if (contextType === 'course') {
        navigate(`/guided/${contextId || questionId}`);
      } else {
        // Default to free canvas
        navigate('/home');
      }
    } catch (error) {
      console.error('Error creating design:', error);
      toast.error('Failed to create design');
    }
  };

  const handleLoadDesign = () => {
    if (savedDesign) {
      setCurrentDesign(savedDesign);
      toast.success('Design loaded');

      if (onDesignSelected) {
        onDesignSelected();
      }

      // Navigate to the appropriate route based on context type
      if (contextType === 'question') {
        navigate(`/design/${questionId}`);
      } else if (contextType === 'course') {
        navigate(`/guided/${contextId || questionId}`);
      } else {
        // Default to free canvas
        navigate('/home');
      }
    }
  };

  const handleDeleteDesign = async () => {
    if (savedDesign) {
      try {
        await deleteDesign(questionId, contextType, contextId);
        setSavedDesign(null);
        toast.success('Design deleted');
        setIsDeleteDialogOpen(false);
      } catch (error) {
        console.error('Error deleting design:', error);
        toast.error('Failed to delete design');
      }
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString();
    } catch (e) {
      return 'Unknown date';
    }
  };

  return (
    <div>
      {savedDesign ? (
        <Card className="mb-4 bg-white border-gray-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2 text-gray-900 font-semibold">
              <PenTool className="h-4 w-4 text-purple-600" />
              Saved Design
            </CardTitle>
            <CardDescription className="text-gray-700">
              Continue working on your existing design
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-600 flex items-center mb-2">
              <Clock className="h-3 w-3 mr-1" />
              Last modified: {formatDate(savedDesign.lastModified)}
            </div>
            <div className="text-sm text-gray-900">
              <span className="font-semibold">Components:</span> {savedDesign.nodes.length}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
              <AlertDialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="bg-white border-gray-200">
                <AlertDialogHeader>
                  <AlertDialogTitle className="text-gray-900">Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription className="text-gray-700">
                    This will permanently delete your saved design for this question.
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>
                    Cancel
                  </AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteDesign}
                    className="bg-gradient-to-r from-red-500/80 to-red-600/80 hover:from-red-600/90 hover:to-red-700/90 text-white border-none"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <Button
              onClick={handleLoadDesign}
              className="gap-1 bg-gradient-to-r from-blue-500/80 to-indigo-600/80 hover:from-blue-600/90 hover:to-indigo-700/90 text-white border-none backdrop-blur-sm"
            >
              Continue Design
              <ArrowRight className="h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <Card className="mb-4 bg-white border-gray-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center gap-2 text-gray-900 font-semibold">
              <Plus className="h-4 w-4 text-purple-600" />
              Start New Design
            </CardTitle>
            <CardDescription className="text-gray-700">
              Create a new design for this question
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              You haven't created a design for this question yet.
              Start a new design to begin working on your solution.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleCreateNewDesign}
              className="w-full gap-1 bg-gradient-to-r from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white border-none backdrop-blur-sm"
            >
              Create New Design
              <ArrowRight className="h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
      )}

      {savedDesign && (
        <Dialog>
          <DialogTrigger asChild>
            <Button
              variant="outline"
              className="w-full gap-1"
            >
              <Plus className="h-4 w-4" />
              Create New Design
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white border-gray-200">
            <DialogHeader>
              <DialogTitle className="text-gray-900">Create New Design</DialogTitle>
              <DialogDescription className="text-gray-700">
                You already have a saved design for this question.
                Creating a new design will not delete your existing design.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                onClick={handleCreateNewDesign}
                className="bg-gradient-to-r from-green-500/80 to-emerald-600/80 hover:from-green-600/90 hover:to-emerald-700/90 text-white border-none backdrop-blur-sm"
              >
                Create New Design
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default DesignManager;
