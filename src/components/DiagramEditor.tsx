import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  Node,
  Edge,
  ReactFlowInstance,
  OnConnectStart,
  OnConnectStartParams,
  HandleType,
  Connection,
  ConnectionMode,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import '../styles/edges.css';
import { nodeTypes, getDefaultEdgeOptions, edgeTypes } from './DiagramEditorConstants';
import { useNodesHandlers } from '@/hooks/useNodesHandlers';
import { useConnectionValidation } from '@/hooks/useConnectionValidation';
import { useEdgeHandlers } from '@/hooks/useEdgeHandlers';
import { useDragHandlers } from '@/hooks/useDragHandlers';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import { useClipboard } from '@/hooks/useClipboard';
import { useSimpleUndo } from '@/hooks/useSimpleUndo';
import { processLoadedEdges } from '@/utils/edgeProcessing';
import { useDesign } from '@/contexts/DesignContext';
import { useCourse } from '@/contexts/CourseContext';
import { useCourseStepValidation } from '@/hooks/useCourseStepValidation';
import { useEdgeType } from '@/contexts/EdgeTypeContext';
import EdgeTypeSelector from './EdgeTypeSelector';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';


interface DiagramEditorProps {
  onNodeSelect: (node: Node | null, openRightPanel?: boolean) => void;
  setReactFlowInstance: (instance: ReactFlowInstance) => void;
  modifiedNodesRef?: React.RefObject<Set<string>>;
  deletedNodesRef?: React.RefObject<Set<string>>;
  onSetResetting?: (setResetting: (resetting: boolean) => void) => void;
  selectedComponent?: Node | null;
  onCanvasChange?: (changeType: 'canvas' | 'major') => void;
  sidebarUserInitiatedDeletion?: React.MutableRefObject<boolean>; // <-- add this prop
}

const DiagramEditor: React.FC<DiagramEditorProps> = ({
  onNodeSelect,
  setReactFlowInstance,
  modifiedNodesRef,
  deletedNodesRef,
  onSetResetting,
  selectedComponent,
  onCanvasChange,
  sidebarUserInitiatedDeletion // <-- add this prop
}) => {
  // Create local tracking refs if not provided by parent
  const localModifiedNodesRef = useRef<Set<string>>(new Set());
  const localDeletedNodesRef = useRef<Set<string>>(new Set());

  // Use provided refs or fall back to local refs
  const trackModifiedNodes = modifiedNodesRef || localModifiedNodesRef;
  const trackDeletedNodes = deletedNodesRef || localDeletedNodesRef;
  // Use a ref to access the instance in the onDrop callback
  const reactFlowInstance = useRef<ReactFlowInstance | null>(null);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  
  // Simple debug for edge clearing only
  const debugEdgeClearing = useCallback((newEdges: any, source: string) => {
    const edgeCount = Array.isArray(newEdges) ? newEdges.length : 0;
    const currentCount = edges.length;
    
    if (edgeCount === 0 && currentCount > 0) {
      debugLog(`🚨 EDGES CLEARED by ${source}! ${currentCount} → 0`, {
        oldEdges: edges.map(e => e.id)
      });
    }
  }, [edges]);
  const [connectionInfo, setConnectionInfo] = useState<{
    nodeId: string | null;
    handleType: HandleType | null;
    handlePosition: string | null;
  }>({
    nodeId: null,
    handleType: null,
    handlePosition: null
  });

  // Get course context (for course step validation hook)
  useCourse();

  // Use course step validation hook
  useCourseStepValidation(nodes, edges);

  // Track the last time we marked changes as unsaved
  const lastUnsavedChangeTimeRef = useRef<number>(0);
  // Debounce interval for marking changes as unsaved (in milliseconds)
  const UNSAVED_CHANGES_DEBOUNCE = 500;

  // Get design context
  const { setHasUnsavedChanges, currentDesign } = useDesign();

  // Get selected edge type
  const { getEdgeType } = useEdgeType();

  // Initialize clipboard functionality
  const { copyToClipboard, pasteFromClipboard } = useClipboard();

  // Initialize simple undo functionality
  const {
    trackNodeAdded,
    trackEdgeAdded,
    trackMultipleAdded,
    trackMultipleDeleted,
    trackItemsDeleted,
    undo,
    redo
  } = useSimpleUndo();

  // Get selected nodes and edges for keyboard shortcuts
  const selectedNodes = nodes.filter(node => node.selected);
  const selectedEdges = edges.filter(edge => edge.selected);

  // Keyboard shortcut handlers
  const handleCopy = useCallback((nodesToCopy: Node[], edgesToCopy: Edge[]) => {
    // Copy doesn't need undo tracking since it doesn't modify the canvas
    copyToClipboard(nodesToCopy, edgesToCopy);
  }, [copyToClipboard]);

  const handlePaste = useCallback(() => {
    // Get current state before paste
    const currentNodeCount = nodes.length;
    const currentEdgeCount = edges.length;

    // Perform paste
    pasteFromClipboard(nodes, edges, (newNodes) => {
      // Track newly added nodes
      const addedNodeIds = newNodes.slice(currentNodeCount).map(n => n.id);
      if (addedNodeIds.length > 0) {
        trackMultipleAdded(addedNodeIds, [], 'Paste components');
      }
      setNodes(newNodes);
    }, (newEdges) => {
      // Track newly added edges
      const addedEdgeIds = newEdges.slice(currentEdgeCount).map(e => e.id);
      if (addedEdgeIds.length > 0) {
        trackMultipleAdded([], addedEdgeIds, 'Paste connections');
      }
      setEdges(newEdges);
    });

    // Trigger autosave for major change (paste)
    onCanvasChange?.('major');
  }, [pasteFromClipboard, nodes, edges, setNodes, setEdges, trackMultipleAdded, onCanvasChange]);

  const handleUndo = useCallback(() => {
    debugLog('⏳ Temporarily disabling auto-save for undo operation');
    // Temporarily disable auto-save during undo to prevent race conditions
    (window as any).__autoSaveDisabled = true;
    
    undo(nodes, edges, setNodes, setEdges);
    
    // Re-enable auto-save after undo completes
    setTimeout(() => {
      debugLog('✅ Re-enabling auto-save after undo operation');
      (window as any).__autoSaveDisabled = false;
    }, 500);
  }, [undo, nodes, edges, setNodes, setEdges]);

  const handleRedo = useCallback(() => {
    debugLog('⏳ Temporarily disabling auto-save for redo operation');
    // Temporarily disable auto-save during redo to prevent race conditions
    (window as any).__autoSaveDisabled = true;
    
    redo(nodes, edges, setNodes, setEdges);
    
    // Re-enable auto-save after redo completes
    setTimeout(() => {
      debugLog('✅ Re-enabling auto-save after redo operation');
      (window as any).__autoSaveDisabled = false;
    }, 500);
  }, [redo, nodes, edges, setNodes, setEdges]);

  // Track user-initiated deletions to prevent automatic/internal React Flow deletions
  const lastUserActionTime = useRef(0);

  // Use custom hooks to handle different aspects of functionality
  const { handleNodesChange: originalHandleNodesChange, findCompositeAtPoint, ensureProperNodeOrder, setResetting, isResetting } = useNodesHandlers({
    nodes,
    setNodes,
    onNodesChange,
    modifiedNodesRef: trackModifiedNodes,
    deletedNodesRef: trackDeletedNodes
  });

  // Refactored: Pass userInitiated as an argument
  const handleNodesChange = useCallback((changes: any[], userInitiated = false) => {
    // Check for sidebar-initiated deletion
    let effectiveUserInitiated = userInitiated;
    if (sidebarUserInitiatedDeletion && sidebarUserInitiatedDeletion.current) {
      effectiveUserInitiated = true;
      sidebarUserInitiatedDeletion.current = false;
    }
    const deletionChanges = changes.filter(change => change.type === 'remove');
    const additionChanges = changes.filter(change => change.type === 'add');
    const positionChanges = changes.filter(change => change.type === 'position');

    if (deletionChanges.length > 0) {
      debugLog('🔍 Detected node deletions in handleNodesChange:', deletionChanges);
      debugLog('🔍 User initiated deletion:', effectiveUserInitiated);
      debugLog('🔍 Is resetting:', isResetting.current);
      debugLog('🔍 Time since last user action:', Date.now() - lastUserActionTime.current, 'ms');

      const isResettingOperation = isResetting.current;

      if (!effectiveUserInitiated && !isResettingOperation) {
        debugLog('🚫 Skipping node deletions - not user initiated and not resetting (likely React Flow internal)');
        return;
      }

      // Find the actual node objects that are being deleted
      const nodesToDelete = deletionChanges
        .map(change => nodes.find(node => node.id === change.id))
        .filter(node => node !== undefined);

      if (nodesToDelete.length > 0) {
        debugLog('📦 Tracking deleted nodes for undo:', nodesToDelete.map(n => n.id));

        // Clear selected component if it's being deleted
        if (selectedComponent && nodesToDelete.some(node => node.id === selectedComponent.id)) {
          onNodeSelect(null);
        }

        // Track the deletions for undo
        trackItemsDeleted(nodesToDelete, [], `Delete ${nodesToDelete.length} nodes via React Flow`);

        // Trigger autosave for major change (deletion)
        onCanvasChange?.('major');
      }
    }

    if (additionChanges.length > 0) {
      onCanvasChange?.('major');
    }

    if (positionChanges.length > 0) {
      onCanvasChange?.('canvas');
    }

    originalHandleNodesChange(changes);
  }, [originalHandleNodesChange, nodes, trackItemsDeleted, onNodeSelect, selectedComponent, onCanvasChange, isResetting, sidebarUserInitiatedDeletion]);

  // Expose setResetting function to parent component
  useEffect(() => {
    if (onSetResetting) {
      onSetResetting(setResetting);
    }
  }, [onSetResetting, setResetting]);

  const { isValidConnection } = useConnectionValidation({ nodes });

  // Wrap onEdgesChange to trigger autosave for edge changes and prevent automatic deletions
  const handleEdgesChange = useCallback((changes: any[], userInitiated = false) => {
    const deletionChanges = changes.filter(change => change.type === 'remove');
    const additionChanges = changes.filter(change => change.type === 'add');
    const otherChanges = changes.filter(change => change.type !== 'remove' && change.type !== 'add');

    // Only process edge deletions if they are user-initiated OR during reset operations
    if (deletionChanges.length > 0) {
      debugLog('🔍 Detected edge deletions in handleEdgesChange:', deletionChanges);
      debugLog('🔍 User initiated deletion:', userInitiated);
      debugLog('🔍 Is resetting:', isResetting.current);
      debugLog('🔍 Time since last user action:', Date.now() - lastUserActionTime.current, 'ms');

      const isResettingOperation = isResetting.current;
      const timeSinceUserAction = Date.now() - lastUserActionTime.current;

      // Only process deletions if:
      // 1. User initiated, OR
      // 2. This is a reset operation, OR  
      // 3. Very recent user action (within 1 second) - for immediate feedback
      if (!userInitiated && !isResettingOperation && timeSinceUserAction > 1000) {
        debugLog('🚫 Skipping edge deletions - not user initiated and not recent user action');
        // Don't call onEdgesChange for deletions to prevent React Flow from removing edges
        // Only process non-deletion changes
        const nonDeletionChanges = changes.filter(change => change.type !== 'remove');
        if (nonDeletionChanges.length > 0) {
          onEdgesChange(nonDeletionChanges);
        }
        return;
      }

      // Find the actual edge objects that are being deleted BEFORE removing them from state
      debugLog('🔍 Looking for edges to delete. Current edges:', edges.map(e => e.id));
      debugLog('🔍 Deletion changes:', deletionChanges);
      
      const edgesToDelete = deletionChanges
        .map(change => {
          const foundEdge = edges.find(edge => edge.id === change.id);
          debugLog(`🔍 Looking for edge ${change.id}: ${foundEdge ? 'FOUND' : 'NOT FOUND'}`);
          return foundEdge;
        })
        .filter(edge => edge !== undefined);

      debugLog('🔍 Edges to delete:', edgesToDelete.length, edgesToDelete.map(e => e.id));

      // Remove deleted edges from state (always do this even if we can't find the edge objects)
      setEdges(eds => {
        const edgesBeforeFilter = eds.length;
        const filteredEdges = eds.filter(edge => !deletionChanges.some(change => change.id === edge.id));
        const edgesAfterFilter = filteredEdges.length;
        debugLog(`🔄 Edge removal: ${edgesBeforeFilter} → ${edgesAfterFilter} (removed ${edgesBeforeFilter - edgesAfterFilter})`);
        
        if (edgesBeforeFilter === edgesAfterFilter) {
          debugLog('⚠️ No edges were actually removed from state - ID mismatch?');
          debugLog('Edges in state:', eds.map(e => e.id));
          debugLog('IDs to remove:', deletionChanges.map(c => c.id));
        }
        
        return filteredEdges;
      });

      // Only track for undo if we found the actual edge objects AND they haven't been tracked elsewhere
      if (edgesToDelete.length > 0) {
        debugLog('📦 Tracking deleted edges for undo:', edgesToDelete.map(e => e.id));
        debugLog('🔄 Triggering auto-save for edge deletion');
        trackItemsDeleted([], edgesToDelete, `Delete ${edgesToDelete.length} edges via React Flow`);
      } else {
        debugLog('❌ No edges to delete found in handleEdgesChange - edge removal handled but tracking assumed done elsewhere');
      }

      onCanvasChange?.('major');
    }

    if (additionChanges.length > 0) {
      onCanvasChange?.('major');
    }

    if (otherChanges.length > 0) {
      onCanvasChange?.('canvas');
    }

    onEdgesChange(changes);
  }, [onEdgesChange, onCanvasChange, isResetting, edges, trackItemsDeleted, setEdges, lastUserActionTime]);

  const {
    onConnect: originalOnConnect,
    onEdgeClick
  } = useEdgeHandlers({
    nodes,
    edges,
    setEdges,
    isValidConnection,
    connectionInfo,
    trackItemsDeleted,
    handleEdgesChange
  });

  // Wrap the onConnect callback to support tutorial functionality and track connections
  const onConnect = useCallback((params: Connection) => {
    // First, check if there's a tutorial connection handler
    if ((window as any).__tutorialConnectHandler) {
      debugLog("Calling tutorial connection handler");
      (window as any).__tutorialConnectHandler(params);
    }

    // Call the original onConnect handler for normal connection handling
    originalOnConnect(params);

    // Track the new edge for undo (we'll generate an ID similar to how React Flow does it)
    const edgeId = `edge-${params.source}-${params.target}`;
    trackEdgeAdded(edgeId, 'Add connection');

    // Trigger autosave for major change (new connection)
    onCanvasChange?.('major');
  }, [originalOnConnect, trackEdgeAdded, onCanvasChange]);

  // New handleDelete that collects all items and tracks as single operation
  const handleDelete = useCallback(() => {
    debugLog('🗑️ handleDelete called!', {
      selectedNodes: selectedNodes.length,
      selectedEdges: selectedEdges.length,
      selectedNodeIds: selectedNodes.map(n => n.id),
      selectedEdgeIds: selectedEdges.map(e => e.id)
    });

    lastUserActionTime.current = Date.now();
    debugLog('✅ Marking deletion as user-initiated via handleDelete');

    const nodesToDelete = selectedNodes.filter(node =>
      node.deletable !== false && !node.data?.isTutorialNode
    );
    const selectedEdgesToDelete = selectedEdges;

    // Find edges connected to deleted nodes
    const nodeIdsToDelete = new Set(nodesToDelete.map(n => n.id));
    const connectedEdges = edges.filter(edge => 
      nodeIdsToDelete.has(edge.source) || nodeIdsToDelete.has(edge.target)
    );

    // Combine all edges to delete (selected + connected), avoiding duplicates
    const allEdgesToDelete = [...selectedEdgesToDelete];
    connectedEdges.forEach(edge => {
      if (!allEdgesToDelete.some(e => e.id === edge.id)) {
        allEdgesToDelete.push(edge);
      }
    });

    debugLog('🔄 Collected items for deletion:', {
      nodes: nodesToDelete.map(n => n.id),
      edges: allEdgesToDelete.map(e => e.id)
    });

    // Track all deletions as a single compound operation
    if (nodesToDelete.length > 0 || allEdgesToDelete.length > 0) {
      const description = nodesToDelete.length > 0 
        ? `Delete ${nodesToDelete.length} node(s) and ${allEdgesToDelete.length} edge(s)`
        : `Delete ${allEdgesToDelete.length} edge(s)`;
      
      trackMultipleDeleted(nodesToDelete, allEdgesToDelete, description);
    }

    // Remove items from state - bypass handleNodesChange/handleEdgesChange to prevent duplicate tracking
    if (nodesToDelete.length > 0) {
      setNodes(nds => nds.filter(node => !nodeIdsToDelete.has(node.id)));
    }
    if (allEdgesToDelete.length > 0) {
      const edgeIdsToDelete = new Set(allEdgesToDelete.map(e => e.id));
      setEdges(eds => eds.filter(edge => !edgeIdsToDelete.has(edge.id)));
    }

    // Trigger auto-save
    onCanvasChange?.('major');
  }, [selectedNodes, selectedEdges, edges, trackMultipleDeleted, setNodes, setEdges, onCanvasChange]);

  const { onNodeDragStart: originalOnNodeDragStart, onNodeDragStop: originalOnNodeDragStop, onDragOver, onDrop: originalOnDrop } = useDragHandlers({
    nodes,
    setNodes,
    reactFlowRef: reactFlowWrapper,
    reactFlowInstance,
    findCompositeAtPoint,
    ensureProperNodeOrder
  });

  // Use drag handlers directly (drag operations are complex to undo, so we'll skip them for now)
  const onNodeDragStart = originalOnNodeDragStart;
  const onNodeDragStop = originalOnNodeDragStop;

  // Wrap the drop handler to trigger autosave
  const onDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    // Call the original drop handler
    originalOnDrop(event);

    // Trigger autosave for major change (new component dropped)
    onCanvasChange?.('major');
  }, [originalOnDrop, onCanvasChange]);

  // Track when nodes are added by monitoring nodes array changes (for undo functionality only)
  const previousNodeCount = useRef(nodes.length);
  const previousNodeIds = useRef(new Set(nodes.map(n => n.id)));

  useEffect(() => {
    const currentNodeCount = nodes.length;
    const currentNodeIds = new Set(nodes.map(n => n.id));

    // Check if new nodes were added (only track for undo, don't trigger autosave here)
    if (currentNodeCount > previousNodeCount.current) {
      const newNodes = nodes.filter(node => !previousNodeIds.current.has(node.id));
      newNodes.forEach(node => {
        trackNodeAdded(node.id, `Add ${node.data?.label || node.type || 'component'}`);
      });
    }

    // Update refs for next comparison
    previousNodeCount.current = currentNodeCount;
    previousNodeIds.current = currentNodeIds;
  }, [nodes, trackNodeAdded]);

  // Update keyboard shortcuts to use the new handleDelete
  useKeyboardShortcuts({
    selectedNodes,
    selectedEdges,
    onCopy: handleCopy,
    onPaste: handlePaste,
    onUndo: handleUndo,
    onRedo: handleRedo,
    onDelete: () => handleDelete() // always user-initiated
  });

  // Update customKeyboardEvents to use the new handleDelete
  const customKeyboardEvents = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Delete' || event.key === 'Backspace') {
      debugLog('⌨️ Delete key pressed!', {
        key: event.key,
        target: (event.target as HTMLElement).tagName
      });

      const target = event.target as HTMLElement;
      const isEditableTarget =
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.getAttribute('contenteditable') === 'true' ||
        target.closest('.react-flow__node input') !== null ||
        target.closest('[data-editing="true"]') !== null;

      if (isEditableTarget) {
        debugLog('🚫 Skipping delete - user is editing text');
        return;
      }

      debugLog('✅ User-initiated delete via keyboard - marking as user action');
      lastUserActionTime.current = Date.now();
      handleDelete();
    }
  }, [handleDelete]);

  // Track user actions that should allow deletions
  const trackUserAction = useCallback(() => {
    lastUserActionTime.current = Date.now();
  }, []);

  // Set up event listeners to track legitimate user interactions
  useEffect(() => {
    // Track mouse clicks as user actions
    const handleMouseDown = () => trackUserAction();

    // Track keyboard interactions as user actions (but not delete keys, those are handled separately)
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key !== 'Delete' && event.key !== 'Backspace') {
        trackUserAction();
      }
    };

    document.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [trackUserAction]);

  // Set up keyboard event handlers (like Delete key)
  useEffect(() => {
    document.addEventListener('keydown', customKeyboardEvents);
    return () => {
      document.removeEventListener('keydown', customKeyboardEvents);
    };
  }, [customKeyboardEvents]);

  // Clear selected edge when clicking on the canvas
  const onPaneClick = useCallback(() => {
    onNodeSelect(null);  // Clear selected node as well
  }, [onNodeSelect]);

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    debugLog('🎯 Node clicked:', node.id, 'at coordinates:', event.clientX, event.clientY);
    debugLog('🎯 Click target:', (event.target as HTMLElement)?.tagName, (event.target as HTMLElement)?.className);

    // Check if the click was on the "Add Properties" button
    const target = event.target as HTMLElement;
    const isPropertiesButton = target.closest('[data-properties-button]') !== null;

    if (!isPropertiesButton) {
      onNodeSelect(node);
      // Deselect all edges when a node is selected
      setEdges(eds => eds.map(e => ({ ...e, selected: false })));
    }
  }, [onNodeSelect, setEdges]);

  // Handle add properties button click
  const handleAddProperties = useCallback((nodeId: string) => {
    debugLog('🎯 Add Properties clicked for node:', nodeId);
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      // Pass true as second parameter to indicate right panel should open
      onNodeSelect(node, true);
    }
  }, [nodes, onNodeSelect]);

  // When we initialize ReactFlow, make sure it's globally available
  const onInit = useCallback((instance: ReactFlowInstance) => {
    debugLog('ReactFlow initialized');
    reactFlowInstance.current = instance;
    setReactFlowInstance(instance);

    // Don't set default viewport immediately - let the design loading handle it
    setTimeout(() => {
      // If there are tutorial nodes, make sure they're positioned in view
      const tutorialNodes = instance.getNodes().filter(n => n.id.startsWith('tutorial-'));
      if (tutorialNodes.length > 0) {
        debugLog('Found tutorial nodes, fitting view to them');
        instance.fitView({
          padding: 0.2,
          includeHiddenNodes: true,
          nodes: tutorialNodes
        });
      } else {
        // Only set default viewport if no nodes are present (empty canvas)
        const allNodes = instance.getNodes();
        if (allNodes.length === 0) {
          instance.setViewport({ x: 0, y: 0, zoom: 0.85 });
        }
      }
    }, 500);
  }, [setReactFlowInstance]);

  // Custom handler to mark changes as unsaved with debouncing
  const markChangesAsUnsaved = useCallback(() => {
    const now = Date.now();

    // Only update if we haven't updated recently (debounce)
    if (now - lastUnsavedChangeTimeRef.current > UNSAVED_CHANGES_DEBOUNCE) {
      if (nodes.length > 0 || edges.length > 0) {
        debugLog('Marking changes as unsaved (debounced)');
        setHasUnsavedChanges(true);
        lastUnsavedChangeTimeRef.current = now;
      }
    }
  }, [nodes, edges, setHasUnsavedChanges, UNSAVED_CHANGES_DEBOUNCE]);

  // Effect to mark changes as unsaved when nodes or edges change
  useEffect(() => {
    // Skip immediate changes during initial load
    if (currentDesign?.questionId) {
      // Use setTimeout to debounce the changes
      const timer = setTimeout(() => {
        markChangesAsUnsaved();
      }, UNSAVED_CHANGES_DEBOUNCE);

      return () => clearTimeout(timer);
    }
  }, [nodes, edges, markChangesAsUnsaved, currentDesign?.questionId, UNSAVED_CHANGES_DEBOUNCE]);

  // Effect to load nodes and edges from currentDesign - simplified with minimized loading strategy
  useEffect(() => {
    if (currentDesign && currentDesign.nodes) {
      debugLog('Design loaded, setting nodes and edges directly');

      // With our new minimized loading strategy, we don't need complex merging logic
      // We're only loading the design once when the question is opened
      setNodes(currentDesign.nodes);

      // Process loaded edges to add the onChange handler for label editing
      const processedEdges = processLoadedEdges(currentDesign.edges || [], setEdges, currentDesign.nodes);
      debugEdgeClearing(processedEdges, 'design-load');
      setEdges(processedEdges);

      // Auto-fit view when design with nodes is loaded
      if (currentDesign.nodes.length > 0) {
        // Use a longer delay to ensure all nodes are fully rendered
        setTimeout(() => {
          if (reactFlowInstance.current) {
            debugLog('Auto-fitting view for loaded design with nodes');
            reactFlowInstance.current.fitView({
              padding: 0.1,
              duration: 500,
              includeHiddenNodes: false,
              minZoom: 0.1,
              maxZoom: 1.5
            });
          }
        }, 500);
      }
    } else if (currentDesign === null) {
      // When currentDesign is explicitly null (no design found), clear the canvas
      debugLog('No design found, clearing DiagramEditor canvas');
      setNodes([]);
      debugEdgeClearing([], 'currentDesign=null');
      setEdges([]);
    }
  }, [currentDesign]); // Only depend on currentDesign to prevent infinite loops

  // Additional effect to ensure fitView happens when nodes are actually rendered
  useEffect(() => {
    if (nodes.length > 0 && reactFlowInstance.current && currentDesign) {
      // Wait for nodes to be fully rendered in the DOM
      const timer = setTimeout(() => {
        debugLog('Ensuring fitView after nodes are rendered');
        reactFlowInstance.current?.fitView({
          padding: 0.15,
          duration: 400,
          includeHiddenNodes: false,
          minZoom: 0.1,
          maxZoom: 1.2
        });
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [nodes.length, currentDesign]); // Trigger when nodes count changes and we have a design

  // Track connection start to store handle information
  const onConnectStart: OnConnectStart = useCallback(
    (_event, params: OnConnectStartParams) => {
      debugLog(`🔗 Connection starting from: { nodeId: '${params.nodeId}', handleType: '${params.handleType}', handleId: '${params.handleId}'}`);
      debugLog(`🎯 This handle will act as SOURCE for this connection`);

      // Store connection start information for later use
      // Correctly map the handleId to the actual position
      let handlePosition = params.handleId || null;

      // If handleId is not provided, determine position from handleType and default positions
      if (!handlePosition) {
        // Default positions based on handle type
        handlePosition = params.handleType === 'source' ? 'bottom' : 'top';

        // Check if this is a side handle (left/right) based on the visual position of the click
        if (params.handleType === 'source') {
          // For source handles, we default to Right if no handleId is specified
          handlePosition = 'right';
        } else {
          // For target handles, we default to Left if no handleId is specified
          handlePosition = 'left';
        }
      }

      setConnectionInfo({
        nodeId: params.nodeId,
        handleType: params.handleType,
        handlePosition: handlePosition
      });

      debugLog(`Resolved connection info: { nodeId: '${params.nodeId}', handleType: '${params.handleType}', handlePosition: '${handlePosition}'}`);
    },
    []
  );

  // Reset connection info when connection completes or aborts
  const onConnectEnd = useCallback(() => {
    setConnectionInfo({
      nodeId: null,
      handleType: null,
      handlePosition: null
    });
  }, []);

  // Ensure all nodes have the setNodes function and onAddProperties callback in their data
  const nodesWithCallbacks = nodes.map(node => ({
    ...node,
    data: {
      ...node.data,
      setNodes,
      onAddProperties: handleAddProperties
    }
  }));

  return (
    <div className="h-full w-full relative" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodesWithCallbacks}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={onConnect}
        onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        onEdgeClick={onEdgeClick}
        onPaneClick={onPaneClick}
        onInit={onInit}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onNodeClick={onNodeClick}
        onNodeDragStart={onNodeDragStart}
        onNodeDragStop={onNodeDragStop}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={getDefaultEdgeOptions(getEdgeType())}
        edgeTypes={edgeTypes}
        nodesDraggable={true}
        nodesConnectable={true}
        connectionMode={ConnectionMode.Strict}
        connectionRadius={100}
        snapToGrid={true}
        snapGrid={[10, 10]}
        fitView={false}
        fitViewOptions={{ padding: 0.2 }}
        proOptions={{ hideAttribution: true }}
        elevateEdgesOnSelect={true}
        attributionPosition="bottom-left"
        minZoom={0.2}
        maxZoom={4}
        nodeExtent={[
          [-Infinity, -Infinity],
          [Infinity, Infinity]
        ]}
        elementsSelectable={true}
        selectNodesOnDrag={false}
        panOnDrag={true}
        zoomOnScroll={true}
        zoomOnPinch={true}
        panOnScroll={false}
        preventScrolling={true}
      >
        <Controls />
        <MiniMap />
        <Background gap={24} size={1} />
      </ReactFlow>
      
      {/* Edge Type Selector positioned in top-right corner */}
      <div className="absolute top-4 right-4 z-10">
        <EdgeTypeSelector />
      </div>
    </div>
  );
};

export default DiagramEditor;
