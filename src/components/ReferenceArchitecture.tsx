import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  Di<PERSON>Trigger,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileIcon, ExternalLinkIcon } from 'lucide-react';

interface ReferenceArchitectureProps {
  trigger?: React.ReactNode;
}

const ReferenceArchitecture: React.FC<ReferenceArchitectureProps> = ({
  trigger
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center gap-2">
            <FileIcon className="h-4 w-4" />
            View Reference Architecture
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Instagram Reference Architecture</DialogTitle>
          <DialogDescription>
            Compare your design with Instagram's actual architecture
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Core Components</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Frontend Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Mobile Apps (iOS, Android)</li>
                  <li>Web Application</li>
                  <li>Progressive Web App</li>
                  <li>Content Delivery Network (CDN)</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">API Gateway & Load Balancing</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>API Gateway for request routing</li>
                  <li>Load balancers for traffic distribution</li>
                  <li>Rate limiting and throttling</li>
                  <li>Request authentication</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Authentication Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>User authentication</li>
                  <li>OAuth integration</li>
                  <li>Session management</li>
                  <li>Security and privacy controls</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Media Services</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Media processing and transcoding</li>
                  <li>Image/video optimization</li>
                  <li>Content filtering</li>
                  <li>Media metadata extraction</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Data Storage</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Media Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Amazon S3 for object storage</li>
                  <li>Content-addressed storage</li>
                  <li>Multiple storage tiers</li>
                  <li>Geographic replication</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Metadata Storage</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Cassandra for distributed metadata</li>
                  <li>TAO (The Association Object) graph database</li>
                  <li>Redis for caching</li>
                  <li>MySQL for relational data</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Scalability Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Messaging & Queues</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Kafka for event streaming</li>
                  <li>RabbitMQ for task queues</li>
                  <li>Gutter queues for traffic spikes</li>
                  <li>Dead letter queues for error handling</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Caching Strategy</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Multi-level caching</li>
                  <li>CDN for static content</li>
                  <li>Redis for hot data</li>
                  <li>Client-side caching</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Additional Services</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Analytics & Recommendations</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Machine learning for content recommendations</li>
                  <li>Real-time analytics</li>
                  <li>Batch processing pipelines</li>
                  <li>A/B testing framework</li>
                </ul>
              </div>
              
              <div className="border rounded-md p-4">
                <h4 className="font-medium mb-2">Notification Systems</h4>
                <ul className="list-disc list-inside text-sm space-y-1">
                  <li>Push notification service</li>
                  <li>Email delivery system</li>
                  <li>In-app notification queue</li>
                  <li>Notification preferences management</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-500">
            <p>Note: This is a simplified representation of Instagram's architecture. The actual system is more complex and constantly evolving.</p>
          </div>
        </div>
        
        <DialogFooter className="flex justify-between">
          <a 
            href="https://instagram-engineering.com/what-powers-instagram-hundreds-of-instances-dozens-of-technologies-adf2e22da2ad" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
          >
            Learn more from Instagram Engineering <ExternalLinkIcon className="h-3 w-3" />
          </a>
          <DialogClose asChild>
            <Button type="button">Close</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReferenceArchitecture;
