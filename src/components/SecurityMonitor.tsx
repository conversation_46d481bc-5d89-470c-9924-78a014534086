import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, AlertTriangle, Activity, Users, Clock, Ban } from 'lucide-react';
import { rateLimitService } from '@/services/rateLimitService';

const SecurityMonitor: React.FC = () => {
  const [stats, setStats] = useState({
    activeIPs: 0,
    activeEmails: 0,
    globalRequests: 0
  });
  const [emergencyMode, setEmergencyMode] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const updateStats = () => {
      const currentStats = rateLimitService.getStats();
      setStats(currentStats);
    };

    // Initial load
    updateStats();

    // Set up auto-refresh every 5 seconds
    const interval = setInterval(updateStats, 5000);
    setRefreshInterval(interval);

    return () => {
      if (interval) clearInterval(interval);
    };
  }, []);

  const handleEmergencyMode = () => {
    if (emergencyMode) {
      rateLimitService.disableEmergencyMode();
      setEmergencyMode(false);
    } else {
      rateLimitService.enableEmergencyMode(300000); // 5 minutes
      setEmergencyMode(true);
      // Auto-disable after 5 minutes
      setTimeout(() => {
        setEmergencyMode(false);
      }, 300000);
    }
  };

  const getThreatLevel = () => {
    const { activeIPs, globalRequests } = stats;
    
    if (globalRequests > 80 || activeIPs > 50) {
      return { level: 'HIGH', color: 'destructive', icon: AlertTriangle };
    } else if (globalRequests > 50 || activeIPs > 20) {
      return { level: 'MEDIUM', color: 'default', icon: Activity };
    } else {
      return { level: 'LOW', color: 'secondary', icon: Shield };
    }
  };

  const threat = getThreatLevel();
  const ThreatIcon = threat.icon;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Security Monitor</h2>
        <div className="flex items-center gap-2">
          <Badge variant={threat.color as any} className="flex items-center gap-1">
            <ThreatIcon className="h-3 w-3" />
            Threat Level: {threat.level}
          </Badge>
          {emergencyMode && (
            <Badge variant="destructive" className="flex items-center gap-1">
              <Ban className="h-3 w-3" />
              Emergency Mode Active
            </Badge>
          )}
        </div>
      </div>

      {emergencyMode && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Emergency mode is active. All waitlist requests are being blocked.
            This will automatically disable in a few minutes.
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active IPs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeIPs}</div>
            <p className="text-xs text-muted-foreground">
              Unique IPs making requests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Emails</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeEmails}</div>
            <p className="text-xs text-muted-foreground">
              Emails with recent attempts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Global Requests</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.globalRequests}</div>
            <p className="text-xs text-muted-foreground">
              Requests in current window
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Emergency Controls</CardTitle>
          <CardDescription>
            Use these controls during DDoS attacks or suspicious activity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Emergency Mode</h4>
              <p className="text-sm text-muted-foreground">
                Temporarily block all waitlist requests for 5 minutes
              </p>
            </div>
            <Button
              variant={emergencyMode ? "destructive" : "outline"}
              onClick={handleEmergencyMode}
              className="flex items-center gap-2"
            >
              <Ban className="h-4 w-4" />
              {emergencyMode ? 'Disable Emergency Mode' : 'Enable Emergency Mode'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Rate Limiting Configuration</CardTitle>
          <CardDescription>
            Current rate limiting settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h4 className="font-medium mb-2">Per IP Limits</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• 10 requests per minute</li>
                <li>• 5 minute block on violation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Per Email Limits</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• 3 attempts per 5 minutes</li>
                <li>• 15 minute block on violation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Global Limits</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• 100 requests per minute</li>
                <li>• 1 minute block on violation</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Security Features Active</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Badge variant="outline" className="justify-center">
              ✅ Rate Limiting
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Honeypot Fields
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Input Sanitization
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Email Validation
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Bot Detection
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Request Fingerprinting
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Duplicate Prevention
            </Badge>
            <Badge variant="outline" className="justify-center">
              ✅ Emergency Controls
            </Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SecurityMonitor;
