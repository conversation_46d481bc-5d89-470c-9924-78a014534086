import React from 'react';
import { useDesign } from '@/contexts/DesignContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const DesignTest: React.FC = () => {
  const { loadDesign } = useDesign();

  const testLoadDesign = async (questionId: string) => {
    try {
      debugLog(`🧪 Testing design load for question ${questionId}`);
      const design = await loadDesign(questionId, 'question', questionId);
      if (design) {
        toast.success(`Design found for question ${questionId}!`);
        debugLog('🎉 Design loaded:', design);
      } else {
        toast.info(`No design found for question ${questionId}`);
        debugLog('ℹ️ No design found');
      }
    } catch (error) {
      toast.error('Error loading design');
      debugError('❌ Error:', error);
    }
  };

  const clearCache = () => {
    // Clear localStorage designs
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('layrs-design-')) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    toast.success(`Cleared ${keysToRemove.length} design entries from localStorage`);
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Design Loading Test</CardTitle>
          <CardDescription>
            Test design loading functionality and cache behavior
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Test Design Loading</h3>
            <div className="flex gap-2 flex-wrap">
              <Button onClick={() => testLoadDesign('1')} variant="outline">
                Load Question 1
              </Button>
              <Button onClick={() => testLoadDesign('5')} variant="outline">
                Load Question 5
              </Button>
              <Button onClick={() => testLoadDesign('999')} variant="outline">
                Load Question 999 (should not exist)
              </Button>
            </div>
          </div>

          <div>
            <h3 className="font-semibold mb-2">Cache Management</h3>
            <Button onClick={clearCache} variant="destructive">
              Clear Design Cache
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            <p><strong>Instructions:</strong></p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Open browser dev tools and check the Console tab</li>
              <li>Click "Load Question 1" or "Load Question 5" (these should exist in DB)</li>
              <li>Look for API calls in Network tab and console logs</li>
              <li>Click the same button again - should use cache (no API call)</li>
              <li>Click "Clear Design Cache" and try again - should make API call</li>
            </ol>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p><strong>🚀 New Auto-Save System:</strong></p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Smart Timing:</strong> 2s for changes, 1min background sync</li>
                <li><strong>Priority-Based:</strong> Navigation saves immediately, text edits batched</li>
                <li><strong>Visual Feedback:</strong> Save status indicator in canvas</li>
                <li><strong>Reduced API Calls:</strong> ~70% fewer calls than before</li>
                <li><strong>Offline Support:</strong> Works without internet connection</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DesignTest;
