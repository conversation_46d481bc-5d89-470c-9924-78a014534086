
import React, { useState, useCallback, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import {
  ReactFlow,
  Node,
  Edge,
  useNodesState,
  useEdgesState,
  Connection,
  addEdge,
  Handle,
  Position,
  ReactFlowProvider
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { 
  Globe, 
  Database, 
  Zap, 
  ArrowRight, 
  Play, 
  Star,
  CheckCircle,
  Users,
  Brain,
  Target,
  Award,
  TrendingUp,
  Layers,
  Sparkles,
  Code,
  Shield,
  Clock
} from 'lucide-react';

// Custom Frontend Node Component
const FrontendNode = () => (
  <div className="relative group cursor-pointer select-none">
    <Handle
      type="source"
      position={Position.Right}
      className="!w-full !h-full !bg-transparent !border-0 !right-0 !top-0 !transform-none !opacity-0"
      style={{ width: '100%', height: '100%', right: 0, top: 0 }}
    />
    <div className="w-32 h-32 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 animate-pulse pointer-events-none border-2 border-blue-300/50">
      <Globe className="h-16 w-16 text-white drop-shadow-lg" />
    </div>
    <div className="mt-6 text-xl font-bold text-white text-center pointer-events-none drop-shadow-lg">Frontend</div>
    <div className="text-sm text-blue-200 text-center pointer-events-none">User Interface</div>
  </div>
);

// Custom Backend Node Component  
const BackendNode = () => (
  <div className="relative group cursor-pointer select-none">
    <Handle
      type="target"
      position={Position.Left}
      className="!w-full !h-full !bg-transparent !border-0 !left-0 !top-0 !transform-none !opacity-0"
      style={{ width: '100%', height: '100%', left: 0, top: 0 }}
    />
    <div className="w-32 h-32 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center shadow-2xl hover:shadow-orange-500/25 transition-all duration-300 animate-pulse pointer-events-none border-2 border-orange-300/50">
      <Database className="h-16 w-16 text-white drop-shadow-lg" />
    </div>
    <div className="mt-6 text-xl font-bold text-white text-center pointer-events-none drop-shadow-lg">Database</div>
    <div className="text-sm text-orange-200 text-center pointer-events-none">Data Storage</div>
  </div>
);

const nodeTypes = {
  frontend: FrontendNode,
  backend: BackendNode,
};

interface MagicalLandingPageProps {}

const MagicalLandingPageContent: React.FC<MagicalLandingPageProps> = () => {
  const { user } = useAuth();
  const [showChallenge, setShowChallenge] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [autoConnectTimer, setAutoConnectTimer] = useState(5);
  const [showReveal, setShowReveal] = useState(false);

  const initialNodes: Node[] = [
    {
      id: 'frontend',
      type: 'frontend',
      position: { x: 200, y: 200 },
      data: {},
      draggable: false,
    },
    {
      id: 'backend',
      type: 'backend', 
      position: { x: 600, y: 200 },
      data: {},
      draggable: false,
    },
  ];

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Auto-connect timer
  useEffect(() => {
    if (isConnected || !showChallenge) return;
    
    const timer = setInterval(() => {
      setAutoConnectTimer(prev => {
        if (prev <= 1) {
          handleAutoConnect();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isConnected, showChallenge]);

  const handleAutoConnect = () => {
    const newEdge: Edge = {
      id: 'magical-connection',
      source: 'frontend',
      target: 'backend',
      animated: true,
      style: {
        stroke: 'url(#magicalGradient)',
        strokeWidth: 4,
        filter: 'drop-shadow(0 0 8px rgba(255,255,255,0.8))',
      },
    };
    setEdges([newEdge]);
    setIsConnected(true);
    
    // Trigger magical reveal
    setTimeout(() => {
      setShowReveal(true);
      setTimeout(() => {
        setShowChallenge(false);
      }, 1000);
    }, 2000);
  };

  const onConnect = useCallback((params: Connection) => {
    const newEdge = {
      ...params,
      animated: true,
      style: {
        stroke: 'url(#magicalGradient)',
        strokeWidth: 4,
        filter: 'drop-shadow(0 0 8px rgba(255,255,255,0.8))',
      },
    };
    setEdges((eds) => addEdge(newEdge, eds));
    setIsConnected(true);
    
    // Trigger magical reveal
    setTimeout(() => {
      setShowReveal(true);
      setTimeout(() => {
        setShowChallenge(false);
      }, 1000);
    }, 2000);
  }, [setEdges]);

  const features = [
    {
      icon: <Brain className="h-6 w-6" />,
      title: "AI-Powered Feedback",
      description: "Get intelligent suggestions and real-time feedback on your system designs."
    },
    {
      icon: <Target className="h-6 w-6" />,
      title: "Smart Scoring",
      description: "Automatic evaluation of your designs based on best practices and scalability."
    },
    {
      icon: <Layers className="h-6 w-6" />,
      title: "Drag & Drop Canvas",
      description: "Intuitive visual designer for creating complex system architectures."
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: "Interview Prep",
      description: "Practice with real system design questions from top tech companies."
    }
  ];

  const testimonials = [
    {
      name: "Sarah Chen",
      role: "Senior Engineer at Google",
      content: "Layrs transformed my approach to system design. The AI feedback is incredibly insightful.",
      rating: 5
    },
    {
      name: "Mike Rodriguez", 
      role: "Tech Lead at Meta",
      content: "Perfect for interview preparation. I aced my system design rounds thanks to Layrs.",
      rating: 5
    },
    {
      name: "Emily Johnson",
      role: "Engineering Manager at Netflix", 
      content: "Our entire team uses Layrs for architecture reviews. Game changer!",
      rating: 5
    }
  ];

  if (showChallenge) {
    return (
      <div className={`fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden transition-all duration-1000 ${showReveal ? 'opacity-0 scale-110' : ''}`}>
        {/* Animated background particles */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-2 h-2 bg-blue-400 rounded-full animate-ping opacity-70"></div>
          <div className="absolute top-40 right-32 w-1 h-1 bg-orange-400 rounded-full animate-ping opacity-60" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-32 left-1/3 w-1.5 h-1.5 bg-purple-400 rounded-full animate-ping opacity-50" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-20 right-20 w-2 h-2 bg-blue-300 rounded-full animate-ping opacity-40" style={{ animationDelay: '0.5s' }}></div>
        </div>

        {/* Challenge prompt */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center z-10 -mt-20">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
            <span className="bg-gradient-to-r from-blue-400 to-orange-400 bg-clip-text text-transparent">
              Can you connect these?
            </span>
          </h1>
          <p className="text-lg text-gray-300 mb-8 animate-fade-in opacity-80">
            Think like a systems engineer...
          </p>
          
          {/* Auto-connect countdown */}
          {!isConnected && autoConnectTimer > 0 && (
            <div className="text-sm text-gray-400 animate-pulse">
              Auto-connecting in {autoConnectTimer}s...
            </div>
          )}
        </div>

        {/* React Flow Canvas */}
        <div className="w-full h-full relative z-10">
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            nodeTypes={nodeTypes}
            fitView
            fitViewOptions={{ padding: 0.2 }}
            nodesDraggable={false}
            nodesConnectable={true}
            elementsSelectable={false}
            zoomOnScroll={false}
            zoomOnPinch={false}
            panOnDrag={false}
            proOptions={{ hideAttribution: true }}
            style={{ background: 'transparent' }}
          >
            <defs>
              <linearGradient id="magicalGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#60a5fa" />
                <stop offset="50%" stopColor="#ffffff" />
                <stop offset="100%" stopColor="#fb923c" />
              </linearGradient>
            </defs>
          </ReactFlow>
        </div>

        {/* Success message */}
        {isConnected && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center z-20 mt-40">
            <div className="animate-scale-in">
              <div className="inline-flex items-center gap-3 px-8 py-4 bg-white/10 backdrop-blur-lg border border-white/20 rounded-full text-white font-bold text-xl shadow-2xl">
                <Zap className="h-6 w-6 animate-pulse text-yellow-400" />
                Now you're thinking in systems
                <Sparkles className="h-6 w-6 animate-pulse text-blue-400" />
              </div>
              <p className="mt-6 text-lg text-gray-300 animate-fade-in">
                Welcome to the world of system design...
              </p>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 animate-fade-in">
      {/* Navigation */}
      <nav className="sticky top-0 z-50 bg-white/95 backdrop-blur-lg border-b border-gray-100">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Layers className="h-6 w-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Layrs</span>
            </div>
            
            <div className="flex items-center space-x-4">
              {user ? (
                <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-semibold shadow-lg">
                  <Link to="/questions">Open Canvas</Link>
                </Button>
              ) : (
                <div className="flex space-x-3">
                  <Button asChild variant="ghost" className="text-gray-600 hover:text-gray-900 font-medium">
                    <Link to="/login">Sign In</Link>
                  </Button>
                  <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-semibold shadow-lg">
                    <Link to="/register">Join Waitlist</Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-20 pb-24 px-6 relative overflow-hidden">
        <div className="max-w-7xl mx-auto text-center animate-slide-in-right">
          <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white border-none hover:from-blue-600 hover:to-purple-600 px-4 py-2 rounded-full font-medium mb-8">
            <Sparkles className="w-4 h-4 mr-2" />
            Now you're thinking in systems
          </Badge>
          
          <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight mb-8">
            Master System Design
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent block mt-2">
              Like Never Before
            </span>
          </h1>
          
          <p className="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-4xl mx-auto mb-12">
            AI-powered system design practice platform. Get intelligent feedback, practice with real problems, and ace your next interview.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
            <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-10 py-4 text-lg rounded-xl font-semibold shadow-xl hover:shadow-2xl transition-all group">
              <Link to={user ? "/questions" : "/register"}>
                Join the Waitlist
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-2 border-gray-300 text-gray-700 hover:bg-gray-50 px-10 py-4 text-lg rounded-xl font-semibold group">
              <a href="#demo">
                <Play className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
                Watch Demo
              </a>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mt-20 pt-12 border-t border-gray-200">
            <div className="text-center group">
              <div className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">10K+</div>
              <div className="text-lg font-semibold text-gray-700 mb-1">Engineers</div>
              <div className="text-sm text-gray-500">Practicing</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">500+</div>
              <div className="text-lg font-semibold text-gray-700 mb-1">Problems</div>
              <div className="text-sm text-gray-500">Curated</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2 group-hover:text-green-600 transition-colors">95%</div>
              <div className="text-lg font-semibold text-gray-700 mb-1">Success Rate</div>
              <div className="text-sm text-gray-500">Interviews</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl lg:text-5xl font-bold text-gray-900 mb-2 group-hover:text-yellow-600 transition-colors">4.9/5</div>
              <div className="text-lg font-semibold text-gray-700 mb-1">Rating</div>
              <div className="text-sm text-gray-500">User Score</div>
            </div>
          </div>
        </div>
      </section>

      {/* Demo Video Section */}
      <section id="demo" className="py-20 px-6 bg-gray-900 text-white">
        <div className="max-w-6xl mx-auto text-center">
          <div className="space-y-6 mb-12">
            <h2 className="text-4xl lg:text-5xl font-bold">
              See Layrs in Action
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Watch how engineers master system design with our AI-powered platform
            </p>
          </div>
          
          <div className="relative">
            <div className="aspect-video bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl border border-gray-700 overflow-hidden shadow-2xl">
              <div className="absolute inset-0 flex items-center justify-center">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full shadow-xl">
                  <Play className="mr-3 h-6 w-6" />
                  Play Demo Video
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Everything You Need to Excel
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive tools designed specifically for system design mastery
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 overflow-hidden group">
                <CardHeader className="pb-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl text-white group-hover:scale-110 transition-transform">
                      {feature.icon}
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-2xl font-bold text-gray-900 mb-2">{feature.title}</CardTitle>
                      <CardDescription className="text-gray-600 text-lg leading-relaxed">
                        {feature.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-6 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Loved by Engineers Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              Join thousands who've transformed their system design skills
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border border-gray-200 hover:shadow-xl transition-all duration-300 group">
                <CardContent className="pt-8">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 text-base leading-relaxed mb-6 font-medium">
                    "{testimonial.content}"
                  </blockquote>
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div>
                      <div className="font-bold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-24 px-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-600 relative overflow-hidden">
        <div className="max-w-4xl mx-auto text-center relative">
          <div className="space-y-8">
            <h2 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
              Ready to Master System Design?
            </h2>
            <p className="text-xl lg:text-2xl text-blue-100 leading-relaxed max-w-3xl mx-auto">
              Join our waitlist and be among the first to experience the future of system design education.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center pt-8">
              <Button asChild size="lg" className="bg-white text-purple-600 hover:bg-gray-50 px-10 py-4 text-xl rounded-xl font-bold shadow-2xl hover:shadow-3xl transition-all group">
                <Link to={user ? "/questions" : "/register"}>
                  Join the Waitlist
                  <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>

            <div className="pt-8 text-blue-100">
              <p className="text-lg font-medium">🎉 Early access • No credit card required</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-6 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <Layers className="h-6 w-6 text-white" />
            </div>
            <span className="text-2xl font-bold">Layrs</span>
          </div>
          <p className="text-gray-400 mb-8">
            Master the unseen architecture of great systems. One layer at a time.
          </p>
          <div className="border-t border-gray-800 pt-8">
            <p className="text-gray-400">&copy; 2024 Layrs. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

const MagicalLandingPage: React.FC<MagicalLandingPageProps> = () => {
  return (
    <ReactFlowProvider>
      <MagicalLandingPageContent />
    </ReactFlowProvider>
  );
};

export default MagicalLandingPage;
