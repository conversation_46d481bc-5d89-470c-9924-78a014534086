import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuestions } from '@/contexts/QuestionsContext';
import { useSubscription } from '@/hooks/useSubscription';
import Header from '@/components/Header';
import QuestionHeader from '@/components/question-detail/QuestionHeader';
import QuestionCard from '@/components/question-detail/QuestionCard';
import QuestionTabs from '@/components/question-detail/QuestionTabs';
import QuestionDesignSection from '@/components/question-detail/QuestionDesignSection';
import LoadingState from '@/components/question-detail/LoadingState';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Lock, Crown, ArrowLeft } from 'lucide-react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

const QuestionDetail: React.FC = () => {
  const { questionId } = useParams<{ questionId: string }>();
  const navigate = useNavigate();
  const { getQuestionById, currentQuestion, setCurrentQuestion } = useQuestions();
  const { canAccessQuestion, hasActiveSubscription } = useSubscription();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [accessChecked, setAccessChecked] = useState(false);



  // Check access to the question
  useEffect(() => {
    const checkAccess = async () => {
      if (questionId) {
        try {
          const questionIdNum = parseInt(questionId, 10);
          if (isNaN(questionIdNum)) {
            debugError('Invalid question ID:', questionId);
            setHasAccess(false);
            setAccessChecked(true);
            return;
          }

          const canAccess = await canAccessQuestion(questionIdNum);
          setHasAccess(canAccess);
          setAccessChecked(true);

          if (!canAccess) {
            debugWarn(`User does not have access to question ${questionId}`);
          }
        } catch (error) {
          debugError('Error checking question access:', error);
          setHasAccess(false);
          setAccessChecked(true);
        }
      }
    };

    checkAccess();
  }, [questionId, canAccessQuestion]);

  // Load question data
  useEffect(() => {
    const loadQuestion = async () => {
      if (questionId && hasAccess) {
        try {
          const questionIdNum = parseInt(questionId, 10);
          if (isNaN(questionIdNum)) {
            debugError('Invalid question ID:', questionId);
            navigate('/questions');
            return;
          }

          const question = await getQuestionById(questionIdNum);
          if (question) {
            setCurrentQuestion(question);
          } else {
            // Question not found, redirect to questions list
            navigate('/questions');
          }
        } catch (error) {
          debugError('Error loading question:', error);
          navigate('/questions');
        }
      }
    };

    if (accessChecked) {
      loadQuestion();
    }
  }, [questionId, hasAccess, accessChecked, getQuestionById, setCurrentQuestion, navigate]);

  const handleStartAssessment = () => {
    // Navigate to the design page with the current question ID
    if (questionId) {
      navigate(`/design/${questionId}`);
    }
  };

  // Show loading state while checking access or loading question
  if (!accessChecked || (hasAccess && !currentQuestion)) {
    return <LoadingState onStartAssessment={handleStartAssessment} />;
  }

  // Show access denied screen for locked questions
  if (hasAccess === false) {
    return (
      <div className="flex flex-col min-h-screen bg-gray-100">
        <Header onStartAssessment={() => {}} />

        <div className="flex-1 p-6">
          <div className="max-w-3xl mx-auto">
            <Card className="border-orange-200 bg-orange-50">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Lock className="w-16 h-16 text-orange-500" />
                </div>
                <CardTitle className="text-2xl text-orange-800">Question Access Restricted</CardTitle>
                <CardDescription className="text-lg text-orange-700">
                  This question is only available to Pro subscribers.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-white p-4 rounded-lg border border-orange-200">
                  <h3 className="font-semibold text-orange-900 mb-2">What you get with Pro:</h3>
                  <ul className="space-y-2 text-orange-800">
                    <li className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-orange-600" />
                      Access to all system design questions
                    </li>
                    <li className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-orange-600" />
                      Advanced assessment features
                    </li>
                    <li className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-orange-600" />
                      Priority support
                    </li>
                  </ul>
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => navigate('/profile')}
                    className="flex-1 bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600"
                  >
                    <Crown className="h-4 w-4 mr-2" />
                    Upgrade to Pro
                  </Button>
                  <Button
                    onClick={() => navigate('/questions')}
                    variant="outline"
                    className="flex-1"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Questions
                  </Button>
                </div>

                <div className="text-center">
                  <p className="text-sm text-orange-600">
                    You can access the <strong>URL Shortener</strong> question with your current plan.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  // Show the question if user has access
  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      <Header onStartAssessment={handleStartAssessment} />

      <div className="flex-1 p-6">
        <div className="max-w-5xl mx-auto">
          <QuestionHeader question={currentQuestion} />
          <QuestionCard question={currentQuestion} onStartDesign={handleStartAssessment} />
          <QuestionTabs question={currentQuestion} />

          {questionId && <QuestionDesignSection questionId={questionId} contextType="question" contextId={questionId} />}
        </div>
      </div>
    </div>
  );
};

export default QuestionDetail;
