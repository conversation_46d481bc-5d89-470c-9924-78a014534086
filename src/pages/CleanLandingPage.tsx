import React, { useEffect, useState, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useDesign } from '@/contexts/DesignContext';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Clock, ArrowRight, PenTool, User, TrendingUp, Target, Play, FileText, Home, Zap, Users, BookOpen, Menu } from 'lucide-react';
import { useQuestions } from '@/contexts/QuestionsContext';
import AuthNav from '@/components/AuthNav';
import AnimatedSystemPreview from '@/components/AnimatedSystemPreview';
import InteractiveFeatureCards from '@/components/InteractiveFeatureCards';
import FlowingDataBackground from '@/components/FlowingDataBackground';
import DemoVideo from '@/components/DemoVideo';
import InteractiveGate from '@/components/InteractiveGate';

const CleanLandingPage: React.FC = () => {
  const { user } = useAuth();
  const { listDesigns, isLoading } = useDesign();
  const { questions } = useQuestions();
  const [recentDesigns, setRecentDesigns] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showGate, setShowGate] = useState(!user); // Skip gate for logged-in users
  const [isRevealing, setIsRevealing] = useState(false);
  const [revealStage, setRevealStage] = useState(0);

  // Debug log
  console.log('user', user, 'showGate', showGate, 'recentDesigns', recentDesigns, 'loading', loading, 'isLoading', isLoading);

  const fetchDesigns = useCallback(async () => {
    if (!user) {
      console.log('No user found, skipping design load');
      return;
    }
    
    console.log('Fetching designs for user:', user.id);
    setLoading(true);
    
    try {
      const designs = await listDesigns();
      console.log('Raw fetched designs:', designs);
      console.log('Number of designs:', designs.length);
      
      if (!designs || designs.length === 0) {
        console.log('No designs found');
        setRecentDesigns([]);
        return;
      }
      
      // Get the most recent 3 designs sorted by lastModified
      const validDesigns = designs.filter(design => {
        console.log('Checking design:', design);
        return design && (design.questionId || design.name);
      });
      
      console.log('Valid designs after filter:', validDesigns);
      
      const sortedDesigns = validDesigns
        .sort((a, b) => {
          const dateA = a.lastModified ? new Date(a.lastModified).getTime() : 0;
          const dateB = b.lastModified ? new Date(b.lastModified).getTime() : 0;
          return dateB - dateA;
        })
        .slice(0, 3);
      
      console.log('Final sorted recent designs:', sortedDesigns);
      setRecentDesigns(sortedDesigns);
    } catch (error) {
      console.error('Error fetching designs:', error);
      setRecentDesigns([]);
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
      fetchDesigns();
    }
  }, [user, fetchDesigns]);

  const getUserFirstName = () => {
    if (!user) return '';
    const email = user.email || '';
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const handleGateComplete = () => {
    setIsRevealing(true);
    
    // Staggered reveal animation
    setTimeout(() => setRevealStage(1), 0);    // Navigation (0ms)
    setTimeout(() => setRevealStage(2), 300);  // Hero section (300ms)
    setTimeout(() => setRevealStage(3), 600);  // Demo section (600ms)
    setTimeout(() => setRevealStage(4), 900);  // Features/Content (900ms)
    setTimeout(() => setRevealStage(5), 1200); // Footer (1200ms)
    
    setTimeout(() => {
      setShowGate(false);
    }, 800);
  };

  if (showGate && !user) {
    return <InteractiveGate onComplete={handleGateComplete} />;
  }

  return (
    <div className={`min-h-screen bg-gradient-to-t from-purple-100 to-indigo-200 relative overflow-hidden ${
      isRevealing ? 'animate-fade-in' : ''
    }`}>
      {/* Flowing Data Background */}
      <FlowingDataBackground />

      {/* Layered Glow Effects - Desktop Only - Purple Theme */}
      <div className="hidden lg:block absolute inset-0 pointer-events-none" style={{ zIndex: 1 }}>
        <div className="absolute inset-[0.5rem] rounded-[12rem] bg-white/2 shadow-[0_0_40px_rgba(196,181,253,0.5)] blur-[4px]"></div>
        <div className="absolute inset-[3rem] rounded-[12rem] bg-white/2 shadow-[0_0_30px_rgba(196,181,253,0.4)] blur-[3px]"></div>
        <div className="absolute inset-[6rem] rounded-[12rem] bg-white/2 shadow-[0_0_20px_rgba(196,181,253,0.3)] blur-[2px]"></div>
        <div className="absolute inset-[10rem] rounded-[8rem] bg-white/2 shadow-[inset_0_0_30px_rgba(255,255,255,0.4)] blur-[1px]"></div>
      </div>

      {/* Abstract Geometric Shape - Purple Theme - Lower z-index */}
      <div aria-hidden="true" className="absolute inset-x-0 top-1/2 transform -translate-y-1/2 overflow-hidden blur-3xl pointer-events-none" style={{ zIndex: 2 }}>
        <div 
          style={{clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'}}
          className="relative left-1/2 aspect-[1155/678] w-96 -translate-x-1/2 bg-gradient-to-tr from-purple-500 to-indigo-600 opacity-30 sm:w-[36rem] md:w-[72rem]"
        ></div>
      </div>

      {/* Floating Navigation Bar - Higher z-index */}
      <div className={`mx-auto fixed flex left-0 right-0 top-0 w-full items-center justify-between max-w-[76rem] select-none ring-1 ring-white/20 bg-white/10 backdrop-blur-lg shadow-lg lg:rounded-full lg:mt-3 transition-all duration-500 ${
        user ? 'translate-y-0 opacity-100' : (revealStage >= 1 ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0')
      }`} style={{ zIndex: 100 }}>
        <header className="relative isolate w-full">
          <nav className="flex items-center justify-between p-3 lg:p-2">
            {/* Logo */}
            <div className="flex lg:flex-1 ml-2 -mt-0.5">
              <Link to="/" className="flex items-center gap-x-0.5 transition border-b border-transparent hover:border-white/60">
                <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-800 bg-clip-text text-transparent mt-1">
                  Layrs
                </div>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <div className="flex lg:hidden">
              <button
                type="button"
                className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                <span className="sr-only">Open main menu</span>
                <Menu className="size-5.5 mr-1 outline-none" />
              </button>
            </div>

            {/* Desktop Navigation Links */}
            <div className="hidden lg:flex lg:gap-x-6 -ml-12">
              <Link 
                to="/" 
                className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1"
              >
                <Home className="h-4 w-4" />
                Home
              </Link>
              <Link 
                to="/questions" 
                className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1"
              >
                <FileText className="h-4 w-4" />
                Questions
              </Link>
              <Link 
                to="/canvas" 
                className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1"
              >
                <PenTool className="h-4 w-4" />
                Canvas
              </Link>
              <button className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                Tutorials
              </button>
              <button className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1">
                <Users className="h-4 w-4" />
                Community
              </button>
              <button className="text-sm/6 font-semibold text-gray-700 transition border-b border-transparent hover:border-white/60 flex items-center gap-1">
                <Zap className="h-4 w-4" />
                Pricing
              </button>
            </div>

            {/* Auth Navigation */}
            <div className="hidden lg:flex lg:flex-1 lg:justify-end">
              <AuthNav />
            </div>
          </nav>

          {/* Mobile Menu - Glassmorphism Style */}
          {mobileMenuOpen && (
            <div className="lg:hidden absolute top-full left-0 right-0 bg-white/10 backdrop-blur-lg border-t border-white/20 rounded-b-2xl shadow-lg mt-1">
              <div className="px-4 py-6 space-y-4">
                <Link 
                  to="/" 
                  className="block text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Home className="h-4 w-4" />
                  Home
                </Link>
                <Link 
                  to="/questions" 
                  className="block text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <FileText className="h-4 w-4" />
                  Questions
                </Link>
                <Link 
                  to="/canvas" 
                  className="block text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <PenTool className="h-4 w-4" />
                  Canvas
                </Link>
                <button className="block w-full text-left text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2">
                  <BookOpen className="h-4 w-4" />
                  Tutorials
                </button>
                <button className="block w-full text-left text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Community
                </button>
                <button className="block w-full text-left text-base font-semibold text-gray-700 hover:text-gray-900 transition flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Pricing
                </button>
                <div className="pt-4 border-t border-white/20">
                  <AuthNav />
                </div>
              </div>
            </div>
          )}
        </header>
      </div>

      <section className={`pb-20 sm:pb-24 relative transition-all duration-700 ${
        user
          ? 'pt-40 translate-y-0 opacity-100 scale-100'
          : (revealStage >= 2
              ? 'pt-32 sm:pt-36 translate-y-0 opacity-100 scale-100'
              : 'pt-32 sm:pt-36 translate-y-10 opacity-0 scale-95')
      }`} style={{ zIndex: 50 }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            {user ? (
              <div className="mb-12">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-zinc-900 mb-6 leading-tight">
                  {getGreeting()}, <span className="bg-gradient-to-r from-purple-600 to-indigo-800 bg-clip-text text-transparent">{getUserFirstName()}</span>
                </h1>
                <p className="text-xl text-zinc-600 max-w-2xl mx-auto mb-8">
                  Ready to design your next system architecture?
                </p>
              </div>
            ) : (
              <div className="mb-16">
                <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold text-zinc-900 mb-8 leading-tight">
                  Master System Design
                  <br />
                  <span className="bg-gradient-to-r from-purple-600 to-indigo-800 bg-clip-text text-transparent">Like a Pro</span>
                </h1>
                <p className="text-xl text-zinc-600 max-w-3xl mx-auto mb-10 leading-relaxed">
                  Interactive practice, real-world scenarios, and expert guidance to help you excel in system design interviews and build better architectures.
                </p>
              </div>
            )}

            {/* Quick Actions for logged-in users */}
            {user && (
              <div className="flex flex-wrap gap-4 justify-center mb-16">
                <Button asChild size="lg" className="bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white px-8 py-4 font-semibold shadow-lg hover:shadow-xl transition-all duration-200">
                  <Link to="/questions" className="flex items-center gap-2">
                    <PenTool className="h-5 w-5" />
                    New Design
                  </Link>
                </Button>
                <Button asChild size="lg" variant="outline" className="border-2 border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 font-semibold transition-all duration-200">
                  <Link to="/questions" className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Practice Questions
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Demo Video Section - Medium z-index */}
      <section className={`py-16 relative transition-all duration-700 delay-100 ${
        user ? 'translate-y-0 opacity-100' : (revealStage >= 3 ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0')
      }`} style={{ zIndex: 40 }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-zinc-900 mb-4">
              See System Design in Action
            </h2>
            <p className="text-lg text-zinc-600 max-w-2xl mx-auto">
              Watch how professionals approach system design challenges with our interactive platform
            </p>
          </div>
          <div className="relative" style={{ zIndex: 45 }}>
            <DemoVideo />
          </div>
        </div>
      </section>

      {/* Content Sections - Medium z-index */}
      <div className={`transition-all duration-700 delay-200 relative ${
        user ? 'translate-y-0 opacity-100' : (revealStage >= 4 ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0')
      }`} style={{ zIndex: 30 }}>
        {/* Recent Designs Section */}
        {user && (
          <section className="py-20 bg-white/60 backdrop-blur-sm relative" style={{ zIndex: 35 }}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold text-zinc-900 mb-4">Your Recent Work</h2>
                <p className="text-lg text-zinc-600 max-w-2xl mx-auto">
                  Continue where you left off or start fresh with a new design
                </p>
              </div>
              
              {loading || isLoading ? (
                <div className="grid md:grid-cols-3 gap-8">
                  {[...Array(3)].map((_, i) => (
                    <Card key={i} className="bg-white/80 backdrop-blur-sm border border-purple-100 shadow-lg animate-pulse">
                      <CardHeader>
                        <div className="h-6 bg-purple-200 rounded mb-3"></div>
                        <div className="h-4 bg-purple-100 rounded"></div>
                      </CardHeader>
                      <CardContent>
                        <div className="h-4 bg-purple-100 rounded mb-2"></div>
                        <div className="h-4 bg-purple-100 rounded"></div>
                      </CardContent>
                      <CardFooter>
                        <div className="h-10 bg-purple-100 rounded w-full"></div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : recentDesigns.length === 0 ? (
                <div className="text-center py-20">
                  <div className="w-24 h-24 mx-auto mb-8 rounded-full bg-gradient-to-r from-purple-100 to-indigo-200 flex items-center justify-center">
                    <PenTool className="h-12 w-12 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-zinc-900 mb-3">No designs yet</h3>
                  <p className="text-zinc-600 mb-8 max-w-md mx-auto">
                    Start your system design journey by creating your first architecture diagram
                  </p>
                  <Button asChild className="bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200">
                    <Link to="/questions" className="flex items-center gap-2">
                      <PenTool className="h-4 w-4" />
                      Create Your First Design
                    </Link>
                  </Button>
                </div>
              ) : (
                <div className="grid md:grid-cols-3 gap-8">
                  {recentDesigns.map((design, idx) => {
                    let displayTitle = design.name || design.questionId;
                    if (design.contextType === 'question' && design.questionId) {
                      const question = questions.find(q => q.id === design.questionId);
                      if (question && question.title) {
                        displayTitle = question.title;
                      }
                    }
                    return (
                      <Card key={`${design.questionId || design.name || 'design'}-${idx}`} className="bg-white/80 backdrop-blur-sm border border-purple-100 shadow-lg hover:shadow-xl transition-all duration-200 group">
                        <CardHeader className="pb-4">
                          <CardTitle className="text-lg text-zinc-900 truncate flex items-center gap-2">
                            <PenTool className="h-4 w-4 text-purple-600" />
                            {displayTitle}
                          </CardTitle>
                          <CardDescription className="text-zinc-500 flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {design.lastModified ? new Date(design.lastModified).toLocaleDateString() : 'Recently'}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-zinc-500">Components:</span>
                            <span className="text-zinc-900 font-medium">{design.nodes?.length || 0}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-zinc-500">Type:</span>
                            <span className="text-purple-600 capitalize font-medium">{design.contextType || 'design'}</span>
                          </div>
                        </CardContent>
                        <CardFooter>
                          <Button asChild className="w-full gap-2 bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white font-semibold group-hover:shadow-lg transition-all duration-200">
                            <Link to={
                              design.contextType === 'question' && design.questionId 
                                ? `/design/${design.questionId}` 
                                : design.contextType === 'course' && design.contextId
                                ? `/guided/${design.contextId}`
                                : '/canvas'
                            }>
                              Continue Design
                              <ArrowRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              )}

              {/* Stats Section */}
              {recentDesigns.length > 0 && (
                <div className="mt-20 grid md:grid-cols-3 gap-8">
                  <div className="text-center p-8 bg-white/80 backdrop-blur-sm border border-purple-100 rounded-xl shadow-lg">
                    <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-100 to-emerald-100 flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="text-2xl font-bold text-zinc-900 mb-1">{recentDesigns.length}</div>
                    <div className="text-zinc-500">Active Designs</div>
                  </div>
                  <div className="text-center p-8 bg-white/80 backdrop-blur-sm border border-purple-100 rounded-xl shadow-lg">
                    <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-100 to-indigo-200 flex items-center justify-center">
                      <Target className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="text-2xl font-bold text-zinc-900 mb-1">
                      {recentDesigns.reduce((total, design) => total + (design.nodes?.length || 0), 0)}
                    </div>
                    <div className="text-zinc-500">Total Components</div>
                  </div>
                  <div className="text-center p-8 bg-white/80 backdrop-blur-sm border border-purple-100 rounded-xl shadow-lg">
                    <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-gradient-to-r from-purple-100 to-indigo-100 flex items-center justify-center">
                      <User className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="text-2xl font-bold text-zinc-900 mb-1">Pro</div>
                    <div className="text-zinc-500">Account Level</div>
                  </div>
                </div>
              )}
            </div>
          </section>
        )}

        {/* Features Section for non-logged-in users */}
        {!user && (
          <section className="py-20 bg-white/60 backdrop-blur-sm relative" style={{ zIndex: 35 }}>
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-16">
                <h2 className="text-3xl sm:text-4xl font-bold text-zinc-900 mb-4">
                  Everything you need to master system design
                </h2>
                <p className="text-lg text-zinc-600 max-w-2xl mx-auto">
                  From interactive practice to real-world scenarios, we've got you covered
                </p>
              </div>
              <div className="relative" style={{ zIndex: 40 }}>
                <InteractiveFeatureCards />
              </div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        {!user && (
          <section className="py-20 relative" style={{ zIndex: 30 }}>
            <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
              <div className="bg-white/80 backdrop-blur-sm border border-purple-100 rounded-2xl p-12 shadow-xl">
                <h2 className="text-3xl sm:text-4xl font-bold text-zinc-900 mb-6">
                  Ready to ace your next system design interview?
                </h2>
                <p className="text-lg text-zinc-600 mb-10 max-w-2xl mx-auto leading-relaxed">
                  Join thousands of engineers who have improved their system design skills with our platform
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg" className="bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800 text-white px-8 py-4 font-semibold shadow-lg hover:shadow-xl transition-all duration-200">
                    <Link to="/register">Get Started Free</Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="border-2 border-purple-200 text-purple-700 hover:bg-purple-50 px-8 py-4 font-semibold transition-all duration-200">
                    <Link to="/login">Sign In</Link>
                  </Button>
                </div>
              </div>
            </div>
          </section>
        )}
      </div>

      {/* Footer - Lower z-index */}
      <footer className={`border-t border-purple-100 bg-white/80 backdrop-blur-sm relative transition-all duration-700 delay-300 ${
        user ? 'translate-y-0 opacity-100' : (revealStage >= 5 ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0')
      }`} style={{ zIndex: 20 }}>
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center text-zinc-500">
            <p>&copy; 2024 Layrs. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Global CSS to ensure React Flow components render properly */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Ensure React Flow edges are visible */
          .react-flow__edge {
            z-index: 1000 !important;
            pointer-events: all !important;
          }
          
          .react-flow__edge-path {
            stroke-width: 2 !important;
            stroke-linecap: round !important;
            stroke-linejoin: round !important;
          }
          
          /* Ensure React Flow containers have proper stacking */
          .react-flow {
            position: relative !important;
            z-index: 100 !important;
          }
          
          /* Improve edge visibility on desktop */
          @media (min-width: 1024px) {
            .react-flow__edge-path {
              stroke-width: 3 !important;
              filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1)) !important;
            }
          }
        `
      }} />
    </div>
  );
};

export default CleanLandingPage;
