import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"

interface FeedbackItem {
  date: string;
  count: number;
  ratings: number[];
}

const FeedbackAnalytics: React.FC = () => {
  const [feedbackData, setFeedbackData] = useState<FeedbackItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeedbackData = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/feedback');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        setFeedbackData(data);
      } catch (e: any) {
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchFeedbackData();
  }, []);

  if (loading) {
    return <div>Loading feedback data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

        // Calculate statistics
        const totalRatings = feedbackData.reduce((sum: number, item: any) => sum + (item.count || 0), 0);
        const ratingBreakdown = feedbackData.reduce((acc: Record<number, number>, item: any) => {
          if (item.ratings && Array.isArray(item.ratings)) {
            item.ratings.forEach((rating: any) => {
              const ratingValue = rating.rating || rating;
              acc[ratingValue] = (acc[ratingValue] || 0) + 1;
            });
          }
          return acc;
        }, {});

  const chartData = Object.keys(ratingBreakdown).map(key => ({
    rating: key,
    count: ratingBreakdown[key]
  }));

  return (
    <div className="container mx-auto p-4">
      <Card className="shadow-md rounded-md">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Feedback Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          {feedbackData.length > 0 ? (
            <ResponsiveContainer width="100%" height={400}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="rating" name="Rating" />
                <YAxis name="Count" />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" fill="#8884d8" name="Rating Count" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="text-gray-500">No feedback data available.</div>
          )}
        </CardContent>
      </Card>
      <div className="mt-4">
        <h3 className="text-md font-semibold">Total Ratings: {totalRatings}</h3>
      </div>
    </div>
  );
};

export default FeedbackAnalytics;
