import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Node as XYFlowNode, ReactFlowInstance, ReactFlowProvider } from '@xyflow/react';
import { useParams, useNavigate } from 'react-router-dom';

import Header from '@/components/Header';
import DesignContextSidebar from '@/components/DesignContextSidebar';
import DiagramEditor from '@/components/DiagramEditor';
import ComponentMetadataSidebar from '@/components/sidebar/ComponentMetadataSidebar';

import TopComponentPalette from '@/components/TopComponentPalette';
import BestSubmissionOverlay from '@/components/BestSubmissionOverlay';

import { componentTypes } from '@/components/ComponentTypes';
import { AssessmentResult } from '@/services/assessmentService';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

// Import our hooks
import { useDesignState } from '@/hooks/useDesignState.tsx';
import { useDesignLoader } from '@/hooks/useDesignLoader';
import { useSimpleManualSave } from '@/hooks/useManualSave';
import { useAutoSaveIntegration } from '@/hooks/useAutoSaveIntegration';

import '@/utils/autoSaveDebugger'; // Import debugger for browser console access
import { useNodeManager } from '@/components/NodeManager';
import SaveStatusIndicator from '@/components/SaveStatusIndicator';
import { SaveStatus } from '@/hooks/useSmartAutoSave';
import { useQuestions } from '@/contexts/QuestionsContext';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { loadBestDesignFromSupabase, getBestScore } from '@/services/bestDesignService';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import { toast } from 'sonner';
import TutorialOverlay from '@/components/TutorialOverlay';
import FloatingFeedbackButton from '@/components/FloatingFeedbackButton';
import { saveAssessmentResult, loadAssessmentResult, removeAssessmentResult } from '@/utils/assessmentPersistence';
import { useAssessmentAnalytics } from '@/hooks/useAnalytics';
import { processLoadedEdges } from '@/utils/edgeProcessing';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

interface IndexProps {
  resetCanvas?: boolean;
}

// Helper function to split requirements like ProblemContext
function splitRequirements(question) {
  if (
    question.functional_requirements && question.functional_requirements.length > 0 &&
    question.non_functional_requirements && question.non_functional_requirements.length > 0
  ) {
    return {
      assumptions: question.functional_requirements.join('\n'),
      constraints: question.non_functional_requirements.join('\n'),
    };
  }
  // Fallback: legacy requirements split by keywords
  const nfrKeywords = [
    'scalability', 'performance', 'security', 'reliability',
    'availability', 'maintainability', 'usability', 'efficiency',
    'fast', 'secure', 'reliable', 'available', 'maintainable', 'usable',
    'efficient', 'scale', 'latency', 'throughput', 'response time',
    'concurrent', 'simultaneously', 'load', 'traffic', 'volume'
  ];
  const functionalReqs = [];
  const nonFunctionalReqs = [];
  (question.requirements || []).forEach(req => {
    const lowerReq = req.toLowerCase();
    if (nfrKeywords.some(keyword => lowerReq.includes(keyword))) {
      nonFunctionalReqs.push(req);
    } else {
      functionalReqs.push(req);
    }
  });
  return {
    assumptions: functionalReqs.join('\n'),
    constraints: nonFunctionalReqs.join('\n'),
  };
}

const Index: React.FC<IndexProps> = ({ resetCanvas = false }) => {
  const { questionId } = useParams<{ questionId: string }>();
  const navigate = useNavigate();

  const [selectedComponent, setSelectedComponent] = useState<XYFlowNode | null>(null);
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState<boolean>(false);
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState<boolean>(false);
  const [hasBestDesign, setHasBestDesign] = useState<boolean>(false);
  const [showTutorial, setShowTutorial] = useState<boolean>(false);
  const [showBestSubmissionOverlay, setShowBestSubmissionOverlay] = useState<boolean>(false);

  const [assessmentPanelDismissed, setAssessmentPanelDismissed] = useState<boolean>(false);

  // Panel sizing state
  const [leftPanelWidth, setLeftPanelWidth] = useState<number>(320); // 320px default
  const [rightPanelWidth, setRightPanelWidth] = useState<number>(320); // 320px default

  // Ref for the left panel DOM element to track its width
  const leftPanelDomRef = useRef<HTMLDivElement>(null);
  // Ref for the right panel DOM element to track its width
  const rightPanelDomRef = useRef<HTMLDivElement>(null);

  // Get user for best design functionality
  const { user } = useAuth();

  // Get subscription status for access control
  const { canAccessQuestion, hasActiveSubscription } = useSubscription();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [accessChecked, setAccessChecked] = useState(false);

  // Initialize assessment analytics
  const { startAssessment, recordAssessment } = useAssessmentAnalytics();

  // Get questions context to set current question
  const { getQuestionById, setCurrentQuestion, questions, loading } = useQuestions();

  // Check access to the question
  useEffect(() => {
    const checkAccess = async () => {
      if (questionId) {
        try {
          const questionIdNum = parseInt(questionId, 10);
          if (isNaN(questionIdNum)) {
            debugError('Invalid question ID:', questionId);
            setHasAccess(false);
            setAccessChecked(true);
            navigate('/questions');
            return;
          }

          const canAccess = await canAccessQuestion(questionIdNum);
          setHasAccess(canAccess);
          setAccessChecked(true);

          if (!canAccess) {
            debugWarn(`User does not have access to design for question ${questionId}`);
            // Redirect to question detail page which will show access denied
            navigate(`/questions/${questionId}`);
            return;
          }
        } catch (error) {
          debugError('Error checking question access:', error);
          setHasAccess(false);
          setAccessChecked(true);
          navigate('/questions');
          return;
        }
      } else {
        // Free canvas mode - always accessible
        setHasAccess(true);
        setAccessChecked(true);
      }
    };

    checkAccess();
  }, [questionId, canAccessQuestion, navigate]);

  // Set current question based on URL parameter - wait for questions to be loaded first and access to be checked
  useEffect(() => {
    debugLog('Index.tsx - useEffect for setting current question:', {
      questionId: questionId,
      questionsLoading: loading,
      questionsCount: questions.length,
      hasGetQuestionById: typeof getQuestionById === 'function',
      accessChecked: accessChecked,
      hasAccess: hasAccess
    });

    // Don't try to load question if questions are still loading from Supabase or access hasn't been checked
    if (loading || !accessChecked) {
      debugLog('Index.tsx - Questions still loading or access not checked, waiting...');
      return;
    }

    // Don't load question if user doesn't have access
    if (questionId && hasAccess === false) {
      debugLog('Index.tsx - User does not have access to this question');
      return;
    }

    if (questionId && hasAccess) {
      const loadQuestion = async () => {
        try {
          const questionIdNum = parseInt(questionId, 10);
          if (isNaN(questionIdNum)) {
            debugError('Invalid question ID:', questionId);
            return;
          }

          const question = await getQuestionById(questionIdNum);
          debugLog('Index.tsx - Question lookup result:', {
            questionId: questionId,
            foundQuestion: question,
            questionTitle: question?.title
          });

          if (question) {
            debugLog('Index.tsx - Setting current question from URL parameter:', question);
            setCurrentQuestion(question);
          } else {
            debugWarn('Index.tsx - Question not found for ID:', questionId);
          }
        } catch (error) {
          debugError('Index.tsx - Error loading question:', error);
        }
      };

      loadQuestion();
    } else {
      debugLog('Index.tsx - No questionId, clearing current question');
      // Clear current question for free canvas
      setCurrentQuestion(null);
    }
  }, [questionId, loading, questions.length, getQuestionById, setCurrentQuestion, accessChecked, hasAccess]); // Depend on questions being loaded and access being checked

  // Store the setResetting function from DiagramEditor
  const setResettingRef = useRef<((resetting: boolean) => void) | null>(null);

  // State for save status
  const [saveStatus, setSaveStatus] = useState<SaveStatus>(SaveStatus.IDLE);
  const [lastSaved, setLastSaved] = useState<Date | undefined>();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Callback to receive setResetting function from DiagramEditor
  const handleSetResetting = useCallback((setResetting: (resetting: boolean) => void) => {
    setResettingRef.current = setResetting;
  }, []);

  // Use our custom hook to manage design state
  const designState = useDesignState();

  // Get questions context to clear current question in free canvas mode
  const { currentQuestion: globalCurrentQuestion } = useQuestions();

  // Extract necessary values from design state
  const {
    nodes, setNodes,
    edges, setEdges,
    userJourneys, setUserJourneys,
    assumptions, setAssumptions,
    constraints, setConstraints,
    isAssessing, setIsAssessing,
    shownToasts,
    originalDesignRef,
    modifiedNodesRef,
    deletedNodesRef,
    currentQuestion,
    currentDesign,
    setCurrentDesign,
    saveDesign,
    loadDesign,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    mergeNodesAndEdges
  } = designState;

  // Callback to reset user inputs
  const handleResetUserInputs = useCallback(() => {
    setUserJourneys('');
    setAssumptions('');
    setConstraints('');
  }, [setUserJourneys, setAssumptions, setConstraints]);

  // Use node manager for selection and deletion
  const nodeManager = useNodeManager({
    reactFlowInstance,
    setSelectedComponent,
    selectedComponent,
    setRightPanelCollapsed
  });

  // Custom node selection handler that also sets panel size
  const handleNodeSelect = useCallback((node: XYFlowNode | null, openRightPanel: boolean = false) => {
    nodeManager.handleNodeSelect(node, openRightPanel);
    // With overlay layout, no need to adjust panel sizes
    if (!node) {
      // When clicking on canvas (node is null), dismiss assessment panel and close right panel
      setAssessmentPanelDismissed(true);
      setRightPanelCollapsed(true);
    }
  }, [nodeManager]);

  // Add a ref to track user-initiated deletions for sidebar
  const sidebarUserInitiatedDeletion = useRef(false);

  // Updated node deletion handler for sidebar
  const handleDeleteNode = useCallback((nodeId: string) => {
    sidebarUserInitiatedDeletion.current = true;
    setNodes(nds => nds.filter(node => node.id !== nodeId));
    setEdges(eds => eds.filter(edge => edge.source !== nodeId && edge.target !== nodeId));
    setSelectedComponent(null);
  }, [setNodes, setEdges, setSelectedComponent]);

  // Panel sizes are now fixed with the overlay layout

  // Reset canvas effect when resetCanvas is true
  useEffect(() => {
    if (resetCanvas && reactFlowInstance) {
      debugLog("Resetting canvas as requested by resetCanvas prop");

      // Disable node tracking during reset to prevent deletion events
      if (setResettingRef.current) {
        setResettingRef.current(true);
      }

      // Clear tracking refs first to prevent deletion events from being processed
      modifiedNodesRef.current.clear();
      deletedNodesRef.current.clear();
      originalDesignRef.current = null;

      // Use ReactFlow's direct methods to avoid triggering change events
      reactFlowInstance.setNodes([]);
      reactFlowInstance.setEdges([]);

      // Clear state
      setNodes([]);
      setEdges([]);
      setUserJourneys('');
      setAssumptions('');
      setConstraints('');
      setSelectedComponent(null);
      setAssessmentResult(null);
      
      // Clear assessment result from localStorage
      if (designQuestionId) {
        removeAssessmentResult(designQuestionId, 'question');
      }

      // Clear design data from localStorage to prevent restoration on tab switch
      if (designQuestionId) {
        const designKey = `layrs-design-${contextType}-${designQuestionId}`;
        localStorage.removeItem(designKey);
        debugLog(`🗑️ Removed design from localStorage: ${designKey}`);
      }

      // Re-enable node tracking after reset
      setTimeout(() => {
        if (setResettingRef.current) {
          setResettingRef.current(false);
        }
      }, 100);
    }
  }, [resetCanvas, reactFlowInstance, setNodes, setEdges, setUserJourneys, setAssumptions, setConstraints, setSelectedComponent, originalDesignRef, modifiedNodesRef, deletedNodesRef]);

  // Determine the context type based on the route
  const contextType = questionId ? 'question' : 'free';

  // For free canvas, use a dedicated ID that's completely separate from any question ID
  // For questions, use the question ID
  const contextId = questionId || 'free-canvas';

  // Use a dedicated question ID for free canvas to ensure it doesn't use any question's design
  const designQuestionId = questionId || 'free-canvas';

  // Clear current question when in free canvas mode
  useEffect(() => {
    if (!questionId && globalCurrentQuestion) {
      debugLog('Clearing current question for free canvas mode');
      setCurrentQuestion(null);
    }
  }, [questionId, globalCurrentQuestion]); // Remove setCurrentQuestion from dependencies

  // Function to check if user has a best design for this question/context
  const checkBestDesign = useCallback(async () => {
    if (user && designQuestionId) {
      try {
        const bestScore = await getBestScore(
          user.id,
          designQuestionId,
          contextType as DesignContextCategory,
          contextId
        );
        setHasBestDesign(bestScore !== null);
      } catch (error) {
        debugError('Error checking best design:', error);
        setHasBestDesign(false);
      }
    } else {
      setHasBestDesign(false);
    }
  }, [user, designQuestionId, contextType, contextId]);

  // Check best design status on mount and when dependencies change
  useEffect(() => {
    checkBestDesign();
  }, [checkBestDesign]);

  // Use our custom hooks for loading and auto-saving designs
  useDesignLoader({
    currentQuestionId: designQuestionId,
    contextType,
    contextId,
    loadDesign,
    setCurrentDesign,
    setUserJourneys,
    setAssumptions,
    setConstraints,
    setNodes,
    setEdges,
    shownToasts,
    originalDesignRef,
    modifiedNodesRef,
    deletedNodesRef,
    reactFlowInstance,
    currentDesign,
    nodes,
    edges
  });

  // Handle save status changes
  const handleStatusChange = (status: SaveStatus, lastSavedDate?: Date) => {
    setSaveStatus(status);
    if (lastSavedDate) {
      setLastSaved(lastSavedDate);
    }
  };

  // Use the simple manual save hook
  const { saveNow } = useSimpleManualSave({
    currentQuestionId: designQuestionId,
    contextType,
    contextId,
    reactFlowInstance,
    userJourneys,
    assumptions,
    constraints,
    saveDesign,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    originalDesignRef,
    deletedNodesRef,
    onStatusChange: handleStatusChange
  });

  // Use auto-save integration (disable temporarily if canvas is being reset)
  const autoSave = useAutoSaveIntegration({
    currentQuestionId: designQuestionId,
    contextType,
    contextId,
    reactFlowInstance,
    userJourneys,
    assumptions,
    constraints,
    saveDesign,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    onStatusChange: handleStatusChange,
    isOnline,
    enabled: true // Re-enable auto-save after fixing questions table issue
  });

  // Handle canvas changes for autosave
  const handleCanvasChange = useCallback((changeType: 'canvas' | 'major') => {
    autoSave.handleCanvasChange(changeType);
  }, [autoSave]);

  // Expose immediate auto-save trigger globally for edge deletion
  useEffect(() => {
    (window as any).__triggerImmediateAutoSave = () => {
      debugLog('🚀 Immediate auto-save triggered via global function');
      autoSave.handleCanvasChange('major');
    };
    
    return () => {
      delete (window as any).__triggerImmediateAutoSave;
    };
  }, [autoSave]);



  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Automatically load any existing design on mount (no prompts)
  useEffect(() => {
    if (designQuestionId && autoSave.checkForUnsavedChanges && reactFlowInstance) {
      const unsaved = autoSave.checkForUnsavedChanges();
      if (unsaved) {
        // Automatically load the design without prompting
        debugLog('🔄 Auto-loading existing design from localStorage');

        // Load the unsaved design data into the canvas
        setNodes(unsaved.nodes || []);

        // Process loaded edges to add the onChange handler for label editing
        const processedEdges = processLoadedEdges(unsaved.edges || [], setEdges);
        setEdges(processedEdges);

        setUserJourneys(unsaved.userJourneys || '');
        setAssumptions(unsaved.assumptions || '');
        setConstraints(unsaved.constraints || '');

        // Update React Flow instance
        reactFlowInstance.setNodes(unsaved.nodes || []);
        reactFlowInstance.setEdges(processedEdges);

        // Update tracking references
        const now = Date.now();
        originalDesignRef.current = {
          nodes: unsaved.nodes || [],
          edges: unsaved.edges || [],
          timestamp: now
        };
        deletedNodesRef.current.clear();
        modifiedNodesRef.current.clear();
        setHasUnsavedChanges(false); // Mark as saved since we just loaded it
      }
    }
  }, [designQuestionId, autoSave.checkForUnsavedChanges, reactFlowInstance]);

  // Load assessment result when question changes
  useEffect(() => {
    if (designQuestionId) {
      const savedAssessment = loadAssessmentResult(designQuestionId, 'question');
      if (savedAssessment) {
        setAssessmentResult(savedAssessment);
        // Don't auto-open panel when loading saved assessment - only when user clicks "View Assessment"
      } else {
        setAssessmentResult(null);
      }
    }
  }, [designQuestionId]);

  // Determine if right panel should be visible
  const showRightPanel = (selectedComponent !== null || (assessmentResult !== null && !assessmentPanelDismissed)) && !rightPanelCollapsed;

  // Determine if left panel should be visible
  const showLeftPanel = !leftPanelCollapsed;



  // Panel sizes are fixed with overlay layout

  // Handle left panel tab changes without resizing
  const handleLeftPanelTabChange = useCallback((_tab: string) => {
    // Panel width remains constant with overlay layout
  }, []);

  // Handle assessment completion
  const handleAssessmentComplete = (result: AssessmentResult | null) => {
    setAssessmentResult(result);
    
    // Save assessment result to localStorage
    if (result && designQuestionId) {
      saveAssessmentResult(result, designQuestionId, 'question');
    }
    
    // Clear selected component to show assessment results instead of component properties
    setSelectedComponent(null);
    // Reset dismissal state when new assessment completes
    setAssessmentPanelDismissed(false);
    // Auto-expand right panel when assessment completes
    setRightPanelCollapsed(false);
    // Panel sizes are fixed with the overlay layout
  };

  // Handle viewing assessment results
  const handleViewAssessment = () => {
    if (assessmentResult) {
      // Clear selected component and show assessment in right panel
      setSelectedComponent(null);
      // Reset dismissal state to allow assessment panel to show
      setAssessmentPanelDismissed(false);
      setRightPanelCollapsed(false);
    }
  };

  // Handle loading best design
  const handleLoadBestDesign = async () => {
    if (!user || !designQuestionId || !reactFlowInstance) {
      debugWarn('Cannot load best design: missing user, questionId, or reactFlowInstance');
      return;
    }

    try {
      const bestDesignData = await loadBestDesignFromSupabase(
        user.id,
        designQuestionId,
        contextType as DesignContextCategory,
        contextId
      );

      if (bestDesignData) {
        const { design, score } = bestDesignData;

        // Mark as resetting operation to allow deletions during design loading
        if (setResettingRef.current) {
          debugLog('🔄 Setting resetting=true for best design loading');
          setResettingRef.current(true);
        }

        // Load the design data into the canvas
        setNodes(design.nodes || []);

        // Process loaded edges to add the onChange handler for label editing
        const processedEdges = processLoadedEdges(design.edges || [], setEdges);
        setEdges(processedEdges);

        // Re-enable deletion protection after a short delay
        setTimeout(() => {
          if (setResettingRef.current) {
            debugLog('🔄 Setting resetting=false after best design loading');
            setResettingRef.current(false);
          }
        }, 200);

        setUserJourneys(design.userJourneys || '');
        setAssumptions(design.assumptions || '');
        setConstraints(design.constraints || '');

        // Update React Flow instance
        reactFlowInstance.setNodes(design.nodes || []);
        reactFlowInstance.setEdges(processedEdges);

        // Update tracking references
        const now = Date.now();
        originalDesignRef.current = {
          nodes: design.nodes || [],
          edges: design.edges || [],
          timestamp: now
        };
        deletedNodesRef.current.clear();
        modifiedNodesRef.current.clear();
        setHasUnsavedChanges(false);

        toast.success(`🏆 Best design loaded! Score: ${score}/10`);
      } else {
        toast.error('No best design found');
      }
    } catch (error) {
      debugError('Error loading best design:', error);
      toast.error('Failed to load best design');
    }
  };

  // Handle showing tutorial
  const handleShowTutorial = () => {
    setShowTutorial(true);
  };

  // Handle showing best submission overlay
  const handleShowBestSubmission = () => {
    setShowBestSubmissionOverlay(true);
  };



  // Handle tutorial step changes to provide context
  const handleTutorialStepChange = (stepId: string) => {
    switch (stepId) {
      case 'properties-panel':
        // Step 5: Open right panel and select a component if available
        if (nodes.length > 0) {
          // Select the first node to show properties panel
          setSelectedComponent(nodes[0]);
          setRightPanelCollapsed(false);
        } else {
          // If no nodes, just open the panel
          setRightPanelCollapsed(false);
        }
        break;

      case 'context-inputs':
        // Step 6: Switch to context tab in left panel
        handleLeftPanelTabChange('context');
        break;

      case 'welcome':
      case 'component-palette':
        // Steps 1-2: Components are now in the vertical palette, so switch to context tab
        handleLeftPanelTabChange('context');
        break;

      default:
        // No special action needed for other steps
        break;
    }
  };

  // In handleStartAssessment, compute requirements on-demand
  const handleStartAssessment = useCallback(async () => {
    if (!reactFlowInstance) {
      toast.error("Canvas not ready. Please wait a moment and try again.");
      return;
    }
    if (isAssessing) {
      debugLog("Assessment already in progress, ignoring duplicate request");
      return;
    }
    debugLog("Starting assessment...");
    setIsAssessing(true);
    try {
      const AssessmentHandler = (await import('@/components/AssessmentHandler')).default;
      if (!AssessmentHandler) {
        throw new Error("Failed to load AssessmentHandler module");
      }
      // Compute requirements from currentQuestion at assessment time
      const { assumptions, constraints } = splitRequirements(currentQuestion || {});
      const handler = AssessmentHandler({
        reactFlowInstance,
        userJourneys,
        assumptions,
        constraints,
        currentQuestion,
        onAssessmentComplete: handleAssessmentComplete,
        setIsAssessing,
        user: user,
        questionId: designQuestionId,
        contextType: contextType as DesignContextCategory,
        contextId: contextId,
        onBestDesignSaved: checkBestDesign,
        startAssessment,
        recordAssessment
      });
      if (handler && typeof handler.handleStartAssessment === 'function') {
        debugLog("Calling handler.handleStartAssessment...");
        await handler.handleStartAssessment();
      } else {
        throw new Error("AssessmentHandler did not return a valid handler object");
      }
    } catch (error) {
      debugError("Error in handleStartAssessment:", error);
      toast.error("Failed to start assessment. Please try again.");
      setIsAssessing(false);
    }
  }, [
    reactFlowInstance,
    isAssessing,
    userJourneys,
    currentQuestion,
    handleAssessmentComplete,
    user,
    designQuestionId,
    contextType,
    contextId,
    checkBestDesign,
    startAssessment,
    recordAssessment
  ]);

  // Right panel is now controlled by showRightPanel state

  // Handle closing the right panel
  const handleCloseRightPanel = () => {
    // If we're showing assessment results (no selected component), mark assessment as dismissed
    if (assessmentResult && !selectedComponent) {
      setAssessmentPanelDismissed(true);
    }
    // Clear selected component to close component properties panel
    setSelectedComponent(null);
    // Collapse the panel
    setRightPanelCollapsed(true);
  };

  // Toggle left panel visibility
  const toggleLeftPanel = () => {
    setLeftPanelCollapsed(!leftPanelCollapsed);
  };

  return (
    <div className="flex flex-col h-screen w-full bg-white">
      <Header
        onStartAssessment={handleStartAssessment}
        isAssessing={isAssessing}
        reactFlowInstance={reactFlowInstance}
        assessmentResult={assessmentResult}
        onViewAssessment={handleViewAssessment}
        onLoadBestDesign={handleLoadBestDesign}
        hasBestDesign={hasBestDesign}
        onShowTutorial={handleShowTutorial}
        onResetUserInputs={handleResetUserInputs}
        onShowBestSubmission={handleShowBestSubmission}
        setResetting={setResettingRef.current}
        userContext={{
          problem: currentQuestion?.title || 'System Design Canvas',
          assumptions: assumptions ? assumptions.split('\n').filter(a => a.trim()) : [],
          constraints: constraints ? constraints.split('\n').filter(c => c.trim()) : [],
          userJourneys: userJourneys
        }}
        saveStatusIndicator={
          <SaveStatusIndicator
            status={saveStatus}
            lastSaved={lastSaved}
            isOnline={isOnline}
            onClick={saveNow}
          />
        }
      />

      {/* Tutorial Overlay */}
      <TutorialOverlay
        isOpen={showTutorial}
        onClose={() => setShowTutorial(false)}
        onStepChange={handleTutorialStepChange}
        isGuidedMode={false}
      />

      {/* Best Submission Overlay */}
      {questionId && (
        <BestSubmissionOverlay
          isOpen={showBestSubmissionOverlay}
          onClose={() => setShowBestSubmissionOverlay(false)}
          onLoadDesign={handleLoadBestDesign}
          questionId={designQuestionId}
          contextType={contextType as DesignContextCategory}
          contextId={contextId}
        />
      )}



      <div className="flex-1 overflow-hidden relative">
        {/* Left Panel - Resizable overlay when open */}
        {showLeftPanel && (
          <div
            className="absolute left-0 top-0 h-full bg-white border-r border-gray-200 shadow-lg z-40 resize-x overflow-hidden"
            style={{
              width: `${leftPanelWidth}px`,
              minWidth: '250px',
              maxWidth: '600px'
            }}
          >
            <div ref={leftPanelDomRef} className="h-full flex flex-col component-palette">
              <div className="flex-1 overflow-hidden context-inputs">
                <DesignContextSidebar
                  userJourneys={userJourneys}
                  assumptions={assumptions}
                  constraints={constraints}
                  onUserJourneysChange={setUserJourneys}
                  onAssumptionsChange={setAssumptions}
                  onConstraintsChange={setConstraints}
                  currentQuestion={currentQuestion}
                  reactFlowInstance={reactFlowInstance}
                  contextType={contextType}
                  contextId={contextId}
                  onActiveTabChange={handleLeftPanelTabChange}
                />
              </div>
              {/* Left panel collapse button */}
              <Button
                variant="outline"
                size="sm"
                onClick={toggleLeftPanel}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 bg-white shadow-md border-gray-200"
                aria-label="Collapse panel"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              {/* Resize handle */}
              <div
                className="left-panel-resize-handle"
                onMouseDown={(e) => {
                  e.preventDefault();
                  const startX = e.clientX;
                  const startWidth = leftPanelWidth;

                  const handleMouseMove = (e: MouseEvent) => {
                    const newWidth = Math.max(250, Math.min(600, startWidth + (e.clientX - startX)));
                    setLeftPanelWidth(newWidth);
                  };

                  const handleMouseUp = () => {
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                    document.body.style.cursor = '';
                    document.body.style.userSelect = '';
                  };

                  document.body.style.cursor = 'col-resize';
                  document.body.style.userSelect = 'none';
                  document.addEventListener('mousemove', handleMouseMove);
                  document.addEventListener('mouseup', handleMouseUp);
                }}
              />
            </div>
          </div>
        )}

        {/* Left panel expand button - only show when panel is closed */}
        {!showLeftPanel && (
          <Button
            variant="outline"
            size="sm"
            onClick={toggleLeftPanel}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 bg-white shadow-md border-gray-200"
            aria-label="Expand panel"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        <ResizablePanelGroup direction="horizontal" className="w-full h-full">

        {/* Center Canvas - Resizable */}
        <ResizablePanel
          defaultSize={showRightPanel ? (showLeftPanel ? 50 : 75) : (showLeftPanel ? 75 : 100)}
          className="h-full relative flex flex-col bg-white"
        >
          {/* Top Component Palette - positioned at top, moves with left panel */}
          <div
            className="transition-all duration-300 ease-out flex items-center justify-between p-2 border-b border-gray-200"
            style={{
              marginLeft: showLeftPanel ? `${leftPanelWidth}px` : '0px'
            }}
          >
            <TopComponentPalette
              showComponentPalette={true}
            />
          </div>

          <div className="react-flow flex-1 relative">
            <DiagramEditor
              onNodeSelect={handleNodeSelect}
              setReactFlowInstance={setReactFlowInstance}
              modifiedNodesRef={modifiedNodesRef}
              deletedNodesRef={deletedNodesRef}
              onSetResetting={handleSetResetting}
              selectedComponent={selectedComponent}
              onCanvasChange={handleCanvasChange}
              sidebarUserInitiatedDeletion={sidebarUserInitiatedDeletion}
            />
          </div>
        </ResizablePanel>

        {/* Right Panel - Resizable when open */}
        {showRightPanel && (
          <>
            <ResizableHandle withHandle />
            <ResizablePanel
              defaultSize={25}
              minSize={20}
              maxSize={50}
              className="h-full properties-panel"
              onResize={(size) => {
                // Convert percentage to pixels (approximate)
                const containerWidth = window.innerWidth;
                setRightPanelWidth((size / 100) * containerWidth);
              }}
            >
              <div ref={rightPanelDomRef} className="relative h-full bg-white border-l border-gray-200">
                <ComponentMetadataSidebar
                  selectedComponent={selectedComponent}
                  componentTypes={componentTypes}
                  assessmentResult={assessmentResult}
                  onStartAssessment={handleStartAssessment}
                  onDeleteNode={handleDeleteNode}
                  onClose={handleCloseRightPanel}
                  onCanvasChange={handleCanvasChange}
                />
              </div>
            </ResizablePanel>
          </>
        )}
        </ResizablePanelGroup>
      </div>

      {/* Floating Feedback Button */}
      <FloatingFeedbackButton />
    </div>
  );
};

// Wrapper component that provides ReactFlowProvider
const IndexWithReactFlow: React.FC<IndexProps> = (props) => {
  return (
    <ReactFlowProvider>
      <Index {...props} />
    </ReactFlowProvider>
  );
};

export default IndexWithReactFlow;
