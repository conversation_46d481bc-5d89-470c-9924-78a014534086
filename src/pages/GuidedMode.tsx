import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ImperativePanelHandle } from 'react-resizable-panels';
import Header from '@/components/Header';
import CourseStepsPanel from '@/components/course/CourseStepsPanel';
import CourseSelector from '@/components/course/CourseSelector';
import GuidedModeLeftSidebar from '@/components/course/GuidedModeLeftSidebar';
import DiagramEditor from '@/components/DiagramEditor';
import ComponentMetadataSidebar from '@/components/sidebar/ComponentMetadataSidebar';
import { useCourse } from '@/contexts/CourseContext';
import { useNodeManager } from '@/components/NodeManager';
import { useDesignState } from '@/hooks/useDesignState';
import { useDesignLoader } from '@/hooks/useDesignLoader';
import { useSimpleManualSave } from '@/hooks/useManualSave';
import { useAutoSaveIntegration } from '@/hooks/useAutoSaveIntegration';

import { SaveStatus } from '@/hooks/useSmartAutoSave';
import SaveStatusIndicator from '@/components/SaveStatusIndicator';
import { Node as XYFlowNode, ReactFlowInstance, ReactFlowProvider } from '@xyflow/react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { componentTypes } from '@/components/ComponentTypes';
import { toast } from 'sonner';
import { AssessmentResult } from '@/services/assessmentService';
import { DesignContextCategory } from '@/contexts/DesignContext';
import { loadBestDesignFromSupabase, getBestScore } from '@/services/bestDesignService';
import { useAuth } from '@/contexts/AuthContext';
import TutorialOverlay from '@/components/TutorialOverlay';
import BestSubmissionOverlay from '@/components/BestSubmissionOverlay';
import { useAssessmentAnalytics } from '@/hooks/useAnalytics';
import { processLoadedEdges } from '@/utils/edgeProcessing';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';
import { saveAssessmentResult, loadAssessmentResult, removeAssessmentResult } from '@/utils/assessmentPersistence';

const GuidedMode: React.FC = () => {
  const { courseId } = useParams<{ courseId?: string }>();
  const navigate = useNavigate();
  const [selectedComponent, setSelectedComponent] = React.useState<XYFlowNode | null>(null);
  const [reactFlowInstance, setReactFlowInstance] = React.useState<ReactFlowInstance | null>(null);
  const [showRightPanel, setShowRightPanel] = useState(false);
  const [isAssessing, setIsAssessing] = useState(false);
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null);
  const [leftPanelActiveTab, setLeftPanelActiveTab] = useState<string>("course");
  const [hasBestDesign, setHasBestDesign] = useState<boolean>(false);
  const [showTutorial, setShowTutorial] = useState<boolean>(false);
  const [showBestSubmissionOverlay, setShowBestSubmissionOverlay] = useState<boolean>(false);

  // Ref for the left panel to programmatically resize it
  const leftPanelRef = useRef<ImperativePanelHandle>(null);
  // Ref for the right panel DOM element to track its width
  const rightPanelDomRef = useRef<HTMLDivElement>(null);

  // Get user for best design functionality
  const { user } = useAuth();

  // Initialize assessment analytics
  const { startAssessment, recordAssessment } = useAssessmentAnalytics();

  const {
    isGuidedMode,
    currentCourse,
    setIsGuidedMode,
    getCurrentStep,
    startCourse,
    courses
  } = useCourse();

  const designState = useDesignState();
  const {
    modifiedNodesRef,
    deletedNodesRef,
    loadDesign,
    saveDesign,
    setCurrentDesign,
    currentDesign,
    setNodes,
    setEdges,
    nodes,
    edges,
    setUserJourneys,
    setAssumptions,
    setConstraints,
    userJourneys,
    assumptions,
    constraints,
    hasUnsavedChanges,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    originalDesignRef,
    shownToasts
  } = designState;

  // Callback to reset user inputs
  const handleResetUserInputs = useCallback(() => {
    setUserJourneys('');
    setAssumptions('');
    setConstraints('');
  }, [setUserJourneys, setAssumptions, setConstraints]);

  // Initialize the course from URL parameter if available
  useEffect(() => {
    if (courseId && courses && !isGuidedMode && !currentCourse) {
      const course = courses.find(c => c.id === courseId);
      if (course) {
        startCourse(courseId);
      } else {
        toast.error('Course not found');
        navigate('/guided');
      }
    }
  }, [courseId, courses, isGuidedMode, currentCourse, startCourse, navigate]);

  // Use context-specific design loading and saving
  const contextType = 'course';
  // Use the current course ID if available, otherwise fall back to courseId from URL
  // This prevents switching to 'course-selection' during course initialization
  const effectiveCourseId = currentCourse?.id || courseId || 'course-selection';
  const contextId = effectiveCourseId;

  // Use our custom hooks for loading and auto-saving designs
  useDesignLoader({
    currentQuestionId: effectiveCourseId,
    contextType,
    contextId,
    loadDesign,
    setCurrentDesign,
    setUserJourneys,
    setAssumptions,
    setConstraints,
    setNodes,
    setEdges,
    shownToasts,
    originalDesignRef,
    modifiedNodesRef,
    deletedNodesRef,
    reactFlowInstance,
    currentDesign,
    nodes,
    edges
  });

  // State for save status
  const [saveStatus, setSaveStatus] = useState<SaveStatus>(SaveStatus.IDLE);
  const [lastSaved, setLastSaved] = useState<Date | undefined>();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // Handle save status changes
  const handleStatusChange = (status: SaveStatus, lastSavedDate?: Date) => {
    setSaveStatus(status);
    if (lastSavedDate) {
      setLastSaved(lastSavedDate);
    }
  };

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Function to check if user has a best design for this course
  const checkBestDesign = useCallback(async () => {
    if (user && effectiveCourseId) {
      try {
        const bestScore = await getBestScore(
          user.id,
          effectiveCourseId,
          contextType as DesignContextCategory,
          contextId
        );
        setHasBestDesign(bestScore !== null);
      } catch (error) {
        debugError('Error checking best design:', error);
        setHasBestDesign(false);
      }
    } else {
      setHasBestDesign(false);
    }
  }, [user, effectiveCourseId, contextType, contextId]);

  // Check best design status on mount and when dependencies change
  useEffect(() => {
    checkBestDesign();
  }, [checkBestDesign]);

  // Load assessment result when question changes
  useEffect(() => {
    if (currentQuestion?.id) {
      const savedAssessment = loadAssessmentResult(currentQuestion.id, 'guided', courseId);
      if (savedAssessment) {
        setAssessmentResult(savedAssessment);
      } else {
        setAssessmentResult(null);
      }
    }
  }, [currentQuestion?.id, courseId]);

  // Use the simple manual save hook
  const { saveNow } = useSimpleManualSave({
    currentQuestionId: effectiveCourseId,
    contextType,
    contextId,
    reactFlowInstance,
    userJourneys,
    assumptions,
    constraints,
    saveDesign,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    originalDesignRef,
    deletedNodesRef,
    onStatusChange: handleStatusChange
  });

  // Use auto-save integration
  const autoSave = useAutoSaveIntegration({
    currentQuestionId: effectiveCourseId,
    contextType,
    contextId,
    reactFlowInstance,
    userJourneys,
    assumptions,
    constraints,
    saveDesign,
    setHasUnsavedChanges,
    mergeNodesAndEdges,
    onStatusChange: handleStatusChange,
    isOnline,
    enabled: true // Re-enable auto-save after fixing questions table issue
  });

  // Handle canvas changes for autosave
  const handleCanvasChange = useCallback((changeType: 'canvas' | 'major') => {
    autoSave.handleCanvasChange(changeType);
  }, [autoSave]);

  const nodeManager = useNodeManager({
    reactFlowInstance,
    setSelectedComponent,
    selectedComponent
  });

  // Custom node selection handler to control right panel visibility
  const handleNodeSelect = (node: XYFlowNode | null, openRightPanel: boolean = false) => {
    nodeManager.handleNodeSelect(node, openRightPanel);
    // Only show right panel when explicitly requested
    if (openRightPanel) {
      setShowRightPanel(true);
    } else {
      // Explicitly close right panel when component is selected without requesting it
      // or when clicking on canvas (node is null)
      setShowRightPanel(false);
      // Also clear the selected component to ensure consistency
      setSelectedComponent(null);
    }
  };

  // Handle viewing assessment results
  const handleViewAssessment = () => {
    if (assessmentResult) {
      // Clear selected component and show assessment in right panel
      nodeManager.handleNodeSelect(null, false);
      setShowRightPanel(true);
    }
  };

  // Handle loading best design
  const handleLoadBestDesign = async () => {
    if (!user || !effectiveCourseId || !reactFlowInstance) {
      debugWarn('Cannot load best design: missing user, courseId, or reactFlowInstance');
      return;
    }

    try {
      const bestDesignData = await loadBestDesignFromSupabase(
        user.id,
        effectiveCourseId,
        contextType as DesignContextCategory,
        contextId
      );

      if (bestDesignData) {
        const { design, score } = bestDesignData;

        // Load the design data into the canvas
        setNodes(design.nodes || []);

        // Process loaded edges to add the onChange handler for label editing
        const processedEdges = processLoadedEdges(design.edges || [], setEdges);
        setEdges(processedEdges);

        setUserJourneys(design.userJourneys || '');
        setAssumptions(design.assumptions || '');
        setConstraints(design.constraints || '');

        // Update React Flow instance
        reactFlowInstance.setNodes(design.nodes || []);
        reactFlowInstance.setEdges(processedEdges);

        // Update tracking references
        const now = Date.now();
        originalDesignRef.current = {
          nodes: design.nodes || [],
          edges: design.edges || [],
          timestamp: now
        };
        deletedNodesRef.current.clear();
        modifiedNodesRef.current.clear();
        setHasUnsavedChanges(false);

        toast.success(`🏆 Best design loaded! Score: ${score}/10`);
      } else {
        toast.error('No best design found');
      }
    } catch (error) {
      debugError('Error loading best design:', error);
      toast.error('Failed to load best design');
    }
  };

  // Handle showing tutorial
  const handleShowTutorial = () => {
    setShowTutorial(true);
  };

  // Handle showing best submission overlay
  const handleShowBestSubmission = () => {
    setShowBestSubmissionOverlay(true);
  };

  // Handle tutorial step changes to provide context
  const handleTutorialStepChange = (stepId: string) => {
    switch (stepId) {
      case 'properties-panel':
        // Step 5: Open right panel and select a component if available
        if (nodes.length > 0) {
          // Select the first node to show properties panel
          nodeManager.handleNodeSelect(nodes[0], true);
          setShowRightPanel(true);
        } else {
          // If no nodes, just open the panel
          setShowRightPanel(true);
        }
        break;

      case 'context-inputs':
        // Step 6: In guided mode, context is predefined, so show course tab
        setLeftPanelActiveTab('course');
        break;

      case 'welcome':
      case 'component-palette':
        // Steps 1-2: Switch to components tab
        setLeftPanelActiveTab('course');
        break;

      default:
        // No special action needed for other steps
        break;
    }
  };

  // Exit guided mode and return to course selection
  const handleExitGuidedMode = () => {
    // Save current progress before exiting
    if (currentCourse) {
      // Progress is automatically saved by the useEffect in useCourseState
      debugLog('Saving progress before exiting course');
    }

    // Reset guided mode state
    setIsGuidedMode(false);

    // Navigate back to course selection
    navigate('/guided', { replace: true });
    toast.info('Exited course mode');
  };

  // Handle starting the assessment
  const handleStartAssessment = async () => {
    if (!reactFlowInstance) {
      toast.error("Cannot start assessment - diagram not ready");
      return;
    }

    setIsAssessing(true);
    toast.info("Starting assessment...");

    try {
      // Import AssessmentHandler dynamically
      const AssessmentHandler = (await import('@/components/AssessmentHandler')).default;

      // Create handler with required props
      const handler = AssessmentHandler({
        reactFlowInstance,
        userJourneys: "Instagram user journey",
        assumptions: "Standard Instagram architecture",
        constraints: "High scalability required",
        currentQuestion: null,
        onAssessmentComplete: (result) => {
          setAssessmentResult(result);
          setIsAssessing(false);
          
          // Save assessment result to localStorage
          if (result && currentQuestion?.id) {
            saveAssessmentResult(result, currentQuestion.id, 'guided', courseId);
          }
          
          // Clear selected component to show assessment results instead of component properties
          nodeManager.handleNodeSelect(null, false);
          // Auto-expand right panel when assessment completes
          setShowRightPanel(true);

          if (result) {
            toast.success("Assessment complete!");
          }
        },
        setIsAssessing,
        user: user,
        questionId: effectiveCourseId,
        contextType: contextType as DesignContextCategory,
        contextId: contextId,
        onBestDesignSaved: checkBestDesign,
        startAssessment,
        recordAssessment
      });

      // Start the assessment
      if (handler && typeof handler.handleStartAssessment === 'function') {
        await handler.handleStartAssessment();
      }
    } catch (error) {
      debugError("Assessment error:", error);
      setIsAssessing(false);
      toast.error("Assessment failed. Please try again.");
    }
  };

  const currentStep = getCurrentStep();

  // Determine right panel width based on content type
  const getRightPanelWidth = () => {
    // If showing assessment results (no selected component), use wider panel
    if (assessmentResult && !selectedComponent) {
      return 50; // Wider for assessment results
    }
    // If showing component properties, use narrower panel
    return 25; // Standard width for component properties
  };

  // Create a key that changes when panel content type changes to force re-render
  const rightPanelKey = assessmentResult && !selectedComponent ? 'assessment' : 'component';

  // Use a constant left panel width of 40%
  const leftPanelWidth = 40;

  // Handle left panel tab changes and resize accordingly
  const handleLeftPanelTabChange = useCallback((tab: string) => {
    setLeftPanelActiveTab(tab);

    // Use setTimeout to ensure the tab change completes first
    setTimeout(() => {
      if (leftPanelRef.current) {
        if (tab === 'chat') {
          // Expand to 40% for chat
          leftPanelRef.current.resize(40);
        } else {
          // Shrink to 25% for other tabs
          leftPanelRef.current.resize(25);
        }
      }
    }, 50); // Small delay to ensure tab switch completes
  }, []);

  // Store the setResetting function from DiagramEditor
  const setResettingRef = useRef<((resetting: boolean) => void) | null>(null);

  // Callback to receive setResetting function from DiagramEditor
  const handleSetResetting = useCallback((setResetting: (resetting: boolean) => void) => {
    setResettingRef.current = setResetting;
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <Header
        onStartAssessment={handleStartAssessment}
        reactFlowInstance={reactFlowInstance}
        isAssessing={isAssessing}
        assessmentResult={assessmentResult}
        onViewAssessment={handleViewAssessment}
        onLoadBestDesign={handleLoadBestDesign}
        hasBestDesign={hasBestDesign}
        onShowTutorial={handleShowTutorial}
        onResetUserInputs={handleResetUserInputs}
        onShowBestSubmission={handleShowBestSubmission}
        setResetting={setResettingRef.current}
        userContext={{
          problem: currentCourse?.title || 'Guided Course',
          assumptions: assumptions ? assumptions.split('\n').filter(a => a.trim()) : [],
          constraints: constraints ? constraints.split('\n').filter(c => c.trim()) : [],
          userJourneys: userJourneys
        }}
        saveStatusIndicator={
          <SaveStatusIndicator
            status={saveStatus}
            lastSaved={lastSaved}
            isOnline={isOnline}
            onClick={saveNow}
          />
        }
      />

      {/* Tutorial Overlay */}
      <TutorialOverlay
        isOpen={showTutorial}
        onClose={() => setShowTutorial(false)}
        onStepChange={handleTutorialStepChange}
        isGuidedMode={true}
      />

      {/* Best Submission Overlay */}
      {effectiveCourseId && (
        <BestSubmissionOverlay
          isOpen={showBestSubmissionOverlay}
          onClose={() => setShowBestSubmissionOverlay(false)}
          onLoadDesign={handleLoadBestDesign}
          questionId={effectiveCourseId}
          contextType={contextType as DesignContextCategory}
          contextId={contextId}
        />
      )}

      {!isGuidedMode ? (
        <CourseSelector />
      ) : (
        <div className="flex flex-1 overflow-hidden">
          <ResizablePanelGroup direction="horizontal" className="flex-1">
            {/* Left Sidebar - Course Outline, Components, and Chat */}
            <ResizablePanel
              ref={leftPanelRef}
              defaultSize={leftPanelWidth}
              minSize={20}
              maxSize={50}
              className="h-full"
            >
              <GuidedModeLeftSidebar
                onExitGuidedMode={handleExitGuidedMode}
                currentStepId={currentStep?.id}
                filterComponents={currentStep?.goal?.components}
                reactFlowInstance={reactFlowInstance}
                onActiveTabChange={handleLeftPanelTabChange}
              />
            </ResizablePanel>

            <ResizableHandle withHandle />

            {/* Center Canvas - Diagram Editor */}
            <ResizablePanel
              defaultSize={showRightPanel ? (100 - leftPanelWidth - getRightPanelWidth()) : (100 - leftPanelWidth)}
              className="h-full relative flex flex-col"
            >
              <ReactFlowProvider>
                <div className="flex-1 relative">
                  <DiagramEditor
                    onNodeSelect={handleNodeSelect}
                    setReactFlowInstance={setReactFlowInstance}
                    modifiedNodesRef={modifiedNodesRef}
                    deletedNodesRef={deletedNodesRef}
                    onCanvasChange={handleCanvasChange}
                    onSetResetting={handleSetResetting}
                  />

                  {/* Course Steps Panel (Floating) - keep this for navigation */}
                  <div className="absolute bottom-4 right-4 z-10">
                    <CourseStepsPanel
                      onStartAssessment={handleStartAssessment}
                    />
                  </div>
                </div>
              </ReactFlowProvider>
            </ResizablePanel>

            {/* Right Sidebar - Only show when a component is selected or assessment result is available */}
            {(showRightPanel || assessmentResult) && (
              <>
                <ResizableHandle withHandle />
                <ResizablePanel
                  key={rightPanelKey}
                  defaultSize={getRightPanelWidth()}
                  minSize={20}
                  maxSize={60}
                  className="h-full"
                >
                  <div ref={rightPanelDomRef} className="h-full">
                    <ComponentMetadataSidebar
                    selectedComponent={selectedComponent}
                    componentTypes={componentTypes}
                    assessmentResult={assessmentResult}
                    onStartAssessment={handleStartAssessment}
                    onDeleteNode={nodeManager.handleDeleteNode}
                    onClose={() => {
                      setShowRightPanel(false);
                      setSelectedComponent(null);
                    }}
                    onCanvasChange={handleCanvasChange}
                  />
                  </div>
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        </div>
      )}
    </div>
  );
};

export default GuidedMode;
