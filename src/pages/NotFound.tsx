import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { isFeatureEnabled } from '@/config/featureFlags';

const NotFound = () => {
  const location = useLocation();
  const { user } = useAuth();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-[#1e1b4b] via-[#2e1065] to-[#1e1b4b]">
      <div className="text-center p-8 glass-card rounded-2xl max-w-md">
        <h1 className="text-6xl font-bold mb-6 gradient-text">404</h1>
        <p className="text-xl text-gray-300 mb-8">Oops! This page doesn't exist in our architecture.</p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700">
            <Link to="/">Return Home</Link>
          </Button>

          {user && (
            <Button asChild variant="outline" className="border-white/10 hover:bg-white/5">
              <Link to={isFeatureEnabled('FREE_CANVAS') ? "/canvas" : "/questions"}>
                {isFeatureEnabled('FREE_CANVAS') ? "Go to Canvas" : "Browse Questions"}
              </Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotFound;
