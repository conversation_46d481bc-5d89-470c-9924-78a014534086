import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AnalyticsDashboard from '@/components/admin/AnalyticsDashboard';
import AdminNavigation from '@/components/AdminNavigation';
import { BarChart3, MessageSquare, Users, Activity } from 'lucide-react';

const AnalyticsAdmin: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Admin Navigation */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600">Monitor platform usage and user engagement</p>
          </div>
          <AdminNavigation />
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="chats" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Chat Analytics
            </TabsTrigger>
            <TabsTrigger value="assessments" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Assessments
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              User Sessions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-blue-600" />
                    Chat Metrics
                  </CardTitle>
                  <CardDescription>
                    Track user engagement with the AI assistant
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Total chat sessions initiated</li>
                    <li>• Average messages per conversation</li>
                    <li>• Conversation duration tracking</li>
                    <li>• Context-specific usage (free vs questions)</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-green-600" />
                    Assessment Metrics
                  </CardTitle>
                  <CardDescription>
                    Monitor design assessment completion and performance
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Total assessments completed</li>
                    <li>• Average scores and performance trends</li>
                    <li>• Assessment duration tracking</li>
                    <li>• Best score achievements</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-purple-600" />
                    User Engagement
                  </CardTitle>
                  <CardDescription>
                    Understand user behavior and platform usage
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Session duration and page views</li>
                    <li>• User actions and interactions</li>
                    <li>• Question attempt patterns</li>
                    <li>• Feature usage analytics</li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Key Performance Indicators</CardTitle>
                <CardDescription>
                  Important metrics for platform health and user engagement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">📊</div>
                    <div className="text-sm font-medium">Chat Engagement</div>
                    <div className="text-xs text-gray-600">Messages per session</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">🎯</div>
                    <div className="text-sm font-medium">Assessment Quality</div>
                    <div className="text-xs text-gray-600">Average scores</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">👥</div>
                    <div className="text-sm font-medium">User Retention</div>
                    <div className="text-xs text-gray-600">Session patterns</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">🏆</div>
                    <div className="text-sm font-medium">Achievement Rate</div>
                    <div className="text-xs text-gray-600">Best scores</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="chats">
            <AnalyticsDashboard />
          </TabsContent>

          <TabsContent value="assessments">
            <AnalyticsDashboard />
          </TabsContent>

          <TabsContent value="users">
            <AnalyticsDashboard />
          </TabsContent>
        </Tabs>

        {/* Data Privacy Notice */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle className="text-sm">Data Privacy & Security</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-xs text-gray-600">
              All analytics data is anonymized and aggregated. Individual user data is protected by Row Level Security (RLS) policies. 
              Only authorized administrators can access this dashboard. Data is stored securely in Supabase with proper encryption.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsAdmin;
