/**
 * Test page for System Design Agent integration
 */
import React, { useState } from 'react';
import { Node, Edge } from '@xyflow/react';
import SystemDesignAgentChat from '@/components/SystemDesignAgentChat';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const AgentTest: React.FC = () => {
  // Sample design data for testing
  const [sampleNodes] = useState<Node[]>([
    {
      id: 'web-1',
      type: 'web_server',
      position: { x: 100, y: 100 },
      data: {
        label: 'Web Server',
        technology: 'nginx',
        instances: 2
      }
    },
    {
      id: 'app-1',
      type: 'application_server',
      position: { x: 300, y: 100 },
      data: {
        label: 'Application Server',
        technology: 'node.js',
        instances: 3
      }
    },
    {
      id: 'db-1',
      type: 'database',
      position: { x: 500, y: 100 },
      data: {
        label: 'PostgreSQL Database',
        technology: 'postgresql',
        instances: 1
      }
    }
  ]);

  const [sampleEdges] = useState<Edge[]>([
    {
      id: 'e1',
      source: 'web-1',
      target: 'app-1',
      type: 'default'
    },
    {
      id: 'e2',
      source: 'app-1',
      target: 'db-1',
      type: 'default'
    }
  ]);

  const [useDesignContext, setUseDesignContext] = useState(false);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">System Design Agent Test</h1>
        <p className="text-muted-foreground">
          Test the integration with the local Gemini-powered System Design Agent
        </p>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Configuration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => setUseDesignContext(!useDesignContext)}
              variant={useDesignContext ? "default" : "outline"}
            >
              {useDesignContext ? "Using" : "Not Using"} Design Context
            </Button>
            {useDesignContext && (
              <Badge variant="secondary">
                {sampleNodes.length} components, {sampleEdges.length} connections
              </Badge>
            )}
          </div>
          
          {useDesignContext && (
            <div className="text-sm text-muted-foreground">
              <p><strong>Sample Design:</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Web Server (nginx, 2 instances)</li>
                <li>Application Server (node.js, 3 instances)</li>
                <li>PostgreSQL Database (1 instance)</li>
              </ul>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Agent Chat Interface */}
      <SystemDesignAgentChat
        nodes={useDesignContext ? sampleNodes : []}
        edges={useDesignContext ? sampleEdges : []}
        userJourneys={useDesignContext ? "Users can browse products, add to cart, and checkout" : undefined}
        assumptions={useDesignContext ? "Peak traffic: 1000 concurrent users" : undefined}
        constraints={useDesignContext ? "Budget: $500/month, Must be highly available" : undefined}
        className="w-full"
      />

      {/* Test Suggestions */}
      <Card>
        <CardHeader>
          <CardTitle>Test Suggestions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Without Design Context:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>"Help me design a URL shortener service"</li>
                <li>"What are the best practices for microservices?"</li>
                <li>"How does Netflix handle video streaming?"</li>
                <li>"Explain database sharding strategies"</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">With Design Context:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>"How can I improve this design?"</li>
                <li>"What are the bottlenecks in this architecture?"</li>
                <li>"Add a caching layer to improve performance"</li>
                <li>"Calculate capacity for 10,000 users"</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentTest;
