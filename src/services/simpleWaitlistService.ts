import { supabase } from '@/lib/supabase';

// Simple rate limiting (client-side)
const rateLimitKey = 'waitlist_attempts';
const maxAttempts = 3;
const windowMs = 5 * 60 * 1000; // 5 minutes

function checkSimpleRateLimit(): { allowed: boolean; retryAfter?: number } {
  const stored = localStorage.getItem(rateLimitKey);
  const now = Date.now();
  
  if (!stored) {
    localStorage.setItem(rateLimitKey, JSON.stringify({ count: 1, timestamp: now }));
    return { allowed: true };
  }
  
  const { count, timestamp } = JSON.parse(stored);
  
  // Reset if window expired
  if (now - timestamp > windowMs) {
    localStorage.setItem(rateLimitKey, JSON.stringify({ count: 1, timestamp: now }));
    return { allowed: true };
  }
  
  // Check if limit exceeded
  if (count >= maxAttempts) {
    const retryAfter = Math.ceil((windowMs - (now - timestamp)) / 1000);
    return { allowed: false, retryAfter };
  }
  
  // Increment count
  localStorage.setItem(rateLimit<PERSON>ey, JSON.stringify({ count: count + 1, timestamp }));
  return { allowed: true };
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
}

export const addToWaitlistSimple = async (
  email: string,
  referralCode?: string
): Promise<{ success: boolean; error?: string; retryAfter?: number }> => {
  try {
    // Basic validation
    if (!email || !isValidEmail(email)) {
      return { success: false, error: 'Please enter a valid email address' };
    }

    // Simple rate limiting
    const rateCheck = checkSimpleRateLimit();
    if (!rateCheck.allowed) {
      return { 
        success: false, 
        error: `Too many attempts. Please try again in ${rateCheck.retryAfter} seconds.`,
        retryAfter: rateCheck.retryAfter
      };
    }

    // Add delay to slow down bots (humans won't notice 500ms)
    await new Promise(resolve => setTimeout(resolve, 500));

    // Insert to database
    const { data, error } = await supabase
      .from('waitlist')
      .insert({
        email: email.toLowerCase().trim(),
        referral_code: referralCode || null,
      })
      .select()
      .single();

    if (error) {
      // Handle duplicate gracefully
      if (error.code === '23505') {
        return { success: true }; // Don't tell them it's a duplicate
      }
      
      console.error('Waitlist error:', error);
      return { success: false, error: 'Failed to join waitlist. Please try again.' };
    }

    // Clear rate limit on success
    localStorage.removeItem(rateLimitKey);
    
    return { success: true };
  } catch (error) {
    console.error('Unexpected error:', error);
    return { success: false, error: 'An unexpected error occurred. Please try again.' };
  }
};
