import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface RecentActivity {
  id: string;
  title: string;
  questionId: string;
  daysAgo: number;
  score?: number;
  status: 'completed' | 'in-progress' | 'attempted';
  contextType: 'question' | 'guided' | 'free';
  updatedAt: string;
}

/**
 * Fetch recent user activity from Supabase
 * Combines data from designs and assessment_analytics tables
 */
export const fetchRecentActivity = async (userId: string, limit: number = 3): Promise<RecentActivity[]> => {
  try {
    debugLog(`Fetching recent activity for user: ${userId}`);

    // First, get recent assessments with scores
    const { data: assessments, error: assessmentError } = await supabase
      .from('assessment_analytics')
      .select('question_id, score, created_at, context_type, context_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit * 2); // Get more to account for duplicates

    if (assessmentError) {
      debugError('Error fetching assessments:', assessmentError);
    }

    // Get recent designs (for cases where user saved but didn't assess)
    const { data: designs, error: designError } = await supabase
      .from('designs')
      .select('question_id, updated_at, context_type, context_id')
      .eq('user_id', userId)
      .order('updated_at', { ascending: false })
      .limit(limit * 2);

    if (designError) {
      debugError('Error fetching designs:', designError);
    }

    // Get questions data to map IDs to titles
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('id, title')
      .eq('is_active', true);

    if (questionsError) {
      debugError('Error fetching questions:', questionsError);
    }

    // Create a map of question IDs to titles
    const questionMap = new Map<string, string>();
    questions?.forEach(q => {
      questionMap.set(q.id.toString(), q.title);
    });

    // Combine and deduplicate activities
    const activityMap = new Map<string, RecentActivity>();

    // Process assessments (these have scores)
    assessments?.forEach(assessment => {
      const key = `${assessment.question_id}-${assessment.context_type}-${assessment.context_id}`;
      const questionTitle = questionMap.get(assessment.question_id) ||
        (assessment.context_type === 'free' ? 'Free Canvas Design' : `Question ${assessment.question_id}`);
      const createdAt = new Date(assessment.created_at);
      const daysAgo = Math.floor((Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24));

      activityMap.set(key, {
        id: key,
        title: questionTitle,
        questionId: assessment.question_id,
        daysAgo,
        score: assessment.score,
        status: 'completed',
        contextType: assessment.context_type as 'question' | 'guided',
        updatedAt: assessment.created_at
      });
    });

    // Process designs (for activities without assessments)
    designs?.forEach(design => {
      const key = `${design.question_id}-${design.context_type}-${design.context_id}`;
      
      // Only add if not already in map (assessments take priority)
      if (!activityMap.has(key)) {
        const questionTitle = questionMap.get(design.question_id) ||
          (design.context_type === 'free' ? 'Free Canvas Design' : `Question ${design.question_id}`);
        const updatedAt = new Date(design.updated_at);
        const daysAgo = Math.floor((Date.now() - updatedAt.getTime()) / (1000 * 60 * 60 * 24));

        activityMap.set(key, {
          id: key,
          title: questionTitle,
          questionId: design.question_id,
          daysAgo,
          score: undefined,
          status: 'in-progress',
          contextType: design.context_type as 'question' | 'guided' | 'free',
          updatedAt: design.updated_at
        });
      }
    });

    // Convert to array, sort by most recent, and limit
    const recentActivities = Array.from(activityMap.values())
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, limit);

    debugLog(`Found ${recentActivities.length} recent activities for user ${userId}`);
    return recentActivities;

  } catch (error) {
    debugError('Error fetching recent activity:', error);
    return [];
  }
};

/**
 * Get all attempted question IDs for a user
 */
export const getAttemptedQuestionIds = async (userId: string): Promise<Set<string>> => {
  try {
    const attemptedIds = new Set<string>();

    // Get question IDs from assessments
    const { data: assessments, error: assessmentError } = await supabase
      .from('assessment_analytics')
      .select('question_id')
      .eq('user_id', userId);

    if (!assessmentError && assessments) {
      assessments.forEach(a => attemptedIds.add(a.question_id));
    }

    // Get question IDs from designs
    const { data: designs, error: designError } = await supabase
      .from('designs')
      .select('question_id')
      .eq('user_id', userId);

    if (!designError && designs) {
      designs.forEach(d => attemptedIds.add(d.question_id));
    }

    return attemptedIds;
  } catch (error) {
    debugError('Error fetching attempted question IDs:', error);
    return new Set<string>();
  }
};

/**
 * Get activity statistics for a user
 */
export const getActivityStats = async (userId: string): Promise<{
  totalAttempted: number;
  totalCompleted: number;
  averageScore: number;
  streak: number;
}> => {
  try {
    // Get total assessments
    const { data: assessments, error: assessmentError } = await supabase
      .from('assessment_analytics')
      .select('score, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (assessmentError) {
      debugError('Error fetching assessment stats:', assessmentError);
      return { totalAttempted: 0, totalCompleted: 0, averageScore: 0, streak: 0 };
    }

    const totalCompleted = assessments?.length || 0;
    const averageScore = totalCompleted > 0 
      ? Math.round(assessments!.reduce((sum, a) => sum + a.score, 0) / totalCompleted)
      : 0;

    // Get total unique questions attempted (including designs without assessments)
    const { data: designs, error: designError } = await supabase
      .from('designs')
      .select('question_id, context_type, context_id')
      .eq('user_id', userId);

    if (designError) {
      debugError('Error fetching design stats:', designError);
    }

    // Count unique question attempts
    const uniqueAttempts = new Set();
    designs?.forEach(d => {
      uniqueAttempts.add(`${d.question_id}-${d.context_type}-${d.context_id}`);
    });
    assessments?.forEach(a => {
      uniqueAttempts.add(`${a.question_id}-${a.context_type}-${a.context_id}`);
    });

    const totalAttempted = uniqueAttempts.size;

    // Calculate streak (consecutive days with activity)
    let streak = 0;
    if (assessments && assessments.length > 0) {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const activityDates = new Set(
        assessments.map(a => {
          const date = new Date(a.created_at);
          date.setHours(0, 0, 0, 0);
          return date.getTime();
        })
      );

      let currentDate = today.getTime();
      while (activityDates.has(currentDate)) {
        streak++;
        currentDate -= 24 * 60 * 60 * 1000; // Go back one day
      }
    }

    return {
      totalAttempted,
      totalCompleted,
      averageScore,
      streak
    };

  } catch (error) {
    debugError('Error fetching activity stats:', error);
    return { totalAttempted: 0, totalCompleted: 0, averageScore: 0, streak: 0 };
  }
};
