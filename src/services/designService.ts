import { SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Define the database table name
const DESIGNS_TABLE = 'designs';

// Interface for the design record in the database
interface DesignRecord {
  id?: string;
  user_id: string;
  question_id: string;
  context_type: DesignContextCategory;
  context_id: string;
  name?: string;
  data: SavedDesign;
  created_at?: string;
  updated_at: string;
}

/**
 * Save a design to Supabase using upsert to reduce API calls
 */
export const saveDesignToSupabase = async (
  userId: string,
  questionId: string,
  design: SavedDesign
): Promise<SavedDesign | null> => {
  try {
    const contextType = design.contextType || 'question';
    const contextId = design.contextId || questionId;

    debugLog(`Saving design to <PERSON>pa<PERSON> for user ${userId}, ${contextType} ${contextId}`);

    const now = new Date().toISOString();

    // Use upsert operation (insert with on_conflict) to handle both insert and update cases
    // This eliminates the need for a separate SELECT query
    const { data, error } = await supabase
      .from(DESIGNS_TABLE)
      .upsert({
        user_id: userId,
        question_id: questionId,
        context_type: contextType,
        context_id: contextId,
        data: design,
        created_at: now, // This will only be used for new records
        updated_at: now  // This will be updated for both new and existing records
      }, {
        onConflict: 'user_id,question_id,context_type,context_id', // The unique constraint to determine conflicts
        ignoreDuplicates: false // We want to update on conflict, not ignore
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting design:', error);
      return null;
    }

    debugLog('Design saved successfully');
    return data?.data as SavedDesign;
  } catch (error) {
    console.error('Error saving design:', error);
    return null;
  }
};

/**
 * Load a design from Supabase
 */
export const loadDesignFromSupabase = async (
  userId: string,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): Promise<SavedDesign | null> => {
  try {
    const id = contextId || questionId;
    debugLog(`🔍 Loading design from Supabase for user ${userId}, ${contextType} ${id}`);
    debugLog(`🔍 Query params: user_id=${userId}, question_id=${questionId}, context_type=${contextType}, context_id=${id}`);

    const { data, error } = await supabase
      .from(DESIGNS_TABLE)
      .select('*')
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        debugLog('✅ No design found for this user and question (this is normal)');
      } else {
        console.error('❌ Error loading design:', error);
      }
      return null;
    }

    debugLog('✅ Design loaded successfully:', data);
    return data?.data as SavedDesign;
  } catch (error) {
    console.error('Error loading design:', error);
    return null;
  }
};

/**
 * List all designs for a user
 */
export const listDesignsFromSupabase = async (
  userId: string,
  contextType?: DesignContextCategory
): Promise<SavedDesign[]> => {
  try {
    let query = supabase
      .from(DESIGNS_TABLE)
      .select('*')
      .eq('user_id', userId);

    // Filter by context type if provided
    if (contextType) {
      query = query.eq('context_type', contextType);
    }

    const { data, error } = await query.order('updated_at', { ascending: false });

    if (error) {
      console.error('Error listing designs:', error);
      return [];
    }

    return data.map(record => record.data as SavedDesign);
  } catch (error) {
    console.error('Error listing designs:', error);
    return [];
  }
};

/**
 * Delete a design from Supabase
 */
export const deleteDesignFromSupabase = async (
  userId: string,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): Promise<boolean> => {
  try {
    const id = contextId || questionId;
    const { error } = await supabase
      .from(DESIGNS_TABLE)
      .delete()
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', id);

    if (error) {
      console.error('Error deleting design:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting design:', error);
    return false;
  }
};
