import { SavedDesign, DesignContextCategory } from '@/contexts/DesignContext';
import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Define the database table name
const BEST_DESIGNS_TABLE = 'best_designs';

// Interface for the best design record in the database
interface BestDesignRecord {
  id?: string;
  user_id: string;
  question_id: string;
  context_type: DesignContextCategory;
  context_id: string;
  name?: string;
  data: SavedDesign;
  score: number;
  created_at?: string;
  updated_at: string;
}

/**
 * Save or update a best design to Supabase
 * Only saves if the new score is higher than the existing best score
 */
export const saveBestDesignToSupabase = async (
  userId: string,
  questionId: string,
  design: SavedDesign,
  score: number
): Promise<{ saved: boolean; previousBest?: number }> => {
  try {
    const contextType = design.contextType || 'question';
    const contextId = design.contextId || questionId;

    debugLog(`Checking best design for user ${userId}, ${contextType} ${contextId}, new score: ${score}`);

    // First, check if there's an existing best design and its score
    const { data: existingBest, error: fetchError } = await supabase
      .from(BEST_DESIGNS_TABLE)
      .select('score')
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', contextId)
      .single();

    if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "not found"
      debugError('Error fetching existing best design:', fetchError);
      return { saved: false };
    }

    const previousBest = existingBest?.score;

    // Only save if this is a new best score or no previous best exists
    if (!existingBest || score > existingBest.score) {
      const now = new Date().toISOString();

      // Use upsert operation to handle both insert and update cases
      const { data, error } = await supabase
        .from(BEST_DESIGNS_TABLE)
        .upsert({
          user_id: userId,
          question_id: questionId,
          context_type: contextType,
          context_id: contextId,
          data: design,
          score: score,
          created_at: now, // This will only be used for new records
          updated_at: now  // This will be updated for both new and existing records
        }, {
          onConflict: 'user_id,question_id,context_type,context_id',
          ignoreDuplicates: false
        })
        .select()
        .single();

      if (error) {
        debugError('Error upserting best design:', error);
        return { saved: false, previousBest };
      }

      debugLog(`✅ New best design saved with score ${score} (previous: ${previousBest || 'none'})`);
      return { saved: true, previousBest };
    } else {
      debugLog(`❌ Score ${score} is not better than existing best ${existingBest.score}`);
      return { saved: false, previousBest };
    }
  } catch (error) {
    debugError('Error saving best design:', error);
    return { saved: false };
  }
};

/**
 * Load the best design from Supabase
 */
export const loadBestDesignFromSupabase = async (
  userId: string,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): Promise<{ design: SavedDesign; score: number } | null> => {
  try {
    const id = contextId || questionId;
    debugLog(`🏆 Loading best design from Supabase for user ${userId}, ${contextType} ${id}`);

    const { data, error } = await supabase
      .from(BEST_DESIGNS_TABLE)
      .select('*')
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        debugLog('No best design found');
        return null;
      }
      debugError('Error loading best design:', error);
      return null;
    }

    debugLog(`🏆 Best design loaded with score ${data.score}`);
    return {
      design: data.data,
      score: data.score
    };
  } catch (error) {
    debugError('Error loading best design:', error);
    return null;
  }
};

/**
 * Get the best score for a specific question/context
 */
export const getBestScore = async (
  userId: string,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): Promise<number | null> => {
  try {
    const id = contextId || questionId;
    
    const { data, error } = await supabase
      .from(BEST_DESIGNS_TABLE)
      .select('score')
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No best score found
      }
      debugError('Error getting best score:', error);
      return null;
    }

    return data.score;
  } catch (error) {
    debugError('Error getting best score:', error);
    return null;
  }
};

/**
 * Delete a best design from Supabase
 */
export const deleteBestDesignFromSupabase = async (
  userId: string,
  questionId: string,
  contextType: DesignContextCategory = 'question',
  contextId?: string
): Promise<boolean> => {
  try {
    const id = contextId || questionId;
    const { error } = await supabase
      .from(BEST_DESIGNS_TABLE)
      .delete()
      .eq('user_id', userId)
      .eq('question_id', questionId)
      .eq('context_type', contextType)
      .eq('context_id', id);

    if (error) {
      debugError('Error deleting best design:', error);
      return false;
    }

    debugLog('🗑️ Best design deleted successfully');
    return true;
  } catch (error) {
    debugError('Error deleting best design:', error);
    return false;
  }
};
