import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Define the waitlist table name
const WAITLIST_TABLE = 'waitlist';

// Interface for the waitlist record
interface WaitlistRecord {
  id?: string;
  email: string;
  referral_code?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Validate email format
 */
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
};

/**
 * Sanitize email input
 */
const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim().replace(/[^\w@.-]/g, '');
};

import { checkWaitlistSecurity, detectSuspiciousActivity, generateRequestFingerprint } from './rateLimitService';

/**
 * Add an email to the waitlist
 * @param email The email address to add
 * @param referralCode Optional referral code
 * @param formData Additional form data for security checks
 * @returns Success status and any error message
 */
export const addToWaitlist = async (
  email: string,
  referralCode?: string,
  formData?: any
): Promise<{ success: boolean; error?: string; data?: WaitlistRecord; retryAfter?: number }> => {
  try {
    // Input validation
    if (!email || typeof email !== 'string') {
      return { success: false, error: 'Email is required' };
    }

    // Sanitize input
    const sanitizedEmail = sanitizeEmail(email);

    // Validate email format
    if (!isValidEmail(sanitizedEmail)) {
      return { success: false, error: 'Please enter a valid email address' };
    }

    // Check email length
    if (sanitizedEmail.length > 254) { // RFC 5321 limit
      return { success: false, error: 'Email address is too long' };
    }

    // Honeypot and bot detection
    if (formData && detectSuspiciousActivity(formData)) {
      // Silently reject bots (don't give them feedback)
      debugWarn('Suspicious activity detected:', { email: sanitizedEmail, formData });
      return {
        success: true, // Lie to the bot
        data: { email: sanitizedEmail, id: 'fake' } as WaitlistRecord
      };
    }

    // Comprehensive rate limiting
    const rateLimitCheck = checkWaitlistSecurity(sanitizedEmail);
    if (!rateLimitCheck.allowed) {
      return {
        success: false,
        error: rateLimitCheck.reason || 'Too many requests. Please try again later.',
        retryAfter: rateLimitCheck.retryAfter
      };
    }

    // Sanitize referral code if provided
    const sanitizedReferralCode = referralCode
      ? referralCode.trim().replace(/[^\w-]/g, '').substring(0, 20)
      : undefined;

    // Create record
    const record: WaitlistRecord = {
      email: sanitizedEmail,
      referral_code: sanitizedReferralCode,
      updated_at: new Date().toISOString()
    };

    // Insert new record
    const { data, error } = await supabase
      .from(WAITLIST_TABLE)
      .insert(record)
      .select()
      .single();

    if (error) {
      debugError('Error adding to waitlist:', error);

      // Handle duplicate email error gracefully
      if (error.code === '23505' || error.message.includes('duplicate') || error.message.includes('unique')) {
        return {
          success: true,
          data: { ...record, id: 'existing' } // Return success for duplicate emails
        };
      }

      // Handle validation errors
      if (error.code === '23514') { // Check constraint violation
        return {
          success: false,
          error: 'Please enter a valid email address'
        };
      }

      return {
        success: false,
        error: 'Failed to join waitlist. Please try again.'
      };
    }

    return {
      success: true,
      data: data as WaitlistRecord
    };
  } catch (error) {
    debugError('Unexpected error adding to waitlist:', error);
    return {
      success: false,
      error: 'An unexpected error occurred. Please try again.'
    };
  }
};

/**
 * Check if an email is already on the waitlist
 * @param email The email address to check
 * @returns Whether the email is on the waitlist
 */
export const checkWaitlist = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from(WAITLIST_TABLE)
      .select('email')
      .eq('email', email.toLowerCase().trim())
      .maybeSingle();

    if (error) {
      debugError('Error checking waitlist:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    debugError('Unexpected error checking waitlist:', error);
    return false;
  }
};

/**
 * Generate a unique referral code for a user
 * @param email The email address to generate a code for
 * @returns The generated referral code
 */
export const generateReferralCode = (email: string): string => {
  // Create a simple hash from the email and current timestamp
  const hash = btoa(`${email}-${Date.now()}`).replace(/[^a-zA-Z0-9]/g, '');
  return hash.substring(0, 8).toUpperCase();
};
