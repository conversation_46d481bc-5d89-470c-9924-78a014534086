/**
 * Service for handling interactions with the local System Design Agent
 * Integrates with the Gemini-powered agent running on localhost:9000
 */
import { Node, Edge } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

/**
 * Interface for agent chat message
 */
export interface AgentChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: Date;
  design_context?: any;
  tool_calls?: string[];
}

/**
 * Interface for agent chat response
 */
interface AgentChatResponse {
  response: string;
  error?: string;
}

/**
 * Interface for design analysis response
 */
interface DesignAnalysisResponse {
  status: string;
  analysis: string;
  scores?: {
    total: number;
    scalability: number;
    fault_tolerance: number;
    consistency: number;
    max_score: number;
  };
  error?: string;
}

/**
 * Session Manager for the System Design Agent
 */
class AgentSessionManager {
  private currentSession: string | null = null;
  private storageKey = 'design_agent_sessions';
  private currentSessionKey = 'current_design_session';

  constructor() {
    this.init();
  }

  private init() {
    // Load current session on initialization
    const savedSessionId = localStorage.getItem(this.currentSessionKey);
    if (savedSessionId) {
      this.currentSession = savedSessionId;
    }
  }

  // Generate unique session ID
  private generateSessionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `design_session_${timestamp}_${random}`;
  }

  // Get current session or create new one
  getCurrentSession(): string {
    if (!this.currentSession) {
      this.currentSession = this.generateSessionId();
      localStorage.setItem(this.currentSessionKey, this.currentSession);
    }
    return this.currentSession;
  }

  // Start new session
  startNewSession(): string {
    this.currentSession = this.generateSessionId();
    localStorage.setItem(this.currentSessionKey, this.currentSession);
    return this.currentSession;
  }
}

// Global session manager instance
const sessionManager = new AgentSessionManager();

/**
 * Prepare design data for the agent API
 */
const prepareDesignDataForAgent = (
  nodes: Node[],
  edges: Edge[],
  userJourneys?: string,
  assumptions?: string,
  constraints?: string
) => {
  // Convert nodes to agent format
  const components = nodes.map(node => ({
    id: node.id,
    type: node.type || 'component',
    label: node.data?.label || node.id,
    position: node.position,
    properties: {
      ...node.data,
      technology: node.data?.technology,
      instances: node.data?.instances || 1,
      capacity: node.data?.capacity
    }
  }));

  // Convert edges to agent format
  const connections = edges.map(edge => ({
    id: edge.id,
    from: edge.source,
    to: edge.target,
    type: edge.type || 'connection',
    label: edge.label || '',
    properties: edge.data || {}
  }));

  // Prepare requirements from user inputs
  const requirements = [];
  if (userJourneys) requirements.push(`User Journeys: ${userJourneys}`);
  if (assumptions) requirements.push(`Assumptions: ${assumptions}`);
  if (constraints) requirements.push(`Constraints: ${constraints}`);

  return {
    components,
    connections,
    requirements,
    metadata: {
      node_count: nodes.length,
      edge_count: edges.length,
      created_at: new Date().toISOString()
    }
  };
};

/**
 * Chat with the System Design Agent
 */
export const chatWithAgent = async (
  message: string,
  nodes: Node[] = [],
  edges: Edge[] = [],
  userJourneys?: string,
  assumptions?: string,
  constraints?: string
): Promise<string> => {
  try {
    const sessionId = sessionManager.getCurrentSession();

    // Prepare design data if nodes/edges are provided
    const designData = nodes.length > 0 ? prepareDesignDataForAgent(
      nodes,
      edges,
      userJourneys,
      assumptions,
      constraints
    ) : null;

    debugLog("Sending message to System Design Agent:", {
      message,
      sessionId,
      hasDesignData: !!designData
    });

    // Make the API call to local agent
    const response = await fetch('https://api.layrs.me/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        design: designData,
        session_id: sessionId
      })
    });

    if (!response.ok) {
      throw new Error(`Agent request failed with status: ${response.status}`);
    }

    const data = await response.json() as AgentChatResponse;

    if (data.error) {
      throw new Error(data.error);
    }

    return data.response || "I'm sorry, I couldn't process your request.";
  } catch (error) {
    debugError("Error during agent chat request:", error);
    return "I'm sorry, I couldn't connect to the system design agent.";
  }
};

/**
 * Analyze design with the System Design Agent
 */
export const analyzeDesignWithAgent = async (
  nodes: Node[],
  edges: Edge[],
  userJourneys?: string,
  assumptions?: string,
  constraints?: string
): Promise<string> => {
  try {
    const designData = prepareDesignDataForAgent(
      nodes,
      edges,
      userJourneys,
      assumptions,
      constraints
    );

    const response = await fetch('https://api.layrs.me/api/agent/analyze-design', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify({
        design: designData
      })
    });

    debugLog('Analysis response status:', response.status);
    debugLog('Analysis response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      debugError('Analysis request failed:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });
      throw new Error(`Analysis request failed with status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json() as DesignAnalysisResponse;

    if (data.error) {
      throw new Error(data.error);
    }

    // Return the full response as JSON string so the assessment service can parse it properly
    // This allows the assessment service to access both analysis text and scores
    if (data.scores) {
      return JSON.stringify(data);
    }

    // Fallback for responses without scores (legacy format)
    return data.analysis || "Analysis completed successfully.";
  } catch (error) {
    debugError("Error during design analysis:", error);
    return "I'm sorry, I couldn't analyze the design. Please make sure the agent is running.";
  }
};

/**
 * Check if the agent is available
 */
export const checkAgentAvailability = async (): Promise<boolean> => {
  try {
    const response = await fetch('https://api.layrs.me/api/test');
    return response.ok;
  } catch (error) {
    return false;
  }
};

/**
 * Start a new design session
 */
export const startNewDesignSession = (): string => {
  return sessionManager.startNewSession();
};
