import { supabase } from '@/lib/supabase';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface Subscription {
  id: string;
  subscription_id: string;
  product_id: string;
  status: string;
  recurring_pre_tax_amount: number;
  currency: string;
  quantity: number;
  next_billing_date?: string;
  trial_period_days: number;
  addons: any[];
  customer: any;
  metadata: any;
  cancel_at_next_billing_date?: boolean;
  cancelled_at?: string;
}

export interface PlanInfo {
  id: string;
  name: string;
  price: number;
  credits: number;
  period: string;
  features: string[];
}

export interface CreateSubscriptionRequest {
  plan_type: 'pro';
  customer_data: {
    name: string;
    email: string;
  };
  billing_cycle?: 'monthly' | 'yearly';
  billing_address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    zipcode?: string;
  };
  return_url?: string;
}

export interface CreateSubscriptionResponse {
  success: boolean;
  payment_link?: string;
  subscription_id?: string;
  error?: string;
}

export interface UserData {
  subscription: Subscription | null;
  planInfo: PlanInfo | null;
  credits: number;
  isProUser: boolean;
  canUpgrade: boolean;
}

class SubscriptionService {
  private userDataCache: Map<string, { data: UserData; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Check if the current user has an active subscription
   */
  async hasActiveSubscription(userId?: string): Promise<boolean> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        debugWarn('No user ID provided for subscription check');
        return false;
      }

      // Check cache first
      const cached = this.userDataCache.get(targetUserId);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        debugLog(`Using cached subscription data for user: ${targetUserId}`);
        const isPro = cached.data.planInfo?.id === 'pro' || cached.data.isProUser === true;
        return isPro;
      }

      debugLog(`Fetching fresh subscription data for user: ${targetUserId}`);

      // Call the get-user-data edge function to get user plan
      const { data, error } = await supabase.functions.invoke('get-user-data', {
        body: { user_id: targetUserId }
      });

      if (error) {
        debugError('Error calling get-user-data function:', error);
        // If edge function fails, fall back to basic plan (no access to premium questions)
        return false;
      }

      // Transform and cache the response
      const isProUser = data?.planInfo?.id === 'pro';
      const userData: UserData = {
        subscription: data?.subscription || null,
        planInfo: data?.planInfo || {
          id: 'basic',
          name: 'Basic Plan',
          price: 0,
          credits: 10,
          period: 'monthly',
          features: [
            '1 practice problems',
            '10 credits for chat & assessment',
            'Community access',
            'Email support'
          ]
        },
        credits: data?.credits || 10,
        isProUser,
        canUpgrade: !isProUser
      };

      // Cache the result
      this.userDataCache.set(targetUserId, {
        data: userData,
        timestamp: now
      });

      debugLog(`User ${targetUserId} has pro plan: ${isProUser}, planInfo:`, data?.planInfo);
      return isProUser;
    } catch (error) {
      debugError('Error in hasActiveSubscription:', error);
      return false;
    }
  }

  /**
   * Get comprehensive user data including subscription, plan info, and credits
   */
  async getUserData(userId?: string): Promise<UserData> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        throw new Error('No user ID available');
      }

      // Check cache first
      const cached = this.userDataCache.get(targetUserId);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        debugLog(`Using cached user data for user: ${targetUserId}`);
        return cached.data;
      }

      debugLog(`Fetching fresh user data for user: ${targetUserId}`);

      // Call the get-user-data Supabase function
      const { data, error } = await supabase.functions.invoke('get-user-data', {
        body: { user_id: targetUserId }
      });

      if (error) {
        debugError('Error fetching user data from edge function:', error);
        // Fall back to basic plan data
        throw new Error(`Edge function failed: ${error.message || 'Unknown error'}`);
      }

      // Transform the response to match UserData interface
      const isProUser = data?.planInfo?.id === 'pro';
      const userData: UserData = {
        subscription: data?.subscription || null,
        planInfo: data?.planInfo || {
          id: 'basic',
          name: 'Basic Plan',
          price: 0,
          credits: 10,
          period: 'monthly',
          features: [
            '1 practice problems',
            '10 credits for chat & assessment',
            'Community access',
            'Email support'
          ]
        },
        credits: data?.credits || 10,
        isProUser,
        canUpgrade: !isProUser
      };

      // Cache the result
      this.userDataCache.set(targetUserId, {
        data: userData,
        timestamp: now
      });

      debugLog('Fetched and cached user data:', userData);
      return userData;
    } catch (error) {
      debugError('Error in getUserData:', error);
      // Return default data for basic users
      return {
        subscription: null,
        planInfo: {
          id: 'basic',
          name: 'Basic Plan',
          price: 0,
          credits: 10,
          period: 'monthly',
          features: [
            '1 practice problems',
            '10 credits for chat & assessment',
            'Community access',
            'Email support'
          ]
        },
        credits: 10,
        isProUser: false,
        canUpgrade: true
      };
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(request: CreateSubscriptionRequest): Promise<CreateSubscriptionResponse> {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('User not authenticated');
      }

      const response = await supabase.functions.invoke('create-subscription', {
        body: request,
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (response.error) {
        console.error('Edge function error:', response.error);
        return {
          success: false,
          error: response.error.message || 'Failed to create subscription'
        };
      }

      // Ensure response has the expected structure
      const data = response.data;
      if (!data) {
        return {
          success: false,
          error: 'No data received from subscription service'
        };
      }

      // If the response doesn't have success field, add it based on payment_link presence
      if (data.payment_link && !('success' in data)) {
        return {
          success: true,
          payment_link: data.payment_link,
          subscription_id: data.subscription_id
        };
      }

      return data;
    } catch (error) {
      console.error('Error creating subscription:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Format price for display
   */
  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  /**
   * Check if user can access a specific question
   * Basic users can only access URL shortener (question ID: 1)
   * Pro users can access all questions
   */
  async canAccessQuestion(questionId: number, userId?: string): Promise<boolean> {
    try {
      debugLog(`Checking access for question ${questionId}`);

      // URL shortener question (ID: 1) is always accessible to all users
      if (questionId === 1) {
        debugLog(`Question ${questionId} is URL shortener - access granted to all users`);
        return true;
      }

      // For all other questions, check if user has pro plan via get-user-data function
      const hasProPlan = await this.hasActiveSubscription(userId);
      debugLog(`Question ${questionId} - user has pro plan: ${hasProPlan}`);
      return hasProPlan;
    } catch (error) {
      debugError('Error checking question access:', error);
      return false;
    }
  }

  /**
   * Get list of accessible question IDs for a user
   */
  async getAccessibleQuestionIds(userId?: string): Promise<number[]> {
    try {
      const hasProPlan = await this.hasActiveSubscription(userId);

      if (hasProPlan) {
        // Pro users can access all questions - return empty array to indicate no filtering needed
        debugLog('User has pro plan - access to all questions');
        return [];
      } else {
        // Basic users can only access URL shortener
        debugLog('User has basic plan - access only to URL shortener');
        return [1];
      }
    } catch (error) {
      debugError('Error getting accessible question IDs:', error);
      return [1]; // Default to basic access (URL shortener only)
    }
  }

  /**
   * Clear the user data cache
   */
  clearCache(userId?: string): void {
    if (userId) {
      this.userDataCache.delete(userId);
      debugLog(`Cleared cache for user: ${userId}`);
    } else {
      this.userDataCache.clear();
      debugLog('Cleared all user data cache');
    }
  }
}

export const subscriptionService = new SubscriptionService();

// Export clearCache function for external use
export const clearSubscriptionCache = (userId?: string) => {
  subscriptionService.clearCache(userId);
};
