import { toast } from 'sonner';
import { Node, Edge } from '@xyflow/react';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';
import { debuglog } from 'util';

// Define the expected structure of node data
interface NodeData {
  type?: string;
  label?: string;
  metadata?: Record<string, any>;
}

// Define interfaces for the assessment result
export interface AssessmentScore {
  rating: number;
  [key: string]: number;
}

interface ValidationResponse {
  overall_assessment: string;
  strengths: string[];
  weaknesses: string[];
  questions?: string[];
  rating: number;
  scoring_explanation: string;
  micro_analysis?: string;
  interview_questions?: string[];
}

export interface AssessmentResult {
  context: string;
  score: AssessmentScore;
  validationWarnings?: string[]; // Local validation warnings
  validationResponse?: ValidationResponse; // Properly typed validation response
}

// Define interface for the nested component structure
export interface ComponentPayload {
  id: string;
  type: string;
  label: string;
  metadata?: Record<string, any>;
  children?: {
    components: ComponentPayload[];
    connections: ConnectionPayload[];
  };
}

export interface ConnectionPayload {
  source: string;
  target: string;
  connectionNumber: string;
  label: string;
}

// Define interface for the assessment payload
export interface AssessmentPayload {
  components: ComponentPayload[];
  connections: ConnectionPayload[];
  context: {
    CUJs: string;
    assumptions: string;
    constraints: string;
  };
}

/**
 * Prepare a nested structure of components and connections for assessment
 */
export const prepareNestedStructure = (nodes: Node[], edges: Edge[]) => {
  const components: ComponentPayload[] = [];
  const connections: ConnectionPayload[] = [];

  // Create a map of parent IDs to their children
  const parentChildMap: Record<string, string[]> = {};

  // First pass: identify parent-child relationships
  nodes.forEach(node => {
    const parentId = node.parentId;
    if (parentId) {
      if (!parentChildMap[parentId]) {
        parentChildMap[parentId] = [];
      }
      parentChildMap[parentId].push(node.id);
    }
  });

  // Second pass: create component payloads for top-level nodes
  nodes.forEach(node => {
    // Skip if this is a child node
    if (node.parentId) return;

    const nodeData = node.data as NodeData | undefined;
    const component: ComponentPayload = {
      id: node.id,
      type: nodeData?.type || 'unknown',
      label: nodeData?.label || node.id,
      metadata: nodeData?.metadata || {}
    };

    // If this node has children, add them
    if (parentChildMap[node.id]) {
      component.children = {
        components: [],
        connections: []
      };

      // Add child components
      parentChildMap[node.id].forEach(childId => {
        const childNode = nodes.find(n => n.id === childId);
        if (childNode) {
          const childData = childNode.data as NodeData | undefined;
          component.children!.components.push({
            id: childNode.id,
            type: childData?.type || 'unknown',
            label: childData?.label || childNode.id,
            metadata: childData?.metadata || {}
          });
        }
      });

      // Add connections between children
      edges.forEach(edge => {
        const sourceNode = nodes.find(n => n.id === edge.source);
        const targetNode = nodes.find(n => n.id === edge.target);
        const sourceParentId = sourceNode?.parentId;
        const targetParentId = targetNode?.parentId;

        // Only include connections between children of this parent
        if (sourceParentId === node.id && targetParentId === node.id) {
          component.children!.connections.push({
            source: edge.source,
            target: edge.target,
            connectionNumber: (edge.data?.connectionNumber as string) || edge.id,
            label: edge.label as string
          });
        }
      });
    }

    components.push(component);
  });

  // Add top-level connections
  edges.forEach(edge => {
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);
    const sourceParentId = sourceNode?.parentId;
    const targetParentId = targetNode?.parentId;

    // Only include connections between top-level nodes
    if (!sourceParentId && !targetParentId) {
      connections.push({
        source: edge.source,
        target: edge.target,
        connectionNumber: (edge.data?.connectionNumber as string) || edge.id,
        label: edge.label as string
      });
    }
  });

  return { components, connections };
};



/**
 * Interface for the new structured backend response
 */
interface BackendAnalysisResponse {
  status: string;
  analysis: string;
  scores: {
    total: number;
    scalability: number;
    reliability: number;
    security: number;
    deliverySpeed: number;
  };
}

/**
 * Parse markdown analysis text to extract different sections
 */
const parseAnalysisMarkdown = (analysisText: string) => {
  const sections = {
    overall_assessment: '',
    micro_analysis: '',
    strengths: [] as string[],
    weaknesses: [] as string[],
    improvements: [] as string[],
    interview_questions: [] as string[],
    questions: [] as string[],
    scoring_explanation: '',
    scores: {} as Record<string, number>,
    overall_score: 0
  };

  // Split the text into lines for processing
  const lines = analysisText.split('\n');
  let currentSection = '';
  let currentContent: string[] = [];

  const processCurrentSection = () => {
    if (currentSection && currentContent.length > 0) {
      const content = currentContent.join('\n').trim();
      const sectionLower = currentSection.toLowerCase();
      debugLog("HERE")
      debugLog("Current section:", sectionLower);
      debugLog("Content:", content);
      switch (sectionLower) {
        case 'strengths':
          sections.strengths = extractListItems(content);
          break;
        case 'weaknesses / improvements':
          sections.weaknesses = extractListItems(content);
          break;
        case 'interview follow‑up questions':
        case 'interview follow-up questions':
        case 'interview questions':
          sections.interview_questions = extractListItems(content);
          break;
        case 'evaluation scores':
        case 'rating':
        case 'ratings':
        case 'scoring explanation':
        case 'evaluation':
          sections.scoring_explanation = content;
          break;
        case 'micro analysis':
          sections.micro_analysis = content;
          break;
        case 'scores':
        case 'Overall Score':
          const { scores, overall_score } = extractScores(content) || { scores: {}, overall_score: 0 };
          debugLog("Lets check")
          debugLog(scores)
          debugLog(overall_score)
          sections.scores = scores;
          sections.overall_score = overall_score;
          break;
        case 'architecture analysis':
        case 'component analysis':
          // These are main analysis sections - combine them for overall assessment
          if (sections.overall_assessment) {
            sections.overall_assessment += '\n\n' + content;
          } else {
            sections.overall_assessment = content;
          }
          break;
        default:
          // If it's the first section or unrecognized, treat as overall assessment
          break;
      }
    }
  };

  for (const line of lines) {
    // Check for section headers (## Section Name or ### Section Name)
    const headerMatch = line.match(/^#{2,3}\s+(.+)$/);
    if (headerMatch) {
      // Process previous section before starting new one
      processCurrentSection();

      currentSection = headerMatch[1].trim();
      currentContent = [];
      continue;
    }

    // Add line to current content
    currentContent.push(line);
  }

  // Process the last section
  processCurrentSection();

  // If no overall assessment was found, use the first part of the analysis
  if (!sections.overall_assessment) {
    // Try splitting by ### first, then by ##
    let firstPart = analysisText.split('###')[0].trim();
    if (!firstPart) {
      firstPart = analysisText.split('##')[0].trim();
    }
    sections.overall_assessment = firstPart || analysisText;
  }

  return sections;
};

/**
 * Extract list items from text content
 */
const extractListItems = (content: string): string[] => {
  const items: string[] = [];
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmed = line.trim();

    // Skip empty lines and headers
    if (!trimmed || trimmed.startsWith('#')) continue;

    // Handle numbered lists (1. item, 2. item)
    const numberedMatch = trimmed.match(/^\d+\.\s*(.+)$/);
    if (numberedMatch) {
      items.push(numberedMatch[1].trim());
      continue;
    }

    // Handle bullet points (- item, * item)
    const bulletMatch = trimmed.match(/^[-*]\s*(.+)$/);
    if (bulletMatch) {
      items.push(bulletMatch[1].trim());
      continue;
    }

    // Handle bold items (**item**)
    const boldMatch = trimmed.match(/^\*\*(.+?)\*\*:?\s*(.*)$/);
    if (boldMatch) {
      const title = boldMatch[1];
      const description = boldMatch[2];
      items.push(description ? `${title}: ${description}` : title);
      continue;
    }

    // If it's a regular line with content, add it as an item
    if (trimmed.length > 0) {
      // Remove any remaining bullet points or dashes at the start
      const cleanedItem = trimmed.replace(/^[•\-\*\+]\s*/, '').trim();
      if (cleanedItem.length > 0) {
        items.push(cleanedItem);
      }
    }
  }

  return items;
};

/**
 * Parse the agent analysis result and convert to AssessmentResult format
 * Now handles both structured JSON responses and legacy text responses
 */
const parseAgentAnalysisToAssessmentResult = (analysisResult: string, currentQuestion?: any): AssessmentResult => {
  // Try to parse as JSON first (new structured format)
  try {
    const parsedResponse: BackendAnalysisResponse = JSON.parse(analysisResult);



    if (parsedResponse.status === 'success' && parsedResponse.analysis && parsedResponse.scores) {
      // Extract structured data from the new format
      const analysis = parsedResponse.analysis;

      // Map scores directly - they're already on the correct scale (0-10)
      // Backend field mapping:
      // - 'total' -> 'rating' (overall score displayed at top)
      // - 'scalability' -> 'scalability' (displayed as "Scalability")
      // - 'fault_tolerance' -> 'fault_tolerance' (displayed as "Fault Tolerance")
      // - 'consistency' -> 'consistency' (displayed as "Consistency")
      

      // Parse sections from the analysis markdown text
      const parsedSections = parseAnalysisMarkdown(analysis);

      // Extract scores from the new JSON format first, fallback to parsed sections
      const scoresData = parsedResponse.scores
      debugLog("marking scores");
      debugLog(scoresData);
      
      const rating =  parsedSections.overall_score || 7;
      const scalability = scoresData?.scalability || parsedResponse.scores?.scalability || parsedSections.scores['Scalability'] || 7;
      const reliability = scoresData?.reliability || parsedSections.scores['Reliability'] || 7;
      const security = scoresData?.security || parsedSections.scores['Security'] || 7;
      const deliverability = scoresData?.deliverySpeed || parsedSections.scores['DeliverySpeed'] || 7;

      debugLog(parsedSections);
      console.log(parsedSections.overall_assessment)
      console.log(parsedSections.micro_analysis)
      console.log(parsedSections.strengths)
      console.log(parsedSections.weaknesses)
      console.log(parsedSections.interview_questions)
      console.log(parsedSections.scoring_explanation)

      // Create the validation response with parsed sections
      const validationResponse: ValidationResponse = {
        overall_assessment: parsedSections.overall_assessment || analysis,
        strengths: parsedSections.strengths.length > 0 ? parsedSections.strengths : [""],
        weaknesses: parsedSections.weaknesses.length > 0 ? parsedSections.weaknesses : [""],
        rating: rating,
        scoring_explanation: parsedSections.scoring_explanation || `Analysis completed with rating ${rating}/10. Scalability: ${scalability}/10, Fault Tolerance: ${reliability}/10, Consistency: ${security}/10.`,
        micro_analysis: parsedSections.micro_analysis || undefined,
        interview_questions: parsedSections.interview_questions
      };

      const result = {
        context: analysis,
        score: {
          rating: rating,  // Required for compatibility
          total_rating: rating,  // Overall score
          scalability: scalability,
          reliability: reliability,
          security: security,
          deliverability: deliverability
        },
        validationResponse: validationResponse
      };



      return result;
    }
  } catch (parseError) {
    debugLog('Response is not JSON, falling back to text parsing');
  }

  // Fallback to legacy text parsing for backward compatibility
  return parseLegacyTextResponse(analysisResult, currentQuestion);
};

/**
 * Extract sections from analysis text using improved parsing
 */
const extractSection = (text: string, sectionName: string): string[] => {
  const patterns = {
    'strengths': /\*\*(?:\d+\.\s*)?strengths?:?\*\*\s*(.*?)(?=\*\*(?:\d+\.\s*)?(?:weaknesses?|improvements?|suggestions?|scores?|explanation)|$)/is,
    'weaknesses': /\*\*(?:\d+\.\s*)?weaknesses?:?\*\*\s*(.*?)(?=\*\*(?:\d+\.\s*)?(?:strengths?|improvements?|suggestions?|scores?|explanation)|$)/is,
    'improvements': /\*\*(?:\d+\.\s*)?improvements?:?\*\*\s*(.*?)(?=\*\*(?:\d+\.\s*)?(?:strengths?|weaknesses?|scores?|explanation)|$)/is
  };

  const pattern = patterns[sectionName as keyof typeof patterns];
  if (!pattern) return [];

  const match = text.match(pattern);
  if (!match) return [];

  // Split by bullet points and clean up
  return match[1]
    .split(/[•\-\*]\s*/)
    .map(item => item.trim())
    .filter(item => item.length > 10) // Filter out very short items
    .map(item => item.replace(/\n+/g, ' ').trim())
    .slice(0, 5); // Limit to 5 items per section
};

/**
 * Legacy text parsing for backward compatibility
 */
const parseLegacyTextResponse = (analysisResult: string, currentQuestion?: any): AssessmentResult => {
  let rating = 7; // Default rating

  // Use question context to adjust default rating if available
  if (currentQuestion?.difficulty === 'easy') {
    rating = 8;
  } else if (currentQuestion?.difficulty === 'hard') {
    rating = 6;
  }

  // Try to extract rating from the text
  const ratingMatch = analysisResult.match(/(?:rating|score).*?(\d+(?:\.\d+)?)/i);
  if (ratingMatch) {
    rating = Math.min(10, Math.max(1, parseFloat(ratingMatch[1])));
  }

  // Extract sections using the improved parser
  const strengths = extractSection(analysisResult, 'strengths');
  const weaknesses = extractSection(analysisResult, 'weaknesses');

  // Create the validation response
  const validationResponse: ValidationResponse = {
    overall_assessment: analysisResult,
    strengths: strengths.length > 0 ? strengths : ["Good component organization", "Clear system structure"],
    weaknesses: weaknesses.length > 0 ? weaknesses : ["Could benefit from more detailed analysis"],
    questions: ["How will the system handle increased load?"],
    rating: rating,
    scoring_explanation: `Analysis completed with rating ${rating}/10 based on system design principles.`
  };

  return {
    context: analysisResult,
    score: {
      rating: rating,
      scalability: Math.max(1, rating - 1),
      fault_tolerance: Math.max(1, rating - 1),
      clarity: Math.min(10, rating + 1)
    },
    validationResponse: validationResponse
  };
};

/**
 * Assess a design using the System Design Agent with proper nested structure
 * Falls back to Supabase if the agent is unavailable
 */
export const assessDesign = async (payload: AssessmentPayload, currentQuestion?: any): Promise<AssessmentResult> => {
  debugLog("assessDesign called with payload:", {
    componentsCount: payload.components.length,
    connectionsCount: payload.connections.length,
    hasContext: !!payload.context,
    hasQuestion: !!currentQuestion
  });

  try {
    debugLog("Sending design for assessment to System Design Agent...");

    // Use the nested structure directly for the Layrs Agent
    const analysisResult = await analyzeDesignWithLayrsAgent(
      payload.components,
      payload.connections,
      payload.context.CUJs,
      payload.context.assumptions,
      payload.context.constraints,
      currentQuestion
    );

    debugLog("Received analysis result from Layrs Agent:", analysisResult);

    // Parse the analysis result and convert to AssessmentResult format
    const assessmentResult = parseAgentAnalysisToAssessmentResult(analysisResult, currentQuestion);

    debugLog("Assessment result parsed successfully:", {
      hasScore: !!assessmentResult.score,
      rating: assessmentResult.score?.rating,
      hasValidationResponse: !!assessmentResult.validationResponse
    });

    return assessmentResult;
  } catch (error) {
    debugError('Error using Layrs Agent service:', error);
    debugError('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'Unknown'
    });

    // Show more specific error message
    if (error instanceof Error && error.message.includes('fetch')) {
      toast.warning("Network error occurred, falling back to Supabase function");
    } else {
      toast.warning("Layrs Agent service unavailable, falling back to Supabase function");
    }

    // Fall back to Supabase function
    try {
      debugLog("Attempting fallback to Supabase...");
      return await assessDesignWithSupabase(payload, currentQuestion);
    } catch (fallbackError) {
      debugError('Fallback to Supabase also failed:', fallbackError);
      throw new Error('Both primary and fallback assessment services failed');
    }
  }
};

/**
 * Analyze design with the Layrs Agent using proper nested structure
 */
const analyzeDesignWithLayrsAgent = async (
  components: ComponentPayload[],
  connections: ConnectionPayload[],
  userJourneys?: string,
  assumptions?: string,
  constraints?: string,
  currentQuestion?: any
): Promise<string> => {
  try {
    // Transform components to the format expected by Layrs Agent (with nested structure)
    const transformedComponents = transformComponentsForLayrsAgent(components);
    const transformedConnections = transformConnectionsForLayrsAgent(connections);

    // Prepare the design data in the format expected by the backend
    const designData = {
      problem: currentQuestion?.description || currentQuestion?.title || "System Design Problem",
      system_name: currentQuestion?.title || "System Design",
      components: transformedComponents,
      connections: transformedConnections,
      core_user_journey: userJourneys || "",
      functional_requirements: assumptions ? assumptions.split('\n').filter(a => a.trim()) : [],
      non_functional_requirements: constraints ? constraints.split('\n').filter(c => c.trim()) : []
    };

    // console.log("Sending design data to Layrs Agent:");
    // console.log("- Components:", transformedComponents.length);
    // console.log("- Connections:", transformedConnections.length);
    // console.log("- Composite components with nested structure:",
    //   transformedComponents.filter(c => c.type === 'composite' && c.components).length);
    // console.log("Full design data:", JSON.stringify(designData, null, 2));

    debugLog("Making API request to Layrs Agent...");

    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

    try {
      const response = await fetch('https://api.layrs.me/api/agent/analyze-design', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify({
          design: designData,
        }),
        signal: controller.signal
      });

      console.log(response);

      clearTimeout(timeoutId);
      debugLog('Layrs Agent response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        debugError('Layrs Agent request failed:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });

        // Provide user-friendly error messages based on status code
        let userMessage = "We're having trouble analyzing your design right now.";
        if (response.status >= 500) {
          userMessage = "Our analysis service is temporarily unavailable. Please try again in a few moments.";
        } else if (response.status === 429) {
          userMessage = "Too many requests. Please wait a moment before trying again.";
        } else if (response.status === 401 || response.status === 403) {
          userMessage = "Authentication issue. Please refresh the page and try again.";
        } else if (response.status === 400) {
          userMessage = "There's an issue with your design data. Please try simplifying your design and try again.";
        }

        throw new Error(userMessage);
      }

      const data = await response.json();
      debugLog('Layrs Agent response data:', data);

      if (data.error) {
        throw new Error(data.error);
      }

      // Return the full response as JSON string so the assessment service can parse it properly
      if (data.scores) {
        debugLog("Returning structured response with scores");
        return JSON.stringify(data);
      }

      // Fallback for responses without scores (legacy format)
      debugLog("Returning legacy format response");
      return data.analysis || "Analysis completed successfully.";
    } catch (fetchError) {
      clearTimeout(timeoutId);

      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        throw new Error('Request timed out after 30 seconds');
      }

      throw fetchError;
    }
  } catch (error) {
    debugError("Error during Layrs Agent analysis:", error);
    throw error;
  }
};

/**
 * Valid component subtypes recognized by Layrs Agent
 */
const VALID_SUBTYPES = [
  // Client-side components
  'client', 'mobile_app', 'web_app', 'desktop_app',

  // Network components
  'load_balancer', 'api_gateway', 'cdn', 'firewall', 'proxy',

  // Server components
  'server', 'web_server', 'application_server', 'auth_server',

  // Storage components
  'db', 'sql_db', 'nosql_db', 'document_db', 'graph_db', 'time_series_db',
  'object_storage', 'file_system', 'cache',

  // Messaging components
  'message_queue', 'event_bus', 'stream_processor',

  // Compute components
  'container', 'serverless', 'batch_processor',

  // Monitoring components
  'monitoring', 'logging', 'alerting'
];

/**
 * Transform components to the format expected by Layrs Agent
 */
const transformComponentsForLayrsAgent = (components: ComponentPayload[]): any[] => {
  return components.map(component => {
    // Determine the component subtype (no longer used in payload)
    // const subtype = getSubtypeFromComponent(component);

    const result: any = {
      id: component.id,
      type: component.type === 'composite' ? 'composite' : 'primitive',
      // subtype: subtype, // <-- REMOVE THIS LINE
      label: component.label || component.id
    };

    // Add enhanced metadata based on component subtype
    // result.metadata = enhanceMetadata(component.metadata || {}, subtype, component.type);
    result.metadata = component.metadata || {};

    // Add nested components if this is a composite component
    if (component.children) {
      result.components = transformComponentsForLayrsAgent(component.children.components);

      // Add connections between children if available
      if (component.children.connections && component.children.connections.length > 0) {
        result.connections = transformConnectionsForLayrsAgent(component.children.connections);
      }
    }

    return result;
  });
};

/**
 * Transform connections to the format expected by Layrs Agent
 */
const transformConnectionsForLayrsAgent = (connections: ConnectionPayload[]): any[] => {
  return connections.map((connection, index) => ({
    id: connection.connectionNumber || `conn-${index}`,
    start: connection.source,
    end: connection.target,
    // Use connectionNumber as the label directly
    label: connection.label || `${index + 1}`
  }));
};

/**
 * Get the subtype of a component based on its type
 */
const getSubtypeFromComponent = (component: ComponentPayload): string => {
  // If the component already has a subtype in metadata, use it
  if (component.metadata?.subtype) {
    const subtype = component.metadata.subtype as string;
    // Validate that it's a recognized subtype
    if (VALID_SUBTYPES.includes(subtype.toLowerCase())) {
      return subtype.toLowerCase();
    }
    // If not valid, we'll infer from the type
  }

  // Otherwise, infer from the type
  const typeMapping: Record<string, string> = {
    'database': 'sql_db',
    'nosql': 'nosql_db',
    'mongodb': 'nosql_db',
    'redis': 'cache',
    'memcached': 'cache',
    'server': 'application_server',
    'web': 'web_server',
    'client': 'web_app',
    'mobile': 'mobile_app',
    'desktop': 'desktop_app',
    'load_balancer': 'load_balancer',
    'api_gateway': 'api_gateway',
    'cdn': 'cdn',
    'queue': 'message_queue',
    'kafka': 'message_queue',
    'rabbitmq': 'message_queue',
    'eventbus': 'event_bus',
    'lambda': 'serverless',
    'function': 'serverless',
    'container': 'container',
    'docker': 'container',
    'kubernetes': 'container',
    'monitoring': 'monitoring',
    'logging': 'logging',
    'alerting': 'alerting',
    'composite': 'composite'
  };

  // Try to match the type to a known mapping
  const lowerType = component.type.toLowerCase();
  for (const [key, value] of Object.entries(typeMapping)) {
    if (lowerType.includes(key)) {
      return value;
    }
  }

  // Default fallback - use server as a safe default
  debugWarn(`Unknown component type: ${component.type}, defaulting to 'server'`);
  return 'server';
};

/**
 * Enhance metadata based on component subtype
 */
const enhanceMetadata = (
  originalMetadata: Record<string, any>,
  subtype: string,
  _componentType: string // Prefixed with underscore to indicate it's not used
): Record<string, any> => {
  // Create a copy of the original metadata
  const metadata = { ...originalMetadata };

  // Add subtype-specific metadata if not already present
  switch (subtype) {
    case 'sql_db':
    case 'nosql_db':
    case 'db':
      if (!metadata.purpose) {
        metadata.purpose = `stores ${subtype === 'sql_db' ? 'relational' : 'application'} data`;
      }

      // Add schema if not present
      if (!metadata.schema) {
        metadata.schema = {
          "users": {
            "id": "primary key",
            "username": "string",
            "email": "string",
            "created_at": "timestamp"
          },
          "items": {
            "id": "primary key",
            "name": "string",
            "description": "text",
            "user_id": "foreign key references users(id)"
          }
        };
      }
      break;

    case 'cache':
      if (!metadata.purpose) {
        metadata.purpose = "caches frequently accessed data";
      }

      if (!metadata.config) {
        metadata.config = {
          "key_pattern": "resource:id",
          "ttl": "60 minutes",
          "eviction_policy": "lru"
        };
      }
      break;

    case 'server':
    case 'web_server':
    case 'application_server':
      if (!metadata.logic) {
        metadata.logic = `handles ${subtype === 'web_server' ? 'HTTP requests' : 'business logic'}`;
      }

      if (!metadata.api_endpoints && (subtype === 'application_server' || subtype === 'web_server')) {
        metadata.api_endpoints = [
          "GET /api/resource - Retrieve resources",
          "POST /api/resource - Create a new resource",
          "PUT /api/resource/:id - Update a resource",
          "DELETE /api/resource/:id - Delete a resource"
        ];
      }
      break;

    case 'load_balancer':
      if (!metadata.algorithm) {
        metadata.algorithm = "round-robin";
      }

      if (!metadata.health_checks) {
        metadata.health_checks = {
          "endpoint": "/health",
          "interval": "5s",
          "timeout": "3s"
        };
      }
      break;

    case 'message_queue':
    case 'event_bus':
      if (!metadata.topics) {
        metadata.topics = [
          "user-events: Events related to user actions",
          "system-events: System-level events"
        ];
      }

      if (!metadata.message_format) {
        metadata.message_format = "json";
      }
      break;
  }

  return metadata;
};



/**
 * Extract scores from the new scoring format
 */
export const extractScores = (responseData: any): { scores: Record<string, number>; overall_score: number } | null => {
  try {
    let jsonStr = typeof responseData === 'string' ? responseData.trim() : responseData;

    if (typeof jsonStr === 'string') {
      // Remove markdown backticks if present
      if (jsonStr.startsWith('```json') || jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```json/, '').replace(/^```/, '').replace(/```$/, '').trim();
      }

      responseData = JSON.parse(jsonStr);
    }

    debugLog("Parsed response data:", responseData);
    debugLog("Type of parsed response data:", typeof responseData);
    if (responseData && responseData.scores && typeof responseData.overall_score === 'number') {
      const { scores, overall_score } = responseData;
      debugLog("Extracting scores");
      debugLog(scores);
      debugLog(overall_score);
      
      // Validate that scores is an object with numeric values
      if (typeof scores === 'object' && scores !== null) {
        const validScores: Record<string, number> = {};
        let hasValidScores = false;
        
        for (const [key, value] of Object.entries(scores)) {
          debugLog(`Score key: ${key}, value: ${value}`);
          debugLog('Score value type:', typeof value);
          debugLog('Score key type', typeof key);
          if (typeof value === 'number') {
            validScores[key] = value;
            hasValidScores = true;
          }
        }
        
        if (hasValidScores) {
          return {
            scores: validScores,
            overall_score
          };
        }
      }
    }
    
    return null;
  } catch (error) {
    debugError('Error extracting scores:', error);
    return null;
  }
};

/**
 * Fallback method to assess a design using the Supabase function
 */
const assessDesignWithSupabase = async (payload: AssessmentPayload, currentQuestion?: any): Promise<AssessmentResult> => {
  try {
    debugLog("Falling back to Supabase function for assessment...");

    // Enhance the payload with question information if available
    const enhancedPayload = {
      ...payload,
      question: currentQuestion ? {
        title: currentQuestion.title,
        description: currentQuestion.description,
        requirements: currentQuestion.requirements,
        constraints: currentQuestion.constraints
      } : undefined
    };

    const response = await fetch('https://tcdieywqoophloivzszp.supabase.co/functions/v1/openai-request-handler', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      body: JSON.stringify(enhancedPayload),
    });

    debugLog('Supabase assessment response status:', response.status);
    debugLog('Supabase assessment response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      debugError('Supabase assessment request failed:', {
        status: response.status,
        statusText: response.statusText,
        body: errorText
      });

      // Provide user-friendly error messages based on status code
      let userMessage = "We're having trouble analyzing your design right now.";
      if (response.status >= 500) {
        userMessage = "Our analysis service is temporarily unavailable. Please try again in a few moments.";
      } else if (response.status === 429) {
        userMessage = "Too many requests. Please wait a moment before trying again.";
      } else if (response.status === 401 || response.status === 403) {
        userMessage = "Authentication issue. Please refresh the page and try again.";
      } else if (response.status === 400) {
        userMessage = "There's an issue with your design data. Please try simplifying your design and try again.";
      }

      throw new Error(userMessage);
    }

    // Parse the response
    const data = await response.json();

    // Extract the content from the OpenAI response format
    if (data.choices && data.choices[0] && data.choices[0].message) {
      const contentStr = data.choices[0].message.content;
      // Parse the content string which itself is a JSON string
      try {
        return JSON.parse(contentStr);
      } catch (jsonError) {
        debugError('Error parsing JSON content:', jsonError);
        return {
          context: contentStr || "Assessment completed, but couldn't parse the structured feedback.",
          score: { rating: 0 }
        };
      }
    } else if (data.feedback) {
      // Handle direct feedback format
      return {
        context: data.feedback,
        score: data.scores || { rating: 0 }
      };
    } else {
      throw new Error("Unexpected response format");
    }
  } catch (error) {
    debugError('Error during Supabase assessment:', error);
    throw error;
  }
};


