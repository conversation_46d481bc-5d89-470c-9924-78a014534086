/**
 * Chat History Service - Performance-First with Complete Persistence
 * Handles loading, storing, and caching of chat conversations
 */

import { ChatMessage } from './chatService';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

export interface BackendMessage {
  id: string;
  session_id: string;
  context_type: string;
  context_id: string;
  role: 'user' | 'assistant';
  content: string;
  design_context?: any;
  created_at: string;
  timestamp?: string;
}

export interface ChatHistoryResponse {
  messages: BackendMessage[];
  total_count?: number;
  has_more?: boolean;
}

export interface StoredChatSession {
  messages: ChatMessage[];
  sessionId: string;
  lastUpdated: number;
  contextType: string;
  contextId: string;
  totalMessages: number;
}

export interface ChatCache {
  [contextKey: string]: StoredChatSession;
}

class ChatHistoryManager {
  private cache: ChatCache = {};
  private readonly STORAGE_KEY = 'layrs_chat_sessions';
  private readonly MAX_ACTIVE_SESSIONS = 10;
  private readonly MESSAGES_PER_SESSION = 5;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes
  private readonly SUPABASE_URL = 'https://tcdieywqoophloivzszp.supabase.co';
  private readonly SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRjZGlleXdxb29waGxvaXZ6c3pwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3NDMzMzksImV4cCI6MjA2MjMxOTMzOX0.Ypel9zPDOVJVBiEZFT-__cTZqgQL2-prOxiUXjieihM';

  constructor() {
    this.loadFromLocalStorage();
    this.setupOnlineOfflineHandlers();
  }

  /**
   * Generate cache key for context
   */
  private getCacheKey(contextType: string, contextId: string): string {
    return `${contextType}-${contextId}`;
  }

  /**
   * Load chat sessions from localStorage on initialization
   */
  private loadFromLocalStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        this.cache = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading chat sessions from localStorage:', error);
      this.cache = {};
    }
  }

  /**
   * Save chat sessions to localStorage
   */
  private saveToLocalStorage(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.cache));
    } catch (error) {
      console.error('Error saving chat sessions to localStorage:', error);
      // If storage is full, cleanup old sessions and try again
      this.cleanupOldSessions(5);
      try {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.cache));
      } catch (retryError) {
        console.error('Failed to save even after cleanup:', retryError);
      }
    }
  }

  /**
   * Setup online/offline event handlers
   */
  private setupOnlineOfflineHandlers(): void {
    window.addEventListener('online', () => {
      debugLog('🌐 Back online - chat sync available');
    });

    window.addEventListener('offline', () => {
      debugLog('📱 Offline mode - using cached chats only');
    });
  }

  /**
   * Check if we're online
   */
  private isOnline(): boolean {
    return navigator.onLine;
  }

  /**
   * Convert backend message format to frontend ChatMessage format
   */
  private convertToFrontendFormat(backendMessages: BackendMessage[]): ChatMessage[] {
    return backendMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      sender: msg.role,
      timestamp: new Date(msg.created_at || msg.timestamp || Date.now())
    }));
  }

  /**
   * Check if cached data should be refreshed
   */
  private shouldRefreshCache(session: StoredChatSession): boolean {
    if (!this.isOnline()) return false;

    const now = Date.now();
    const timeSinceUpdate = now - session.lastUpdated;
    return timeSinceUpdate > this.CACHE_DURATION;
  }

  /**
   * Cleanup old sessions to free up localStorage space
   */
  private cleanupOldSessions(keepCount: number = this.MAX_ACTIVE_SESSIONS): void {
    const sessions = Object.entries(this.cache);

    if (sessions.length <= keepCount) return;

    // Sort by lastUpdated (oldest first)
    sessions.sort(([, a], [, b]) => a.lastUpdated - b.lastUpdated);

    // Remove oldest sessions
    const toRemove = sessions.slice(0, sessions.length - keepCount);
    toRemove.forEach(([key]) => {
      delete this.cache[key];
    });

    debugLog(`🗑️ Cleaned up ${toRemove.length} old chat sessions`);
  }

  /**
   * Load chat history for a specific context (Performance-First)
   */
  async loadChatHistory(contextType: string, contextId: string, sessionId?: string): Promise<ChatMessage[]> {
    const cacheKey = this.getCacheKey(contextType, contextId);
    const cached = this.cache[cacheKey];

    // 1. Return cached data immediately if available (Performance First!)
    if (cached && cached.messages.length > 0) {
      debugLog(`⚡ Instant load: ${cached.messages.length} messages from cache for ${contextType}:${contextId}`);

      // Background refresh if cache is stale and we're online
      if (this.shouldRefreshCache(cached)) {
        this.refreshCacheInBackground(contextType, contextId, sessionId);
      }

      return cached.messages;
    }

    // 2. No cache - fetch from backend if online
    if (this.isOnline()) {
      return await this.fetchFromBackend(contextType, contextId, sessionId);
    }

    // 3. Offline with no cache - return empty
    debugLog(`📱 Offline mode: No cached messages for ${contextType}:${contextId}`);
    return [];
  }

  /**
   * Fetch chat history from Supabase backend
   */
  private async fetchFromBackend(contextType: string, contextId: string, sessionId?: string): Promise<ChatMessage[]> {
    try {
      debugLog(`🌐 Fetching from Supabase: ${contextType}:${contextId}`);

      // Build Supabase query URL
      let url = `${this.SUPABASE_URL}/rest/v1/conversation_history?context_type=eq.${contextType}&context_id=eq.${contextId}&order=timestamp.desc&limit=${this.MESSAGES_PER_SESSION}`;

      if (sessionId) {
        url += `&session_id=eq.${sessionId}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'apikey': this.SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          debugLog(`📭 No chat history found for ${contextType}:${contextId}`);
          return [];
        }
        throw new Error(`Failed to load chat history: ${response.status}`);
      }

      const data: BackendMessage[] = await response.json();

      // Convert and reverse to get chronological order (oldest first)
      const messages = this.convertToFrontendFormat(data.reverse());

      // Cache the results
      this.updateCache(contextType, contextId, messages, sessionId || data[0]?.session_id || '', data.length);

      debugLog(`✅ Loaded ${messages.length} messages from Supabase for ${contextType}:${contextId}`);
      return messages;

    } catch (error) {
      console.error('Error fetching from Supabase:', error);
      return [];
    }
  }

  /**
   * Refresh cache in background (non-blocking)
   */
  private async refreshCacheInBackground(contextType: string, contextId: string, sessionId?: string): Promise<void> {
    try {
      debugLog(`🔄 Background refresh for ${contextType}:${contextId}`);
      await this.fetchFromBackend(contextType, contextId, sessionId);
    } catch (error) {
      console.warn('Background refresh failed:', error);
    }
  }

  /**
   * Update cache with new messages
   */
  private updateCache(contextType: string, contextId: string, messages: ChatMessage[], sessionId: string, totalCount: number): void {
    const cacheKey = this.getCacheKey(contextType, contextId);

    this.cache[cacheKey] = {
      messages,
      sessionId,
      lastUpdated: Date.now(),
      contextType,
      contextId,
      totalMessages: totalCount
    };

    // Cleanup if we have too many sessions
    if (Object.keys(this.cache).length > this.MAX_ACTIVE_SESSIONS) {
      this.cleanupOldSessions();
    }

    this.saveToLocalStorage();
  }

  /**
   * Add a new message to the cache (real-time updates)
   */
  addMessageToCache(contextType: string, contextId: string, message: ChatMessage, sessionId: string): void {
    const cacheKey = this.getCacheKey(contextType, contextId);

    if (!this.cache[cacheKey]) {
      this.cache[cacheKey] = {
        messages: [],
        sessionId,
        lastUpdated: Date.now(),
        contextType,
        contextId,
        totalMessages: 0
      };
    }

    // Add message and keep only last N messages
    this.cache[cacheKey].messages.push(message);
    if (this.cache[cacheKey].messages.length > this.MESSAGES_PER_SESSION) {
      this.cache[cacheKey].messages = this.cache[cacheKey].messages.slice(-this.MESSAGES_PER_SESSION);
    }

    this.cache[cacheKey].lastUpdated = Date.now();
    this.cache[cacheKey].totalMessages++;

    this.saveToLocalStorage();
    debugLog(`💾 Added message to cache for ${contextType}:${contextId}`);

    // Background sync to Supabase if online
    if (this.isOnline()) {
      this.syncMessageToSupabase(message, sessionId, contextType, contextId).catch(error => {
        console.warn('Failed to sync message to Supabase:', error);
      });
    }
  }

  /**
   * Ensure session exists in user_sessions table
   */
  private async ensureSessionExists(sessionId: string, contextType: string, contextId: string): Promise<void> {
    try {
      // Check if session exists
      const checkResponse = await fetch(`${this.SUPABASE_URL}/rest/v1/user_sessions?session_id=eq.${sessionId}`, {
        method: 'GET',
        headers: {
          'apikey': this.SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      if (checkResponse.ok) {
        const sessions = await checkResponse.json();
        if (sessions.length > 0) {
          return; // Session already exists
        }
      }

      // Create session if it doesn't exist
      const createResponse = await fetch(`${this.SUPABASE_URL}/rest/v1/user_sessions`, {
        method: 'POST',
        headers: {
          'apikey': this.SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          problem_statement: `Chat session for ${contextType}:${contextId}`,
          metadata: {
            context_type: contextType,
            context_id: contextId,
            created_by: 'chat_history_service'
          }
        })
      });

      if (!createResponse.ok) {
        throw new Error(`Failed to create session: ${createResponse.status}`);
      }

      debugLog(`✅ Created session ${sessionId} for ${contextType}:${contextId}`);
    } catch (error) {
      console.warn('Failed to ensure session exists:', error);
    }
  }

  /**
   * Sync message to Supabase backend
   */
  private async syncMessageToSupabase(message: ChatMessage, sessionId: string, contextType: string, contextId: string): Promise<void> {
    try {
      // Ensure session exists first
      await this.ensureSessionExists(sessionId, contextType, contextId);

      const response = await fetch(`${this.SUPABASE_URL}/rest/v1/conversation_history`, {
        method: 'POST',
        headers: {
          'apikey': this.SUPABASE_ANON_KEY,
          'Authorization': `Bearer ${this.SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: sessionId,
          role: message.sender,
          content: message.content,
          context_type: contextType,
          context_id: contextId,
          timestamp: message.timestamp.toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to sync message: ${response.status}`);
      }

      debugLog(`✅ Synced message to Supabase for ${contextType}:${contextId}`);
    } catch (error) {
      console.error('Failed to sync message to Supabase:', error);
      // TODO: Add to retry queue for offline sync
    }
  }

  /**
   * Clear cache for a specific context (New Session)
   */
  clearCache(contextType: string, contextId: string): void {
    const cacheKey = this.getCacheKey(contextType, contextId);
    delete this.cache[cacheKey];
    this.saveToLocalStorage();
    debugLog(`🗑️ Cleared cache for ${contextType}:${contextId}`);
  }

  /**
   * Get cache status for debugging
   */
  getCacheStatus(): { [key: string]: { messageCount: number; lastUpdated: Date; isStale: boolean; totalMessages: number } } {
    const status: any = {};

    Object.entries(this.cache).forEach(([key, session]) => {
      status[key] = {
        messageCount: session.messages.length,
        lastUpdated: new Date(session.lastUpdated),
        isStale: this.shouldRefreshCache(session),
        totalMessages: session.totalMessages
      };
    });

    return status;
  }

  /**
   * Check if we have cached data for a context
   */
  hasCachedData(contextType: string, contextId: string): boolean {
    const cacheKey = this.getCacheKey(contextType, contextId);
    return !!(this.cache[cacheKey] && this.cache[cacheKey].messages.length > 0);
  }

  /**
   * Get online status
   */
  getOnlineStatus(): boolean {
    return this.isOnline();
  }
}

// Global instance
export const chatHistoryManager = new ChatHistoryManager();

// Export convenience functions
export const loadChatHistory = (contextType: string, contextId: string, sessionId?: string) =>
  chatHistoryManager.loadChatHistory(contextType, contextId, sessionId);

export const addMessageToCache = (contextType: string, contextId: string, message: ChatMessage, sessionId: string) =>
  chatHistoryManager.addMessageToCache(contextType, contextId, message, sessionId);

export const clearChatCache = (contextType: string, contextId: string) =>
  chatHistoryManager.clearCache(contextType, contextId);

export const hasCachedChatData = (contextType: string, contextId: string) =>
  chatHistoryManager.hasCachedData(contextType, contextId);

export const getChatCacheStatus = () =>
  chatHistoryManager.getCacheStatus();

export const isOnline = () =>
  chatHistoryManager.getOnlineStatus();

// Make available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).layrsDebug = {
    ...(window as any).layrsDebug,
    chatHistory: {
      loadChatHistory,
      addMessageToCache,
      clearChatCache,
      hasCachedChatData,
      getCacheStatus: getChatCacheStatus,
      isOnline,
      manager: chatHistoryManager,
      // Debug functions
      testCachePerformance: async () => {
        debugLog('🧪 Testing cache performance...');
        const start = performance.now();

        // Test loading from cache
        const cached = await loadChatHistory('question', '1');
        const cacheTime = performance.now() - start;

        debugLog(`⚡ Cache load time: ${cacheTime.toFixed(2)}ms`);
        debugLog(`📋 Cached messages: ${cached.length}`);

        return { cacheTime, messageCount: cached.length };
      },

      simulateOffline: () => {
        debugLog('📱 Simulating offline mode...');
        Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
        window.dispatchEvent(new Event('offline'));
      },

      simulateOnline: () => {
        debugLog('🌐 Simulating online mode...');
        Object.defineProperty(navigator, 'onLine', { value: true, writable: true });
        window.dispatchEvent(new Event('online'));
      }
    }
  };
}
