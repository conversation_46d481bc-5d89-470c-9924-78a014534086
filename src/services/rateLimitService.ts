interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDurationMs?: number;
}

interface RateLimitEntry {
  count: number;
  firstRequest: number;
  blocked?: boolean;
  blockUntil?: number;
}

class RateLimitService {
  private ipLimits = new Map<string, RateLimitEntry>();
  private emailLimits = new Map<string, RateLimitEntry>();
  private globalLimits = new Map<string, RateLimitEntry>();

  // Different limits for different scenarios
  private configs = {
    // Per IP limits
    ip: {
      windowMs: 60000, // 1 minute
      maxRequests: 10, // 10 requests per minute per IP
      blockDurationMs: 300000 // 5 minute block
    },
    // Per email limits  
    email: {
      windowMs: 300000, // 5 minutes
      maxRequests: 3, // 3 attempts per 5 minutes per email
      blockDurationMs: 900000 // 15 minute block
    },
    // Global limits (all requests)
    global: {
      windowMs: 60000, // 1 minute
      maxRequests: 100, // 100 total requests per minute
      blockDurationMs: 60000 // 1 minute block
    }
  };

  private getClientIP(): string {
    // In a real app, get this from request headers
    // For now, simulate with a random IP for testing
    if (typeof window !== 'undefined') {
      return 'client-browser'; // Browser fallback
    }
    return '127.0.0.1';
  }

  private cleanupExpiredEntries(limitsMap: Map<string, RateLimitEntry>, windowMs: number) {
    const now = Date.now();
    for (const [key, entry] of limitsMap.entries()) {
      // Remove expired entries
      if (now - entry.firstRequest > windowMs) {
        limitsMap.delete(key);
      }
      // Remove expired blocks
      if (entry.blocked && entry.blockUntil && now > entry.blockUntil) {
        limitsMap.delete(key);
      }
    }
  }

  private checkLimit(
    key: string, 
    limitsMap: Map<string, RateLimitEntry>, 
    config: RateLimitConfig
  ): { allowed: boolean; retryAfter?: number; reason?: string } {
    const now = Date.now();
    
    // Cleanup expired entries
    this.cleanupExpiredEntries(limitsMap, config.windowMs);
    
    const entry = limitsMap.get(key);
    
    // Check if currently blocked
    if (entry?.blocked && entry.blockUntil && now < entry.blockUntil) {
      return {
        allowed: false,
        retryAfter: Math.ceil((entry.blockUntil - now) / 1000),
        reason: 'Temporarily blocked due to rate limit violation'
      };
    }
    
    // If no entry or window expired, create new entry
    if (!entry || (now - entry.firstRequest) > config.windowMs) {
      limitsMap.set(key, {
        count: 1,
        firstRequest: now
      });
      return { allowed: true };
    }
    
    // Increment count
    entry.count++;
    
    // Check if limit exceeded
    if (entry.count > config.maxRequests) {
      // Block if configured
      if (config.blockDurationMs) {
        entry.blocked = true;
        entry.blockUntil = now + config.blockDurationMs;
      }
      
      return {
        allowed: false,
        retryAfter: config.blockDurationMs ? Math.ceil(config.blockDurationMs / 1000) : Math.ceil((config.windowMs - (now - entry.firstRequest)) / 1000),
        reason: 'Rate limit exceeded'
      };
    }
    
    return { allowed: true };
  }

  public checkWaitlistRateLimit(email: string): { 
    allowed: boolean; 
    retryAfter?: number; 
    reason?: string;
    remainingRequests?: number;
  } {
    const ip = this.getClientIP();
    const globalKey = 'global';
    
    // Check global limits first
    const globalCheck = this.checkLimit(globalKey, this.globalLimits, this.configs.global);
    if (!globalCheck.allowed) {
      return {
        ...globalCheck,
        reason: 'Service temporarily unavailable due to high traffic'
      };
    }
    
    // Check IP limits
    const ipCheck = this.checkLimit(ip, this.ipLimits, this.configs.ip);
    if (!ipCheck.allowed) {
      return {
        ...ipCheck,
        reason: 'Too many requests from your location'
      };
    }
    
    // Check email limits
    const emailCheck = this.checkLimit(email, this.emailLimits, this.configs.email);
    if (!emailCheck.allowed) {
      return {
        ...emailCheck,
        reason: 'Too many attempts for this email address'
      };
    }
    
    // Calculate remaining requests
    const ipEntry = this.ipLimits.get(ip);
    const remainingRequests = ipEntry 
      ? Math.max(0, this.configs.ip.maxRequests - ipEntry.count)
      : this.configs.ip.maxRequests;
    
    return { 
      allowed: true,
      remainingRequests
    };
  }

  public getStats() {
    return {
      activeIPs: this.ipLimits.size,
      activeEmails: this.emailLimits.size,
      globalRequests: this.globalLimits.get('global')?.count || 0
    };
  }

  // Emergency brake - block all requests
  public enableEmergencyMode(durationMs: number = 300000) { // 5 minutes default
    const now = Date.now();
    this.globalLimits.set('emergency', {
      count: 999999,
      firstRequest: now,
      blocked: true,
      blockUntil: now + durationMs
    });
  }

  public disableEmergencyMode() {
    this.globalLimits.delete('emergency');
  }
}

// Singleton instance
export const rateLimitService = new RateLimitService();

// Enhanced waitlist function with comprehensive rate limiting
export const checkWaitlistSecurity = (email: string) => {
  return rateLimitService.checkWaitlistRateLimit(email);
};

// Honeypot detection
export const detectSuspiciousActivity = (formData: any): boolean => {
  // Check for honeypot fields (hidden fields that bots might fill)
  if (formData.website || formData.phone || formData.company) {
    return true;
  }
  
  // Check for suspicious patterns
  if (formData.email && (
    formData.email.includes('test') ||
    formData.email.includes('bot') ||
    formData.email.includes('spam') ||
    formData.email.length > 100
  )) {
    return true;
  }
  
  return false;
};

// Request fingerprinting for bot detection
export const generateRequestFingerprint = (): string => {
  if (typeof window === 'undefined') return 'server';
  
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Bot detection', 2, 2);
  }
  
  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + 'x' + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL()
  ].join('|');
  
  return btoa(fingerprint).substring(0, 32);
};
