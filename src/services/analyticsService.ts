import { supabase } from '@/lib/supabase';
import { track } from '@vercel/analytics';
import { debugLog, debugWarn, debugError } from '@/utils/debugLogger';

// Types for analytics events
export interface ChatAnalytics {
  id?: string;
  user_id: string;
  session_id: string;
  message_count: number;
  conversation_duration_seconds?: number;
  question_id?: string;
  context_type?: 'free' | 'question' | 'guided';
  context_id?: string;
  created_at?: string;
}

export interface AssessmentAnalytics {
  id?: string;
  user_id: string;
  question_id: string;
  context_type: 'question' | 'guided';
  context_id: string;
  score: number;
  duration_seconds: number;
  node_count: number;
  edge_count: number;
  components_used: string[];
  is_best_score: boolean;
  created_at?: string;
}

export interface UserSessionAnalytics {
  id?: string;
  user_id: string;
  session_start: string;
  session_end?: string;
  page_views: number;
  actions_performed: number;
  questions_attempted: string[];
  created_at?: string;
}

// Chat Analytics Functions
export const chatAnalytics = {
  // Start a new chat session
  startChatSession: async (
    userId: string,
    questionId?: string,
    contextType: 'free' | 'question' | 'guided' = 'free',
    contextId?: string
  ): Promise<string> => {
    const sessionId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      const { error } = await supabase
        .from('chat_analytics')
        .insert({
          user_id: userId,
          session_id: sessionId,
          message_count: 0,
          question_id: questionId,
          context_type: contextType,
          context_id: contextId,
        });

      if (error) {
        debugLog('Error inserting chat analytics:', error);
        // Don't throw error for analytics - just log it
        return sessionId;
      }

      // Track in Vercel Analytics
      track('chat_session_started', {
        questionId,
        contextType,
        contextId
      });

      return sessionId;
    } catch (error) {
      debugLog('Error starting chat session:', error);
      return sessionId; // Return sessionId anyway for local tracking
    }
  },

  // Update chat session with message count and duration
  updateChatSession: async (
    sessionId: string,
    messageCount: number,
    durationSeconds?: number
  ): Promise<void> => {
    try {
      const { error } = await supabase
        .from('chat_analytics')
        .update({
          message_count: messageCount,
          conversation_duration_seconds: durationSeconds,
        })
        .eq('session_id', sessionId);

      if (error) throw error;
    } catch (error) {
      debugLog('Error updating chat session:', error);
    }
  },

  // End chat session
  endChatSession: async (
    sessionId: string,
    finalMessageCount: number,
    totalDurationSeconds: number
  ): Promise<void> => {
    try {
      await chatAnalytics.updateChatSession(sessionId, finalMessageCount, totalDurationSeconds);

      // Track in Vercel Analytics
      track('chat_session_ended', {
        sessionId,
        messageCount: finalMessageCount,
        duration: totalDurationSeconds
      });
    } catch (error) {
      debugLog('Error ending chat session:', error);
    }
  }
};

// Assessment Analytics Functions
export const assessmentAnalytics = {
  // Record assessment completion
  recordAssessment: async (
    userId: string,
    questionId: string,
    contextType: 'question' | 'guided',
    contextId: string,
    score: number,
    durationSeconds: number,
    nodeCount: number,
    edgeCount: number,
    componentsUsed: string[],
    isBestScore: boolean = false
  ): Promise<void> => {
    try {
      const { error } = await supabase
        .from('assessment_analytics')
        .insert({
          user_id: userId,
          question_id: questionId,
          context_type: contextType,
          context_id: contextId,
          score,
          duration_seconds: durationSeconds,
          node_count: nodeCount,
          edge_count: edgeCount,
          components_used: componentsUsed,
          is_best_score: isBestScore,
        });

      if (error) {
        debugLog('Error inserting assessment analytics:', error);
        // Don't throw error for analytics - just log it
        return;
      }

      // Track in Vercel Analytics
      track('assessment_completed', {
        questionId,
        contextType,
        contextId,
        score,
        duration: durationSeconds,
        nodeCount,
        edgeCount,
        componentsCount: componentsUsed.length,
        isBestScore
      });

    } catch (error) {
      debugLog('Error recording assessment:', error);
    }
  },

  // Get assessment statistics
  getAssessmentStats: async (userId?: string) => {
    try {
      let query = supabase
        .from('assessment_analytics')
        .select('*');

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;
      if (error) throw error;

      return {
        totalAssessments: data?.length || 0,
        averageScore: data?.reduce((sum, item) => sum + item.score, 0) / (data?.length || 1),
        averageDuration: data?.reduce((sum, item) => sum + item.duration_seconds, 0) / (data?.length || 1),
        bestScores: data?.filter(item => item.is_best_score).length || 0,
        data
      };
    } catch (error) {
      debugLog('Error getting assessment stats:', error);
      return null;
    }
  }
};

// User Session Analytics Functions
export const sessionAnalytics = {
  // Start user session
  startSession: async (userId: string): Promise<string> => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      const { error } = await supabase
        .from('user_session_analytics')
        .insert({
          user_id: userId,
          session_start: new Date().toISOString(),
          page_views: 1,
          actions_performed: 0,
          questions_attempted: [],
        });

      if (error) throw error;
      return sessionId;
    } catch (error) {
      debugLog('Error starting user session:', error);
      return sessionId;
    }
  },

  // Update session activity
  updateSession: async (
    userId: string,
    pageViews: number,
    actionsPerformed: number,
    questionsAttempted: string[]
  ): Promise<void> => {
    try {
      const { error } = await supabase
        .from('user_session_analytics')
        .update({
          page_views: pageViews,
          actions_performed: actionsPerformed,
          questions_attempted: questionsAttempted,
        })
        .eq('user_id', userId)
        .is('session_end', null);

      if (error) throw error;
    } catch (error) {
      debugLog('Error updating session:', error);
    }
  },

  // End session
  endSession: async (userId: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from('user_session_analytics')
        .update({
          session_end: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .is('session_end', null);

      if (error) throw error;
    } catch (error) {
      debugLog('Error ending session:', error);
    }
  }
};

// Test Analytics Functions
export const testAnalytics = {
  // Test if analytics tables are accessible
  testConnection: async () => {
    try {
      // Test basic read access to each table
      const { data: chatTest, error: chatError } = await supabase
        .from('chat_analytics')
        .select('count')
        .limit(1);

      const { data: assessmentTest, error: assessmentError } = await supabase
        .from('assessment_analytics')
        .select('count')
        .limit(1);

      const { data: sessionTest, error: sessionError } = await supabase
        .from('user_session_analytics')
        .select('count')
        .limit(1);

      return {
        chatAnalytics: !chatError,
        assessmentAnalytics: !assessmentError,
        sessionAnalytics: !sessionError,
        errors: {
          chat: chatError?.message,
          assessment: assessmentError?.message,
          session: sessionError?.message
        }
      };
    } catch (error) {
      debugLog('Analytics connection test failed:', error);
      return {
        chatAnalytics: false,
        assessmentAnalytics: false,
        sessionAnalytics: false,
        errors: {
          general: (error as Error).message
        }
      };
    }
  }
};

// General Analytics Functions
export const generalAnalytics = {
  // Get platform-wide statistics (admin only)
  getPlatformStats: async () => {
    try {
      // Get chat statistics
      const { data: chatData, error: chatError } = await supabase
        .from('chat_analytics')
        .select('*');

      if (chatError) {
        debugLog('Error fetching chat analytics:', chatError);
        // If permission denied, return empty data instead of throwing
        if (chatError.code === 'PGRST301' || chatError.message?.includes('permission denied')) {
          debugWarn('Admin access required for platform statistics');
          return null;
        }
        throw chatError;
      }

      // Get assessment statistics
      const { data: assessmentData, error: assessmentError } = await supabase
        .from('assessment_analytics')
        .select('*');

      if (assessmentError) {
        debugLog('Error fetching assessment analytics:', assessmentError);
        if (assessmentError.code === 'PGRST301' || assessmentError.message?.includes('permission denied')) {
          return null;
        }
        throw assessmentError;
      }

      // Get session statistics
      const { data: sessionData, error: sessionError } = await supabase
        .from('user_session_analytics')
        .select('*');

      if (sessionError) {
        debugLog('Error fetching session analytics:', sessionError);
        if (sessionError.code === 'PGRST301' || sessionError.message?.includes('permission denied')) {
          return null;
        }
        throw sessionError;
      }

      return {
        chats: {
          totalSessions: chatData?.length || 0,
          totalMessages: chatData?.reduce((sum, item) => sum + item.message_count, 0) || 0,
          averageMessagesPerSession: chatData?.length ?
            (chatData.reduce((sum, item) => sum + item.message_count, 0) / chatData.length) : 0,
          averageSessionDuration: chatData?.length ?
            (chatData.reduce((sum, item) => sum + (item.conversation_duration_seconds || 0), 0) / chatData.length) : 0,
        },
        assessments: {
          totalAssessments: assessmentData?.length || 0,
          averageScore: assessmentData?.length ?
            (assessmentData.reduce((sum, item) => sum + item.score, 0) / assessmentData.length) : 0,
          averageDuration: assessmentData?.length ?
            (assessmentData.reduce((sum, item) => sum + item.duration_seconds, 0) / assessmentData.length) : 0,
          bestScoreCount: assessmentData?.filter(item => item.is_best_score).length || 0,
        },
        sessions: {
          totalSessions: sessionData?.length || 0,
          averagePageViews: sessionData?.length ?
            (sessionData.reduce((sum, item) => sum + item.page_views, 0) / sessionData.length) : 0,
          averageActions: sessionData?.length ?
            (sessionData.reduce((sum, item) => sum + item.actions_performed, 0) / sessionData.length) : 0,
        }
      };
    } catch (error) {
      debugLog('Error getting platform stats:', error);
      return null;
    }
  }
};
