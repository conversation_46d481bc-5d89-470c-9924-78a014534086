/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import { Webhook } from "npm:standardwebhooks@1.0.0";
// Define common headers for all responses
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, webhook-signature, webhook-timestamp, webhook-id",
  "Content-Type": "application/json"
};
// Load environment variables
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, DODO_WEBHOOK_SECRET } = Deno.env.toObject();
// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
// Initialize Standard Webhooks
const webhook = new Webhook(DODO_WEBHOOK_SECRET);
// Helper function to create standardized responses
function createResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  });
}
// Process different event types
async function processWebhookEvent(event) {
  if (!event || !event.type) {
    console.error("Invalid event object or missing event type");
    return false;
  }
  console.log(`Processing event type: ${event.type}`);
  try {
    switch(event.type){
      case 'payment.succeeded':
        await handlePaymentSucceeded(event.data);
        break;
      case 'subscription.active':
        await handleSubscriptionActive(event.data);
        break;
      case 'subscription.cancelled':
        await handleSubscriptionCancelled(event.data);
        break;
      case 'refund.succeeded':
        await handleRefundSucceeded(event.data);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
        // Still return true as we successfully received the event
        return true;
    }
    return true;
  } catch (error) {
    console.error(`Error processing ${event.type} event:`, error);
    return false;
  }
}
// Handler for payment.succeeded event
async function handlePaymentSucceeded(data) {
  console.log("Payment succeeded:", data);
  // Store the payment in the database
  const { error } = await supabase.from("payments").insert({
    payment_id: data.payment_id,
    subscription_id: data.subscription_id,
    customer_id: data.customer?.customer_id,
    customer: data.customer,
    total_amount: data.total_amount,
    currency: data.currency,
    status: data.status,
    payment_method: data.payment_method,
    payment_method_type: data.payment_method_type,
    created_at: new Date().toISOString(),
    metadata: data.metadata,
    billing: data.billing,
    business_id: data.business_id,
    error_message: data.error_message,
    discount_id: data.discount_id,
    settlement_amount: data.settlement_amount,
    settlement_currency: data.settlement_currency
  });
  if (error) {
    console.error("Error storing payment:", error);
    throw error;
  }
  console.log(`Payment ${data.payment_id} stored successfully`);
}
// Handler for subscription.active event
async function handleSubscriptionActive(data) {
  console.log("Subscription active:", data);
  // Update the subscription status in the database
  const { error } = await supabase.from("subscriptions").update({
    status: 'active',
    updated_at: new Date().toISOString()
  }).eq("subscription_id", data.subscription_id);
  if (error) {
    console.error("Error updating subscription:", error);
    throw error;
  }
  console.log(`Subscription ${data.subscription_id} updated to active`);
}
// Handler for subscription.cancelled event
async function handleSubscriptionCancelled(data) {
  console.log("Subscription cancelled:", data);
  // Update the subscription status in the database
  const { error } = await supabase.from("subscriptions").update({
    status: 'cancelled',
    cancelled_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }).eq("subscription_id", data.subscription_id);
  if (error) {
    console.error("Error updating subscription:", error);
    throw error;
  }
  console.log(`Subscription ${data.subscription_id} updated to cancelled`);
}
// Handler for refund.succeeded event
async function handleRefundSucceeded(data) {
  console.log("Refund succeeded:", data);
  // Store the refund in the database
  const { error } = await supabase.from("refunds").insert({
    refund_id: data.refund_id,
    payment_id: data.payment_id,
    customer_id: data.customer?.customer_id,
    amount: data.amount,
    currency: data.currency,
    status: data.status,
    reason: data.reason,
    created_at: new Date().toISOString(),
    metadata: data
  });
  if (error) {
    console.error("Error storing refund:", error);
    throw error;
  }
  console.log(`Refund ${data.refund_id} stored successfully`);
}
// Main webhook handler
Deno.serve(async (req)=>{
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      status: 200,
      headers: corsHeaders
    });
  }
  if (req.method !== "POST") {
    return createResponse({
      error: "Method not allowed"
    }, 405);
  }
  try {
    // Get webhook headers
    const webhookId = req.headers.get("webhook-id") || "";
    const signature = req.headers.get("webhook-signature") || "";
    const timestamp = req.headers.get("webhook-timestamp") || "";
    console.log(`\n=== WEBHOOK RECEIVED ===`);
    console.log(`Webhook ID: ${webhookId}`);
    console.log(`Timestamp: ${timestamp}`);
    console.log(`Signature: ${signature}`);
    // Create webhook headers object for verification
    const webhookHeaders = {
      "webhook-id": webhookId,
      "webhook-signature": signature,
      "webhook-timestamp": timestamp
    };
    try {
      // Get the body as JSON
      const body = await req.json();
      // Convert to string for verification
      // We need to use JSON.stringify to ensure consistent string representation
      const rawBody = JSON.stringify(body);
      // Log the raw body for debugging
      console.log("Raw body for verification:", rawBody);
      // Verify the webhook using Standard Webhooks library
      await webhook.verify(rawBody, webhookHeaders);
      console.log('✅ Webhook signature validated');
      // Process the webhook event
      const isProcessed = await processWebhookEvent(body);
      if (!isProcessed) {
        console.log('❌ Failed to process webhook event');
        // Still return 200 to acknowledge receipt
        return createResponse({
          received: true,
          processed: false,
          message: "Event received but processing failed"
        });
      }
      console.log('✅ Webhook event processed successfully');
      console.log('========================\n');
      // Return success response
      return createResponse({
        received: true,
        processed: true,
        message: "Webhook processed successfully"
      });
    } catch (verificationError) {
      console.error('❌ Invalid webhook signature');
      console.error(verificationError);
      // Return 401 for invalid signatures
      return createResponse({
        error: "Invalid webhook signature",
        details: verificationError.message
      }, 401);
    }
  } catch (err) {
    console.error("Error processing webhook:", err);
    // Still return a 200 response to acknowledge receipt
    // This prevents DodoPayments from retrying the webhook
    return createResponse({
      received: true,
      processed: false,
      error: err.message
    });
  }
});
whsec_hEG1SQyrNhhHIW4De5t4rnVE