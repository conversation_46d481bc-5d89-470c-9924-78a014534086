import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Load environment variables
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, DODO_PAYMENTS_API_KEY } = Deno.env.toObject();

// Initialize Supabase admin client
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Helper function to create standardized responses
function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  });
}

interface UpdateSubscriptionRequest {
  subscription_id: string;
  action: 'cancel' | 'pause' | 'resume';
  cancel_at_next_billing_date?: boolean;
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS preflight request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  if (req.method !== 'POST') {
    console.log(`Method not allowed: ${req.method}`);
    return createResponse({
      error: 'Method not allowed'
    }, 405);
  }

  try {
    // Get user from JWT
    const authHeader = req.headers.get('authorization');
    if (!authHeader) {
      console.log('Missing authorization header');
      return createResponse({
        error: 'Unauthorized'
      }, 401);
    }

    // Create Supabase client for user authentication
    const supabase = createClient(SUPABASE_URL, process.env.SUPABASE_ANON_KEY || '', {
      global: {
        headers: { Authorization: authHeader },
      },
    });

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('Authentication error:', authError);
      return createResponse({
        error: 'Unauthorized',
        details: authError?.message
      }, 401);
    }

    console.log(`Authenticated user: ${user.id}`);

    // Parse request body
    const body: UpdateSubscriptionRequest = await req.json();
    const { subscription_id, action, cancel_at_next_billing_date } = body;

    if (!subscription_id || !action) {
      return createResponse({
        error: 'Missing required fields: subscription_id and action'
      }, 400);
    }

    // Verify user owns this subscription
    const { data: subscription, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('subscription_id', subscription_id)
      .eq('user_id', user.id)
      .single();

    if (subscriptionError || !subscription) {
      console.error('Subscription not found or access denied:', subscriptionError);
      return createResponse({
        error: 'Subscription not found or access denied'
      }, 404);
    }

    console.log(`Updating subscription ${subscription_id} with action: ${action}`);

    // Prepare DodoPayments API request
    let updatePayload: any = {};

    switch (action) {
      case 'cancel':
        updatePayload = {
          cancel_at_next_billing_date: cancel_at_next_billing_date ?? true
        };
        break;
      case 'pause':
        updatePayload = {
          status: 'paused'
        };
        break;
      case 'resume':
        updatePayload = {
          status: 'active'
        };
        break;
      default:
        return createResponse({
          error: 'Invalid action. Must be cancel, pause, or resume'
        }, 400);
    }

    // Call DodoPayments API
    const dodoResponse = await fetch(`https://test.dodopayments.com/subscriptions/${subscription_id}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${DODO_PAYMENTS_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updatePayload),
    });

    if (!dodoResponse.ok) {
      const errorData = await dodoResponse.text();
      console.error('DodoPayments API error:', errorData);
      return createResponse({
        error: 'Failed to update subscription with payment provider',
        details: errorData
      }, 500);
    }

    const updatedSubscription = await dodoResponse.json();
    console.log('DodoPayments response:', updatedSubscription);

    // Update local subscription record
    const { error: updateError } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: updatedSubscription.status,
        cancel_at_next_billing_date: updatedSubscription.cancel_at_next_billing_date,
        cancelled_at: updatedSubscription.cancelled_at,
        updated_at: new Date().toISOString()
      })
      .eq('subscription_id', subscription_id);

    if (updateError) {
      console.error('Error updating local subscription:', updateError);
      // Don't fail the request since DodoPayments was updated successfully
    }

    return createResponse({
      success: true,
      message: `Subscription ${action} successful`,
      subscription: {
        subscription_id: updatedSubscription.subscription_id,
        status: updatedSubscription.status,
        cancel_at_next_billing_date: updatedSubscription.cancel_at_next_billing_date,
        cancelled_at: updatedSubscription.cancelled_at,
        next_billing_date: updatedSubscription.next_billing_date
      }
    });

  } catch (error) {
    console.error('update-subscription function error:', error);
    return createResponse({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    }, 500);
  }
});
