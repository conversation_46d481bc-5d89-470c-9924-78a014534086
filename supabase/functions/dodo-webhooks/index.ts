/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { Webhook } from "npm:standardwebhooks@1.0.0"

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, webhook-signature, webhook-timestamp, webhook-id, authorization, x-client-info, apikey",
  "Content-Type": "application/json"
}

// Load environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL') ?? ''
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
const DODO_WEBHOOK_SECRET = Deno.env.get('DODO_WEBHOOK_SECRET') ?? ''

// Initialize Supabase client with service role key (bypasses RLS)
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

// Initialize Standard Webhooks
const webhook = new Webhook(DODO_WEBHOOK_SECRET)

// Helper function to create standardized responses
function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  })
}

interface WebhookEvent {
  type: string
  data: {
    id: string
    object: string
    [key: string]: any
  }
  created_at: string
  livemode: boolean
}

interface PaymentData {
  id: string
  amount: number
  currency: string
  status: string
  customer_id: string
  subscription_id?: string
  metadata?: Record<string, any>
  customer?: any
  payment_method?: string
  payment_method_type?: string
  business_id?: string
  error_message?: string
  discount_id?: string
  settlement_amount?: number
  settlement_currency?: string
  billing?: any
}

interface SubscriptionData {
  id: string
  customer_id: string
  product_id: string
  status: string
  current_period_start: string
  current_period_end: string
  metadata?: Record<string, any>
  recurring_pre_tax_amount?: number
  tax_inclusive?: boolean
  currency?: string
  quantity?: number
  trial_period_days?: number
  subscription_period_interval?: string
  payment_frequency_interval?: string
  subscription_period_count?: number
  payment_frequency_count?: number
  billing_cycle?: string
  customer?: any
  discount_id?: string
  billing?: any
  on_demand?: boolean
  addons?: any[]
  payment_link?: string
}

interface RefundData {
  id: string
  payment_id: string
  amount: number
  currency: string
  status: string
  reason?: string
  business_id?: string
  metadata?: Record<string, any>
}

// Process different event types
async function processWebhookEvent(event: WebhookEvent) {
  if (!event || !event.type) {
    console.error("Invalid event object or missing event type")
    return false
  }

  console.log(`Processing event type: ${event.type}`)

  try {
    switch (event.type) {
      // Payment Events
      case 'payment.succeeded':
        await handlePaymentSucceeded(supabase, event)
        break

      case 'payment.failed':
        await handlePaymentFailed(supabase, event)
        break

      case 'payment.processing':
        await handlePaymentProcessing(supabase, event)
        break

      case 'payment.cancelled':
        await handlePaymentCancelled(supabase, event)
        break

      // Subscription Events
      case 'subscription.active':
        await handleSubscriptionActive(supabase, event)
        break

      case 'subscription.renewed':
        await handleSubscriptionRenewed(supabase, event)
        break

      case 'subscription.cancelled':
        await handleSubscriptionCancelled(supabase, event)
        break

      case 'subscription.plan_changed':
        await handleSubscriptionPlanChanged(supabase, event)
        break

      case 'subscription.on_hold':
        await handleSubscriptionOnHold(supabase, event)
        break

      case 'subscription.paused':
        await handleSubscriptionPaused(supabase, event)
        break

      case 'subscription.failed':
        await handleSubscriptionFailed(supabase, event)
        break

      case 'subscription.expired':
        await handleSubscriptionExpired(supabase, event)
        break

      // Refund Events
      case 'refund.succeeded':
        await handleRefundSucceeded(supabase, event)
        break

      case 'refund.failed':
        await handleRefundFailed(supabase, event)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
        // Still return true as we successfully received the event
        return true
    }
    return true
  } catch (error) {
    console.error(`Error processing ${event.type} event:`, error)
    return false
  }
}

async function handlePaymentSucceeded(supabase: any, event: WebhookEvent) {
  const payment = event.data as any
  console.log('Processing payment.succeeded:', payment.payment_id || payment.id)
  console.log('Full payment data:', JSON.stringify(payment, null, 2))

  try {
    // Find user by customer_id in metadata or create mapping
    const userId = payment.metadata?.user_id || payment.customer?.customer_id
    if (!userId) {
      throw new Error('No user ID found in payment metadata')
    }

    // Insert or update payment record with enhanced schema
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .upsert({
        user_id: userId,
        payment_id: payment.payment_id || payment.id,
        status: 'succeeded',
        total_amount: payment.total_amount / 100, // Convert from cents
        currency: payment.currency,
        payment_method: payment.payment_method || 'dodopayments',
        payment_method_type: payment.payment_method_type || null,
        customer: payment.customer || null,
        subscription_id: payment.subscription_id || '',
        customer_id: payment.customer?.customer_id || payment.customer_id,
        metadata: payment.metadata || {},
        business_id: payment.business_id || null,
        error_message: null,
        discount_id: payment.discount_id || null,
        settlement_amount: payment.settlement_amount || null,
        settlement_currency: payment.settlement_currency || null,
        billing: payment.billing || null,
      }, {
        onConflict: 'payment_id'
      })
      .select()
      .single()

    if (paymentError) {
      console.error('Error inserting payment:', paymentError)
      throw paymentError
    }

    // If this is a subscription payment, allocate credits
    if (payment.subscription_id) {
      const { data: subscription } = await supabase
        .from('subscriptions')
        .select('product_id')
        .eq('subscription_id', payment.subscription_id)
        .single()

      if (subscription?.product_id) {
        const { data: plan } = await supabase
          .from('plans')
          .select('credits')
          .eq('dodopayments_plan_id', subscription.product_id)
          .single()

        if (plan?.credits) {
          await supabase.rpc('add_user_credits', {
            user_uuid: userId,
            credit_amount: plan.credits
          })

          console.log(`Added ${plan.credits} credits to user ${userId} for payment ${payment.id}`)
        }
      }
    }

    console.log('Payment processed successfully:', paymentData.id)
  } catch (error) {
    console.error('Error processing payment.succeeded:', error)
    throw error
  }
}

async function handlePaymentFailed(supabase: any, event: WebhookEvent) {
  const payment = event.data as any
  console.log('Processing payment.failed:', payment.payment_id || payment.id)

  try {
    const userId = payment.metadata?.user_id || payment.customer?.customer_id
    if (!userId) {
      throw new Error('No user ID found in payment metadata')
    }

    // Insert or update failed payment record with enhanced schema
    await supabase
      .from('payments')
      .upsert({
        user_id: userId,
        payment_id: payment.payment_id || payment.id,
        status: 'failed',
        total_amount: payment.total_amount / 100,
        currency: payment.currency,
        payment_method: payment.payment_method || 'dodopayments',
        payment_method_type: payment.payment_method_type || null,
        customer: payment.customer || null,
        subscription_id: payment.subscription_id || '',
        customer_id: payment.customer?.customer_id || payment.customer_id,
        metadata: payment.metadata || {},
        business_id: payment.business_id || null,
        error_message: payment.error_message || null,
        discount_id: payment.discount_id || null,
        settlement_amount: payment.settlement_amount || null,
        settlement_currency: payment.settlement_currency || null,
        billing: payment.billing || null,
      }, {
        onConflict: 'payment_id'
      })

    console.log('Failed payment recorded:', payment.id)
  } catch (error) {
    console.error('Error processing payment.failed:', error)
    throw error
  }
}

async function handleSubscriptionActive(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.active:', subscription.subscription_id || subscription.id)
  console.log('Full subscription data:', JSON.stringify(subscription, null, 2))

  try {
    // Try multiple ways to get user ID
    const userId = subscription.metadata?.user_id ||
                   subscription.customer?.customer_id ||
                   subscription.customer_id

    if (!userId) {
      console.error('No user ID found in subscription data:', {
        metadata: subscription.metadata,
        customer: subscription.customer,
        customer_id: subscription.customer_id
      })
      throw new Error('No user ID found in subscription data')
    }

    console.log('Found user ID:', userId)

    // Insert or update subscription with comprehensive schema
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .upsert({
        user_id: userId,
        subscription_id: subscription.subscription_id || subscription.id,
        recurring_pre_tax_amount: subscription.recurring_pre_tax_amount || null,
        tax_inclusive: subscription.tax_inclusive !== undefined ? subscription.tax_inclusive : true,
        currency: subscription.currency || 'USD',
        status: 'active',
        product_id: subscription.product_id,
        quantity: subscription.quantity || 1,
        trial_period_days: subscription.trial_period_days || 0,
        subscription_period_interval: subscription.subscription_period_interval || null,
        payment_frequency_interval: subscription.payment_frequency_interval || 'Month',
        subscription_period_count: subscription.subscription_period_count || null,
        payment_frequency_count: subscription.payment_frequency_count || 1,
        billing_cycle: subscription.billing_cycle || null,
        next_billing_date: subscription.current_period_end,
        previous_billing_date: subscription.current_period_start,
        customer: subscription.customer || null,
        metadata: subscription.metadata || null,
        discount_id: subscription.discount_id || null,
        billing: subscription.billing || null,
        on_demand: subscription.on_demand || false,
        addons: subscription.addons || [],
        payment_link: subscription.payment_link || null,
      }, { 
        onConflict: 'subscription_id' 
      })

    if (subscriptionError) {
      console.error('Error upserting subscription:', subscriptionError)
      throw subscriptionError
    }

    // Handle subscription upgrade - set credits based on plan type
    const { data: plan } = await supabase
      .from('plans')
      .select('id, credits')
      .eq('dodopayments_plan_id', subscription.product_id)
      .single()

    if (plan?.credits) {
      // For subscription upgrades, we SET credits rather than ADD
      // This ensures pro users get exactly 100 credits, not 100 + existing credits
      await supabase.rpc('set_user_credits', {
        user_uuid: userId,
        credit_amount: plan.credits
      })

      console.log(`Set user ${userId} credits to ${plan.credits} for plan ${plan.id}`)
    }

    console.log('Subscription activated successfully:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.active:', error)
    throw error
  }
}

async function handleSubscriptionRenewed(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.renewed:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription dates and billing info
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        previous_billing_date: subscription.current_period_start,
        next_billing_date: subscription.current_period_end,
        recurring_pre_tax_amount: subscription.recurring_pre_tax_amount || null,
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription:', updateError)
      throw updateError
    }

    // Allocate credits for renewal
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('user_id, product_id')
      .eq('subscription_id', subscription.subscription_id || subscription.id)
      .single()

    if (subscriptionData?.product_id) {
      const { data: plan } = await supabase
        .from('plans')
        .select('credits')
        .eq('dodopayments_plan_id', subscriptionData.product_id)
        .single()

      if (plan?.credits) {
        await supabase.rpc('add_user_credits', {
          user_uuid: subscriptionData.user_id,
          credit_amount: plan.credits
        })
      }
    }

    console.log('Subscription renewed successfully:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.renewed:', error)
    throw error
  }
}

async function handleSubscriptionCancelled(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.cancelled:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription status
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'cancelled',
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription:', updateError)
      throw updateError
    }

    console.log('Subscription cancelled successfully:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.cancelled:', error)
    throw error
  }
}

async function handleRefundSucceeded(supabase: any, event: WebhookEvent) {
  const refund = event.data as any
  console.log('Processing refund.succeeded:', refund.id)

  try {
    // Complete the refund using new schema
    const { data: success } = await supabase.rpc('complete_refund', {
      dodo_refund_id: refund.id
    })

    if (!success) {
      // If refund doesn't exist, create it
      const { data: payment } = await supabase
        .from('payments')
        .select('user_id')
        .eq('payment_id', refund.payment_id)
        .single()

      if (payment) {
        await supabase.rpc('process_refund', {
          user_uuid: payment.user_id,
          dodo_payment_id: refund.payment_id,
          refund_amount: refund.amount / 100,
          refund_currency: refund.currency,
          dodo_refund_id: refund.id,
          refund_reason: refund.reason || null,
          refund_business_id: refund.business_id || null,
          refund_metadata: refund.metadata || {}
        })

        // Mark as succeeded
        await supabase.rpc('complete_refund', {
          dodo_refund_id: refund.id
        })
      }
    }

    console.log('Refund processed successfully:', refund.id)
  } catch (error) {
    console.error('Error processing refund.succeeded:', error)
    throw error
  }
}

async function handleRefundFailed(supabase: any, event: WebhookEvent) {
  const refund = event.data as any
  console.log('Processing refund.failed:', refund.id)

  try {
    // Update refund status to failed
    const { error: updateError } = await supabase
      .from('refunds')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString(),
      })
      .eq('refund_id', refund.id)

    if (updateError) {
      console.error('Error updating refund:', updateError)
      throw updateError
    }

    console.log('Refund failure recorded:', refund.id)
  } catch (error) {
    console.error('Error processing refund.failed:', error)
    throw error
  }
}

async function handlePaymentProcessing(supabase: any, event: WebhookEvent) {
  const payment = event.data as any
  console.log('Processing payment.processing:', payment.payment_id || payment.id)

  try {
    const userId = payment.metadata?.user_id || payment.customer?.customer_id
    if (!userId) {
      throw new Error('No user ID found in payment metadata')
    }

    // Insert or update payment record with processing status
    const { error: paymentError } = await supabase
      .from('payments')
      .upsert({
        user_id: userId,
        payment_id: payment.payment_id || payment.id,
        status: 'processing',
        total_amount: payment.total_amount / 100,
        currency: payment.currency,
        payment_method: payment.payment_method || 'dodopayments',
        payment_method_type: payment.payment_method_type || null,
        customer: payment.customer || null,
        subscription_id: payment.subscription_id || '',
        customer_id: payment.customer?.customer_id || payment.customer_id,
        metadata: payment.metadata || {},
        business_id: payment.business_id || null,
        error_message: null,
        discount_id: payment.discount_id || null,
        settlement_amount: payment.settlement_amount || null,
        settlement_currency: payment.settlement_currency || null,
        billing: payment.billing || null,
      }, {
        onConflict: 'payment_id'
      })

    if (paymentError) {
      console.error('Error upserting payment:', paymentError)
      throw paymentError
    }

    console.log('Payment processing status recorded:', payment.id)
  } catch (error) {
    console.error('Error processing payment.processing:', error)
    throw error
  }
}

async function handlePaymentCancelled(supabase: any, event: WebhookEvent) {
  const payment = event.data as any
  console.log('Processing payment.cancelled:', payment.payment_id || payment.id)

  try {
    const userId = payment.metadata?.user_id || payment.customer?.customer_id
    if (!userId) {
      throw new Error('No user ID found in payment metadata')
    }

    // Insert or update payment record with cancelled status
    const { error: paymentError } = await supabase
      .from('payments')
      .upsert({
        user_id: userId,
        payment_id: payment.payment_id || payment.id,
        status: 'cancelled',
        total_amount: payment.total_amount / 100,
        currency: payment.currency,
        payment_method: payment.payment_method || 'dodopayments',
        payment_method_type: payment.payment_method_type || null,
        customer: payment.customer || null,
        subscription_id: payment.subscription_id || '',
        customer_id: payment.customer?.customer_id || payment.customer_id,
        metadata: payment.metadata || {},
        business_id: payment.business_id || null,
        error_message: payment.error_message || null,
        discount_id: payment.discount_id || null,
        settlement_amount: payment.settlement_amount || null,
        settlement_currency: payment.settlement_currency || null,
        billing: payment.billing || null,
      }, {
        onConflict: 'payment_id'
      })

    if (paymentError) {
      console.error('Error upserting payment:', paymentError)
      throw paymentError
    }

    console.log('Payment cancellation recorded:', payment.id)
  } catch (error) {
    console.error('Error processing payment.cancelled:', error)
    throw error
  }
}

async function handleSubscriptionPlanChanged(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.plan_changed:', subscription.subscription_id || subscription.id)

  try {
    const userId = subscription.metadata?.user_id || subscription.customer_id
    if (!userId) {
      throw new Error('No user ID found in subscription metadata')
    }

    // Update subscription with new plan details
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .update({
        product_id: subscription.product_id,
        recurring_pre_tax_amount: subscription.recurring_pre_tax_amount || null,
        quantity: subscription.quantity || 1,
        billing_cycle: subscription.billing_cycle || null,
        next_billing_date: subscription.current_period_end,
        previous_billing_date: subscription.current_period_start,
        metadata: subscription.metadata || null,
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (subscriptionError) {
      console.error('Error updating subscription plan:', subscriptionError)
      throw subscriptionError
    }

    // Update user credits based on new plan
    const { data: plan } = await supabase
      .from('plans')
      .select('id, credits')
      .eq('dodopayments_plan_id', subscription.product_id)
      .single()

    if (plan?.credits) {
      // For plan changes, we SET credits to the new plan's allocation
      await supabase.rpc('set_user_credits', {
        user_uuid: userId,
        credit_amount: plan.credits
      })

      console.log(`Updated user ${userId} credits to ${plan.credits} for new plan ${plan.id}`)
    }

    console.log('Subscription plan changed successfully:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.plan_changed:', error)
    throw error
  }
}

async function handleSubscriptionOnHold(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.on_hold:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription status to on_hold
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'on_hold',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription to on_hold:', updateError)
      throw updateError
    }

    // Note: We don't remove credits when subscription goes on hold
    // This allows users to continue using the service temporarily
    console.log('Subscription put on hold:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.on_hold:', error)
    throw error
  }
}

async function handleSubscriptionPaused(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.paused:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription status to paused
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'paused',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription to paused:', updateError)
      throw updateError
    }

    // Note: We don't remove credits when subscription is paused
    // This allows users to resume without losing their credits
    console.log('Subscription paused:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.paused:', error)
    throw error
  }
}

async function handleSubscriptionFailed(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.failed:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription status to failed
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'failed',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription to failed:', updateError)
      throw updateError
    }

    // For failed subscriptions, we don't allocate credits
    // User remains on basic plan
    console.log('Subscription failed:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.failed:', error)
    throw error
  }
}

async function handleSubscriptionExpired(supabase: any, event: WebhookEvent) {
  const subscription = event.data as any
  console.log('Processing subscription.expired:', subscription.subscription_id || subscription.id)

  try {
    // Update subscription status to expired
    const { error: updateError } = await supabase
      .from('subscriptions')
      .update({
        status: 'expired',
        updated_at: new Date().toISOString(),
      })
      .eq('subscription_id', subscription.subscription_id || subscription.id)

    if (updateError) {
      console.error('Error updating subscription to expired:', updateError)
      throw updateError
    }

    // Get user ID for credit management
    const userId = subscription.metadata?.user_id || subscription.customer_id
    if (userId) {
      // Reset user to basic plan credits (10 credits)
      const { data: basicPlan } = await supabase
        .from('plans')
        .select('credits')
        .eq('id', 'basic')
        .single()

      if (basicPlan?.credits !== undefined) {
        await supabase.rpc('set_user_credits', {
          user_uuid: userId,
          credit_amount: basicPlan.credits
        })

        console.log(`Reset user ${userId} to basic plan credits: ${basicPlan.credits}`)
      }
    }

    console.log('Subscription expired:', subscription.subscription_id || subscription.id)
  } catch (error) {
    console.error('Error processing subscription.expired:', error)
    throw error
  }
}

// Main webhook handler
Deno.serve(async (req) => {
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      status: 200,
      headers: corsHeaders
    })
  }

  if (req.method !== "POST") {
    return createResponse({
      error: "Method not allowed"
    }, 405)
  }

  try {
    // Get webhook headers
    const webhookId = req.headers.get("webhook-id") || ""
    const signature = req.headers.get("webhook-signature") || ""
    const timestamp = req.headers.get("webhook-timestamp") || ""

    console.log(`\n=== WEBHOOK RECEIVED ===`)
    console.log(`Webhook ID: ${webhookId}`)
    console.log(`Timestamp: ${timestamp}`)
    console.log(`Signature: ${signature}`)

    // Create webhook headers object for verification
    const webhookHeaders = {
      "webhook-id": webhookId,
      "webhook-signature": signature,
      "webhook-timestamp": timestamp
    }

    try {
      // Get the body as JSON
      const body = await req.json()
      // Convert to string for verification
      const rawBody = JSON.stringify(body)

      console.log("Raw body for verification:", rawBody)

      // Verify the webhook using Standard Webhooks library
      await webhook.verify(rawBody, webhookHeaders)
      console.log('✅ Webhook signature validated')

      // Process the webhook event
      const isProcessed = await processWebhookEvent(body)

      if (!isProcessed) {
        console.log('❌ Failed to process webhook event')
        // Still return 200 to acknowledge receipt
        return createResponse({
          received: true,
          processed: false,
          message: "Event received but processing failed"
        })
      }

      console.log('✅ Webhook event processed successfully')
      console.log('========================\n')

      // Return success response
      return createResponse({
        received: true,
        processed: true,
        message: "Webhook processed successfully"
      })

    } catch (verificationError) {
      console.error('❌ Invalid webhook signature')
      console.error(verificationError)
      // Return 401 for invalid signatures
      return createResponse({
        error: "Invalid webhook signature",
        details: verificationError.message
      }, 401)
    }

  } catch (err) {
    console.error("Error processing webhook:", err)
    // Still return a 200 response to acknowledge receipt
    // This prevents DodoPayments from retrying the webhook
    return createResponse({
      received: true,
      processed: false,
      error: err.message
    })
  }
})