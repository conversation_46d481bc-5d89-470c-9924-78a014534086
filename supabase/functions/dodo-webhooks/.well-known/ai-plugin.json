{"schema_version": "v1", "name_for_human": "DodoPayments Webhook Handler", "name_for_model": "dodo_webhooks", "description_for_human": "Handles DodoPayments webhook events for subscription and payment processing", "description_for_model": "Processes DodoPayments webhook events including subscription.active, payment.succeeded, etc.", "auth": {"type": "none"}, "api": {"type": "openapi", "url": "https://tcdieywqoophloivzszp.supabase.co/functions/v1/dodo-webhooks"}}