/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import DodoPayments from "npm:dodopayments"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-supabase-auth',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE, PATCH',
  'Access-Control-Max-Age': '86400',
  'Content-Type': 'application/json'
}

// Load environment variables
const {
  SUPABASE_URL,
  SUPABASE_ANON_KEY,
  SUPABASE_SERVICE_ROLE_KEY,
  DODO_PAYMENTS_API_KEY,
  DODO_ENV,
  SITE_URL
} = Deno.env.toObject()

// Initialize DodoPayments client
const dodoClient = new DodoPayments({
  bearerToken: DODO_PAYMENTS_API_KEY,
  environment: DODO_ENV || 'test_mode'
})

interface CreateSubscriptionRequest {
  plan_type: 'pro'
  customer_data: {
    name: string
    email: string
  }
  billing_cycle?: 'monthly' | 'yearly'
  billing_address?: {
    street?: string
    city?: string
    state?: string
    country?: string
    zipcode?: string
  }
  return_url?: string
}

interface CreateSubscriptionResponse {
  success: boolean
  payment_link?: string
  subscription_id?: string
  error?: string
}

// Helper function to create standardized responses
function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  })
}

// Helper function to format billing address
function formatBillingAddress(addressData?: CreateSubscriptionRequest['billing_address']) {
  if (!addressData) return null

  return {
    city: addressData.city || '',
    country: getCountryCode(addressData.country || 'US'),
    state: addressData.state || '',
    street: addressData.street || '',
    zipcode: addressData.zipcode || ''
  }
}

// Helper function to convert country name to country code
function getCountryCode(countryName: string): string {
  if (!countryName) return 'US'

  const countryCodes: Record<string, string> = {
    'india': 'IN',
    'united states': 'US',
    'united kingdom': 'GB',
    'canada': 'CA',
    'australia': 'AU'
  }

  return countryCodes[countryName.toLowerCase()] || countryName.toUpperCase()
}

// Helper function to create or get DodoPayments customer
async function createOrGetDodoCustomer(
  supabaseAdmin: any,
  userId: string,
  customerData: CreateSubscriptionRequest['customer_data']
) {
  try {
    // Check if user already has a customer record
    const { data: existingCustomer, error: customerError } = await supabaseAdmin
      .from('customers')
      .select('dodo_customer_id, name, email')
      .eq('user_id', userId)
      .single()

    if (customerError && customerError.code !== 'PGRST116') {
      console.error('Error fetching customer:', customerError)
      throw new Error('Failed to fetch customer data')
    }

    // If customer exists, return the existing customer ID
    if (existingCustomer?.dodo_customer_id) {
      console.log('Using existing DodoPayments customer:', existingCustomer.dodo_customer_id)
      return existingCustomer.dodo_customer_id
    }

    // Create new customer in DodoPayments
    console.log('Creating new DodoPayments customer for user:', userId)
    const dodoCustomer = await dodoClient.customers.create({
      name: customerData.name,
      email: customerData.email,
      metadata: {
        user_id: userId,
        created_via: 'subscription_creation'
      }
    })

    // Store customer record in database
    const { error: insertError } = await supabaseAdmin
      .from('customers')
      .insert({
        user_id: userId,
        dodo_customer_id: dodoCustomer.customer_id,
        name: customerData.name,
        email: customerData.email,
        metadata: {
          created_via: 'subscription_creation',
          dodo_metadata: dodoCustomer.metadata || {}
        }
      })

    if (insertError) {
      console.error('Error storing customer in database:', insertError)
      // Don't throw error here as customer was created successfully in DodoPayments
      // The subscription can still proceed
    }

    console.log('Created new DodoPayments customer:', dodoCustomer.customer_id)
    return dodoCustomer.customer_id
  } catch (error) {
    console.error('Error creating/getting DodoPayments customer:', error)
    throw error
  }
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS preflight request')
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  if (req.method !== 'POST') {
    return createResponse({ error: 'Method not allowed' }, 405)
  }

  try {
    // Log request for debugging
    console.log('Request headers:', Object.fromEntries(req.headers.entries()))
    console.log('Request method:', req.method)
    console.log('Request URL:', req.url)

    // Get user from JWT
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      console.log('Missing authorization header')
      return createResponse({ error: 'Unauthorized' }, 401)
    }

    // Create Supabase client for user authentication
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      global: {
        headers: { Authorization: authHeader },
      },
    })

    // Create admin client for database operations
    const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Verify user authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      console.error('Authentication error:', authError)
      return createResponse({
        error: 'Unauthorized',
        details: authError?.message
      }, 401)
    }

    console.log(`Authenticated user: ${user.id}`)

    // Parse request body
    const requestBody: CreateSubscriptionRequest = await req.json()
    console.log('Received request:', JSON.stringify(requestBody, null, 2))

    // Validate request
    if (!requestBody.plan_type || requestBody.plan_type !== 'pro') {
      console.error('Invalid plan type:', requestBody.plan_type)
      return createResponse({
        error: 'Invalid plan type. Only "pro" is supported.'
      }, 400)
    }

    if (!requestBody.customer_data?.name || !requestBody.customer_data?.email) {
      console.error('Missing customer data:', requestBody.customer_data)
      return createResponse({
        error: 'Customer data required (name and email)'
      }, 400)
    }

    // Set defaults
    const billingCycle = requestBody.billing_cycle || 'monthly'
    const returnUrl = requestBody.return_url || `${SITE_URL || 'https://www.layrs.me'}/profile?upgrade=success`

    // Check if user already has an active pro subscription
    const { data: existingSubscription, error: subscriptionCheckError } = await supabaseAdmin
      .from('subscriptions')
      .select('product_id, status')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single()

    if (subscriptionCheckError && subscriptionCheckError.code !== 'PGRST116') {
      console.error('Error checking existing subscription:', subscriptionCheckError)
      return createResponse({
        error: 'Database error checking existing subscription'
      }, 500)
    }

    if (existingSubscription?.product_id === 'pro') {
      console.log('User already has active pro subscription')
      return createResponse({
        error: 'User already has an active pro subscription'
      }, 400)
    }

    // Get pro plan details from database
    const { data: proPlan, error: planError } = await supabaseAdmin
      .from('plans')
      .select('dodopayments_plan_id, price, credits')
      .eq('id', 'pro')
      .single()

    if (planError || !proPlan?.dodopayments_plan_id) {
      console.error('Pro plan configuration error:', planError)
      return createResponse({
        error: 'Pro plan configuration not found',
        details: 'Please contact support'
      }, 500)
    }

    console.log('Found pro plan:', proPlan)

    // Create or get DodoPayments customer
    const customerId = await createOrGetDodoCustomer(
      supabaseAdmin,
      user.id,
      requestBody.customer_data
    )

    // Format billing address
    const billingAddress = formatBillingAddress(requestBody.billing_address)

    // Create DodoPayments subscription using SDK (following reference implementation pattern)
    const subscriptionData = {
      customer: {
        customer_id: customerId
      },
      product_id: proPlan.dodopayments_plan_id,
      quantity: 1,
      payment_link: true,
      return_url: returnUrl,
      metadata: {
        user_id: user.id,
        plan_type: 'pro',
        internal_plan_id: 'pro',
        billing_cycle: billingCycle,
        email: requestBody.customer_data.email,
        created_via: 'profile_upgrade'
      },
      billing: billingAddress || {
        city: 'Bengaluru',
        country: 'IN',
        state: 'Karnataka',
        street: 'Default Address',
        zipcode: '560001'
      },
      allowed_payment_method_types: [
        'credit',
        'debit',
        'upi_collect'
      ],
      billing_currency: 'USD',
      show_saved_payment_methods: false
    }

    console.log('Creating subscription with data:', JSON.stringify(subscriptionData, null, 2))

    // Create the subscription in DodoPayments
    const subscription = await dodoClient.subscriptions.create(subscriptionData)
    console.log(`DodoPayments subscription created: ${subscription.subscription_id}`)

    // Fetch detailed subscription information
    const subscriptionDetails = await dodoClient.subscriptions.retrieve(subscription.subscription_id)
    console.log('Detailed subscription data:', JSON.stringify(subscriptionDetails, null, 2))

    // Store subscription in database with comprehensive details (following reference implementation)
    const { error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .insert({
        user_id: user.id,
        subscription_id: subscriptionDetails.subscription_id,
        recurring_pre_tax_amount: subscriptionDetails.recurring_pre_tax_amount || proPlan.price,
        tax_inclusive: subscriptionDetails.tax_inclusive || true,
        currency: subscriptionDetails.currency || 'USD',
        status: subscriptionDetails.status || 'pending',
        created_at: subscriptionDetails.created_at || new Date().toISOString(),
        product_id: subscriptionDetails.product_id || 'pro',
        quantity: subscriptionDetails.quantity || 1,
        trial_period_days: subscriptionDetails.trial_period_days || 0,
        subscription_period_interval: subscriptionDetails.subscription_period_interval || (billingCycle === 'yearly' ? 'Year' : 'Month'),
        payment_frequency_interval: subscriptionDetails.payment_frequency_interval || 'Month',
        subscription_period_count: subscriptionDetails.subscription_period_count || (billingCycle === 'yearly' ? 1 : 1),
        payment_frequency_count: subscriptionDetails.payment_frequency_count || 1,
        billing_cycle: billingCycle,
        next_billing_date: subscriptionDetails.next_billing_date,
        previous_billing_date: subscriptionDetails.previous_billing_date || subscriptionDetails.created_at,
        customer: subscriptionDetails.customer || requestBody.customer_data,
        metadata: {
          user_id: user.id,
          plan_type: 'pro',
          internal_plan_id: 'pro',
          billing_cycle: billingCycle,
          email: requestBody.customer_data.email,
          dodo_subscription_id: subscriptionDetails.subscription_id,
          created_via: 'profile_upgrade',
          created_at: new Date().toISOString(),
          ...subscriptionDetails.metadata
        },
        discount_id: subscriptionDetails.discount_id,
        cancelled_at: subscriptionDetails.cancelled_at,
        billing: subscriptionDetails.billing || billingAddress,
        on_demand: subscriptionDetails.on_demand || false,
        addons: subscriptionDetails.addons || [],
        payment_link: subscription.payment_link
      })

    if (subscriptionError) {
      console.error('Error storing subscription in database:', subscriptionError)
      // Don't return error here as subscription was created successfully in DodoPayments
      // Just log the error and continue
    }

    console.log('Subscription created successfully:', subscriptionDetails.subscription_id)

    // Return success response (matching frontend expectations)
    return createResponse({
      success: true,
      user_id: user.id,
      subscription_id: subscriptionDetails.subscription_id,
      payment_link: subscription.payment_link
    }, 201)

  } catch (error) {
    console.error('create-subscription function error:', error)

    // Handle DodoPayments specific errors
    if (error.message?.includes('DodoPayments')) {
      return createResponse({
        success: false,
        error: 'Payment service error',
        details: error.message
      }, 400)
    }

    // Handle general errors
    return createResponse({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error occurred'
    }, 500)
  }
})