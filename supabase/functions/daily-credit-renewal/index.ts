/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
  'Content-Type': 'application/json'
}

// Load environment variables
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY } = Deno.env.toObject()

// Helper function to create standardized responses
function createResponse(data: any, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  })
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    console.log('Starting daily credit renewal process...')

    // Create Supabase admin client
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

    // Renew credits for basic plan users only
    const { data, error } = await supabase.rpc('renew_daily_credits')

    if (error) {
      console.error('Error renewing daily credits:', error)
      return createResponse({
        success: false,
        error: 'Failed to renew daily credits',
        details: error.message
      }, 500)
    }

    console.log(`Successfully renewed credits for ${data} basic plan users`)

    // Get summary statistics
    const { data: stats, error: statsError } = await supabase
      .from('user_credits')
      .select(`
        credits,
        subscriptions!inner(product_id, status)
      `)
      .eq('subscriptions.status', 'active')

    if (statsError) {
      console.error('Error fetching stats:', statsError)
    }

    const basicUsers = stats?.filter(s => s.subscriptions.product_id === 'basic').length || 0
    const proUsers = stats?.filter(s => s.subscriptions.product_id === 'pro').length || 0

    return createResponse({
      success: true,
      renewed_users: data,
      summary: {
        basic_plan_users: basicUsers,
        pro_plan_users: proUsers,
        total_active_users: basicUsers + proUsers
      },
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Daily credit renewal error:', error)
    return createResponse({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500)
  }
})
