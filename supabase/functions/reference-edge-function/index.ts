/// <reference types="https://esm.sh/@supabase/functions-js@1.0.0/edge-runtime.d.ts" />
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import DodoPayments from "npm:dodopayments";
// Define common headers for all responses
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, GET, OPTIONS, PUT, DELETE, PATCH",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type, x-supabase-auth",
  "Access-Control-Max-Age": "86400",
  "Content-Type": "application/json"
};
// Load environment variables
const { SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, DODO_PAYMENTS_API_KEY, DODO_ENV, DODO_MONTHLY_PRODUCT_ID, DODO_YEARLY_PRODUCT_ID, APP_URL } = Deno.env.toObject();
// Initialize Supabase and DodoPayments clients
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const client = new DodoPayments({
  bearerToken: DODO_PAYMENTS_API_KEY,
  environment: DODO_ENV || 'test_mode' // Default to 'test' if not provided
});
// Helper function to create standardized responses
function createResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: corsHeaders
  });
}
// Helper function to format address data
function formatBillingAddress(addressData) {
  if (!addressData) return null;
  return {
    city: addressData.city || '',
    country: addressData.country ? getCountryCode(addressData.country) : 'US',
    state: addressData.state || '',
    street: [
      addressData.addressLine1,
      addressData.addressLine2
    ].filter(Boolean).join(', '),
    zipcode: addressData.zipCode || ''
  };
}
// Helper function to convert country name to country code
function getCountryCode(countryName) {
  if (!countryName) return 'US';
  const countryCodes = {
    'india': 'IN',
    'united states': 'US',
    'united kingdom': 'GB'
  };
  return countryCodes[countryName.toLowerCase()] || countryName;
}
Deno.serve(async (req)=>{
  // Handle CORS preflight request
  if (req.method === "OPTIONS") {
    console.log("Handling OPTIONS preflight request");
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }
  try {
    // Log the entire request for debugging
    console.log("Request headers:", Object.fromEntries(req.headers.entries()));
    console.log("Request method:", req.method);
    console.log("Request URL:", req.url);
    // Parse request body with error handling
    let requestData;
    try {
      // Check if the request has a body
      const clonedReq = req.clone();
      const text = await clonedReq.text();
      console.log("Raw request body:", text);
      console.log("Content length:", req.headers.get("content-length"));
      if (!text || text.trim() === '') {
        // For debugging purposes, return a 200 response with empty data
        // This will help us see if we can at least reach the function
        return createResponse({
          error: "Empty request body",
          details: "The request body is empty or missing",
          debug: {
            headers: Object.fromEntries(req.headers.entries()),
            method: req.method,
            url: req.url
          }
        }, 200); // Using 200 instead of 400 for debugging
      }
      requestData = JSON.parse(text);
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      return createResponse({
        error: "Invalid JSON",
        details: "Could not parse the request body as JSON",
        parseError: String(parseError)
      }, 400);
    }
    // Log the incoming request
    console.log("Received request:", JSON.stringify(requestData, null, 2));
    // Validate required fields
    if (!requestData.userId) {
      console.error("Missing required field: userId", requestData);
      return createResponse({
        error: "Missing required field: userId is required"
      }, 400);
    }
    if (!requestData.billingCycle || ![
      'monthly',
      'yearly'
    ].includes(requestData.billingCycle.toLowerCase())) {
      console.error("Invalid billing cycle:", requestData.billingCycle);
      return createResponse({
        error: "Invalid billingCycle. Must be 'monthly' or 'yearly'"
      }, 400);
    }
    const userId = requestData.userId;
    const billingCycle = requestData.billingCycle.toLowerCase();
    const email = requestData.email;
    const provider = requestData.provider;
    const returnUrl = requestData.returnUrl || `${APP_URL || 'https://www.cloakai.tech'}/dashboard`;
    // Get product ID based on billing cycle
    const productId = billingCycle === 'monthly' ? DODO_MONTHLY_PRODUCT_ID : DODO_YEARLY_PRODUCT_ID;
    if (!productId) {
      console.error(`Product ID not found for billing cycle: ${billingCycle}`);
      return createResponse({
        error: "Product configuration not found",
        details: "The requested billing cycle is not available"
      }, 400);
    }
    try {
      // 1️⃣ Get the user profile to retrieve the customer ID
      const { data: profile, error: profileError } = await supabase.from("profiles").select("*").eq("id", userId).single();
      if (profileError || !profile) {
        console.error("Error retrieving user profile:", profileError);
        return createResponse({
          error: "User profile not found",
          details: profileError?.message || "Make sure the user exists and has a profile"
        }, 404);
      }
      if (!profile.dodo_customer_id) {
        console.error("User doesn't have a DodoPayments customer ID:", userId);
        return createResponse({
          error: "User doesn't have a DodoPayments customer ID",
          details: "Please create a customer first using the createCustomer endpoint"
        }, 400);
      }
      // 2️⃣ Format billing address from request
      const billingAddress = formatBillingAddress(requestData.addressData);
      // 3️⃣ Create subscription in DodoPayments
      const subscriptionData = {
        customer: {
          customer_id: profile.dodo_customer_id
        },
        product_id: productId,
        quantity: 1,
        payment_link: true,
        return_url: returnUrl,
        metadata: {
          user_id: userId,
          billing_cycle: billingCycle,
          email: email,
          provider: provider
        },
        billing: billingAddress || {
          city: "Bengaluru",
          country: "IN",
          state: "Karnataka",
          street: "Default Address",
          zipcode: "560001"
        },
        allowed_payment_method_types: [
          "credit",
          "debit",
          "upi_collect"
        ],
        billing_currency: "USD",
        show_saved_payment_methods: false
      };
      console.log("Creating subscription with data:", JSON.stringify(subscriptionData, null, 2));
      // Create the subscription in Dodo Payments
      const subscription = await client.subscriptions.create(subscriptionData);
      console.log(`DodoPayments subscription created: ${subscription.subscription_id}`);
      // Fetch detailed subscription information from Dodo Payments
      console.log(`Fetching detailed subscription information for ${subscription.subscription_id}`);
      const subscriptionDetails = await client.subscriptions.retrieve(subscription.subscription_id);
      console.log("Detailed subscription data:", JSON.stringify(subscriptionDetails, null, 2));
      // 4️⃣ Store subscription in database with complete details
      const { error: subscriptionError } = await supabase.from("subscriptions").insert({
        user_id: userId,
        subscription_id: subscriptionDetails.subscription_id,
        recurring_pre_tax_amount: subscriptionDetails.recurring_pre_tax_amount,
        tax_inclusive: subscriptionDetails.tax_inclusive || true,
        currency: subscriptionDetails.currency || "USD",
        status: subscriptionDetails.status || 'pending',
        created_at: subscriptionDetails.created_at || new Date().toISOString(),
        product_id: subscriptionDetails.product_id || productId,
        quantity: subscriptionDetails.quantity || 1,
        trial_period_days: subscriptionDetails.trial_period_days || 0,
        subscription_period_interval: subscriptionDetails.subscription_period_interval || (billingCycle === 'yearly' ? 'Year' : 'Month'),
        payment_frequency_interval: subscriptionDetails.payment_frequency_interval || 'Month',
        subscription_period_count: subscriptionDetails.subscription_period_count || 10,
        payment_frequency_count: subscriptionDetails.payment_frequency_count || 1,
        next_billing_date: subscriptionDetails.next_billing_date,
        previous_billing_date: subscriptionDetails.previous_billing_date || subscriptionDetails.created_at,
        customer: subscriptionDetails.customer,
        metadata: {
          user_id: userId,
          email: email,
          provider: provider,
          billing_cycle: billingCycle,
          ...subscriptionDetails.metadata
        },
        discount_id: subscriptionDetails.discount_id,
        cancelled_at: subscriptionDetails.cancelled_at,
        billing: subscriptionDetails.billing || billingAddress,
        on_demand: subscriptionDetails.on_demand || false,
        addons: subscriptionDetails.addons || [],
        payment_link: subscription.payment_link
      });
      if (subscriptionError) {
        console.error("Error storing subscription in database:", subscriptionError);
      // We don't return an error here because the subscription was created successfully in DodoPayments
      // We'll just log the error and continue
      }
      // 5️⃣ Return success response with only user_id, subscription_id and payment_link
      return createResponse({
        user_id: userId,
        subscription_id: subscriptionDetails.subscription_id,
        payment_link: subscription.payment_link
      }, 201);
    } catch (paymentError) {
      console.error("DodoPayments error:", paymentError);
      return createResponse({
        error: "Failed to create subscription",
        details: paymentError.message
      }, 400);
    }
  } catch (err) {
    console.error("createSubscription error:", err);
    return createResponse({
      error: "Internal server error",
      details: err.message
    }, 500);
  }
});
