// Simple script to update questions with company data
// Run this with: node scripts/updateCompanies.js

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'https://tcdieywqoophloivzszp.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRjZGlleXdxb29waGxvaXZ6c3pwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3NDMzMzksImV4cCI6MjA2MjMxOTMzOX0.Ypel9zPDOVJVBiEZFT-__cTZqgQL2-prOxiUXjieihM';

const supabase = createClient(supabaseUrl, supabaseKey);

// Mapping of question titles to companies that ask them
const questionCompaniesMap = {
  "WhatsApp": ["Meta (Facebook)", "Google", "Microsoft", "Amazon", "Apple"],
  "Chat Application": ["Google", "Meta", "Amazon", "Microsoft", "Twitter", "LinkedIn", "Slack"],
  "Airbnb": ["Airbnb", "Google", "Meta", "Microsoft"],
  "Spotify Playlist": ["Spotify", "Google", "Amazon", "Apple"],
  "Uber ETA": ["Uber", "Google", "Lyft", "DoorDash"],
  "Amazon Shopping Cart": ["Amazon", "Google", "Microsoft", "Shopify"],
  "Google Calendar": ["Google", "Apple", "Microsoft"],
  "Twitter Direct Messages": ["Twitter", "Google", "Meta", "LinkedIn"],
  "Instagram Stories": ["Meta (Instagram)", "Snapchat", "TikTok"],
  "Cache System": ["Google", "Amazon", "Meta", "Microsoft", "Netflix"],
  "Twitter Feed": ["Twitter", "Google", "Meta", "LinkedIn"],
  "Google Drive": ["Google", "Amazon", "Meta", "Microsoft"],
  "Facebook Messenger": ["Meta (Facebook)", "Google", "Amazon", "Microsoft"],
  "Instagram Feed": ["Meta (Instagram)", "Snapchat"],
  "Instagram Ads": ["Meta (Instagram)", "Google", "Snapchat"],
  "Uber Eats": ["Uber", "DoorDash", "Grubhub"],
  "Spotify Connect": ["Spotify", "Apple", "Google"],
  "Twitter Search": ["Twitter", "Google", "Microsoft"],
  "Amazon SQS": ["Amazon", "Microsoft", "Google"],
  "Netflix Playlist": ["Netflix", "Hulu", "Amazon Prime Video"],
  "Amazon Redshift": ["Amazon", "Snowflake", "Google"],
  "Twitter Notifications": ["Twitter", "Meta", "Google"],
  "Instagram Reels": ["Meta (Instagram)", "TikTok", "Snapchat"],
  "Google Search": ["Google", "Bing (Microsoft)", "Baidu"],
  "Facebook News Feed": ["Meta (Facebook)", "LinkedIn", "Twitter"],
  "Uber Dispatch System": ["Uber", "Lyft", "DoorDash"],
  "YouTube": ["Google", "Facebook (Meta)", "TikTok"],
  "Gmail": ["Google", "Microsoft (Outlook)"],
  "Google Maps": ["Google", "Apple", "HERE Technologies"],
  "Amazon DynamoDB": ["Amazon", "Google", "Microsoft"],
  "Uber Pool": ["Uber", "Lyft"],
  "Google Docs": ["Google", "Microsoft (Office 365)", "Apple"],
  "Amazon Recommendations": ["Amazon", "Netflix", "Spotify"],
  "Google Photos": ["Google", "Apple", "Amazon"],
  "Netflix Recommendations": ["Netflix", "Amazon Prime Video", "Hulu"],
  "Amazon EC2": ["Amazon", "Microsoft Azure", "Google Cloud"],
  "Uber Freight": ["Uber", "Convoy", "C.H. Robinson"],
  "Spotify Discover Weekly": ["Spotify", "Apple", "Google"],
  "Google Analytics": ["Google", "Adobe", "Mixpanel"],
  "Facebook Messenger Payments": ["Meta (Facebook)", "PayPal", "Google Pay"],
  "AWS S3": ["Amazon", "Google Cloud Storage", "Microsoft Azure"],
  "Google AdWords": ["Google", "Facebook Ads", "Microsoft Advertising"],
  "URL Shortener": ["Google", "Meta (Facebook)", "Bitly"],
  "Twitter Polls": ["Twitter", "Facebook", "LinkedIn"],
  "Twitter Lists": ["Twitter", "LinkedIn"],
  "To-Do List App": ["Google", "Microsoft", "Apple"],
  "Simple E-commerce Checkout": ["Amazon", "Google", "Shopify", "Walmart"],
  "User Authentication System": ["Google", "Meta", "Amazon", "Microsoft", "Okta"],
  "Blog Platform": ["Google (Blogger)", "Medium", "WordPress", "Meta (Facebook Notes)"]
};

async function updateQuestionsWithCompanies() {
  try {
    console.log('🏢 Starting to update questions with company data...');

    // Get all questions from the database
    const { data: questions, error: fetchError } = await supabase
      .from('questions')
      .select('id, title, companies')
      .eq('is_active', true);

    if (fetchError) {
      console.error('❌ Error fetching questions:', fetchError);
      return;
    }

    if (!questions || questions.length === 0) {
      console.log('📋 No questions found in database');
      return;
    }

    console.log(`📋 Found ${questions.length} questions to update`);

    // Update each question with company data
    let updatedCount = 0;
    let skippedCount = 0;

    for (const question of questions) {
      const companies = questionCompaniesMap[question.title];
      
      if (companies) {
        // Update the question with companies
        const { data: updateData, error: updateError } = await supabase
          .from('questions')
          .update({ companies })
          .eq('id', question.id)
          .select();

        if (updateError) {
          console.error(`❌ Error updating question ${question.id} (${question.title}):`, updateError);
        } else {
          console.log(`✅ Updated question ${question.id} (${question.title}) with ${companies.length} companies:`, companies);
          console.log('Update result:', updateData);
          updatedCount++;
        }
      } else {
        console.log(`⚠️ No company data found for question: ${question.title}`);
        skippedCount++;
      }
    }

    console.log(`🎉 Update complete! Updated: ${updatedCount}, Skipped: ${skippedCount}`);
  } catch (error) {
    console.error('❌ Error updating questions with companies:', error);
  }
}

// Run the update
updateQuestionsWithCompanies().then(() => {
  console.log('Script completed');
  process.exit(0);
}).catch(error => {
  console.error('Script failed:', error);
  process.exit(1);
});
