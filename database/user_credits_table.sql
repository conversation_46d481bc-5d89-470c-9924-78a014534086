-- User Credits Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create user_credits table
CREATE TABLE IF NOT EXISTS user_credits (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  credits INTEGER NOT NULL DEFAULT 0,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS user_credits_credits_idx ON user_credits(credits);
CREATE INDEX IF NOT EXISTS user_credits_updated_at_idx ON user_credits(updated_at);

-- Enable Row Level Security (RLS)
ALTER TABLE user_credits ENABLE ROW LEVEL SECURITY;

-- No user access to credits - admin only

-- Create policy for admin to view all credits
CREATE POLICY "Admin can view all credits" ON user_credits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create policy for admin to manage all credits
CREATE POLICY "Admin can manage all credits" ON user_credits
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_user_credits_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_user_credits_updated_at 
    BEFORE UPDATE ON user_credits 
    FOR EACH ROW EXECUTE FUNCTION update_user_credits_updated_at();

-- Create function to get user's credit balance
CREATE OR REPLACE FUNCTION get_user_credits(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    credit_balance INTEGER;
BEGIN
    SELECT COALESCE(credits, 0) INTO credit_balance
    FROM user_credits
    WHERE user_id = user_uuid;
    
    -- If no record exists, return 0
    IF credit_balance IS NULL THEN
        credit_balance := 0;
    END IF;
    
    RETURN credit_balance;
END;
$$ LANGUAGE plpgsql;

-- Create function to add credits to user
CREATE OR REPLACE FUNCTION add_user_credits(user_uuid UUID, credit_amount INTEGER)
RETURNS INTEGER AS $$
DECLARE
    new_balance INTEGER;
BEGIN
    -- Insert or update user credits
    INSERT INTO user_credits (user_id, credits)
    VALUES (user_uuid, credit_amount)
    ON CONFLICT (user_id)
    DO UPDATE SET 
        credits = user_credits.credits + credit_amount,
        updated_at = NOW()
    RETURNING credits INTO new_balance;
    
    RETURN new_balance;
END;
$$ LANGUAGE plpgsql;

-- Create function to deduct credits from user
CREATE OR REPLACE FUNCTION deduct_user_credits(user_uuid UUID, credit_amount INTEGER)
RETURNS INTEGER AS $$
DECLARE
    current_balance INTEGER;
    new_balance INTEGER;
BEGIN
    -- Get current balance
    SELECT COALESCE(credits, 0) INTO current_balance
    FROM user_credits
    WHERE user_id = user_uuid;
    
    -- Check if user has enough credits
    IF current_balance < credit_amount THEN
        RAISE EXCEPTION 'Insufficient credits. Current balance: %, Required: %', current_balance, credit_amount;
    END IF;
    
    -- Deduct credits
    UPDATE user_credits 
    SET credits = credits - credit_amount,
        updated_at = NOW()
    WHERE user_id = user_uuid
    RETURNING credits INTO new_balance;
    
    -- If no record exists, create one with negative balance (should not happen in normal flow)
    IF new_balance IS NULL THEN
        INSERT INTO user_credits (user_id, credits)
        VALUES (user_uuid, -credit_amount)
        RETURNING credits INTO new_balance;
    END IF;
    
    RETURN new_balance;
END;
$$ LANGUAGE plpgsql;

-- Create function to set user credits (useful for admin operations)
CREATE OR REPLACE FUNCTION set_user_credits(user_uuid UUID, credit_amount INTEGER)
RETURNS INTEGER AS $$
DECLARE
    new_balance INTEGER;
BEGIN
    -- Insert or update user credits
    INSERT INTO user_credits (user_id, credits)
    VALUES (user_uuid, credit_amount)
    ON CONFLICT (user_id)
    DO UPDATE SET 
        credits = credit_amount,
        updated_at = NOW()
    RETURNING credits INTO new_balance;
    
    RETURN new_balance;
END;
$$ LANGUAGE plpgsql;

-- Create function to initialize new users with basic plan and credits
CREATE OR REPLACE FUNCTION initialize_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Give new users 10 free credits
    INSERT INTO user_credits (user_id, credits)
    VALUES (NEW.id, 10)
    ON CONFLICT (user_id) DO NOTHING;
    
    -- Create basic plan subscription for new users
    INSERT INTO subscriptions (
        user_id,
        subscription_id,
        recurring_pre_tax_amount,
        tax_inclusive,
        currency,
        status,
        product_id,
        quantity,
        trial_period_days,
        subscription_period_interval,
        payment_frequency_interval,
        subscription_period_count,
        payment_frequency_count,
        billing_cycle,
        customer,
        metadata,
        on_demand,
        addons
    ) VALUES (
        NEW.id,
        'basic_' || NEW.id::text,
        0,
        true,
        'USD',
        'active',
        'basic',
        1,
        0,
        'month',
        'Month',
        1,
        1,
        'monthly',
        jsonb_build_object(
            'email', NEW.email,
            'name', COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
        ),
        jsonb_build_object(
            'created_at', NOW(),
            'auto_assigned', true
        ),
        false,
        '[]'::jsonb
    ) ON CONFLICT (subscription_id) DO NOTHING;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically initialize new users
CREATE TRIGGER initialize_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION initialize_new_user();