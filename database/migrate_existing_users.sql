-- Migration Script: Assign Basic Plan to Existing Users
-- Run this SQL script in your Supabase SQL Editor AFTER creating the plans table

-- Step 1: Identify users without subscriptions
-- This is for verification - you can run this to see how many users need migration
/*
SELECT 
    u.id, 
    u.email, 
    u.created_at,
    uc.credits
FROM auth.users u 
LEFT JOIN subscriptions s ON u.id = s.user_id 
LEFT JOIN user_credits uc ON u.id = uc.user_id
WHERE s.user_id IS NULL
ORDER BY u.created_at;
*/

-- Step 2: Create basic plan subscriptions for all existing users without subscriptions
INSERT INTO subscriptions (
    user_id,
    subscription_id,
    recurring_pre_tax_amount,
    tax_inclusive,
    currency,
    status,
    product_id,
    quantity,
    trial_period_days,
    subscription_period_interval,
    payment_frequency_interval,
    subscription_period_count,
    payment_frequency_count,
    billing_cycle,
    next_billing_date,
    previous_billing_date,
    customer,
    metadata,
    discount_id,
    billing,
    on_demand,
    addons,
    payment_link
)
SELECT 
    u.id as user_id,
    'basic_' || u.id::text as subscription_id,  -- Internal subscription ID
    0 as recurring_pre_tax_amount,              -- Basic plan is free
    true as tax_inclusive,
    'USD' as currency,
    'active' as status,
    'basic' as product_id,                      -- Links to our basic plan
    1 as quantity,
    0 as trial_period_days,
    'month' as subscription_period_interval,
    'Month' as payment_frequency_interval,
    1 as subscription_period_count,
    1 as payment_frequency_count,
    'monthly' as billing_cycle,
    NULL as next_billing_date,                  -- No billing for free plan
    NULL as previous_billing_date,
    jsonb_build_object(
        'email', u.email,
        'name', COALESCE(u.raw_user_meta_data->>'full_name', u.email)
    ) as customer,
    jsonb_build_object(
        'migrated_at', NOW(),
        'migration_type', 'existing_user_basic_plan'
    ) as metadata,
    NULL as discount_id,
    NULL as billing,
    false as on_demand,
    '[]'::jsonb as addons,
    NULL as payment_link
FROM auth.users u 
LEFT JOIN subscriptions s ON u.id = s.user_id 
WHERE s.user_id IS NULL;  -- Only users without existing subscriptions

-- Step 3: Ensure all users have correct credit balance for basic plan
-- Update existing users' credits to match basic plan allocation (10 credits)
INSERT INTO user_credits (user_id, credits)
SELECT u.id, 10
FROM auth.users u
LEFT JOIN user_credits uc ON u.id = uc.user_id
WHERE uc.user_id IS NULL
ON CONFLICT (user_id) DO UPDATE SET
    credits = GREATEST(user_credits.credits, 10),  -- Don't reduce existing credits
    updated_at = NOW();

-- Step 4: Verification query - run this to confirm migration
/*
SELECT 
    COUNT(*) as total_users,
    COUNT(s.user_id) as users_with_subscriptions,
    COUNT(uc.user_id) as users_with_credits
FROM auth.users u
LEFT JOIN subscriptions s ON u.id = s.user_id
LEFT JOIN user_credits uc ON u.id = uc.user_id;

-- Should show equal counts for all three columns
*/

-- Step 5: View migrated users (optional)
/*
SELECT 
    u.email,
    s.product_id,
    s.status,
    uc.credits,
    s.created_at as subscription_created
FROM auth.users u
JOIN subscriptions s ON u.id = s.user_id
JOIN user_credits uc ON u.id = uc.user_id
WHERE s.product_id = 'basic'
ORDER BY s.created_at DESC;
*/