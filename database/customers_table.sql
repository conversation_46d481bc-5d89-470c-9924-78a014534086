-- Customers Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  dodo_customer_id TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_user_id ON customers(user_id);
CREATE INDEX IF NOT EXISTS idx_customers_dodo_customer_id ON customers(dodo_customer_id);
CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email);

-- Enable Row Level Security (RLS)
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can only see their own customer record
CREATE POLICY "Users can view own customer record" ON customers
  FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own customer record
CREATE POLICY "Users can update own customer record" ON customers
  FOR UPDATE USING (auth.uid() = user_id);

-- Only authenticated users can insert (handled by edge functions)
CREATE POLICY "Service role can insert customers" ON customers
  FOR INSERT WITH CHECK (true);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_customers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER customers_updated_at_trigger
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_customers_updated_at();

-- Create function to get or create customer
CREATE OR REPLACE FUNCTION get_or_create_customer(
    p_user_id UUID,
    p_dodo_customer_id TEXT,
    p_name TEXT,
    p_email TEXT,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    dodo_customer_id TEXT,
    name TEXT,
    email TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    metadata JSONB
) AS $$
BEGIN
    -- Try to get existing customer
    RETURN QUERY
    SELECT c.id, c.user_id, c.dodo_customer_id, c.name, c.email, c.created_at, c.updated_at, c.metadata
    FROM customers c
    WHERE c.user_id = p_user_id;
    
    -- If no customer found, create one
    IF NOT FOUND THEN
        RETURN QUERY
        INSERT INTO customers (user_id, dodo_customer_id, name, email, metadata)
        VALUES (p_user_id, p_dodo_customer_id, p_name, p_email, p_metadata)
        RETURNING customers.id, customers.user_id, customers.dodo_customer_id, 
                  customers.name, customers.email, customers.created_at, 
                  customers.updated_at, customers.metadata;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON customers TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Comments for documentation
COMMENT ON TABLE customers IS 'Stores DodoPayments customer information linked to Supabase users';
COMMENT ON COLUMN customers.user_id IS 'Reference to auth.users.id';
COMMENT ON COLUMN customers.dodo_customer_id IS 'Customer ID from DodoPayments API';
COMMENT ON COLUMN customers.name IS 'Customer full name';
COMMENT ON COLUMN customers.email IS 'Customer email address';
COMMENT ON COLUMN customers.metadata IS 'Additional customer metadata from DodoPayments';
