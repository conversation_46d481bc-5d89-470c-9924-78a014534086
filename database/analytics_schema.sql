-- Analytics Tables for Layrs Platform
-- Run these SQL commands in your Supabase SQL Editor

-- 1. Chat Analytics Table
CREATE TABLE IF NOT EXISTS chat_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL UNIQUE,
    message_count INTEGER DEFAULT 0,
    conversation_duration_seconds INTEGER,
    question_id TEXT,
    context_type TEXT CHECK (context_type IN ('free', 'question', 'guided')),
    context_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Assessment Analytics Table
CREATE TABLE IF NOT EXISTS assessment_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    question_id TEXT NOT NULL,
    context_type TEXT NOT NULL CHECK (context_type IN ('question', 'guided')),
    context_id TEXT NOT NULL,
    score INTEGER NOT NULL CHECK (score >= 0 AND score <= 10),
    duration_seconds INTEGER NOT NULL,
    node_count INTEGER DEFAULT 0,
    edge_count INTEGER DEFAULT 0,
    components_used TEXT[] DEFAULT '{}',
    is_best_score BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. User Session Analytics Table
CREATE TABLE IF NOT EXISTS user_session_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    session_start TIMESTAMP WITH TIME ZONE NOT NULL,
    session_end TIMESTAMP WITH TIME ZONE,
    page_views INTEGER DEFAULT 1,
    actions_performed INTEGER DEFAULT 0,
    questions_attempted TEXT[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Component Usage Analytics Table (for tracking which components are most used)
CREATE TABLE IF NOT EXISTS component_usage_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    component_type TEXT NOT NULL,
    question_id TEXT,
    context_type TEXT CHECK (context_type IN ('free', 'question', 'guided')),
    context_id TEXT,
    usage_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_chat_analytics_user_id ON chat_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_created_at ON chat_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_chat_analytics_question_id ON chat_analytics(question_id);

CREATE INDEX IF NOT EXISTS idx_assessment_analytics_user_id ON assessment_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_assessment_analytics_question_id ON assessment_analytics(question_id);
CREATE INDEX IF NOT EXISTS idx_assessment_analytics_created_at ON assessment_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_assessment_analytics_score ON assessment_analytics(score);

CREATE INDEX IF NOT EXISTS idx_user_session_analytics_user_id ON user_session_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_user_session_analytics_session_start ON user_session_analytics(session_start);

CREATE INDEX IF NOT EXISTS idx_component_usage_analytics_user_id ON component_usage_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_component_usage_analytics_component_type ON component_usage_analytics(component_type);

-- Row Level Security (RLS) Policies
ALTER TABLE chat_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE assessment_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_session_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE component_usage_analytics ENABLE ROW LEVEL SECURITY;

-- Policies for chat_analytics
CREATE POLICY "Users can view their own chat analytics" ON chat_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own chat analytics" ON chat_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own chat analytics" ON chat_analytics
    FOR UPDATE USING (auth.uid() = user_id);

-- Policies for assessment_analytics
CREATE POLICY "Users can view their own assessment analytics" ON assessment_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own assessment analytics" ON assessment_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies for user_session_analytics
CREATE POLICY "Users can view their own session analytics" ON user_session_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own session analytics" ON user_session_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own session analytics" ON user_session_analytics
    FOR UPDATE USING (auth.uid() = user_id);

-- Policies for component_usage_analytics
CREATE POLICY "Users can view their own component usage analytics" ON component_usage_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own component usage analytics" ON component_usage_analytics
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admin policies (for <EMAIL> to view all analytics)
CREATE POLICY "Admin can view all chat analytics" ON chat_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

CREATE POLICY "Admin can view all assessment analytics" ON assessment_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

CREATE POLICY "Admin can view all session analytics" ON user_session_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

CREATE POLICY "Admin can view all component usage analytics" ON component_usage_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for chat_analytics
CREATE TRIGGER update_chat_analytics_updated_at 
    BEFORE UPDATE ON chat_analytics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for easy analytics querying
CREATE OR REPLACE VIEW analytics_summary AS
SELECT 
    'chat' as metric_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT user_id) as unique_users,
    DATE_TRUNC('day', created_at) as date
FROM chat_analytics
GROUP BY DATE_TRUNC('day', created_at)

UNION ALL

SELECT 
    'assessment' as metric_type,
    COUNT(*) as total_count,
    COUNT(DISTINCT user_id) as unique_users,
    DATE_TRUNC('day', created_at) as date
FROM assessment_analytics
GROUP BY DATE_TRUNC('day', created_at)

ORDER BY date DESC;
