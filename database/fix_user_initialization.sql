-- Fix User Initialization: Ensure all users have basic plan and credits
-- Run this SQL in Supabase SQL Editor

BEGIN;

-- 1. First, let's check if the trigger exists and recreate it
DROP TRIGGER IF EXISTS initialize_new_user_trigger ON auth.users;

-- 2. Recreate the initializat  ion function - <PERSON><PERSON><PERSON> create credits, no subscription
CREATE OR REPLACE FUNCTION initialize_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Log the user creation
    RAISE NOTICE 'Initializing new user: %', NEW.id;

    -- Give new users 10 free credits (basic plan default)
    INSERT INTO user_credits (user_id, credits)
    VALUES (NEW.id, 10)
    ON CONFLICT (user_id) DO NOTHING;

    RAISE NOTICE 'User initialization completed for: % (10 credits assigned)', NEW.id;
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error initializing user %: %', NEW.id, SQLERRM;
        RETURN NEW; -- Don't fail user creation if initialization fails
END;
$$ LANGUAGE plpgsql;

-- 3. Recreate the trigger
CREATE TRIGGER initialize_new_user_trigger
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION initialize_new_user();

-- 4. Migrate existing users who don't have credits or subscriptions
-- First, add credits for users who don't have any
INSERT INTO user_credits (user_id, credits)
SELECT 
    u.id,
    10 as credits
FROM auth.users u
LEFT JOIN user_credits uc ON u.id = uc.user_id
WHERE uc.user_id IS NULL;

-- 5. No need to create basic plan subscriptions - users are on basic plan by default
-- Only pro plan users will have subscription records

-- 6. Create function to handle subscription upgrades
CREATE OR REPLACE FUNCTION handle_subscription_upgrade(
    p_user_id UUID,
    p_new_plan_id TEXT
)
RETURNS VOID AS $$
BEGIN
    -- Cancel any existing pro subscriptions (in case of plan changes)
    UPDATE subscriptions
    SET status = 'cancelled',
        cancelled_at = NOW(),
        updated_at = NOW()
    WHERE user_id = p_user_id
    AND status = 'active'
    AND product_id = 'pro';

    -- Set credits based on plan type
    IF p_new_plan_id = 'pro' THEN
        -- Set credits to 100 for pro users (replace, don't add)
        UPDATE user_credits
        SET credits = 100,
            updated_at = NOW()
        WHERE user_id = p_user_id;
    ELSE
        -- Back to basic plan - set to 10 credits
        UPDATE user_credits
        SET credits = 10,
            updated_at = NOW()
        WHERE user_id = p_user_id;
    END IF;

    RAISE NOTICE 'Subscription upgrade completed for user: % to plan: %', p_user_id, p_new_plan_id;
END;
$$ LANGUAGE plpgsql;

-- 7. Create function for daily credit renewal (for basic plan users only)
CREATE OR REPLACE FUNCTION renew_daily_credits()
RETURNS INTEGER AS $$
DECLARE
    renewed_count INTEGER := 0;
BEGIN
    -- Renew credits for basic plan users (users WITHOUT active pro subscriptions)
    UPDATE user_credits
    SET credits = 10,
        updated_at = NOW()
    WHERE user_id NOT IN (
        SELECT user_id
        FROM subscriptions
        WHERE status = 'active'
        AND product_id = 'pro'
    );

    GET DIAGNOSTICS renewed_count = ROW_COUNT;

    RAISE NOTICE 'Renewed daily credits for % basic plan users', renewed_count;
    RETURN renewed_count;
END;
$$ LANGUAGE plpgsql;

COMMIT;

-- 8. Verify the migration worked
SELECT
    'Total Users' as metric,
    COUNT(*) as count
FROM auth.users
UNION ALL
SELECT
    'Users with Credits' as metric,
    COUNT(*) as count
FROM user_credits
UNION ALL
SELECT
    'Pro Plan Users (with active subscriptions)' as metric,
    COUNT(*) as count
FROM subscriptions
WHERE status = 'active' AND product_id = 'pro'
UNION ALL
SELECT
    'Basic Plan Users (no active subscription)' as metric,
    COUNT(*) as count
FROM auth.users u
WHERE NOT EXISTS (
    SELECT 1 FROM subscriptions s
    WHERE s.user_id = u.id
    AND s.status = 'active'
    AND s.product_id = 'pro'
);
