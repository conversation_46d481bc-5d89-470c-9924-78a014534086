-- Refunds Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create refunds table with DodoPayments schema
CREATE TABLE IF NOT EXISTS refunds (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  refund_id TEXT NOT NULL,
  payment_id TEXT NOT NULL,
  business_id TEXT NOT NULL,
  amount NUMERIC NOT NULL,
  currency TEXT NOT NULL,
  status TEXT NOT NULL,
  reason TEXT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NULL DEFAULT NOW(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  metadata JSONB NULL DEFAULT '{}'::jsonb
);

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS refunds_user_id_idx ON refunds(user_id);
CREATE INDEX IF NOT EXISTS refunds_refund_id_idx ON refunds(refund_id);
CREATE INDEX IF NOT EXISTS refunds_payment_id_idx ON refunds(payment_id);
CREATE INDEX IF NOT EXISTS refunds_business_id_idx ON refunds(business_id);
CREATE INDEX IF NOT EXISTS refunds_status_idx ON refunds(status);
CREATE INDEX IF NOT EXISTS refunds_created_at_idx ON refunds(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE refunds ENABLE ROW LEVEL SECURITY;

-- No user access to refunds - admin only

-- Create policy for admin to view all refunds
CREATE POLICY "Admin can view all refunds" ON refunds
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create policy for admin to manage all refunds
CREATE POLICY "Admin can manage all refunds" ON refunds
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_refunds_updated_at 
    BEFORE UPDATE ON refunds 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to process refund with new schema
CREATE OR REPLACE FUNCTION process_refund(
    user_uuid UUID,
    dodo_payment_id TEXT,
    refund_amount NUMERIC,
    refund_currency TEXT,
    dodo_refund_id TEXT,
    refund_reason TEXT DEFAULT NULL,
    refund_business_id TEXT DEFAULT NULL,
    refund_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    refund_uuid UUID;
    payment_info RECORD;
    credits_to_deduct INTEGER;
BEGIN
    -- Get payment info using DodoPayments payment_id
    SELECT p.total_amount, s.product_id
    INTO payment_info
    FROM payments p
    LEFT JOIN subscriptions s ON p.subscription_id = s.subscription_id
    WHERE p.payment_id = dodo_payment_id AND p.user_id = user_uuid;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Payment not found for user';
    END IF;
    
    -- Calculate credits to deduct (proportional to refund amount)
    IF payment_info.product_id IS NOT NULL THEN
        -- Get credits from plan mapped to product
        SELECT credits INTO credits_to_deduct
        FROM plans 
        WHERE dodopayments_plan_id = payment_info.product_id;
        
        IF credits_to_deduct > 0 AND payment_info.total_amount > 0 THEN
            credits_to_deduct := ROUND((refund_amount / payment_info.total_amount) * credits_to_deduct);
        ELSE
            credits_to_deduct := 0;
        END IF;
    ELSE
        credits_to_deduct := 0;
    END IF;
    
    -- Create refund record
    INSERT INTO refunds (
        user_id,
        refund_id,
        payment_id,
        business_id,
        amount,
        currency,
        status,
        reason,
        metadata
    ) VALUES (
        user_uuid,
        dodo_refund_id,
        dodo_payment_id,
        refund_business_id,
        refund_amount,
        refund_currency,
        'pending',
        refund_reason,
        refund_metadata
    ) RETURNING id INTO refund_uuid;
    
    -- Deduct credits if user has them
    IF credits_to_deduct > 0 THEN
        BEGIN
            PERFORM deduct_user_credits(user_uuid, credits_to_deduct);
        EXCEPTION
            WHEN OTHERS THEN
                -- If deduction fails, set credits to 0
                PERFORM set_user_credits(user_uuid, 0);
        END;
    END IF;
    
    RETURN refund_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create function to complete refund
CREATE OR REPLACE FUNCTION complete_refund(dodo_refund_id TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    refund_record RECORD;
    payment_total NUMERIC;
BEGIN
    -- Update refund status
    UPDATE refunds 
    SET status = 'succeeded', updated_at = NOW()
    WHERE refund_id = dodo_refund_id
    RETURNING * INTO refund_record;
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if this is a full refund by comparing with original payment amount
    SELECT total_amount INTO payment_total
    FROM payments
    WHERE payment_id = refund_record.payment_id;
    
    -- If full refund, cancel associated subscription
    IF payment_total IS NOT NULL AND refund_record.amount >= payment_total THEN
        UPDATE subscriptions 
        SET status = 'cancelled', 
            cancelled_at = NOW(),
            updated_at = NOW()
        WHERE subscription_id IN (
            SELECT subscription_id 
            FROM payments 
            WHERE payment_id = refund_record.payment_id
        );
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user's refund history (admin only)
CREATE OR REPLACE FUNCTION get_user_refund_history(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    refund_id TEXT,
    payment_id TEXT,
    amount NUMERIC,
    currency TEXT,
    status TEXT,
    reason TEXT,
    business_id TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id,
        r.refund_id,
        r.payment_id,
        r.amount,
        r.currency,
        r.status,
        r.reason,
        r.business_id,
        r.created_at
    FROM refunds r
    WHERE r.user_id = user_uuid
    ORDER BY r.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create view for refund analytics
CREATE OR REPLACE VIEW refund_analytics AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    status,
    currency,
    business_id,
    COUNT(*) as refund_count,
    SUM(amount) as total_refunded,
    AVG(amount) as average_refund
FROM refunds
GROUP BY DATE_TRUNC('month', created_at), status, currency, business_id
ORDER BY month DESC;