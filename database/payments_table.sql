-- Payments Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create payments table with DodoPayments schema
CREATE TABLE IF NOT EXISTS payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  payment_id TEXT NOT NULL,
  status TEXT NOT NULL,
  total_amount NUMERIC NOT NULL,
  currency TEXT NOT NULL,
  payment_method TEXT NULL,
  payment_method_type TEXT NULL,
  customer JSONB NULL,
  subscription_id TEXT NOT NULL,
  customer_id TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  metadata JSONB NULL DEFAULT '{}'::jsonb,
  business_id TEXT NULL,
  error_message TEXT NULL,
  discount_id TEXT NULL,
  settlement_amount BIGINT NULL,
  settlement_currency TEXT NULL,
  billing JSONB NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS payments_user_id_idx ON payments(user_id);
CREATE INDEX IF NOT EXISTS payments_payment_id_idx ON payments(payment_id);
CREATE INDEX IF NOT EXISTS payments_customer_id_idx ON payments(customer_id);
CREATE INDEX IF NOT EXISTS payments_subscription_id_idx ON payments(subscription_id);
CREATE INDEX IF NOT EXISTS payments_status_idx ON payments(status);
CREATE INDEX IF NOT EXISTS payments_created_at_idx ON payments(created_at);
CREATE INDEX IF NOT EXISTS payments_payment_method_idx ON payments(payment_method);
CREATE INDEX IF NOT EXISTS payments_business_id_idx ON payments(business_id);

-- Enable Row Level Security (RLS)
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- No user access to payments - admin only

-- Create policy for admin to view all payments
CREATE POLICY "Admin can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create policy for admin to manage all payments
CREATE POLICY "Admin can manage all payments" ON payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create view for payment summary
CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    status,
    currency,
    payment_method,
    COUNT(*) as transaction_count,
    SUM(total_amount) as total_amount,
    AVG(total_amount) as average_amount,
    SUM(settlement_amount) as total_settlement
FROM payments
GROUP BY DATE_TRUNC('month', created_at), status, currency, payment_method
ORDER BY month DESC;

-- Create function to get user's payment history (admin only)
CREATE OR REPLACE FUNCTION get_user_payment_history(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    payment_id TEXT,
    total_amount NUMERIC,
    currency TEXT,
    status TEXT,
    payment_method TEXT,
    customer_id TEXT,
    created_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.payment_id,
        p.total_amount,
        p.currency,
        p.status,
        p.payment_method,
        p.customer_id,
        p.created_at
    FROM payments p
    WHERE p.user_id = user_uuid
    ORDER BY p.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get total revenue
CREATE OR REPLACE FUNCTION get_total_revenue(start_date TIMESTAMPTZ DEFAULT NULL, end_date TIMESTAMPTZ DEFAULT NULL)
RETURNS NUMERIC AS $$
DECLARE
    total_revenue NUMERIC;
BEGIN
    IF start_date IS NULL THEN
        start_date := '1970-01-01'::timestamptz;
    END IF;
    
    IF end_date IS NULL THEN
        end_date := NOW();
    END IF;
    
    SELECT COALESCE(SUM(total_amount), 0) INTO total_revenue
    FROM payments
    WHERE status = 'succeeded'
    AND created_at BETWEEN start_date AND end_date;
    
    RETURN total_revenue;
END;
$$ LANGUAGE plpgsql;