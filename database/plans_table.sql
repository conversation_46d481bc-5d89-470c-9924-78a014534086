-- Plans Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create plans table
CREATE TABLE IF NOT EXISTS plans (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  price NUMERIC NOT NULL,
  period TEXT NOT NULL,
  credits INTEGER NOT NULL DEFAULT 0,
  dodopayments_plan_id TEXT
);

-- Insert default plans
INSERT INTO plans (id, name, price, period, credits, dodopayments_plan_id) VALUES
('basic', 'Basic Plan', 0, 'monthly', 10, 'basic'),
('pro', 'Pro Plan', 999, 'monthly', 100, 'pro')
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  price = EXCLUDED.price,
  period = EXCLUDED.period,
  credits = EXCLUDED.credits,
  dodopayments_plan_id = EXCLUDED.dodopayments_plan_id;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_plans_period ON plans(period);
CREATE INDEX IF NOT EXISTS idx_plans_dodopayments_plan_id ON plans(dodopayments_plan_id);

-- Enable Row Level Security (RLS)
ALTER TABLE plans ENABLE ROW LEVEL SECURITY;

-- No public access to plans - admin only

-- Create policy to allow admin to manage plans
CREATE POLICY "Admin can manage plans" ON plans
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );