-- Subscriptions Table for DodoPayments Integration
-- Run this SQL command in your Supabase SQL Editor

-- Create subscriptions table with comprehensive DodoPayments schema
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_id TEXT NOT NULL,
  recurring_pre_tax_amount NUMERIC NULL,
  tax_inclusive BOOLEAN NULL DEFAULT true,
  currency TEXT NULL DEFAULT 'USD',
  status TEXT NOT NULL,
  created_at TIMESTAMPTZ NULL DEFAULT NOW(),
  product_id TEXT NOT NULL,
  quantity INTEGER NULL DEFAULT 1,
  trial_period_days INTEGER NULL DEFAULT 0,
  subscription_period_interval TEXT NULL,
  payment_frequency_interval TEXT NULL DEFAULT 'Month',
  subscription_period_count INTEGER NULL,
  payment_frequency_count INTEGER NULL DEFAULT 1,
  billing_cycle TEXT NULL,
  next_billing_date TIMESTAMPTZ NULL,
  previous_billing_date TIMESTAMPTZ NULL,
  customer JSONB NULL,
  metadata JSONB NULL,
  discount_id TEXT NULL,
  cancelled_at TIMESTAMPTZ NULL,
  billing JSONB NULL,
  on_demand BOOLEAN NULL DEFAULT false,
  addons JSONB NULL DEFAULT '[]'::jsonb,
  payment_link TEXT NULL,
  updated_at TIMESTAMPTZ NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS subscriptions_user_id_idx ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS subscriptions_subscription_id_idx ON subscriptions(subscription_id);
CREATE INDEX IF NOT EXISTS subscriptions_status_idx ON subscriptions(status);
CREATE INDEX IF NOT EXISTS subscriptions_product_id_idx ON subscriptions(product_id);
CREATE INDEX IF NOT EXISTS subscriptions_next_billing_date_idx ON subscriptions(next_billing_date);
CREATE INDEX IF NOT EXISTS subscriptions_cancelled_at_idx ON subscriptions(cancelled_at);
CREATE INDEX IF NOT EXISTS subscriptions_discount_id_idx ON subscriptions(discount_id);
CREATE INDEX IF NOT EXISTS subscriptions_on_demand_idx ON subscriptions(on_demand);

-- Enable Row Level Security (RLS)
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own subscriptions
CREATE POLICY "Users can view their own subscriptions" ON subscriptions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can only view their own subscriptions - no insert/update
-- All subscription management handled by webhooks

-- Create policy for admin to view all subscriptions
CREATE POLICY "Admin can view all subscriptions" ON subscriptions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create policy for admin to manage all subscriptions
CREATE POLICY "Admin can manage all subscriptions" ON subscriptions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.email = '<EMAIL>'
        )
    );

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to get user's active subscription
CREATE OR REPLACE FUNCTION get_user_active_subscription(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    subscription_id TEXT,
    product_id TEXT,
    status TEXT,
    recurring_pre_tax_amount NUMERIC,
    currency TEXT,
    quantity INTEGER,
    next_billing_date TIMESTAMPTZ,
    trial_period_days INTEGER,
    addons JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.subscription_id,
        s.product_id,
        s.status,
        s.recurring_pre_tax_amount,
        s.currency,
        s.quantity,
        s.next_billing_date,
        s.trial_period_days,
        s.addons
    FROM subscriptions s
    WHERE s.user_id = user_uuid 
    AND s.status = 'active'
    ORDER BY s.created_at DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;