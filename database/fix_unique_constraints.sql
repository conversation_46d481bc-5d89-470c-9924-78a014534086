-- Fix Missing Unique Constraints for Webhook Processing
-- Run this SQL command in your Supabase SQL Editor

-- Add unique constraint on subscription_id (required for upsert operations)
ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_subscription_id_unique UNIQUE (subscription_id);

-- Add unique constraint on payment_id (required for upsert operations)  
ALTER TABLE payments ADD CONSTRAINT payments_payment_id_unique UNIQUE (payment_id);

-- Verify the constraints were added
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid IN (
    SELECT oid FROM pg_class WHERE relname IN ('subscriptions', 'payments')
) 
AND contype = 'u'  -- unique constraints
ORDER BY conrelid, conname;
